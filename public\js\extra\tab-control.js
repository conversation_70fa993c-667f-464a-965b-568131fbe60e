// 标签页控制模块 - 确保同一时间只有一个标签激活
document.addEventListener('DOMContentLoaded', function() {
    const summaryTabBtn = document.getElementById('summaryTabBtn');
    const mindmapTabBtn = document.getElementById('mindmapTabBtn');
    const knowledgegraphTabBtn = document.getElementById('knowledgegraphTabBtn');
    
    const summaryTab = document.getElementById('summaryTab');
    const mindmapTab = document.getElementById('mindmapTab');
    const knowledgegraphTab = document.getElementById('knowledgegraphTab');
    
    const aiSummaryDiv = document.getElementById('aiSummary');
    const aiMindmapDiv = document.getElementById('aiMindmap');
    
    if (!summaryTabBtn || !mindmapTabBtn || !knowledgegraphTabBtn) {
        console.error('标签按钮未找到');
        return;
    }
    
    // 辅助函数：清除所有标签的激活状态
    function clearAllActiveStatus() {
        // 移除按钮激活状态
        summaryTabBtn.classList.remove('active');
        mindmapTabBtn.classList.remove('active');
        knowledgegraphTabBtn.classList.remove('active');
        
        // 移除内容区域激活状态
        summaryTab.classList.remove('active');
        mindmapTab.classList.remove('active');
        knowledgegraphTab.classList.remove('active');
    }
    
    // 辅助函数：创建下载工具栏
    function createDownloadToolbar(container, type, content) {
        if (typeof window.createDownloadToolbar === 'function') {
            window.createDownloadToolbar(container, type, content);
        }
    }
    
    // 辅助函数：重置脑图视图
    function resetMindmapView() {
        if (typeof window.resetMindmapView === 'function') {
            window.resetMindmapView();
        }
    }
    
    // 摘要标签点击处理
    summaryTabBtn.addEventListener('click', function() {
        clearAllActiveStatus();
        summaryTabBtn.classList.add('active');
        summaryTab.classList.add('active');
        
        // 如果有内容，添加下载工具栏
        if (aiSummaryDiv.innerHTML && !aiSummaryDiv.innerHTML.includes('点击下方按钮生成')) {
            createDownloadToolbar(summaryTab, 'summary', aiSummaryDiv.innerHTML);
        }
    });
    
    // 脑图标签点击处理
    mindmapTabBtn.addEventListener('click', function() {
        clearAllActiveStatus();
        mindmapTabBtn.classList.add('active');
        mindmapTab.classList.add('active');
        
        // 只重置视图，不清空内容
        resetMindmapView();
        
        // 如果有内容，添加下载工具栏
        if (aiMindmapDiv.querySelector('svg')) {
            createDownloadToolbar(mindmapTab, 'mindmap', aiMindmapDiv.querySelector('svg').outerHTML);
        }
    });
    
    // 知识图谱标签点击处理
    knowledgegraphTabBtn.addEventListener('click', function() {
        clearAllActiveStatus();
        knowledgegraphTabBtn.classList.add('active');
        knowledgegraphTab.classList.add('active');
        
        // 对于知识图谱，我们可能需要重新调整图谱大小
        if (typeof window.autoFitContent === 'function') {
            setTimeout(window.autoFitContent, 10);
        }
    });
    
    // 确保页面加载时默认选中第一个标签
    summaryTabBtn.click();
}); 