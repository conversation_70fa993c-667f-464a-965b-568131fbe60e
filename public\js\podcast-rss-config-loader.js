/**
 * 播客RSS Feed配置加载器
 * 统一管理RSS修复配置，避免多文件维护
 */
class PodcastRSSConfigLoader {
    constructor() {
        this.config = null;
        this.fixes = {};
        this.loaded = false;
    }

    // 加载配置文件
    async loadConfig() {
        if (this.loaded) {
            return this.config;
        }

        try {
            console.log('🔄 正在加载播客RSS修复配置...');
            const response = await fetch('/config/podcast-rss-fixes.json?v=' + Date.now());
            
            if (!response.ok) {
                throw new Error(`配置文件加载失败: ${response.status}`);
            }

            this.config = await response.json();
            this.processFixes();
            this.loaded = true;

            console.log('✅ 播客RSS修复配置加载完成');
            console.log(`📊 配置版本: ${this.config.version}`);
            console.log(`📅 更新时间: ${this.config.lastUpdated}`);
            console.log(`🔧 可用修复数量: ${Object.keys(this.fixes).length}`);

            return this.config;
        } catch (error) {
            console.error('❌ 配置文件加载失败:', error);
            // 降级到默认配置
            this.useDefaultConfig();
            return this.config;
        }
    }

    // 处理修复配置
    processFixes() {
        this.fixes = {};
        
        // 处理主要修复配置
        if (this.config.fixes) {
            Object.entries(this.config.fixes).forEach(([key, fix]) => {
                if (fix.enabled !== false) {
                    this.fixes[key] = fix;
                    console.log(`🔧 已启用修复: ${fix.name} (${key})`);
                }
            });
        }

        // 处理示例配置中的启用项
        if (this.config.examples) {
            Object.entries(this.config.examples).forEach(([key, fix]) => {
                if (fix.enabled === true) {
                    this.fixes[key] = fix;
                    console.log(`🔧 已启用示例修复: ${fix.name} (${key})`);
                }
            });
        }
    }

    // 使用默认配置（降级方案）
    useDefaultConfig() {
        console.warn('⚠️ 使用默认RSS修复配置');
        this.config = {
            version: "1.0.0-fallback",
            description: "默认RSS修复配置（降级方案）",
            fixes: {
                mckinsey: {
                    name: "McKinsey Inside the Strategy Room",
                    keywords: ["mckinsey", "strategy room"],
                    urlKeywords: ["mckinsey"],
                    correctUrl: "https://www.omnycontent.com/d/playlist/708664bd-6843-4623-8066-aede00ce0c8a/a7ee33f2-d500-4226-b99c-af04013945d6/36587f70-89f9-4631-ac19-af04013945e0/podcast.rss",
                    enabled: true
                }
            }
        };
        this.processFixes();
        this.loaded = true;
    }

    // 核心修复函数
    fixRssUrl(originalUrl, podcastName = '') {
        if (!originalUrl) return originalUrl;
        
        console.log(`===== RSS URL修复检查 =====`);
        console.log(`原始URL: ${originalUrl}`);
        console.log(`播客名称: ${podcastName}`);
        
        const lowerPodcastName = podcastName.toLowerCase();
        const lowerUrl = originalUrl.toLowerCase();
        
        // 遍历所有启用的修复配置
        for (const [key, fix] of Object.entries(this.fixes)) {
            // 检查播客名称关键词
            const nameMatched = fix.keywords?.some(keyword => 
                lowerPodcastName.includes(keyword.toLowerCase())
            );
            
            // 检查URL关键词
            const urlMatched = fix.urlKeywords?.some(keyword => 
                lowerUrl.includes(keyword.toLowerCase())
            );
            
            if (nameMatched || urlMatched) {
                const fixedUrl = fix.correctUrl;
                console.log(`✅ 检测到${fix.name}播客，URL已修复`);
                console.log(`🔗 修复URL: ${fixedUrl}`);
                console.log(`🎯 匹配方式: ${nameMatched ? '名称' : ''}${nameMatched && urlMatched ? '+' : ''}${urlMatched ? 'URL' : ''}`);
                return fixedUrl;
            }
        }
        
        console.log(`ℹ️ 无需修复，使用原始URL`);
        return originalUrl;
    }

    // 获取所有可用的修复配置
    getAllFixes() {
        return this.fixes;
    }

    // 获取配置统计信息
    getStats() {
        const totalFixes = Object.keys(this.config?.fixes || {}).length;
        const totalExamples = Object.keys(this.config?.examples || {}).length;
        const enabledFixes = Object.keys(this.fixes).length;
        
        return {
            version: this.config?.version || 'unknown',
            lastUpdated: this.config?.lastUpdated || 'unknown',
            totalFixes,
            totalExamples,
            enabledFixes,
            loaded: this.loaded
        };
    }

    // 重新加载配置
    async reload() {
        this.loaded = false;
        this.config = null;
        this.fixes = {};
        return await this.loadConfig();
    }
}

// 创建全局单例
window.podcastRSSConfig = new PodcastRSSConfigLoader();

// 自动加载配置
document.addEventListener('DOMContentLoaded', async () => {
    await window.podcastRSSConfig.loadConfig();
});

// 导出给其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PodcastRSSConfigLoader;
} 