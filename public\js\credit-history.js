/**
 * 积分历史记录管理
 * 负责显示用户积分使用和充值历史
 */
(function() {
    'use strict';

    // 操作类型映射
    const OPERATION_TYPE_MAP = {
        'summary': 'AI摘要',
        'mindmap': 'AI脑图',
        'epub_extract': 'EPUB解析',
        'recharge': '积分充值',
        'knowledgegraph': 'AI知识图谱',
        'initialization': '系统初始化',
        'admin_add': '管理员添加',
        'smart_qa': '智能问答',
        'audio_analysis': '录音分析',
        'signup_bonus': '赠送积分',
        'purchase': '积分购买',
        'generate_timestamps': '生成时间戳',
        'grammar_analysis': '语法分析'
    };
    
    // 英文操作类型映射
    const OPERATION_TYPE_MAP_EN = {
        'summary': 'AI Summary',
        'mindmap': 'AI Mind Map',
        'epub_extract': 'EPUB Extract',
        'recharge': 'Credit Recharge',
        'knowledgegraph': 'AI Knowledge Graph',
        'initialization': 'System Init',
        'admin_add': 'Admin Add',
        'smart_qa': 'Smart Q&A',
        'audio_analysis': 'Audio Analysis',
        'signup_bonus': 'Signup Bonus',
        'purchase': 'Credit Purchase',
        'generate_timestamps': 'Generate Timestamps',
        'grammar_analysis': 'Grammar Analysis'
    };

    // 分页设置
    const PAGE_SIZE = 9;
    
    // 缓存设置 - 新增
    const CREDITS_CACHE_TIMEOUT = 60000; // 积分缓存有效期60秒

    /**
     * 积分历史记录管理器类
     */
    class CreditHistoryManager {
        constructor() {
            this.supabase = null;
            this.currentPage = 1;
            this.totalPages = 1;
            this.isLoading = false;
            this.historyData = [];
            this.userId = null;
            this.currentCredits = 0;
            this.needsReinitialization = false;
            this.initializationFailed = false;
            
            // 缓存相关属性 - 新增
            this.lastCreditsUpdate = 0;
            this.lastHistoryUpdate = 0;
            this.lastLoggedCredits = null; // 添加用于记录上次日志输出的积分值
            
            // 防重复调用标志
            this.isShowingModal = false;
            this.lastCreditsUpdateTime = 0;
            
            // 初始化DOM元素引用
            this.initDomElements();
            
            // 初始化事件监听器
            this.initEventListeners();
        }

        /**
         * 初始化DOM元素引用
         */
        initDomElements() {
            // 创建模态框结构
            this.createModalStructure();
            
            // 获取DOM元素引用
            this.modal = document.getElementById('creditHistoryModal');
            this.closeBtn = document.getElementById('creditHistoryClose');
            this.tableBody = document.getElementById('creditHistoryTableBody');
            this.loadingElement = document.getElementById('creditHistoryLoading');
            this.emptyElement = document.getElementById('creditHistoryEmpty');
            this.currentCreditsElement = document.getElementById('currentCreditsValue');
            
            // 分页元素
            this.prevPageBtn = document.getElementById('prevPageBtn');
            this.nextPageBtn = document.getElementById('nextPageBtn');
            this.pageInfoElement = document.getElementById('pageInfo');
            
            // 积分图标点击触发
            this.creditsIcon = document.querySelector('.credits-icon');
        }

        /**
         * 创建模态框HTML结构
         */
        createModalStructure() {
            // 先删除可能存在的旧模态框
            const existingModal = document.getElementById('creditHistoryModal');
            if (existingModal) {
                existingModal.remove();
                console.log('删除了已存在的积分历史模态框');
            }
            
            // 添加内联样式
            const styleElement = document.createElement('style');
            styleElement.id = 'credit-history-inline-styles';
            styleElement.textContent = `
                /* 积分历史模态框的内联样式 - 最高优先级 */
                #creditHistoryModal {
                    display: none !important;
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100vw !important;
                    height: 100vh !important;
                    background: rgba(0, 0, 0, 0.8) !important;
                    z-index: 99999 !important;
                    justify-content: center !important;
                    align-items: center !important;
                }
                
                #creditHistoryModal.show {
                    display: flex !important;
                }
                
                #creditHistoryModal .credit-history-container {
                    background: white !important;
                    border-radius: 12px !important;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.3) !important;
                    width: 95% !important;
                    max-width: 1400px !important;
                    min-height: 60vh !important;
                    max-height: 85vh !important;
                    overflow: hidden !important;
                    display: flex !important;
                    flex-direction: column !important;
                    position: relative !important;
                }
                
                .credit-history-content {
                    flex: 1 !important;
                    display: flex !important;
                    flex-direction: column !important;
                    min-height: 400px !important;
                    overflow: hidden !important;
                }
                
                /* 书籍和章节显示样式 */
                .book-chapter-container {
                    display: flex;
                    flex-direction: row;
                    gap: 4px;
                }
                
                .book-title {
                    font-weight: bold;
                    color: #333;
                }
                
                .chapter-divider {
                    color: #333;
                }
                
                .credit-history-table {
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 16px;
                }
                
                .credit-history-th {
                    padding: 12px 16px;
                    border-bottom: 1px solid #e8e8e8;
                    background-color: #f5f5f5;
                    font-weight: 500;
                    text-align: center;
                    position: sticky;
                    top: 0;
                    z-index: 1;
                }
                
                .book-header {
                    width: 35%;
                }
                
                .model-header {
                    width: 10%;
                }
                
                .credits-change-header {
                    width: 15%;
                }
                
                .current-credits-header {
                    width: 10%;
                }
                
                .credit-history-cell {
                    padding: 12px 16px !important;
                    border-bottom: 1px solid #eee !important;
                    font-size: 15px !important;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                
                .credit-history-cell.book-cell {
                    white-space: normal !important;
                    word-wrap: break-word;
                    max-width: 300px;
                }
                
                .credit-history-cell.model-cell {
                    width: 15%;
                }
                
                .credit-history-cell.credits-change-cell,
                .credit-history-cell.current-credits-cell {
                    text-align: center !important;
                    font-weight: 500 !important;
                }

                /* 加载中样式优化 - 文字+动态省略号 */
                .credit-history-loading {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 40px;
                    color: #666;
                    flex: 1;
                    min-height: 200px;
                }
                
                .credit-history-loading span {
                    font-size: 16px;
                    color: #4a5568;
                    font-weight: 500;
                    position: relative;
                }
                
                .credit-history-loading span::after {
                    content: '';
                    animation: loading-dots 1.5s steps(4, end) infinite;
                    position: absolute;
                    left: 100%;
                    top: 0;
                    width: 20px;
                    text-align: left;
                }
                
                @keyframes loading-dots {
                    0% { content: ''; }
                    25% { content: '.'; }
                    50% { content: '..'; }
                    75% { content: '...'; }
                    100% { content: ''; }
                }
                
                /* 旋转动画 */
                @keyframes credit-history-spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                
                /* 表格行悬停效果 */
                #creditHistoryModal table tbody tr:hover {
                    background-color: #f9f9f9 !important;
                }
                
                /* 按钮悬停效果 */
                #creditHistoryModal button:hover:not(:disabled) {
                    background-color: #e3f2fd !important;
                    border-color: #1e88e5 !important;
                }
                

            `;
            
            // 移除可能存在的旧样式
            const existingStyle = document.getElementById('credit-history-inline-styles');
            if (existingStyle) {
                existingStyle.remove();
            }
            document.head.appendChild(styleElement);
            
            const modalHtml = `
                <div id="creditHistoryModal">
                    <div class="credit-history-container">
                            <div class="credit-history-header">
                                <h2 data-i18n="credit_history.title">积分使用记录</h2>
                                <button id="creditHistoryClose" class="credit-history-close">&times;</button>
                        </div>
                        <div class="credit-history-content">
                            <div id="creditHistoryLoading" class="credit-history-loading">
                                <span data-i18n="credit_history.loading">加载中</span>
                            </div>
                            <div id="creditHistoryEmpty" style="display: none; padding: 40px; text-align: center; color: #666;" data-i18n="credit_history.no_records">
                                暂无积分使用记录
                            </div>

                            <div style="flex: 1; overflow-y: auto; padding: 0 15px;">
                                <table class="credit-history-table">
                                    <thead>
                                        <tr>
                                            <th class="credit-history-th" data-i18n="credit_history.operation_time">操作时间</th>
                                            <th class="credit-history-th" data-i18n="credit_history.operation_type">操作类型</th>
                                            <th class="credit-history-th book-header" data-i18n="credit_history.book_name">音频</th>
                                            <th class="credit-history-th credits-change-header" data-i18n="credit_history.credits_change">积分变动</th>
                                            <th class="credit-history-th current-credits-header" data-i18n="credit_history.current_credits">当前积分</th>
                                        </tr>
                                    </thead>
                                    <tbody id="creditHistoryTableBody"></tbody>
                                </table>
                            </div>
                        </div>
                        <div style="padding: 10px 16px; border-top: 1px solid #eee; background: #f9f9f9; display: flex; justify-content: space-between; align-items: center;">
                            <div style="font-weight: 500; color: #1e88e5; font-size: 13px;">
                                <span data-i18n="credit_history.current_credits_label">当前积分:</span> <span id="currentCreditsValue">0</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 8px; font-size: 12px;">
                                <button id="prevPageBtn" style="padding: 4px 8px; border: 1px solid #ddd; background: white; cursor: pointer; border-radius: 3px; font-size: 11px;" disabled>&lt; 上一页</button>
                                <span id="pageInfo" style="font-size: 11px;">第 1 页 / 共 1 页</span>
                                <button id="nextPageBtn" style="padding: 4px 8px; border: 1px solid #ddd; background: white; cursor: pointer; border-radius: 3px; font-size: 11px;" disabled>下一页 &gt;</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 将模态框添加到body
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = modalHtml;
            document.body.appendChild(tempDiv.firstElementChild);
        }

        /**
         * 初始化事件监听器
         */
        initEventListeners() {
            // 关闭按钮点击事件
            if (this.closeBtn) {
                this.closeBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('关闭按钮被点击');
                    this.hideModal();
                });
            }
            
            // 点击模态框背景关闭
            if (this.modal) {
                this.modal.addEventListener('click', (e) => {
                    if (e.target === this.modal) {
                        console.log('点击模态框背景关闭');
                        this.hideModal();
                    }
                });
            }
            
            // ESC键关闭模态框
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.modal.style.display === 'flex') {
                    console.log('ESC键关闭模态框');
                    this.hideModal();
                }
            });
            
            // 分页按钮点击事件
            if (this.prevPageBtn) {
                this.prevPageBtn.addEventListener('click', () => this.loadPreviousPage());
            }
            if (this.nextPageBtn) {
                this.nextPageBtn.addEventListener('click', () => this.loadNextPage());
            }
            
            // 积分图标点击事件
            if (this.creditsIcon) {
                this.creditsIcon.addEventListener('click', () => this.showModal());
            }
        }

        /**
         * 初始化积分历史记录管理器
         */
        async init() {
            try {
                // 获取Supabase客户端
                this.supabase = window.getSupabaseClient();
                if (!this.supabase) {
                    throw new Error('Supabase客户端未初始化');
                }
                
                try {
                    // 使用统一认证管理器获取用户信息
                    const currentUser = window.getCurrentAuthUser ? window.getCurrentAuthUser() : null;
                    const isAuthenticated = window.isUserAuthenticated ? window.isUserAuthenticated() : false;
                    
                    if (isAuthenticated && currentUser && currentUser.id) {
                        this.userId = currentUser.id;
                        this.needsReinitialization = false;
                        console.log('通过统一认证管理器获得用户ID:', currentUser.id);
                        console.log('积分历史记录管理器初始化成功，用户ID:', this.userId);
                        return;
                    }
                    
                    // 如果用户未登录，说明需要等待登录
                    throw new Error('用户未登录');
                    
                } catch (authError) {
                    // 捕获认证错误，但不抛出异常，允许管理器继续初始化
                    console.warn('获取用户信息失败，积分历史功能将在用户登录后可用:', authError.message);
                    // 设置一个标志表示需要在用户登录后重新初始化
                    this.needsReinitialization = true;
                    
                    // 监听认证状态变化，在用户登录后重新初始化
                    document.addEventListener('auth-state-changed', (event) => {
                        if (event.detail && event.detail.loggedIn && this.needsReinitialization) {
                            console.log('检测到用户登录，重新初始化积分历史记录管理器');
                            this.needsReinitialization = false;
                            setTimeout(() => this.reinitialize(), 1000); // 延迟1秒确保认证状态已完全更新
                        }
                    });
                }
            } catch (error) {
                console.error('初始化积分历史记录管理器失败:', error);
                // 设置标志，表示需要在条件允许时重新初始化
                this.initializationFailed = true;
            }
        }
        
        /**
         * 重新初始化积分历史记录管理器
         */
        async reinitialize() {
            try {
                // 获取Supabase客户端
                this.supabase = window.getSupabaseClient();
                if (!this.supabase) {
                    throw new Error('Supabase客户端未初始化');
                }
                
                // 使用统一认证管理器获取用户信息
                const currentUser = window.getCurrentAuthUser ? window.getCurrentAuthUser() : null;
                const isAuthenticated = window.isUserAuthenticated ? window.isUserAuthenticated() : false;
                
                if (isAuthenticated && currentUser && currentUser.id) {
                    this.userId = currentUser.id;
                    this.initializationFailed = false;
                    console.log('积分历史记录管理器重新初始化成功');
                } else {
                    throw new Error('用户未登录');
                }
            } catch (error) {
                console.error('重新初始化积分历史记录管理器失败:', error);
                // 3秒后再次尝试
                setTimeout(() => {
                    if (this.initializationFailed) {
                        this.reinitialize();
                    }
                }, 3000);
            }
        }

        /**
         * 显示模态框
         */
        async showModal() {
            // 防止重复调用
            if (this.isShowingModal) {
                console.log('模态框正在显示中，忽略重复调用');
                return;
            }
            
            this.isShowingModal = true;
            console.log('显示积分历史模态框');
            
            try {
                // 确保模态框元素存在
                if (!this.modal) {
                    console.error('模态框元素不存在，重新创建');
                    this.createModalStructure();
                    this.initDomElements();
                    this.initEventListeners();
                }
                
                // 简单直接的显示方法
                console.log('准备显示模态框，当前modal元素:', this.modal);
                this.modal.classList.add('show');
                
                // 强制刷新样式
                this.modal.offsetHeight; // 触发重排
                
                console.log('模态框已添加show类，当前classList:', this.modal.classList.toString());
                
                // 立即显示加载状态，避免空白显示
                this.showLoading();
                console.log('显示加载状态');
                
                // 重置分页
                this.currentPage = 1;
                
                // 尝试重新获取用户ID（特别是针对Google登录用户）
                await this.ensureUserIdAvailable();
                console.log('用户ID检查完成:', this.userId);
                
                // 检查用户是否已登录
                if (!this.userId || this.needsReinitialization) {
                    console.log('用户未登录或需要重新初始化，显示提示信息');
                    this.hideLoading(); // 隐藏加载状态
                    this.showEmpty(window.UILanguage ? window.UILanguage.getText('credit_history.login_required') : '请先登录后查看积分历史');
                    return;
                }
                
                // 跳过积分更新：积分历史查询会返回最新积分
                const now = Date.now();
                console.log('跳过独立积分查询，将从积分历史响应中获取最新积分');
                
                // 如果CreditsManager有缓存，先显示缓存值
                if (window.CreditsManager && typeof window.CreditsManager.getCredits === 'function') {
                    const cachedCredits = window.CreditsManager.getCredits();
                    if (cachedCredits !== null && cachedCredits !== undefined) {
                        this.currentCredits = cachedCredits;
                        this.currentCreditsElement.textContent = this.currentCredits;
                        console.log('临时显示CreditsManager缓存积分:', this.currentCredits);
                    }
                }
                this.lastCreditsUpdate = now;
                
                // 强制重新加载历史数据，不使用缓存
                console.log('强制重新加载积分历史数据');
                await this.loadHistoryData();
                this.lastHistoryUpdate = now;
                
                // 只翻译模态框内容，避免全局语言变更
                this.translateModalText();
                
            } catch (error) {
                console.error('显示积分历史模态框时出错:', error);
                this.hideLoading();
                this.showEmpty(window.UILanguage ? window.UILanguage.getText('credit_history.load_failed') : '加载失败，请重试: ' + error.message);
            } finally {
                // 确保标志位被重置
                this.isShowingModal = false;
            }
        }

        /**
         * 确保用户ID可用，特别针对Google登录用户
         */
        async ensureUserIdAvailable() {
            if (this.userId && !this.needsReinitialization) {
                // 验证现有用户ID的有效性
                if (await this.validateUserIdWithBackend(this.userId)) {
                    return; // 已有有效用户ID
                } else {
                    console.warn('现有用户ID验证失败，需要重新获取');
                    this.userId = null;
                    this.needsReinitialization = true;
                }
            }

            try {
                // 使用统一认证管理器获取用户信息
                const currentUser = window.getCurrentAuthUser ? window.getCurrentAuthUser() : null;
                const isAuthenticated = window.isUserAuthenticated ? window.isUserAuthenticated() : false;
                
                if (isAuthenticated && currentUser && currentUser.id) {
                    // 验证用户ID是否与后端一致
                    if (await this.validateUserIdWithBackend(currentUser.id)) {
                        this.userId = currentUser.id;
                        this.needsReinitialization = false;
                        console.log('从统一认证管理器获取到有效用户ID:', this.userId);
                        return;
                    } else {
                        console.warn('统一认证管理器中的用户ID与后端不一致');
                        // 清理可能过期的认证数据
                        if (window.unifiedAuthManager) {
                            window.unifiedAuthManager.clearLocalAuthData();
                        }
                    }
                }

                console.warn('无法获取有效用户ID，用户可能未登录');
                this.needsReinitialization = true;
            } catch (error) {
                console.error('确保用户ID可用时出错:', error);
                this.needsReinitialization = true;
            }
        }

        /**
         * 验证用户ID与后端的一致性（简化版：避免额外的积分API调用）
         */
        async validateUserIdWithBackend(userId) {
            try {
                const authToken = window.getAuthToken ? window.getAuthToken() : null;
                if (!authToken) {
                    console.warn('无认证令牌，用户ID验证失败');
                    return false;
                }

                // 简单验证令牌格式，避免调用积分API
                // JWT令牌应该有3个部分，用点分隔
                const tokenParts = authToken.split('.');
                if (tokenParts.length !== 3) {
                    console.warn('令牌格式无效');
                    this.clearInvalidLocalData();
                    return false;
                }

                // 基本验证通过，认为用户ID有效
                // 真正的验证会在积分历史查询时进行，如果401会自动处理
                console.log('用户ID基本验证通过，将在历史查询时进行完整验证');
                return true;
            } catch (error) {
                console.error('验证用户ID时出错:', error);
                // 网络错误等情况，暂时认为有效
                return true;
            }
        }

        /**
         * 清除无效的本地数据
         */
        clearInvalidLocalData() {
            // 清除认证相关的localStorage数据
            localStorage.removeItem('isAuthenticated');
            localStorage.removeItem('userId');
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('userEmail');
            localStorage.removeItem('userData');
            
            // 重置内部状态
            this.userId = null;
            this.needsReinitialization = true;
            
            console.log('已清除无效的本地认证数据');
        }

        /**
         * 翻译模态框文本，而不触发全局语言变更
         */
        translateModalText() {
            if (!window.UILanguage) return;
            const currentLang = window.UILanguage.getCurrentLanguage ? window.UILanguage.getCurrentLanguage() : 'zh';
            const elements = this.modal.querySelectorAll('[data-i18n]');
            elements.forEach(element => {
                const key = element.getAttribute('data-i18n');
                const translation = window.UILanguage.getText ? window.UILanguage.getText(key) : key;
                if (translation && translation !== key) {
                    element.textContent = translation;
                }
            });
        }

        /**
         * 隐藏模态框
         */
        hideModal() {
            console.log('隐藏积分历史模态框');
            if (this.modal) {
                this.modal.classList.remove('show');
                // 清理加载状态，确保下次打开时状态正确
                this.hideLoading();
                this.hideEmpty();
                // 重置防重复调用标志
                this.isShowingModal = false;
                console.log('模态框已移除show类，已隐藏');
            }
        }

        /**
         * 加载积分历史数据
         */
        async loadHistoryData() {
            if (this.isLoading) return;
            
            this.isLoading = true;
            this.showLoading();

            try {
                const token = await this.getValidToken();
                if (!token) {
                    this.hideLoading();
                    this.showEmpty(this.getLocalizedText('no_token', '请先登录后再查看积分历史'));
                    return;
                }

                // 获取积分历史
                const historyResponse = await fetch('/api/credits/history', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!historyResponse.ok) {
                    throw new Error(`HTTP error! status: ${historyResponse.status}`);
                }

                const historyData = await historyResponse.json();

                if (historyData.success && historyData.history) {
                    this.historyData = historyData.history;
                    // 过滤掉重复的购买记录，只保留充值记录
                    this.historyData = this.historyData.filter(item => item.operation_type !== 'purchase');
                    this.currentCredits = historyData.current_credits || 0;
                    
                    // 分页处理
                    this.totalPages = Math.ceil(this.historyData.length / PAGE_SIZE);
                    if (this.totalPages === 0) this.totalPages = 1;
                    
                    this.hideLoading();
                    this.renderHistoryData();
                    this.updatePagination();
                } else {
                    this.hideLoading();
                    this.showEmpty(this.getLocalizedText('no_history', '暂无积分历史记录'));
                }
            } catch (error) {
                console.error('加载积分历史失败:', error);
                this.hideLoading();
                this.showEmpty(this.getLocalizedText('load_error', '加载积分历史时出错，请稍后再试'));
            } finally {
                this.isLoading = false;
            }
        }

        /**
         * 获取有效的认证令牌
         */
        async getValidToken() {
            // 首先尝试从localStorage获取令牌
            const authToken = localStorage.getItem('authToken');
            if (authToken) {
                return authToken;
            }

            // 如果没有令牌，尝试从Supabase获取
            if (this.supabase) {
                try {
                    const { data: { session } } = await this.supabase.auth.getSession();
                    if (session && session.access_token) {
                        return session.access_token;
                    }
                } catch (error) {
                    console.error('从Supabase获取令牌失败:', error);
                }
            }
            
            throw new Error('无法获取有效的认证令牌');
        }

        /**
         * 渲染历史数据
         */
        renderHistoryData() {
            console.log('开始渲染历史数据，数据长度:', this.historyData?.length);
            
            if (!this.historyData || this.historyData.length === 0) {
                console.log('没有历史数据，显示空状态');
                this.showEmpty();
                return;
            }
            
            // 确保表格body存在
            if (!this.tableBody) {
                console.error('表格body元素不存在');
                this.showEmpty('表格初始化失败');
                return;
            }
            
            this.hideEmpty();
            
            // 清空表格
            this.tableBody.innerHTML = '';

            // 获取适当的语言翻译函数
            const getText = window.UILanguage ? window.UILanguage.getText : key => key;
            
            // 添加数据行
            this.historyData.forEach((item, index) => {
                // 只在开发模式或出错时输出详细日志
                if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
                    if (index < 3) { // 只输出前3条记录的详细信息
                        console.log(`渲染第${index + 1}条记录:`, item);
                        console.log(`- current_credits: ${item.current_credits}, credits_amount: ${item.credits_amount}`);
                    }
                }
                
                const row = document.createElement('tr');
                
                // 正确的时间处理: 将UTC时间转换为本地时间显示
                let formattedDate;
                try {
                    let dateStr = item.operation_time;
                    
                    // 检查时间格式并进行正确的时区转换
                    const hasTimezone = dateStr.includes('Z') || /[+-]\d{2}:\d{2}/.test(dateStr);
                    
                    if (hasTimezone) {
                        // 有时区信息，进行正确的UTC到本地时间转换
                        try {
                            const date = new Date(dateStr);
                            if (!isNaN(date.getTime())) {
                                formattedDate = date.toLocaleString('zh-CN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    second: '2-digit',
                                    hour12: false
                                }).replace(/\//g, '-');
                                // 只在开发模式下输出时间转换日志
                                if (window.location.hostname === 'localhost' && index < 2) {
                                    console.log('UTC时间转换:', formattedDate);
                                }
                            } else {
                                throw new Error('Invalid date');
                            }
                        } catch (parseError) {
                            // 如果Date解析失败，手动处理
                            const timeMatch = dateStr.match(/(\d{4}-\d{2}-\d{2})[T\s](\d{2}:\d{2}:\d{2})/);
                            if (timeMatch) {
                                const [, datePart, timePart] = timeMatch;
                                formattedDate = `${datePart} ${timePart}`;
                            } else {
                                formattedDate = dateStr;
                                console.warn('时间解析失败:', dateStr);
                            }
                        }
                    } else {
                        // 没有时区信息，假设是本地时间直接显示
                        const timeMatch = dateStr.match(/(\d{4}-\d{2}-\d{2})[T\s](\d{2}:\d{2}:\d{2})/);
                        if (timeMatch) {
                            const [, datePart, timePart] = timeMatch;
                            formattedDate = `${datePart} ${timePart}`;
                        } else {
                            // 最后的后备方案
                            formattedDate = item.operation_time ? item.operation_time.replace('T', ' ').substring(0, 19) : '-';
                            console.warn('无法解析时间格式:', dateStr);
                        }
                    }
                } catch (error) {
                    console.error('解析日期时出错:', error, item.operation_time);
                    formattedDate = item.operation_time || '-';
                }
                
                // 操作类型显示 - 智能处理国际化键名
                let operationType;
                if (item.operation_type && item.operation_type.startsWith('credit_history.operations.')) {
                    // 如果是国际化键名，提取实际的操作类型
                    const actualOperationType = item.operation_type.replace('credit_history.operations.', '');
                    const currentLang = window.UILanguage ? window.UILanguage.getCurrentLanguage() : 'zh';
                    
                    if (currentLang === 'en') {
                        operationType = OPERATION_TYPE_MAP_EN[actualOperationType] || actualOperationType;
                    } else {
                        operationType = OPERATION_TYPE_MAP[actualOperationType] || actualOperationType;
                    }
                } else {
                    // 正常的操作类型处理
                    const currentLang = window.UILanguage ? window.UILanguage.getCurrentLanguage() : 'zh';
                    
                    if (currentLang === 'en') {
                        operationType = OPERATION_TYPE_MAP_EN[item.operation_type] || item.operation_type;
                    } else {
                        operationType = OPERATION_TYPE_MAP[item.operation_type] || item.operation_type;
                    }
                }
                
                // 积分变动类 - 根据积分数量的正负来判断
                const isPositiveChange = item.credits_amount > 0;
                const creditClass = isPositiveChange ? 'credits-positive' : 'credits-negative';
                
                // 积分变动符号 - 根据积分数量的正负来判断
                const creditSign = isPositiveChange ? '+' : '-';
                
                // 音频信息显示 - 根据操作类型决定显示内容
                let audioDisplay;
                const currentLang = window.UILanguage ? window.UILanguage.getCurrentLanguage() : 'zh';
                
                // 定义非音频操作类型
                const nonAudioOperations = ['signup_bonus', 'recharge', 'purchase', 'admin_add', 'initialization'];
                
                if (nonAudioOperations.includes(item.operation_type)) {
                    // 对于非音频操作，显示"无"或"N/A"
                    audioDisplay = currentLang === 'en' ? 
                        `<div class="audio-title">N/A</div>` : 
                        `<div class="audio-title">无</div>`;
                } else if (item.book_name && !item.book_name.startsWith('credit_history.operations.')) {
                    // 根据操作类型显示不同的音频信息
                    if (item.operation_type === 'audio_analysis') {
                        // 录音分析/Audio Analysis 显示"录音片段/Recording"
                        audioDisplay = currentLang === 'en' ? 
                            `<div class="audio-title">Recording</div>` : 
                            `<div class="audio-title">录音片段</div>`;
                    } else if (item.operation_type === 'smart_qa') {
                        // Smart Q&A 显示音频文件名称
                        audioDisplay = `<div class="audio-title">${item.book_name}</div>`;
                    } else {
                        // 其他操作类型显示原始内容
                        audioDisplay = `<div class="audio-title">${item.book_name}</div>`;
                    }
                } else {
                    // 如果没有有效音频信息或是技术标识符，显示"无"
                    audioDisplay = currentLang === 'en' ? 
                        `<div class="audio-title">N/A</div>` : 
                        `<div class="audio-title">无</div>`;
                }
                
                // 构建行内容 - 修复当前积分显示逻辑
                const isLatestRecord = (this.currentPage === 1 && this.historyData.indexOf(item) === 0);
                let displayCredits;
                
                if (isLatestRecord) {
                    // 对于最新记录，检查多个来源确保显示正确积分
                    if (this.currentCredits !== null && this.currentCredits !== undefined && this.currentCredits > 0) {
                        // 如果有从外部API获取的积分且大于0，使用它
                        displayCredits = this.currentCredits;
                        console.log('最新记录使用外部API积分:', displayCredits);
                    } else if (item.current_credits !== null && item.current_credits !== undefined && item.current_credits > 0) {
                        // 如果历史记录中的积分大于0，使用它
                        displayCredits = item.current_credits;
                        // 同步更新currentCredits
                        this.currentCredits = item.current_credits;
                        if (this.currentCreditsElement) {
                            this.currentCreditsElement.textContent = this.currentCredits;
                        }
                        console.log('最新记录使用历史记录积分:', displayCredits);
                    } else {
                        // 尝试通过前一条记录计算当前积分
                        if (this.historyData.length > 1) {
                            const previousRecord = this.historyData[1];
                            if (previousRecord && previousRecord.current_credits !== null) {
                                displayCredits = previousRecord.current_credits + item.credits_amount;
                                console.log('最新记录通过计算获得积分:', displayCredits, '(前记录:', previousRecord.current_credits, '变动:', item.credits_amount, ')');
                            } else {
                                displayCredits = '-';
                            }
                        } else {
                            displayCredits = '-';
                        }
                    }
                } else {
                    // 对于其他记录，直接使用记录中的积分
                    displayCredits = (item.current_credits !== null && item.current_credits !== undefined) ? item.current_credits : '-';
                }
                
                row.innerHTML = `
                    <td class="credit-history-cell">${formattedDate}</td>
                    <td class="credit-history-cell">${operationType}</td>
                    <td class="credit-history-cell audio-cell">${audioDisplay}</td>
                    <td class="credit-history-cell credits-change-cell" style="color: ${isPositiveChange ? '#4caf50' : '#f44336'};">${creditSign}${Math.abs(item.credits_amount)}</td>
                    <td class="credit-history-cell current-credits-cell">${displayCredits}</td>
                `;
                
                this.tableBody.appendChild(row);
            });
            
            // 只输出总结信息，不输出每条记录的详细信息
            console.log(`积分历史渲染完成，共${this.historyData.length}条记录`);
            
            // 隐藏加载状态，显示表格数据
            this.hideLoading();
        }

        /**
         * 更新分页信息
         */
        updatePagination() {
            // 获取本地化文本函数
            const getText = window.UILanguage ? window.UILanguage.getText : key => key;
            
            // 更新页码信息，支持多语言
            const pageInfoTemplate = getText('credit_history.page_info') || '第 {current} 页 / 共 {total} 页';
            const pageInfo = pageInfoTemplate
                .replace('{current}', this.currentPage)
                .replace('{total}', this.totalPages);
                
            this.pageInfoElement.textContent = pageInfo;
            this.pageInfoElement.style.fontSize = '12px'; // 增大分页信息字号
            this.pageInfoElement.style.fontSize = '12px'; // 增大分页信息字号
            
            // 更新按钮状态
            this.prevPageBtn.disabled = this.currentPage <= 1;
            this.nextPageBtn.disabled = this.currentPage >= this.totalPages;
        }

        /**
         * 加载上一页
         */
        async loadPreviousPage() {
            if (this.currentPage > 1 && !this.isLoading) {
                this.currentPage--;
                this.showLoading(); // 分页时显示加载状态
                await this.loadHistoryData();
            }
        }

        /**
         * 加载下一页
         */
        async loadNextPage() {
            if (this.currentPage < this.totalPages && !this.isLoading) {
                this.currentPage++;
                this.showLoading(); // 分页时显示加载状态
                await this.loadHistoryData();
            }
        }

        /**
         * 显示加载中状态
         */
        showLoading() {
            if (this.loadingElement) {
                // 清除之前可能设置的强制隐藏样式
                this.loadingElement.removeAttribute('style');
                this.loadingElement.style.display = 'flex';
                this.loadingElement.style.visibility = 'visible';
            }
        }

        /**
         * 隐藏加载中状态
         */
        hideLoading() {
            if (this.loadingElement) {
                this.loadingElement.style.display = 'none';
                this.loadingElement.style.visibility = 'hidden';
            }
        }

        /**
         * 显示空状态
         * @param {string} message - 可选的自定义消息
         */
        showEmpty(message) {
            if (this.emptyElement) {
                // 使用传入的消息或默认多语言文本
                const defaultMessage = window.UILanguage ? 
                                      window.UILanguage.getText('credit_history.no_records') : 
                                      '暂无积分使用记录';
                this.emptyElement.textContent = message || defaultMessage;
                this.emptyElement.style.display = 'block';
                
                // 隐藏表格容器
                const tableContainer = this.tableBody.closest('div');
                if (tableContainer) {
                    tableContainer.style.display = 'none';
                }
            }
        }

        /**
         * 隐藏空状态
         */
        hideEmpty() {
            if (this.emptyElement) {
                this.emptyElement.style.display = 'none';
                
                // 显示表格容器
                const tableContainer = this.tableBody.closest('div');
                if (tableContainer) {
                    tableContainer.style.display = 'block';
                }
            }
        }

        /**
         * 更新当前积分显示
         */
        async updateCurrentCredits() {
            try {
                if (!this.userId) {
                    return;
                }
                
                // 防止短时间内重复调用
                const now = Date.now();
                if (now - this.lastCreditsUpdateTime < 1000) { // 1秒内不重复更新
                    console.log('积分更新请求过于频繁，跳过');
                    return;
                }
                this.lastCreditsUpdateTime = now;
                
                // 尝试获取积分详情（包括到期信息）
                if (window.CreditsManager && typeof window.CreditsManager.getCredits === 'function') {
                    // 直接从API获取详细信息，包括到期时间
                    try {
                        const creditsResponse = await this.fetchCreditsWithExpiry();
                        if (creditsResponse && creditsResponse.success) {
                            this.currentCredits = creditsResponse.credits;
                            this.currentCreditsElement.textContent = this.currentCredits;
                            
                            // 更新到期时间显示
                            this.updateExpiryDisplay(creditsResponse);
                            
                            this.lastCreditsUpdate = Date.now();
                            // 减少重复日志，只在积分变化时输出
                            if (this.currentCredits !== this.lastLoggedCredits) {
                                console.log('获取积分详情成功:', this.currentCredits, '到期时间:', creditsResponse.earliest_expiry);
                                this.lastLoggedCredits = this.currentCredits;
                            }
                            return;
                        }
                    } catch (error) {
                        console.warn('获取积分详情失败，回退到缓存模式:', error);
                    }
                    
                    // 回退到使用CreditsManager的缓存积分
                    const currentCredits = window.CreditsManager.getCredits();
                    if (currentCredits !== null && currentCredits !== undefined) {
                        this.currentCredits = currentCredits;
                        this.currentCreditsElement.textContent = this.currentCredits;
                        this.lastCreditsUpdate = Date.now();
                        // 减少重复日志，只在积分变化时输出
                        if (this.currentCredits !== this.lastLoggedCredits) {
                            console.log('使用CreditsManager缓存积分:', this.currentCredits);
                            this.lastLoggedCredits = this.currentCredits;
                        }
                        return;
                    }
                    
                    // 如果CreditsManager没有缓存，也不要直接发起网络请求
                    // 而是等待CreditsManager自己更新
                    console.log('CreditsManager缓存为空，等待其自动更新积分');
                    
                    // 尝试等待一下，看CreditsManager是否正在更新
                    setTimeout(() => {
                        const retryCredits = window.CreditsManager.getCredits();
                        if (retryCredits !== null && retryCredits !== undefined) {
                            this.currentCredits = retryCredits;
                            this.currentCreditsElement.textContent = this.currentCredits;
                            this.lastCreditsUpdate = Date.now();
                            console.log('延迟获取到CreditsManager积分:', this.currentCredits);
                        } else {
                            // 如果还是没有，显示默认值
                            this.currentCredits = 0;
                            this.currentCreditsElement.textContent = '0';
                            console.log('CreditsManager积分仍不可用，显示默认值0');
                        }
                    }, 500); // 等待500ms
                    
                    return;
                }
                
                // 如果CreditsManager不存在，显示默认值
                console.warn('CreditsManager不可用，无法获取积分');
                this.currentCredits = 0;
                this.currentCreditsElement.textContent = '0';
                
            } catch (error) {
                console.error('获取当前积分失败:', error);
                this.currentCredits = 0;
                this.currentCreditsElement.textContent = '0';
            }
        }

        async fetchCreditsWithExpiry() {
            try {
                const token = await this.getValidToken();
                if (!token) {
                    throw new Error('无效的认证令牌');
                }

                const response = await fetch('/api/credits/get', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('获取积分详情失败:', error);
                return null;
            }
        }

        updateExpiryDisplay(response) {
            // 获取或创建到期时间显示元素
            let expiryElement = document.getElementById('currentCreditsExpiry');
            
            if (!expiryElement) {
                // 创建到期时间显示元素
                const creditsContainer = this.currentCreditsElement?.parentElement;
                if (creditsContainer) {
                    expiryElement = document.createElement('span');
                    expiryElement.id = 'currentCreditsExpiry';
                    expiryElement.style.marginLeft = '10px';
                    expiryElement.style.fontSize = '14px';
                    creditsContainer.appendChild(expiryElement);
                }
            }
            
            if (expiryElement && response.earliest_expiry) {
                const expiryDate = new Date(response.earliest_expiry);
                const now = new Date();
                const timeDiff = expiryDate.getTime() - now.getTime();
                const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
                
                let expiryText = '';
                let expiryClass = '';
                
                if (daysDiff <= 0) {
                    expiryText = this.getLocalizedText('expired', '已过期');
                    expiryClass = 'expired';
                } else if (daysDiff <= 7) {
                    expiryText = this.getLocalizedText('expires_soon', `${daysDiff}天后到期`);
                    expiryClass = 'expiring-soon';
                } else {
                    expiryText = this.getLocalizedText('expires_in', `${daysDiff}天后到期`);
                    expiryClass = 'normal-expiry';
                }
                
                expiryElement.textContent = this.getLocalizedText('expires_label', '到期时间:') + ' ' + expiryText;
                expiryElement.className = `credits-expiry ${expiryClass}`;
                
                // 添加CSS样式（如果尚未添加）
                this.addExpiryStyles();
            } else if (expiryElement) {
                // 隐藏到期信息（传统系统）
                expiryElement.style.display = 'none';
            }
        }

        addExpiryStyles() {
            if (document.getElementById('credit-expiry-styles')) return;
            
            const styleElement = document.createElement('style');
            styleElement.id = 'credit-expiry-styles';
            styleElement.textContent = `
                .credits-expiry {
                    font-weight: 500;
                    padding: 2px 6px;
                    border-radius: 4px;
                    font-size: 12px !important;
                }
                .credits-expiry.normal-expiry {
                    color: #666;
                    background-color: #f0f0f0;
                }
                .credits-expiry.expiring-soon {
                    color: #ff8c00;
                    background-color: #fff3cd;
                }
                .credits-expiry.expired {
                    color: #dc3545;
                    background-color: #f8d7da;
                }
            `;
            document.head.appendChild(styleElement);
        }

        getLocalizedText(key, defaultText) {
            // 获取当前语言
            const currentLang = localStorage.getItem('selectedLanguage') || 'auto';
            const isEnglish = currentLang === 'en' || (currentLang === 'auto' && navigator.language.startsWith('en'));
            
            const texts = {
                'expires_label': isEnglish ? 'Expires:' : '到期时间:',
                'expires_in': (days) => isEnglish ? `${days}d left` : `${days}天后到期`,
                'expires_soon': (days) => isEnglish ? `${days}d left` : `${days}天后到期`,
                'expired': isEnglish ? 'Expired' : '已过期'
            };
            
            const text = texts[key];
            if (typeof text === 'function') {
                // 提取数字并应用函数
                const match = defaultText.match(/(\d+)/);
                return match ? text(match[1]) : defaultText;
            }
            return text || defaultText;
        }


    }

    // 创建全局实例
    window.CreditHistoryManager = new CreditHistoryManager();

    // 在DOM加载完成后初始化
    document.addEventListener('DOMContentLoaded', async () => {
        try {
            // 获取共享的Supabase客户端
            const supabase = window.getSupabaseClient();
            
            // 如果Supabase客户端不可用，延迟初始化
            if (!supabase) {
                console.warn('Supabase客户端不可用，积分历史记录管理器将在客户端可用时初始化');
                // 监听认证状态变化，在用户登录后尝试初始化
                document.addEventListener('auth-state-changed', (event) => {
                    if (event.detail && (event.detail.status === 'authenticated' || event.detail.loggedIn)) {
                        setTimeout(() => {
                            if (window.CreditHistoryManager && (!window.CreditHistoryManager.userId || window.CreditHistoryManager.needsReinitialization)) {
                                window.CreditHistoryManager.init().catch(error => {
                                    console.error('认证状态变化后初始化积分历史记录管理器失败:', error);
                                });
                            }
                        }, 500); // 缩短延迟时间
                    }
                });
            } else {
                // 尝试获取会话，但不抛出异常
                try {
                    // 检查多种认证状态，特别适用于Google登录用户
                    let shouldInit = false;
                    
                    // 检查localStorage中的认证状态（替代直接调用Supabase API）
                    const isAuthenticated = localStorage.getItem('isAuthenticated');
                    const userId = localStorage.getItem('userId');
                    if (isAuthenticated === 'true' && userId) {
                        shouldInit = true;
                        console.log('检测到localStorage中的认证状态，准备初始化积分历史管理器');
                    }
                    
                    if (shouldInit) {
                        // 初始化积分历史记录管理器
                        if (window.CreditHistoryManager) {
                            await window.CreditHistoryManager.init();
                        }
                    } else {
                        console.log('用户未登录，积分历史记录管理器将在用户登录后初始化');
                        // 监听认证状态变化
                        document.addEventListener('auth-state-changed', (event) => {
                            if (event.detail && (event.detail.status === 'authenticated' || event.detail.loggedIn)) {
                                setTimeout(() => {
                                    if (window.CreditHistoryManager && (!window.CreditHistoryManager.userId || window.CreditHistoryManager.needsReinitialization)) {
                                        window.CreditHistoryManager.init().catch(error => {
                                            console.error('认证状态变化后初始化积分历史记录管理器失败:', error);
                                        });
                                    }
                                }, 500); // 缩短延迟时间
                            }
                        });
                    }
                } catch (sessionError) {
                    console.warn('获取用户会话失败，积分历史记录功能可能不可用:', sessionError);
                    
                    // 即使会话获取失败，也要监听认证状态变化
                    document.addEventListener('auth-state-changed', (event) => {
                        if (event.detail && (event.detail.status === 'authenticated' || event.detail.loggedIn)) {
                            setTimeout(() => {
                                if (window.CreditHistoryManager && (!window.CreditHistoryManager.userId || window.CreditHistoryManager.needsReinitialization)) {
                                    window.CreditHistoryManager.init().catch(error => {
                                        console.error('认证状态变化后初始化积分历史记录管理器失败:', error);
                                    });
                                }
                            }, 500);
                        }
                    });
                }
            }
        } catch (error) {
            console.error('初始化积分历史记录管理器过程中出错:', error);
        }

        // 添加点击事件监听器到 user-credits-container
        try {
            const userCreditsContainer = document.querySelector('.user-credits-container');
            if (userCreditsContainer && !userCreditsContainer.hasAttribute('data-history-listener')) {
                userCreditsContainer.setAttribute('data-history-listener', 'true');
                userCreditsContainer.addEventListener('click', () => {
                    if (window.CreditHistoryManager) {
                        window.CreditHistoryManager.showModal();
                    }
                });
            }

            // 额外添加事件监听到积分相关的所有元素，确保事件触发但防止重复
            document.querySelectorAll('.credits-icon, .credits-amount, .user-credits-label').forEach(element => {
                if (!element.hasAttribute('data-history-listener')) {
                    element.setAttribute('data-history-listener', 'true');
                    element.addEventListener('click', (event) => {
                        event.stopPropagation(); // 阻止事件冒泡以防止多次触发

                        if (window.CreditHistoryManager) {
                            window.CreditHistoryManager.showModal();
                        }
                    });
                }
            });
        } catch (uiError) {
            console.warn('设置积分历史记录UI事件监听器时出错:', uiError);
        }
    });

    // 导出全局实例，供其他模块使用
    window.CreditHistoryManager = new CreditHistoryManager();
    
    // 全局函数：在积分操作完成后刷新积分历史
    window.refreshCreditHistoryAfterOperation = function() {
        if (window.CreditHistoryManager && window.CreditHistoryManager.isShowingModal) {
            console.log('检测到积分操作完成，1秒后刷新积分历史');
            setTimeout(() => {
                if (window.CreditHistoryManager.isShowingModal) {
                    window.CreditHistoryManager.loadHistoryData();
                }
            }, 1000); // 延迟1秒确保数据库完全同步
        }
    };

})();
