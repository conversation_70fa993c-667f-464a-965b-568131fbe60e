-- ========================================
-- 修复积分有效期显示为空的问题
-- 为现有用户补充积分批次记录
-- ========================================

-- 1. 为现有已验证用户补充积分批次记录（如果没有）
DO $$
DECLARE
    user_record RECORD;
    v_batch_id UUID;
    processed_count INTEGER := 0;
BEGIN
    RAISE NOTICE '开始为现有用户补充积分批次记录...';
    
    -- 为有积分但没有积分批次的用户创建批次记录
    FOR user_record IN 
        SELECT uc.user_id, uc.credits
        FROM public.user_credits uc
        WHERE uc.credits > 0
          AND NOT EXISTS (
              SELECT 1 FROM public.credit_batches cb 
              WHERE cb.user_id = uc.user_id 
                AND cb.remaining_credits > 0
          )
    LOOP
        -- 为这些用户创建积分批次记录
        INSERT INTO public.credit_batches (
            user_id, credits_amount, remaining_credits, source_type,
            created_at, expires_at, description
        ) VALUES (
            user_record.user_id,
            user_record.credits,
            user_record.credits,
            'signup_bonus',
            NOW() - INTERVAL '1 day', -- 设置为昨天创建，保持逻辑一致性
            NOW() + INTERVAL '29 days', -- 30天有效期减去1天
            '新用户注册赠送积分（数据修复）'
        ) RETURNING id INTO v_batch_id;
        
        processed_count := processed_count + 1;
        RAISE NOTICE '为用户 % 补充积分批次记录，积分: %，批次ID: %', 
                     user_record.user_id, user_record.credits, v_batch_id;
    END LOOP;
    
    RAISE NOTICE '积分批次记录补充完成，处理了 % 个用户', processed_count;
END;
$$;

-- 2. 验证修复结果
SELECT 
    '修复完成' as status,
    (SELECT COUNT(*) FROM public.user_credits WHERE credits > 0) as total_users_with_credits,
    (SELECT COUNT(DISTINCT user_id) FROM public.credit_batches WHERE remaining_credits > 0) as users_with_batches
;

-- 显示成功消息
SELECT 'Credit batch fix completed successfully!' as result; 