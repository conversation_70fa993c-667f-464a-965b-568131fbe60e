from flask import Blueprint, request, jsonify
import os
import re
import http.client
import json

email_validator_bp = Blueprint('email_validator', __name__)

# --- 配置 ---
# 指向一次性邮箱黑名单文件路径
BLOCKLIST_FILE = os.path.join(os.path.dirname(__file__), 'data', 'disposable_email_blocklist.conf')
# --- 配置结束 ---

# 黑名单缓存，避免每次请求都读取文件
_blocklist_domains = None
_blocklist_last_load_time = 0
_blocklist_cache_ttl = 3600  # 缓存有效期1小时

def load_blocklist():
    """从文件加载屏蔽列表域名到集合中，使用缓存机制"""
    global _blocklist_domains, _blocklist_last_load_time
    
    current_time = os.path.getmtime(BLOCKLIST_FILE) if os.path.exists(BLOCKLIST_FILE) else 0
    
    # 如果文件不存在或缓存已过期，重新加载
    if _blocklist_domains is None or current_time > _blocklist_last_load_time:
        try:
            if os.path.exists(BLOCKLIST_FILE):
                with open(BLOCKLIST_FILE, 'r', encoding='utf-8') as f:
                    # 使用集合以获得 O(1) 的平均查找时间
                    _blocklist_domains = {line.strip().lower() for line in f if line.strip()}
                _blocklist_last_load_time = current_time
                print(f"已加载一次性邮箱黑名单，共 {len(_blocklist_domains)} 个域名")
            else:
                print(f"警告：黑名单文件未找到：{BLOCKLIST_FILE}")
                _blocklist_domains = set()  # 空集合
        except Exception as e:
            print(f"错误：加载黑名单时出错：{e}")
            _blocklist_domains = set()  # 出错时使用空集合
    
    return _blocklist_domains

def get_domain_from_email(email):
    """从电子邮件地址中提取域名部分"""
    try:
        return email.split('@')[1].lower()
    except IndexError:
        return None  # 不是有效的电子邮件格式

def is_valid_email_format(email):
    """检查邮箱格式是否有效"""
    # 简单的邮箱格式验证正则表达式
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(email_pattern, email))

def is_disposable_email_local(email):
    """使用本地黑名单检查是否是一次性邮箱"""
    domain = get_domain_from_email(email)
    if not domain:
        return False
    
    blocklist = load_blocklist()
    return domain in blocklist

def is_disposable_email_api(email):
    """使用商业API检查是否是一次性邮箱"""
    try:
        domain = get_domain_from_email(email)
        if not domain:
            return {'valid': True}
        
        # 从环境变量中读取 RapidAPI Key
        rapidapi_key = os.getenv('RAPIDAPI_KEY')
        if not rapidapi_key:
            print("警告：RAPIDAPI_KEY 未设置，跳过API验证")
            return {'valid': True}
        
        # 设置RapidAPI请求
        conn = http.client.HTTPSConnection("mailcheck.p.rapidapi.com")
        headers = {
            'x-rapidapi-key': rapidapi_key,
            'x-rapidapi-host': "mailcheck.p.rapidapi.com"
        }
        
        # 发送请求
        conn.request("GET", f"/?domain={domain}", headers=headers)
        res = conn.getresponse()
        api_data = json.loads(res.read().decode("utf-8"))
        
        # 检查是否是一次性邮箱
        if api_data.get('disposable') or api_data.get('block'):
            return {
                'valid': False,
                'message': api_data.get('text', '临时邮箱不允许注册')
            }
        
        return {'valid': True}
        
    except Exception as e:
        print(f"警告：商业API验证失败: {str(e)}")
        return {'valid': True}  # API失败时默认通过

@email_validator_bp.route('/validate-email', methods=['POST'])
def validate_email():
    """验证邮箱API端点"""
    try:
        data = request.json
        email = data.get('email', '').strip().lower()
        print(f"开始验证邮箱: {email}")
        
        # 检查邮箱格式
        if not is_valid_email_format(email):
            print(f"邮箱格式验证失败: {email}")
            return jsonify({
                'valid': False,
                'reason': 'email_format',
                'message': '邮箱格式不正确'
            })
        
        # 白名单检查
        domain = get_domain_from_email(email)
        print(f"提取的域名: {domain}")
        allowlist_file = os.path.join(os.path.dirname(__file__), 'data', 'allowlist.conf')
        
        if os.path.exists(allowlist_file):
            with open(allowlist_file, 'r', encoding='utf-8') as f:
                allowlist = {line.strip().lower() for line in f if line.strip()}
            
            if domain in allowlist:
                print(f"域名在白名单中，直接通过: {domain}")
                return jsonify({'valid': True})
        
        # 第一级验证：使用本地黑名单检查
        if is_disposable_email_local(email):
            print(f"本地黑名单检查失败: {email}")
            return jsonify({
                'valid': False,
                'reason': 'disposable_email',
                'message': '临时邮箱不允许注册，请使用常规邮箱'
            })
        
        print(f"本地黑名单检查通过: {email}")
        
        # 第二级验证：使用商业API检查
        api_result = is_disposable_email_api(email)
        print(f"API验证结果: {api_result}")
        
        if not api_result.get('valid', True):
            print(f"API验证失败: {email}")
            return jsonify({
                'valid': False,
                'reason': 'disposable_email',
                'message': api_result.get('message', '临时邮箱不允许注册，请使用常规邮箱')
            })
        
        # 所有验证都通过
        print(f"邮箱验证全部通过: {email}")
        return jsonify({'valid': True})
        
    except Exception as e:
        print(f"邮箱验证出错: {str(e)}")
        return jsonify({
            'valid': False,
            'reason': 'server_error',
            'message': str(e)
        }), 500

def register_routes(app):
    """注册路由到Flask应用"""
    app.register_blueprint(email_validator_bp, url_prefix='/api')
