"""
模型调用门面 - 提供统一的模型调用接口
"""
import os
import sys
import logging
import importlib
from flask import Response, request
from api.models_config import get_model_by_id, get_default_model, get_model_internal_id

# 获取已配置的logger
logger = logging.getLogger(__name__)

def call_model_api(model_id, prompt, system_message):
    """
    统一的模型调用接口
    
    参数:
        model_id (str): 模型ID
        prompt (str): 提示内容
        system_message (str): 系统消息
        
    返回:
        Response: Flask响应对象
    """
    # 获取模型配置
    model_config = get_model_by_id(model_id)
    
    # 如果找不到配置，使用默认模型
    if not model_config:
        # 从请求头中获取Accept-Language，用于确定默认模型
        accept_language = request.headers.get('Accept-Language', '')
        language = 'zh'  # 默认中文
        
        # 检查Accept-Language头
        if accept_language and not accept_language.startswith('zh'):
            language = 'en'  # 非中文设为英文
            
        # 从请求参数中获取UI语言设置（如果有）
        ui_lang = request.args.get('ui_lang') or request.form.get('ui_lang')
        if ui_lang:
            language = ui_lang
            
        logger.warning(f"未找到模型 {model_id} 的配置，使用默认模型，语言: {language}")
        model_id = get_default_model(language)
        model_config = get_model_by_id(model_id)
    
    # 提取环境变量、模块名和函数名
    env_var = model_config.get("env_var", "")
    module_name = model_config.get("module", "")
    function_name = model_config.get("function", "")
    internal_id = model_config.get("internal_id", "")
    
    logger.info(f"处理模型请求: ID={model_id}, 内部ID={internal_id}")
    
    # 检查环境变量
    if not env_var or not os.environ.get(env_var):
        logger.warning(f"模型 {model_id} 的环境变量 {env_var} 未设置")
        mock_response = f"# {model_config.get('name', model_id)} API 密钥未设置\n\n请在环境变量中配置 {env_var}."
        return Response(mock_response, content_type='text/plain; charset=utf-8')
    
    # 加载模块和函数
    try:
        if module_name not in sys.modules:
            module = importlib.import_module(module_name)
        else:
            module = sys.modules[module_name]
        
        if not hasattr(module, function_name):
            logger.error(f"模块 {module_name} 中找不到函数 {function_name}")
            mock_response = f"# 模型 {model_config.get('name', model_id)} 函数未正确配置\n\n无法找到 {module_name}.{function_name} 函数."
            return Response(mock_response, content_type='text/plain; charset=utf-8')
        
        # 获取函数引用
        api_function = getattr(module, function_name)
        
        # 注入模型内部ID到请求对象中
        if hasattr(request, '_get_current_object'):
            req = request._get_current_object()
            # 为request对象添加模型内部ID信息
            if not hasattr(req, 'model_info'):
                req.model_info = {}
            req.model_info['internal_id'] = internal_id
            req.model_info['display_name'] = model_config.get('name', model_id)
            
            logger.debug(f"已将模型内部ID {internal_id} 注入到请求对象")
        
        # 针对智谱API的特殊处理 - 直接传递模型名称
        if module_name == 'zhipu_api':
            # 从internal_id获取实际模型名称，去掉前缀
            if internal_id.startswith('zhipu_glm_'):
                # 将internal_id转换为API实际使用的模型名称
                # 例如：zhipu_glm_4_flash -> glm-4-flash
                model_name = internal_id.replace('zhipu_glm_', 'glm-').replace('_', '-')
                logger.info(f"调用智谱API，使用模型名称: {model_name}")
                return api_function(prompt, system_message, model_name)
        
        # 调用API函数
        logger.info(f"调用 {model_id} 模型API")
        return api_function(prompt, system_message)
    
    except ImportError as e:
        logger.error(f"导入模块 {module_name} 失败: {str(e)}")
        mock_response = f"# 模型 {model_config.get('name', model_id)} 模块导入失败\n\n错误: {str(e)}"
        return Response(mock_response, content_type='text/plain; charset=utf-8')
    
    except Exception as e:
        logger.error(f"调用模型 {model_id} API时出错: {str(e)}")
        mock_response = f"# 调用 {model_config.get('name', model_id)} API时出错\n\n错误: {str(e)}"
        return Response(mock_response, content_type='text/plain; charset=utf-8')