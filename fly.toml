# fly.toml app configuration file generated for reader-app on 2025-05-16T10:40:55+08:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'reader-app'
primary_region = 'lax'

[build]
  dockerfile = 'Dockerfile'

[env]
  PORT = '8080'
  ENVIRONMENT = 'production'
  DEBUG = 'false'
  APP_URL = 'https://booksum.vip'
  ALLOWED_ORIGINS = 'http://127.0.0.1:3000,http://localhost:3000,https://booksum.vip'
  # Supabase配置 - 添加以解决认证问题
  SUPABASE_URL = 'https://ldagkyriuiaaqniwtbwk.supabase.co'
  SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxkYWdreXJpdWlhYXFuaXd0YndrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkxOTExMTQsImV4cCI6MjA1NDc2NzExNH0.MRXzxhHGbjxwkR3OX2ijLo-q6sj2m270Rn5wFhxCcrs'
  # 可选的服务角色密钥
  # SUPABASE_SERVICE_ROLE_KEY = 'your-supabase-service-role-key'

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  cpu_kind = 'shared'
  cpus = 1
  memory_mb = 1024
