"""
流式响应处理器模块
提供统一的流式响应处理和积分扣减功能
"""

import json
import logging
import asyncio
from typing import Dict, Any, Callable, Optional, AsyncGenerator
from flask import Response, stream_with_context

logger = logging.getLogger(__name__)

class StreamingHandler:
    """统一的流式响应处理器"""
    
    def __init__(self, auth_token: str, operation: str, model_id: str):
        self.auth_token = auth_token
        self.operation = operation
        self.model_id = model_id
        self.user_id = None
        
    async def verify_user_and_credits(self, content: str = "", multimodal_info: Dict = None) -> Dict[str, Any]:
        """验证用户身份和积分"""
        try:
            from api.credits import check_user_credits_for_operation
            
            has_credits, user_id, credits_info = check_user_credits_for_operation(
                self.auth_token, self.operation, self.model_id,
                content=content, multimodal_info=multimodal_info
            )
            
            self.user_id = user_id
            
            if not has_credits:
                return {
                    "success": False,
                    "error": "积分不足",
                    **credits_info
                }
            
            return {
                "success": True,
                "user_id": user_id,
                **credits_info
            }
            
        except Exception as e:
            logger.error(f"积分验证失败: {e}")
            return {
                "success": False,
                "error": f"积分验证失败: {str(e)}"
            }
    
    async def deduct_credits_on_completion(self, usage_info: Dict[str, Any], audio_info: str = ""):
        """完成时扣减积分"""
        try:
            from api.credits import deduct_user_credits
            
            if not self.user_id:
                logger.error("无法扣减积分：未获取到user_id")
                return
                
            result = deduct_user_credits(
                self.user_id, self.operation, self.model_id,
                token_usage=usage_info, audio_info=audio_info
            )
            
            if result.get('success'):
                logger.info(f"{self.operation}积分扣减成功: {result}")
            else:
                logger.error(f"{self.operation}积分扣减失败: {result}")
                
        except Exception as e:
            logger.error(f"{self.operation}积分扣减异常: {e}")
    
    def create_error_response(self, error_message: str) -> Response:
        """创建错误响应"""
        def error_generator():
            yield f"data: {json.dumps({'type': 'error', 'error': error_message}, ensure_ascii=False)}\n\n"
        
        return Response(
            stream_with_context(error_generator()),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive'
            }
        )
    
    def create_streaming_response(self, generator_func: Callable) -> Response:
        """创建流式响应"""
        def wrapper():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                async_gen = generator_func()
                while True:
                    try:
                        chunk = loop.run_until_complete(async_gen.__anext__())
                        yield chunk
                    except StopAsyncIteration:
                        break
            finally:
                loop.close()
        
        return Response(
            stream_with_context(wrapper()),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive'
            }
        )

class AudioAnalysisHandler(StreamingHandler):
    """音频分析专用处理器"""
    
    def __init__(self, auth_token: str, file_id: str, prompt: str, skip_credits_check: bool = False):
        super().__init__(auth_token, 'audio_analysis', 'qwen_omni_turbo')
        self.file_id = file_id
        self.prompt = prompt
        self.skip_credits_check = skip_credits_check
        
    async def process_audio_analysis(self, audio_manager) -> Response:
        """处理音频分析请求"""
        async def generate():
            try:
                # 检查文件是否存在
                try:
                    audio_path = await audio_manager.get_audio_path(self.file_id)
                except FileNotFoundError:
                    yield f"data: {json.dumps({'type': 'error', 'error': '音频文件不存在'}, ensure_ascii=False)}\n\n"
                    return
                
                # 文件大小检查
                import os
                from pydub import AudioSegment
                
                file_size = os.path.getsize(audio_path)
                filename = os.path.basename(audio_path)
                is_recording_file = "recording" in filename.lower() or file_size < 5 * 1024 * 1024
                max_size = 100 * 1024 * 1024  # 100MB限制
                
                if not is_recording_file and file_size > max_size:
                    yield f"data: {json.dumps({'type': 'error', 'error': f'音频文件太大 ({file_size/1024/1024:.1f}MB)，请使用小于100MB的文件'}, ensure_ascii=False)}\n\n"
                    return
                
                # 积分检查逻辑
                if not await self._handle_credits_check(audio_path):
                    yield f"data: {json.dumps({'type': 'error', 'error': '积分不足'}, ensure_ascii=False)}\n\n"
                    return
                
                # 执行音频分析
                import os
                import base64
                from openai import OpenAI
                
                # 获取API配置
                api_key = os.getenv('DASHSCOPE_API_KEY')
                if not api_key:
                    yield f"data: {json.dumps({'type': 'error', 'error': '千问API配置缺失'}, ensure_ascii=False)}\n\n"
                    return
                
                # 编码音频文件
                with open(audio_path, "rb") as audio_file:
                    base64_audio = base64.b64encode(audio_file.read()).decode("utf-8")
                
                # 确定音频格式
                ext = os.path.splitext(audio_path)[1].lower()
                audio_format = {
                    '.mp3': 'mp3', '.wav': 'wav', '.mp4': 'mp4', 
                    '.m4a': 'aac', '.ogg': 'ogg', '.webm': 'mp3'
                }.get(ext, 'mp3')
                
                # 创建API客户端
                client = OpenAI(
                    api_key=api_key,
                    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                    timeout=120.0,
                    max_retries=2
                )
                
                system_prompt = "你是一个专业的英汉口译专家，能够准确理解并翻译音频内容，请先给出音频的transcript，然后进行翻译和解释，并就其中涉及的语法结构、习语和习惯用法、文化背景提供解释和分析。"
                
                messages = [
                    {"role": "user", "content": system_prompt},
                    {"role": "assistant", "content": "好的，我记住了你的设定。"},
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "input_audio",
                                "input_audio": {
                                    "data": f"data:;base64,{base64_audio}",
                                    "format": audio_format,
                                },
                            },
                            {"type": "text", "text": self.prompt},
                        ],
                    }
                ]
                
                # 流式请求
                stream = client.chat.completions.create(
                    model="qwen-omni-turbo",
                    messages=messages,
                    modalities=["text"],
                    stream=True,
                    stream_options={"include_usage": True},
                    timeout=60.0
                )
                
                # 发送开始事件
                yield f"data: {json.dumps({'type': 'start', 'message': '开始分析音频...'}, ensure_ascii=False)}\n\n"
                
                accumulated_content = ""
                actual_usage = None
                
                for chunk in stream:
                    # 检查usage信息
                    if hasattr(chunk, 'usage') and chunk.usage:
                        actual_usage = chunk.usage
                    
                    if chunk.choices and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        accumulated_content += content
                        yield f"data: {json.dumps({'type': 'chunk', 'accumulated': accumulated_content}, ensure_ascii=False)}\n\n"
                
                # 发送完成事件
                yield f"data: {json.dumps({'type': 'complete', 'final_content': accumulated_content}, ensure_ascii=False)}\n\n"
                
                # 扣减积分
                if actual_usage:
                    usage_info = {
                        'input_tokens': actual_usage.prompt_tokens,
                        'output_tokens': actual_usage.completion_tokens,
                        'total_tokens': actual_usage.total_tokens
                    }
                    
                    if hasattr(actual_usage, 'prompt_tokens_details') and actual_usage.prompt_tokens_details:
                        usage_info['prompt_tokens_details'] = {
                            'audio_tokens': actual_usage.prompt_tokens_details.audio_tokens or 0,
                            'text_tokens': actual_usage.prompt_tokens_details.text_tokens or 0,
                            'cached_tokens': actual_usage.prompt_tokens_details.cached_tokens or 0
                        }
                    
                    await self.deduct_credits_on_completion(usage_info)
                    
            except Exception as e:
                logger.error(f"音频分析失败: {e}")
                yield f"data: {json.dumps({'type': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"
        
        return self.create_streaming_response(generate)
    
    async def _handle_credits_check(self, audio_path: str) -> bool:
        """处理积分检查逻辑"""
        import os
        from pydub import AudioSegment
        
        try:
            # 获取音频时长
            audio_segment = AudioSegment.from_file(audio_path)
            audio_duration = len(audio_segment) / 1000.0
            file_size = os.path.getsize(audio_path)
            
            if self.skip_credits_check:
                # 后端三层验证逻辑
                return await self._backend_verification(audio_duration)
            else:
                # 完整积分检查
                multimodal_info = {
                    "audio_duration": audio_duration,
                    "audio_file_size": file_size,
                    "has_audio": True,
                    "has_image": False
                }
                
                result = await self.verify_user_and_credits(
                    content=self.prompt,
                    multimodal_info=multimodal_info
                )
                
                return result["success"]
                
        except Exception as e:
            logger.error(f"积分检查失败: {e}")
            return False
    
    async def _backend_verification(self, audio_duration: float) -> bool:
        """后端三层验证"""
        from api.auth import get_user_from_token
        from api.credits import get_user_credits_from_cache
        
        # 获取用户ID
        user_data = get_user_from_token(self.auth_token)
        if not user_data:
            return False
        
        user_id = user_data['id']
        estimated_required = 5
        threshold = estimated_required + 10
        
        # 第一层：缓存检查
        cached_credits = get_user_credits_from_cache(user_id)
        if cached_credits is not None and cached_credits >= threshold:
            logger.info(f"🚀 后端缓存验证通过({cached_credits}>={threshold})")
            return True
        
        # 第二层：数据库检查
        try:
            from api.credits import service_supabase_client
            response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
            if response.data and len(response.data) > 0:
                db_credits = response.data[0]['credits']
                if db_credits >= threshold:
                    logger.info(f"🚀 数据库验证通过({db_credits}>={threshold})")
                    return True
        except Exception:
            pass
        
        # 第三层：完整API验证
        multimodal_info = {
            "audio_duration": audio_duration,
            "has_audio": True,
            "has_image": False
        }
        
        result = await self.verify_user_and_credits(
            content=self.prompt,
            multimodal_info=multimodal_info
        )
        
        return result["success"]
    
    async def _analyze_audio(self, audio_path: str):
        """执行音频分析"""
        import os
        import base64
        from openai import OpenAI
        
        # 获取API配置
        api_key = os.getenv('DASHSCOPE_API_KEY')
        if not api_key:
            yield f"data: {json.dumps({'type': 'error', 'error': '千问API配置缺失'}, ensure_ascii=False)}\n\n"
            return
        
        # 编码音频文件
        with open(audio_path, "rb") as audio_file:
            base64_audio = base64.b64encode(audio_file.read()).decode("utf-8")
        
        # 确定音频格式
        ext = os.path.splitext(audio_path)[1].lower()
        audio_format = {
            '.mp3': 'mp3', '.wav': 'wav', '.mp4': 'mp4', 
            '.m4a': 'aac', '.ogg': 'ogg', '.webm': 'mp3'
        }.get(ext, 'mp3')
        
        # 创建API客户端
        client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            timeout=120.0,
            max_retries=2
        )
        
        system_prompt = "你是一个专业的英汉口译专家，能够准确理解并翻译音频内容，请先给出音频的transcript，然后进行翻译和解释，并就其中涉及的语法结构、习语和习惯用法、文化背景提供解释和分析。"
        
        messages = [
            {"role": "user", "content": system_prompt},
            {"role": "assistant", "content": "好的，我记住了你的设定。"},
            {
                "role": "user",
                "content": [
                    {
                        "type": "input_audio",
                        "input_audio": {
                            "data": f"data:;base64,{base64_audio}",
                            "format": audio_format,
                        },
                    },
                    {"type": "text", "text": self.prompt},
                ],
            }
        ]
        
        # 流式请求
        stream = client.chat.completions.create(
            model="qwen-omni-turbo",
            messages=messages,
            modalities=["text"],
            stream=True,
            stream_options={"include_usage": True},
            timeout=60.0
        )
        
        # 发送开始事件
        yield f"data: {json.dumps({'type': 'start', 'message': '开始分析音频...'}, ensure_ascii=False)}\n\n"
        
        accumulated_content = ""
        actual_usage = None
        
        for chunk in stream:
            # 检查usage信息
            if hasattr(chunk, 'usage') and chunk.usage:
                actual_usage = chunk.usage
            
            if chunk.choices and chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                accumulated_content += content
                yield f"data: {json.dumps({'type': 'chunk', 'accumulated': accumulated_content}, ensure_ascii=False)}\n\n"
        
        # 发送完成事件
        yield f"data: {json.dumps({'type': 'complete', 'final_content': accumulated_content}, ensure_ascii=False)}\n\n"
        
        # 扣减积分
        if actual_usage:
            usage_info = {
                'input_tokens': actual_usage.prompt_tokens,
                'output_tokens': actual_usage.completion_tokens,
                'total_tokens': actual_usage.total_tokens
            }
            
            if hasattr(actual_usage, 'prompt_tokens_details') and actual_usage.prompt_tokens_details:
                usage_info['prompt_tokens_details'] = {
                    'audio_tokens': actual_usage.prompt_tokens_details.audio_tokens or 0,
                    'text_tokens': actual_usage.prompt_tokens_details.text_tokens or 0,
                    'cached_tokens': actual_usage.prompt_tokens_details.cached_tokens or 0
                }
            
            await self.deduct_credits_on_completion(usage_info) 