-- ========================================
-- 积分时限系统修复脚本
-- 修复日期: 2025-06-20
-- 说明: 修复 deduct_credits_fifo 函数中的列名歧义问题
-- ========================================

-- 删除原有的有问题的函数
DROP FUNCTION IF EXISTS public.deduct_credits_fifo(UUID, INTEGER);

-- 重新创建修复后的 FIFO 积分扣减函数
CREATE OR REPLACE FUNCTION public.deduct_credits_fifo(
    p_user_id UUID,
    p_credits_to_deduct INTEGER
) RETURNS TABLE(
    success BOOLEAN,
    remaining_credits INTEGER,
    affected_batches JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_current_credits INTEGER;
    v_remaining_to_deduct INTEGER;
    v_batch_record RECORD;
    v_deduct_from_batch INTEGER;
    v_affected_batches JSONB DEFAULT '[]'::JSONB;
    v_batch_info JSONB;
BEGIN
    -- 检查用户当前积分
    SELECT COALESCE(credits, 0) INTO v_current_credits
    FROM public.user_credits 
    WHERE user_id = p_user_id;
    
    -- 检查积分是否足够
    IF v_current_credits < p_credits_to_deduct THEN
        RETURN QUERY SELECT FALSE, v_current_credits, '[]'::JSONB;
        RETURN;
    END IF;
    
    -- 初始化待扣减积分
    v_remaining_to_deduct := p_credits_to_deduct;
    
    -- 按创建时间先进先出扣减
    FOR v_batch_record IN 
        SELECT id, remaining_credits, created_at
        FROM public.credit_batches
        WHERE user_id = p_user_id 
        AND remaining_credits > 0 
        AND NOT is_expired
        AND expires_at > NOW()
        ORDER BY created_at ASC
    LOOP
        EXIT WHEN v_remaining_to_deduct <= 0;
        
        -- 计算从当前批次扣减的积分
        v_deduct_from_batch := LEAST(v_batch_record.remaining_credits, v_remaining_to_deduct);
        
        -- 更新批次剩余积分（明确指定表名避免歧义）
        UPDATE public.credit_batches
        SET remaining_credits = credit_batches.remaining_credits - v_deduct_from_batch
        WHERE id = v_batch_record.id;
        
        -- 记录批次信息
        v_batch_info := jsonb_build_object(
            'batch_id', v_batch_record.id,
            'deducted', v_deduct_from_batch,
            'remaining', v_batch_record.remaining_credits - v_deduct_from_batch
        );
        v_affected_batches := v_affected_batches || v_batch_info;
        
        -- 减少待扣减积分
        v_remaining_to_deduct := v_remaining_to_deduct - v_deduct_from_batch;
    END LOOP;
    
    -- 更新用户总积分
    UPDATE public.user_credits
    SET credits = credits - p_credits_to_deduct,
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    -- 获取更新后的积分
    SELECT credits INTO v_current_credits
    FROM public.user_credits 
    WHERE user_id = p_user_id;
    
    RETURN QUERY SELECT TRUE, v_current_credits, v_affected_batches;
END;
$$;

-- 重新授权
GRANT EXECUTE ON FUNCTION public.deduct_credits_fifo TO service_role;

-- 验证修复
DO $$
BEGIN
    RAISE NOTICE '========================================';
    RAISE NOTICE '积分时限系统修复完成！';
    RAISE NOTICE '========================================';
    RAISE NOTICE '已修复: deduct_credits_fifo 函数中的列名歧义问题';
    RAISE NOTICE '现在可以正常使用 FIFO 积分扣减功能';
    RAISE NOTICE '========================================';
END;
$$; 