<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证状态恢复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status-box {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .log-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .data-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px;
            background: #f8f9fa;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>🔧 认证状态恢复测试</h1>
    
    <div class="status-box warning">
        <h3>⚠️ 问题分析</h3>
        <p><strong>发现问题</strong>：统一认证管理器在Supabase还没完全恢复session时就立即检查了认证状态。</p>
        <p><strong>解决方案</strong>：</p>
        <ul>
            <li>✅ 移除init()中的立即getSession()调用</li>
            <li>✅ 改进waitForAuthState()逻辑，智能等待真正的状态</li>
            <li>✅ 添加手动恢复机制作为后备方案</li>
            <li>✅ 检查localStorage与Supabase状态的一致性</li>
        </ul>
    </div>

    <div class="status-box" id="localStorageStatus">
        <h3>💾 本地存储状态</h3>
        <div id="localStorageData">检查中...</div>
    </div>

    <div class="status-box" id="authStatus">
        <h3>🔐 认证状态</h3>
        <p>状态：<span id="authStatusText">检查中...</span></p>
        <p>用户：<span id="userInfo">-</span></p>
        <p>最后更新：<span id="lastUpdate">-</span></p>
    </div>

    <div class="status-box">
        <h3>🧪 测试操作</h3>
        <button class="button" onclick="checkLocalStorage()">检查localStorage</button>
        <button class="button" onclick="testAuthState()">检查认证状态</button>
        <button class="button" onclick="tryManualRecover()">手动恢复session</button>
        <button class="button" onclick="goToMain()">前往主页面</button>
        <button class="button" onclick="clearLogs()">清除日志</button>
    </div>

    <div class="log-box" id="logBox">
        <strong>实时日志：</strong><br>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="/env.js"></script>
    <script src="js/lib/supabase.min.js"></script>
    <script src="js/supabase-client.js"></script>
    <script src="js/unified-auth-manager.js"></script>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logBox = document.getElementById('logBox');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : type === 'warning' ? '#ffc107' : '#666';
            logBox.innerHTML += `<span style="color: ${color}">[${timestamp}]</span> ${message}<br>`;
            logBox.scrollTop = logBox.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logBox').innerHTML = '<strong>实时日志：</strong><br>';
        }

        // 检查localStorage状态
        function checkLocalStorage() {
            log('🔍 检查localStorage认证数据...');
            
            const authToken = localStorage.getItem('authToken');
            const refreshToken = localStorage.getItem('refreshToken');
            const userData = localStorage.getItem('userData');
            
            const statusDiv = document.getElementById('localStorageData');
            let html = '';
            
            html += `<div class="data-row"><span>认证令牌:</span><span>${authToken ? `存在 (${authToken.length}字符)` : '不存在'}</span></div>`;
            html += `<div class="data-row"><span>刷新令牌:</span><span>${refreshToken ? `存在 (${refreshToken.length}字符)` : '不存在'}</span></div>`;
            html += `<div class="data-row"><span>用户数据:</span><span>${userData ? '存在' : '不存在'}</span></div>`;
            
            if (userData && userData !== 'null') {
                try {
                    const user = JSON.parse(userData);
                    html += `<div class="data-row"><span>用户ID:</span><span>${user.id || '未知'}</span></div>`;
                    html += `<div class="data-row"><span>用户邮箱:</span><span>${user.email || '未知'}</span></div>`;
                } catch (e) {
                    html += `<div class="data-row"><span>用户数据解析:</span><span style="color: red">失败</span></div>`;
                }
            }
            
            statusDiv.innerHTML = html;
            
            const hasValidAuth = !!(authToken && refreshToken && userData && 
                                   authToken !== 'null' && refreshToken !== 'null' && userData !== 'null');
            
            const statusElement = document.getElementById('localStorageStatus');
            if (hasValidAuth) {
                statusElement.className = 'status-box success';
                log('✅ localStorage中有有效的认证数据', 'success');
            } else {
                statusElement.className = 'status-box error';
                log('❌ localStorage中无有效的认证数据', 'error');
            }
        }

        // 更新认证状态显示
        function updateAuthDisplay(authState) {
            const statusElement = document.getElementById('authStatus');
            const statusText = document.getElementById('authStatusText');
            const userInfo = document.getElementById('userInfo');
            const lastUpdate = document.getElementById('lastUpdate');

            if (authState.isAuthenticated) {
                statusElement.className = 'status-box success';
                statusText.textContent = '✅ 已登录';
                userInfo.textContent = authState.user ? authState.user.email : '用户信息不可用';
            } else {
                statusElement.className = 'status-box error';
                statusText.textContent = '❌ 未登录';
                userInfo.textContent = '-';
            }

            lastUpdate.textContent = authState.lastUpdate || '-';
        }

        // 测试函数
        async function testAuthState() {
            log('🧪 开始测试认证状态...');
            
            if (!window.unifiedAuthManager) {
                log('❌ 统一认证管理器不可用', 'error');
                return;
            }

            try {
                const authState = window.unifiedAuthManager.getAuthState();
                log(`📋 当前认证状态: ${JSON.stringify(authState, null, 2)}`);
                updateAuthDisplay(authState);
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function tryManualRecover() {
            log('🔄 尝试手动恢复认证状态...');
            
            if (!window.unifiedAuthManager) {
                log('❌ 统一认证管理器不可用', 'error');
                return;
            }

            try {
                const result = await window.unifiedAuthManager.tryRecoverFromLocalStorage();
                if (result) {
                    log('✅ 手动恢复成功', 'success');
                } else {
                    log('❌ 手动恢复失败', 'error');
                }
                
                // 更新显示
                setTimeout(() => {
                    testAuthState();
                }, 1000);
            } catch (error) {
                log(`❌ 手动恢复出错: ${error.message}`, 'error');
            }
        }

        function goToMain() {
            log('🚀 跳转到主页面...');
            window.location.href = '/main.html';
        }

        // 监听认证状态变化
        document.addEventListener('DOMContentLoaded', async function() {
            log('📋 测试页面开始初始化...');

            // 立即检查localStorage
            checkLocalStorage();

            // 等待统一认证管理器初始化
            let attempts = 0;
            while (!window.unifiedAuthManager?.isInitialized && attempts < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (window.unifiedAuthManager?.isInitialized) {
                log('✅ 统一认证管理器已初始化', 'success');
                
                // 添加认证状态监听器
                window.unifiedAuthManager.addListener((event, session, authState) => {
                    log(`🔔 认证状态变化: ${event}, 已认证: ${authState.isAuthenticated}`, 
                        authState.isAuthenticated ? 'success' : 'warning');
                    updateAuthDisplay(authState);
                });

                // 获取初始状态
                const authState = window.unifiedAuthManager.getAuthState();
                updateAuthDisplay(authState);
                log(`📋 初始认证状态: ${JSON.stringify(authState, null, 2)}`);
            } else {
                log('❌ 统一认证管理器初始化失败', 'error');
            }
        });

        // 页面加载完成时显示测试说明
        window.addEventListener('load', function() {
            log('🎉 认证状态恢复测试页面加载完成！');
            log('📝 请先检查localStorage，然后测试认证状态');
        });
    </script>
</body>
</html>
