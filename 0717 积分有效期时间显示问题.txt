你说得对，这个显示逻辑确实有问题。让我分析一下积分有效期的计算逻辑：
Search files...
Ran tool
Ran tool
Read file: sql/积分时限系统最终修复.sql
找到问题了！在第83行的天数计算逻辑中：

```sql
CASE 
    WHEN cb.expires_at <= NOW() THEN 0
    ELSE EXTRACT(DAY FROM cb.expires_at - NOW())::INTEGER
END as days_until_expiry,
```

**问题分析：**
1. `EXTRACT(DAY FROM cb.expires_at - NOW())` 只提取天数部分，不考虑小时、分钟
2. 如果购买时间是 `2025-07-17 18:03`，到期时间是 `2025-08-16 18:03`
3. 查询时间是 `2025-07-17 18:24`（购买后21分钟）
4. 时间差是 `29天23小时39分钟`，但 `EXTRACT(DAY FROM ...)` 只提取天数部分，结果是 `29天`

**正确的逻辑应该是：**
- 购买后立即查询应该显示 30天（或接近30天）
- 使用 `CEIL()` 函数向上取整，确保不满一天也算作一天

让我修复这个问题：
## 问题分析和解决方案

**问题本质：**
积分有效期显示为29天而不是30天，这是由于SQL中天数计算逻辑不准确造成的。

**原始问题代码：**
```sql
EXTRACT(DAY FROM cb.expires_at - NOW())::INTEGER
```

**问题原因：**
1. `EXTRACT(DAY FROM ...)` 只提取天数部分，忽略小时和分钟
2. 购买时间：2025-07-17 18:03，到期时间：2025-08-16 18:03
3. 查询时间：2025-07-17 18:24（购买后21分钟）
4. 时间差：29天23小时39分钟，但只提取天数部分得到29天

**修复方案：**
使用更精确的计算方法：
```sql
CEIL(EXTRACT(EPOCH FROM cb.expires_at - NOW()) / 86400)::INTEGER
```

**修复逻辑：**
1. `EXTRACT(EPOCH FROM cb.expires_at - NOW())` - 获取时间差的总秒数
2. `/ 86400` - 除以一天的秒数（86400秒）得到精确的天数
3. `CEIL()` - 向上取整，确保不满一天也算作一天
4. `::INTEGER` - 转换为整数

**修复后的效果：**
- 购买后立即查询将显示30天（或接近30天）
- 随着时间推移，天数会准确递减
- 不会出现购买后立即显示29天的问题

**执行修复：**
请运行以下命令来修复天数计算问题：

```bash
./fix_days_calculation.bat
```

修复完成后，重新查看积分有效期，应该会显示正确的30天有效期。

**总结修改内容：**
1. 创建了 `sql/fix_days_calculation.sql` - 修复天数计算逻辑
2. 创建了 `fix_days_calculation.bat` - 运行修复的批处理脚本
3. 使用精确的时间计算方法，确保购买后立即查询显示正确的30天有效期
------------------------------------------

-- ========================================
-- 修复积分有效期天数计算逻辑
-- 解决购买后立即显示29天而不是30天的问题
-- ========================================

-- 1. 修复获取用户积分批次详情函数中的天数计算
CREATE OR REPLACE FUNCTION public.get_user_credit_batches(p_user_id UUID)
RETURNS TABLE(
    batch_id UUID,
    credits_amount INTEGER,
    remaining_credits INTEGER,
    source_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_expired BOOLEAN,
    days_until_expiry INTEGER,
    order_id VARCHAR(255),
    description TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cb.id,
        cb.credits_amount,
        cb.remaining_credits,
        cb.source_type,
        cb.created_at,
        cb.expires_at,
        cb.is_expired,
        CASE 
            WHEN cb.expires_at <= NOW() THEN 0
            ELSE CEIL(EXTRACT(EPOCH FROM cb.expires_at - NOW()) / 86400)::INTEGER
        END as days_until_expiry,
        cb.order_id,
        cb.description
    FROM public.credit_batches cb
    WHERE cb.user_id = p_user_id 
      AND cb.remaining_credits > 0
      AND NOT cb.is_expired
    ORDER BY cb.created_at ASC; -- 按创建时间排序，体现FIFO原则
END;
$$;

-- 2. 测试修复结果
DO $$
DECLARE
    test_user_id UUID;
    test_record RECORD;
BEGIN
    -- 查找一个有积分的用户进行测试
    SELECT user_id INTO test_user_id
    FROM public.user_credits 
    WHERE credits > 0 
    LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE '测试用户ID: %', test_user_id;
        
        -- 测试修复后的函数
        FOR test_record IN 
            SELECT * FROM public.get_user_credit_batches(test_user_id)
        LOOP
            RAISE NOTICE '批次ID: %, 积分: %, 剩余天数: %, 到期时间: %', 
                         test_record.batch_id, 
                         test_record.remaining_credits, 
                         test_record.days_until_expiry,
                         test_record.expires_at;
        END LOOP;
    ELSE
        RAISE NOTICE '没有找到测试用户';
    END IF;
END;
$$;

-- 3. 验证修复
SELECT 
    'Days calculation fixed successfully!' as status,
    'Now using CEIL(EXTRACT(EPOCH FROM expires_at - NOW()) / 86400) for accurate day calculation' as explanation; 