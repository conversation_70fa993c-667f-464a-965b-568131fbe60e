from flask import request, current_app
import logging
from functools import wraps
from api.rate_limit_config import get_auth_limits, get_limit

logger = logging.getLogger(__name__)

def get_user_identifier():
    """
    返回用户标识符，优先使用用户ID，如果未登录则使用IP地址
    
    Returns:
        str: 用户ID或IP地址
    """
    # 尝试从请求头中获取授权令牌
    auth_header = request.headers.get('Authorization', '')
    auth_token = auth_header.replace('Bearer ', '') if auth_header.startswith('Bearer ') else None
    
    if auth_token:
        try:
            # 从Supabase获取用户ID
            from api.credits import supabase_client
            if supabase_client:
                # 验证令牌并获取用户ID
                response = supabase_client.auth.get_user(auth_token)
                if response and response.user and response.user.id:
                    user_id = response.user.id
                    return f"user:{user_id}"
        except Exception as e:
            logger.warning(f"无法从令牌获取用户ID: {str(e)}")
    
    # 如果无法获取用户ID，则使用IP地址
    return f"ip:{request.remote_addr}"

def get_operation_specific_identifier(operation_type):
    """
    根据操作类型返回适合的用户标识符
    
    对于认证相关操作（注册/登录/验证），始终使用IP地址作为标识符
    对于其他操作（已登录后的功能），优先使用用户ID，无法获取时回退到IP地址
    
    Args:
        operation_type: 操作类型，可选值为 'auth' 或 'content'
        
    Returns:
        str: 用户标识符（用户ID或IP地址）
    """
    # 认证相关操作始终使用IP地址
    if operation_type == 'auth':
        return f"ip:{request.remote_addr}"
    
    # 其他操作（内容、积分、支付等）优先使用用户ID
    auth_header = request.headers.get('Authorization', '')
    auth_token = auth_header.replace('Bearer ', '') if auth_header.startswith('Bearer ') else None
    
    if auth_token:
        try:
            # 从Supabase获取用户ID
            from api.credits import supabase_client
            if supabase_client:
                # 验证令牌并获取用户ID
                response = supabase_client.auth.get_user(auth_token)
                if response and response.user and response.user.id:
                    user_id = response.user.id
                    return f"user:{user_id}"
        except Exception as e:
            logger.warning(f"无法从令牌获取用户ID: {str(e)}")
    
    # 如果无法获取用户ID，则回退到IP地址
    return f"ip:{request.remote_addr}"

def tier_based_ratelimit(tier_limits):
    """
    基于用户等级的速率限制装饰器
    
    Args:
        tier_limits (dict): 不同用户等级的速率限制，格式为 {'default': '10 per minute', 'premium': '30 per minute'}
    
    Returns:
        function: 装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 获取用户等级
            user_tier = 'default'  # 默认等级
            
            # 尝试从请求头中获取授权令牌
            auth_header = request.headers.get('Authorization', '')
            auth_token = auth_header.replace('Bearer ', '') if auth_header.startswith('Bearer ') else None
            
            if auth_token:
                try:
                    # 这里可以添加获取用户等级的逻辑
                    # 例如从数据库中查询用户的订阅等级
                    # 目前简单返回默认等级
                    pass
                except Exception as e:
                    logger.warning(f"获取用户等级时出错: {str(e)}")
            
            # 获取对应等级的速率限制
            limit = tier_limits.get(user_tier, tier_limits['default'])
            
            # 应用速率限制
            limiter = current_app.limiter
            endpoint = f"{request.endpoint}:{get_user_identifier()}"
            
            # 检查是否超过限制
            if limiter.check(limit, endpoint):
                return f(*args, **kwargs)
            else:
                logger.warning(f"速率限制超出: {endpoint} 超过了 {limit}")
                return {"error": "请求频率过高，请稍后再试"}, 429
                
        return decorated_function
    return decorator

def get_auth_key_function(limit_type):
    """
    返回用于认证相关操作的键函数
    
    Args:
        limit_type: 限制类型，如'ip'、'email'、'email_domain'等
        
    Returns:
        function: 用于生成限制键的函数
    """
    def get_ip_key():
        return f"auth:ip:{request.remote_addr}"
    
    def get_email_key():
        # 从请求中获取邮箱
        data = request.get_json() or {}
        email = data.get('email', '')
        if not email:
            # 如果没有邮箱，回退到IP
            return get_ip_key()
        return f"auth:email:{email.lower()}"
    
    def get_email_domain_key():
        # 从请求中获取邮箱，并提取域名
        data = request.get_json() or {}
        email = data.get('email', '')
        if not email or '@' not in email:
            # 如果没有有效邮箱，回退到IP
            return get_ip_key()
        domain = email.lower().split('@')[-1]
        return f"auth:domain:{domain}"
    
    # 根据限制类型返回相应的键函数
    key_functions = {
        'ip': get_ip_key,
        'email': get_email_key,
        'email_domain': get_email_domain_key
    }
    
    return key_functions.get(limit_type, get_ip_key)
