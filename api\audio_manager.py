"""
音频文件管理模块
提供音频文件的上传、录制、片段提取和清理功能
"""

import os
import uuid
import time
import glob
import logging
from typing import Optional, Dict, Any
from pydub import AudioSegment

logger = logging.getLogger(__name__)

class AudioFileManager:
    """音频文件管理器 - 实现正确的文件分类和清理逻辑"""
    
    def __init__(self):
        self.audio_files: Dict[str, str] = {}  # file_id -> file_path
        self.temp_dir = os.path.abspath("temp")  # 使用绝对路径
        
        # 文件分类管理 - 按用户要求调整
        self.current_recording_file: Optional[str] = None  # 当前录音文件ID
        self.max_recording_files = 1  # 保留最近1个录音文件
        self.recording_files: list = []  # 录音文件ID列表
        
        self.current_snippet_file: Optional[str] = None  # 当前音频片段文件ID
        self.max_snippet_files = 1  # 保留最近1个音频片段
        self.snippet_files: list = []  # 音频片段文件ID列表
        
        self.max_uploaded_files = 1  # 保留最近1个上传文件（运行期间永不清理）
        self.uploaded_files: list = []  # 上传文件ID列表
        self.original_filenames: Dict[str, str] = {}  # {file_id: original_filename}
        
        # 资源控制：最多3个文件并存（1上传+1录音+1片段）
        self.max_total_files = 3
        
        self.ensure_temp_directory()
        logger.info("音频管理器初始化完成 - 资源控制：最多3个文件并存")
        
    def ensure_temp_directory(self):
        """确保临时目录存在"""
        os.makedirs(self.temp_dir, exist_ok=True)

    async def startup_cleanup(self):
        """应用启动时的清理工作 - 按正确逻辑清理所有临时文件"""
        logger.info("🔄 执行启动时临时文件清理...")
        try:
            temp_files = glob.glob(os.path.join(self.temp_dir, "*"))
            cleaned_count = 0
            
            for file_path in temp_files:
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        cleaned_count += 1
                        logger.debug(f"清理临时文件: {file_path}")
                except Exception as e:
                    logger.warning(f"清理文件失败 {file_path}: {e}")
            
            # 清空所有内存记录
            self.audio_files.clear()
            self.current_recording_file = None
            self.recording_files.clear()
            self.current_snippet_file = None
            self.snippet_files.clear()
            self.uploaded_files.clear()
            self.original_filenames.clear()
            
            logger.info(f"✅ 启动清理完成，已清理 {cleaned_count} 个临时文件，确保每次启动都是干净状态")
            
        except Exception as e:
            logger.error(f"❌ 启动清理失败: {e}")

    async def check_resource_limits(self):
        """检查资源限制，确保最多3个文件并存"""
        await self.check_resource_limits_with_protection([])

    async def check_resource_limits_with_protection(self, protected_file_ids: list):
        """检查资源限制，确保最多3个文件并存，但保护指定的文件不被删除"""
        total_files = len(self.uploaded_files) + len(self.recording_files) + len(self.snippet_files)
        
        if total_files > self.max_total_files:
            logger.warning(f"⚠️ 文件数量超限！当前：{total_files}，限制：{self.max_total_files}")
            # 按优先级清理：先清理最旧的音频片段，再清理最旧的录音，最后是上传文件
            
            # 1. 优先清理最旧的音频片段（但保护受保护的文件）
            while len(self.snippet_files) > 0 and total_files > self.max_total_files:
                found_deletable = False
                for i, old_file_id in enumerate(self.snippet_files):
                    if old_file_id not in protected_file_ids:
                        self.snippet_files.pop(i)
                        await self._delete_file_by_id(old_file_id, "音频片段")
                        total_files -= 1
                        found_deletable = True
                        break
                
                if not found_deletable:
                    break  # 所有snippet文件都被保护，无法继续清理
            
            # 2. 再清理最旧的录音文件（但保护受保护的文件）
            while len(self.recording_files) > 0 and total_files > self.max_total_files:
                found_deletable = False
                for i, old_file_id in enumerate(self.recording_files):
                    if old_file_id not in protected_file_ids:
                        self.recording_files.pop(i)
                        await self._delete_file_by_id(old_file_id, "录音文件")
                        total_files -= 1
                        found_deletable = True
                        break
                
                if not found_deletable:
                    break  # 所有录音文件都被保护，无法继续清理
            
            # 3. 最后清理最旧的上传文件（运行期间一般不会到这一步，但保护受保护的文件）
            while len(self.uploaded_files) > 1 and total_files > self.max_total_files:
                found_deletable = False
                for i, old_file_id in enumerate(self.uploaded_files):
                    if old_file_id not in protected_file_ids:
                        self.uploaded_files.pop(i)
                        await self._delete_file_by_id(old_file_id, "上传文件")
                        total_files -= 1
                        found_deletable = True
                        break
                
                if not found_deletable:
                    break  # 所有上传文件都被保护，无法继续清理
            
            logger.info(f"🧹 资源限制检查完成，当前文件数：{total_files}")

    async def _delete_file_by_id(self, file_id: str, file_type: str):
        """辅助方法：删除指定ID的文件"""
        if file_id in self.audio_files:
            file_path = self.audio_files[file_id]
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"🗑️ 清理旧{file_type}: {file_path}")
                del self.audio_files[file_id]
                
                # 清理相关记录
                if file_id in self.original_filenames:
                    del self.original_filenames[file_id]
            except Exception as e:
                logger.error(f"❌ 清理{file_type}失败: {e}")

    async def register_upload_file(self, file_id: str, file_path: str, original_name: str = None):
        """专门注册上传文件（运行期间永不清理，只在重启时清理）"""
        # 管理上传文件数量
        await self._manage_uploaded_files(file_id)
        self.audio_files[file_id] = file_path
        
        # 保存原始文件名信息
        if original_name:
            self.original_filenames[file_id] = original_name
        
        # 检查资源限制
        await self.check_resource_limits()
            
        logger.info(f"📁 注册新上传文件: {file_id} -> {file_path} (原名: {original_name})")

    async def register_recording_file(self, file_id: str, file_path: str):
        """专门注册录音文件（智能管理，新录音时自动清理旧录音）"""
        # 先注册新文件到音频文件字典和录音列表
        self.audio_files[file_id] = file_path
        self.current_recording_file = file_id
        
        # 管理录音文件数量（清理旧录音）
        await self._manage_recording_files(file_id)
        
        # 检查资源限制，但保护刚注册的新文件
        await self.check_resource_limits_with_protection([file_id])
        
        logger.info(f"🎤 注册新录音文件: {file_id} -> {file_path}")

    async def register_snippet_file(self, file_id: str, file_path: str):
        """专门注册音频片段文件（智能管理，新片段时自动清理旧片段）"""
        # 先注册新文件，避免在清理过程中误删
        self.current_snippet_file = file_id
        self.audio_files[file_id] = file_path
        
        # 再管理文件数量（清理旧片段）
        await self._manage_snippet_files(file_id)
        
        # 检查资源限制，但保护刚注册的新文件
        await self.check_resource_limits_with_protection([file_id])
        
        logger.info(f"✂️ 注册新音频片段: {file_id} -> {file_path}")

    async def _manage_recording_files(self, new_file_id: str):
        """管理录音文件数量，新录音时自动清理旧录音"""
        # 添加新文件到录音列表（如果还没有的话）
        if new_file_id not in self.recording_files:
            self.recording_files.append(new_file_id)
        
        # 如果超过最大数量，删除最旧的文件（但不删除刚添加的新文件）
        while len(self.recording_files) > self.max_recording_files:
            old_file_id = self.recording_files.pop(0)  # 移除最旧的
            # 确保不删除刚添加的新文件
            if old_file_id != new_file_id:
                await self._delete_file_by_id(old_file_id, "录音文件")
            else:
                # 如果要删除的是新文件，跳过并中断循环
                self.recording_files.insert(0, old_file_id)  # 重新加入列表
                break

    async def _manage_snippet_files(self, new_file_id: str):
        """管理音频片段文件数量，新片段时自动清理旧片段"""
        # 添加新文件到片段列表
        if new_file_id not in self.snippet_files:
            self.snippet_files.append(new_file_id)
        
        # 如果超过最大数量，删除最旧的文件（但不删除刚添加的新文件）
        while len(self.snippet_files) > self.max_snippet_files:
            old_file_id = self.snippet_files.pop(0)  # 移除最旧的
            # 确保不删除刚添加的新文件
            if old_file_id != new_file_id:
                await self._delete_file_by_id(old_file_id, "音频片段")
            else:
                # 如果要删除的是新文件，跳过并中断循环
                self.snippet_files.insert(0, old_file_id)  # 重新加入列表
                break

    async def _manage_uploaded_files(self, new_file_id: str):
        """管理上传文件 - 运行期间永不清理，只在重启时清理"""
        # 添加新文件到上传列表
        if new_file_id not in self.uploaded_files:
            self.uploaded_files.append(new_file_id)
        
        # 只有在极端情况下（超过资源限制）才清理最旧的上传文件
        # 这里不主动清理，让check_resource_limits来处理
        logger.info(f"📁 上传文件管理：当前{len(self.uploaded_files)}个上传文件，运行期间永不清理")

    async def extract_audio_snippet(self, file_id: str, current_time: float, duration: int = 6) -> str:
        """提取音频片段 - 从current_time向前截取duration秒 - 性能优化版"""
        try:
            # 获取原始音频文件路径
            audio_path = await self.get_audio_path(file_id)
            
            # 性能优化1：使用更快的音频加载方式
            logger.info(f"开始音频片段提取: {file_id}, 时间点: {current_time}s, 时长: {duration}s")
            start_extract_time = time.time()
            
            # 计算提取范围：从current_time向前截取duration秒
            end_ms = int(current_time * 1000)  # 当前播放位置（毫秒）
            start_ms = max(0, end_ms - (duration * 1000))  # 向前duration秒，确保不小于0
            
            # 性能优化2：使用原始格式提取，避免不必要的转换
            audio = AudioSegment.from_file(audio_path)
            
            # 确保不超过音频长度
            if end_ms > len(audio):
                end_ms = len(audio)
                # 如果结束时间被调整，相应调整开始时间
                start_ms = max(0, end_ms - (duration * 1000))
            
            # 提取片段
            snippet = audio[start_ms:end_ms]
            
            # 生成临时文件 - 性能优化3：使用WAV格式避免MP3编码延迟
            snippet_id = f"snippet_{uuid.uuid4()}"
            snippet_filename = f"{snippet_id}.wav"  # 改为WAV格式提高导出速度
            snippet_path = os.path.abspath(os.path.join(self.temp_dir, snippet_filename))
            
            # 导出片段 - 使用更快的WAV格式
            export_start = time.time()
            snippet.export(snippet_path, format="wav", parameters=["-ac", "1", "-ar", "16000"])  # 单声道，16kHz采样率
            export_time = time.time() - export_start
            
            # 验证文件确实存在且可读
            if not os.path.exists(snippet_path):
                raise FileNotFoundError(f"音频片段文件创建失败: {snippet_path}")
            
            # 验证文件大小
            file_size = os.path.getsize(snippet_path)
            if file_size == 0:
                raise ValueError(f"音频片段文件为空: {snippet_path}")
            
            # 注册片段文件（使用正确的注册方法）
            await self.register_snippet_file(snippet_id, snippet_path)
            
            # 再次验证文件仍然存在（防止注册时被意外删除）
            if not os.path.exists(snippet_path):
                raise FileNotFoundError(f"音频片段文件在注册后消失: {snippet_path}")
            
            total_time = time.time() - start_extract_time
            logger.info(f"⚡ 音频片段提取完成: {file_id} -> {snippet_id}, 时间范围: {start_ms/1000:.1f}s - {end_ms/1000:.1f}s, 文件大小: {file_size} bytes, 导出耗时: {export_time:.2f}s, 总耗时: {total_time:.2f}s")
            return snippet_id
            
        except Exception as e:
            logger.error(f"音频片段提取失败: {e}")
            raise

    async def get_audio_path(self, file_id: str) -> str:
        """获取音频文件路径"""
        logger.debug(f"查找音频文件: {file_id}, 当前注册文件数: {len(self.audio_files)}")
        logger.debug(f"当前已注册的文件ID: {list(self.audio_files.keys())}")
        
        if file_id not in self.audio_files:
            # 尝试从temp目录查找多种格式和命名模式
            potential_extensions = ['.wav', '.mp3', '.mp4', '.m4a', '.webm', '.ogg']
            potential_paths = []
            
            # 1. 检查旧的UUID命名模式（用于录音文件和音频片段）
            for ext in potential_extensions:
                potential_paths.append(os.path.join(self.temp_dir, f"{file_id}{ext}"))
            
            # 2. 检查新的上传文件命名模式（原名_完整ID.扩展名）
            try:
                temp_files = os.listdir(self.temp_dir)
                for temp_file in temp_files:
                    # 检查文件名是否包含完整file_id且以支持的扩展名结尾
                    if file_id in temp_file and any(temp_file.lower().endswith(ext) for ext in potential_extensions):
                        potential_paths.append(os.path.join(self.temp_dir, temp_file))
            except Exception as e:
                logger.warning(f"扫描temp目录失败: {e}")
            
            # 检查是否存在任何格式的文件
            for path in potential_paths:
                if os.path.exists(path):
                    self.audio_files[file_id] = path
                    logger.info(f"找到音频文件: {path}")
                    return path
            
            # 列出temp目录下的所有文件进行调试
            try:
                temp_files = os.listdir(self.temp_dir)
                logger.error(f"音频文件不存在: {file_id}, temp目录文件列表: {temp_files}")
                logger.error(f"尝试查找的路径: {potential_paths}")
            except Exception as e:
                logger.error(f"无法列出temp目录: {e}")
            
            raise FileNotFoundError(f"音频文件不存在: {file_id}")
        
        # 验证已注册的文件是否确实存在
        file_path = self.audio_files[file_id]
        if not os.path.exists(file_path):
            logger.error(f"已注册的音频文件不存在于磁盘: {file_path}")
            # 从注册表中移除无效记录
            del self.audio_files[file_id]
            raise FileNotFoundError(f"音频文件不存在: {file_id}")
        
        return file_path

    async def delete_audio_file(self, file_id: str):
        """删除音频文件"""
        if file_id in self.audio_files:
            file_path = self.audio_files[file_id]
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.debug(f"删除音频文件: {file_path}")
                del self.audio_files[file_id]
                
                # 如果删除的是当前录音文件，清空记录
                if file_id == self.current_recording_file:
                    self.current_recording_file = None
                if file_id == self.current_snippet_file:
                    self.current_snippet_file = None
                
                # 从相关文件列表中移除
                if file_id in self.recording_files:
                    self.recording_files.remove(file_id)
                if file_id in self.snippet_files:
                    self.snippet_files.remove(file_id)
                if file_id in self.uploaded_files:
                    self.uploaded_files.remove(file_id)
                    
            except Exception as e:
                logger.error(f"删除音频文件失败: {e}")
                raise

    async def cleanup_orphaned_files(self):
        """清理孤立文件（手动调用，不自动执行）"""
        try:
            logger.info("清理孤立文件...")
            
            # 获取所有temp目录中的文件
            temp_files = glob.glob(os.path.join(self.temp_dir, "*"))
            registered_files = set(self.audio_files.values())
            orphaned_count = 0
            
            for file_path in temp_files:
                if os.path.isfile(file_path) and file_path not in registered_files:
                    try:
                        os.remove(file_path)
                        orphaned_count += 1
                        logger.debug(f"清理孤立文件: {file_path}")
                    except Exception as e:
                        logger.warning(f"清理孤立文件失败 {file_path}: {e}")
            
            if orphaned_count > 0:
                logger.info(f"孤立文件清理完成，已清理 {orphaned_count} 个文件")
                
            return {"cleaned_files": orphaned_count}
        except Exception as e:
            logger.error(f"清理孤立文件失败: {e}")
            raise

    def get_file_stats(self) -> Dict[str, Any]:
        """获取文件统计信息"""
        try:
            temp_files = glob.glob(os.path.join(self.temp_dir, "*"))
            total_size = sum(os.path.getsize(f) for f in temp_files if os.path.isfile(f))
            total_active_files = len(self.uploaded_files) + len(self.recording_files) + len(self.snippet_files)
            
            return {
                "total_files": len(temp_files),
                "registered_files": len(self.audio_files),
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "current_recording": self.current_recording_file is not None,
                "uploaded_files": len(self.uploaded_files),
                "recording_files": len(self.recording_files),
                "snippet_files": len(self.snippet_files),
                "total_active_files": total_active_files,
                "max_total_files": self.max_total_files,
                "resource_usage": f"{total_active_files}/{self.max_total_files}",
                "within_limits": total_active_files <= self.max_total_files
            }
        except Exception as e:
            logger.error(f"获取文件统计失败: {e}")
            return {"error": str(e)} 