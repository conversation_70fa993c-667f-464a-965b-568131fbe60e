const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const configs = require('./config.js');

// 简单的命令行参数解析
function parseArgs() {
  const args = process.argv.slice(2);
  const result = { file: '' };
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    if ((arg === '--file' || arg === '-f') && i + 1 < args.length) {
      result.file = args[++i];
    } else if (arg === '--help' || arg === '-h') {
      console.log('Usage: node obfuscate.js [options]');
      console.log('Options:');
      console.log('  -f, --file <path>  指定要混淆的单个文件路径');
      console.log('  -h, --help         显示帮助信息');
      process.exit(0);
    }
  }
  
  return result;
}

const argv = parseArgs();

const sourceDir = path.join(__dirname, '..', 'public', 'js');
const outputDir = path.join(__dirname, '..', 'public', 'js-obfuscated');

if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
  console.log(`Created output directory: ${outputDir}`);
}

const fileClassifications = {
  // High Security Files - Authentication, Payment, OAuth, Credits
  highSecurity: [
    'auth.js',
    'oauth-manager.js', 
    'token-refresh.js',
    'token-refresh-helper.js',
    'token-error-handler.js',
    'credits.js',
    'payment.js',
    'payment-packages.js',
    'credit-history.js',
    'credits-loader.js',
    'supabase-client.js'
  ],
  
  // Medium Security Files - Core Business Logic  
  mediumSecurity: [
    'main.js',
    'api-client.js',
    'knowledgeGraph.js',
    'bindNodeAndEdgeEvents.js',
    'knowledge-graph-timer.js',
    'resource-loader.js',
    'env-config.js',
    'app-config.js'
  ],
  
  // Light Security Files - UI and Performance Sensitive
  lightSecurity: [
    'mobile-menu.js',
    'desktop-menu.js',
    'model-selector.js',
    'tab-control.js',
    'firefox-fix.js'
  ],
  
  // Minimal Security Files - Language and Settings
  minimalSecurity: [
    'language.js',
    'language-settings.js',
    'ui-language.js',
    'logout.js'
  ]
};

const reservedNamesByFile = {
  'auth.js': ['supabaseClient', 'onCaptchaVerified', 'onLoginCaptchaVerified', 'onResetCaptchaVerified', 'onTurnstileError', 'turnstile'],
  'oauth-manager.js': ['handleOAuthRedirect', 'processOAuthSession', 'supabaseClient'],
  'token-refresh.js': ['supabaseClient', 'refreshToken'],
  'token-refresh-helper.js': ['supabaseClient', 'refreshToken'],
  'credits.js': ['CreditsManager', 'getCurrentCredits', 'updateCreditsDisplay', 'supabaseClient'],
  'payment.js': ['initializePayment', 'handlePaymentRedirect', 'supabaseClient'],
  'payment-packages.js': ['supabaseClient'],
  'credit-history.js': ['supabaseClient', 'CreditHistoryManager', 'showModal', 'getSupabaseClient'],
  'credits-loader.js': ['supabaseClient'],
  'knowledgeGraph.js': ['initializeGraph', 'updateGraph', 'addNode', 'addLink', 'd3'],
  'bindNodeAndEdgeEvents.js': ['d3'],
  'knowledge-graph-timer.js': ['d3'],
  'main.js': ['marked', 'mermaid', 'supabaseClient', 'showToast', 'ENV'],
  'model-selector.js': ['initializeModelSelector', 'getSelectedModel'],
  'language.js': ['UILanguage', 'loadLanguage', 'translatePage', 'getText', 'getCurrentLanguage'],
  'language-settings.js': ['UILanguage', 'loadLanguage', 'translatePage', 'getText', 'getCurrentLanguage'],
  'ui-language.js': ['UILanguage', 'loadLanguage', 'translatePage', 'getText', 'getCurrentLanguage'],
  'supabase-client.js': ['supabase', 'supabaseClient', 'supabaseClientInstance', 'getSupabaseClient']
};

function getSecurityLevel(filename) {
  const basename = path.basename(filename);
  
  for (const [level, files] of Object.entries(fileClassifications)) {
    if (files.includes(basename)) {
      return level;
    }
  }
  
  if (basename.includes('auth') || basename.includes('token') || basename.includes('oauth') || 
      basename.includes('credit') || basename.includes('payment')) {
    return 'highSecurity';
  }
  
  if (basename.includes('main') || basename.includes('api') || basename.includes('config') ||
      basename.includes('knowledge') || basename.includes('graph')) {
    return 'mediumSecurity';
  }
  
  if (basename.includes('ui') || basename.includes('menu') || basename.includes('mobile') ||
      basename.includes('desktop') || basename.includes('tab')) {
    return 'lightSecurity';
  }
  
  if (basename.includes('lang') || basename.includes('i18n') || basename.includes('language')) {
    return 'minimalSecurity';
  }
  
  return 'mediumSecurity';
}

function generateConfigForFile(filename) {
  const basename = path.basename(filename);
  const securityLevel = getSecurityLevel(filename);
  const baseConfig = configs[securityLevel];
  
  const config = {
    ...baseConfig,
    reservedNames: reservedNamesByFile[basename] || []
  };
  
  console.log(`Using ${securityLevel} config for ${basename}`);
  return config;
}

function scanJsFiles(dir) {
  const files = [];
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    
    if (entry.isDirectory()) {
      files.push(...scanJsFiles(fullPath));
    } else if (entry.name.endsWith('.js') && 
               !entry.name.endsWith('.min.js') && 
               !entry.name.includes('.obfuscated.') &&
               !entry.name.includes('.bundle.')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function obfuscateFile(inputFile) {
  try {
    const fileName = path.basename(inputFile);
    const relativePath = path.relative(sourceDir, path.dirname(inputFile));
    const targetDir = path.join(outputDir, relativePath);
    
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }
    
    const outputFile = path.join(targetDir, fileName);
    
    console.log(`Obfuscating: ${fileName}`);
    
    const config = generateConfigForFile(fileName);
    const tempConfigPath = path.join(__dirname, `temp_config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.js`);
    
    const configContent = `module.exports = ${JSON.stringify(config, null, 2)};`;
    fs.writeFileSync(tempConfigPath, configContent);
    
    const command = `javascript-obfuscator "${inputFile}" --output "${outputFile}" --config "${tempConfigPath}"`;
    execSync(command, { stdio: 'pipe' });
    
    fs.unlinkSync(tempConfigPath);
    
    console.log(`Completed: ${fileName}`);
    return true;
  } catch (error) {
    console.error(`Failed to obfuscate ${inputFile}: ${error.message}`);
    return false;
  }
}

function main() {
  console.log('Starting JavaScript obfuscation process...');
  console.log(`Source: ${sourceDir}`);
  console.log(`Output: ${outputDir}`);
  
  let jsFiles = [];
  
  // 如果指定了单个文件
  if (argv.file) {
    const filePath = path.resolve(process.cwd(), argv.file);
    if (fs.existsSync(filePath) && filePath.endsWith('.js')) {
      jsFiles = [filePath];
      console.log(`Processing single file: ${filePath}`);
    } else {
      console.error(`Error: ${filePath} is not a valid JavaScript file or does not exist.`);
      process.exit(1);
    }
  } else {
    // 否则处理目录下所有文件
    console.log('No specific file specified, processing all JS files...');
    jsFiles = scanJsFiles(sourceDir);
  }
  
  console.log(`Found ${jsFiles.length} JS files to process`);
  
  let successCount = 0;
  let failCount = 0;
  
  for (const file of jsFiles) {
    const success = obfuscateFile(file);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }
  
  console.log('\nObfuscation Results:');
  console.log(`Successfully processed: ${successCount} files`);
  console.log(`Failed: ${failCount} files`);
  
  if (failCount === 0) {
    console.log('All files obfuscated successfully!');
  }
}

main();