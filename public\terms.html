<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms of Service | AudioPilot</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            visibility: hidden; /* Hide content until language is loaded */
        }
        
        .container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .content {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 40px;
        }
        
        h1 {
            font-size: 1.8rem;
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        section {
            margin-bottom: 30px;
        }
        
        h2 {
            font-size: 1.2rem;
            color: #2196F3;
            margin-bottom: 15px;
        }
        
        p, ul {
            margin-bottom: 15px;
            line-height: 1.7;
        }
        
        ul {
            padding-left: 25px;
        }
        
        li {
            margin-bottom: 8px;
        }
        
        strong {
            font-weight: 600;
        }
        
        .contact-info {
            padding: 10px 15px;
            background-color: #f5f7fa;
            border-left: 4px solid #007bff;
            font-weight: 500;
        }
        
        .back-button {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .back-button:hover {
            background-color: #0056b3;
        }
        
        .highlight {
            color: #007bff;
        }

        .language-selector {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            z-index: 100;
        }
        
        .language-btn {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px 10px;
            margin-left: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .language-btn:hover {
            background: #f0f0f0;
        }
        
        .language-btn.active {
            background: #e7f3ff;
            border-color: #2196F3;
            color: #2196F3;
        }
    </style>
</head>
<body>
    <!-- Language selector -->
    <div class="language-selector">
        <button class="language-btn" data-lang="zh">中文</button>
        <button class="language-btn" data-lang="en">English</button>
    </div>
    
    <div class="container">
        <div class="content">
            <h1 data-i18n="terms.title">服务条款</h1>
            
            <section>
                <h2 data-i18n="terms.acceptance.heading">1. 接受条款</h2>
                <p data-i18n="terms.acceptance.content">通过访问或使用我们的服务，您同意受这些条款和我们的隐私政策的约束。如果您不同意这些条款，则不得使用我们的服务。</p>
            </section>
            
            <section>
                <h2 data-i18n="terms.description.heading">2. 服务描述</h2>
                <p data-i18n="terms.description.content">BookSum是一个使用AI工具对电子书内容进行总结摘要的在线服务平台。服务支持用户上传epub电子书等内容，并生成摘要、脑图等可视化输出，适用于研究、学习和分析。我们保留在任何时候不经通知修改、暂停或终止服务的权利。</p>
            </section>
            
            <section>
                <h2 data-i18n="terms.account.heading">3. 用户账户</h2>
                <ul>
                    <li data-i18n="terms.account.accurate">您需要提供准确、完整和最新的注册信息。</li>
                    <li data-i18n="terms.account.security">您有责任维护您个人账户和密码的机密性。</li>
                    <li data-i18n="terms.account.responsibility">您同意对在您的账户下发生的所有活动负责。</li>
                </ul>
            </section>
            
            <section>
                <h2 data-i18n="terms.usage.heading">4. 服务使用</h2>
                <p data-i18n="terms.usage.intro">您同意仅为合法目的使用服务，并遵守以下规定：</p>
                <ul>
                    <li data-i18n="terms.usage.legal">不得上传或传输任何非法、有害、诽谤或侵犯第三方知识产权的电子书内容，否则后果自负。</li>
                    <li data-i18n="terms.usage.unauthorized">不得试图未经授权访问我们的系统、网络或底层代码。</li>
                    <li data-i18n="terms.usage.automated">不得使用自动化脚本、机器人或其他方式未经授权收集服务中的数据。</li>
                    <li data-i18n="terms.usage.security">不得试图绕过我们为保护服务完整性而实施的任何安全或访问控制。</li>
                    <li data-i18n="terms.usage.credits">不得通过任何非法手段获取本平台的服务使用权限，如通过非法技术手段串改用户积分，否则我们有权随时终止您的服务。</li>
                </ul>
            </section>
            
            <section>
                <h2 data-i18n="terms.ip.heading">5. 知识产权</h2>
                <p data-i18n="terms.ip.content">服务中的所有内容、功能和功能，包括但不限于文本、图形、徽标和软件，均为我们或我们的许可方的专有财产，并受知识产权法保护。未经明确书面许可，您不得复制、分发、修改或创建任何衍生作品。</p>
            </section>
            
            <section>
                <h2 data-i18n="terms.data.heading">6. 数据处理与隐私</h2>
                <p data-i18n="terms.data.content">通过使用服务，您同意我们按照隐私政策收集、存储和处理数据。您保留上传内容的所有权，但您授予我们为提供服务而使用、存储和处理内容的许可。</p>
            </section>
            
            <section>
                <h2 data-i18n="terms.disclaimer.heading">7. 免责声明</h2>
                <ul>
                    <li data-i18n="terms.disclaimer.content">服务按"原样"和"可用"提供，不提供任何明示或暗示的保证，包括但不限于适销性、特定用途适用性或非侵权的保证。我们不保证服务将不间断、及时、安全或无错误。</li>
                </ul>
            </section>
            
            <section>
                <h2 data-i18n="terms.limitation.heading">8. 责任限制</h2>
                <ul>
                    <li data-i18n="terms.limitation.content">在法律允许的最大范围内，我们不对因使用服务而产生的任何直接、间接、附带、特殊或惩罚性损害负责。</li>
                </ul>
            </section>
            
            <section>
                <h2 data-i18n="terms.indemnity.heading">9. 赔偿</h2>
                <p data-i18n="terms.indemnity.content">您同意为我们及我们的关联公司、许可方和服务提供商辩护、赔偿并使其免受因您违反这些条款或使用服务而引起的任何索赔、责任、损害、费用或费用。</p>
            </section>
            
            <section>
                <h2 data-i18n="terms.law.heading">10. 适用法律与管辖</h2>
                <p data-i18n="terms.law.content">这些条款受中国法律管辖并按其解释。因这些条款或服务引起的任何法律诉讼或程序应专属提交中国法院。</p>
            </section>
            
            <section>
                <h2 data-i18n="terms.changes.heading">11. 条款变更</h2>
                <p data-i18n="terms.changes.content">我们保留随时修改这些条款的权利。修改后的条款将在网站上发布后立即生效。您继续使用服务即表示接受修改后的条款。</p>
            </section>
            
            <section>
                <h2 data-i18n="terms.contact.heading">12. 联系我们</h2>
                <p data-i18n="terms.contact.intro">如果您对这些服务条款有任何疑问，请通过以下方式联系我们：</p>
                <p class="contact-info" data-i18n="terms.contact.email">电子邮件：<EMAIL></p>
            </section>
            
            <a href="index.html" class="back-button" data-i18n="terms.backbutton">返回首页</a>
        </div>
    </div>

    <!-- Load UI language script -->
    <script src="js/ui-language.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Function to show the page content once language is loaded
            function showPageContent() {
                document.body.style.visibility = 'visible';
            }
            
            // Listen for language loaded event
            document.addEventListener('ui-language-loaded', showPageContent);
            
            // Set up language buttons
            const langButtons = document.querySelectorAll('.language-btn');
            langButtons.forEach(button => {
                const lang = button.getAttribute('data-lang');
                
                // Mark the active language button
                if (window.UILanguage && window.UILanguage.getCurrentLanguage() === lang) {
                    button.classList.add('active');
                }
                
                // Handle language button clicks
                button.addEventListener('click', function() {
                    const selectedLang = this.getAttribute('data-lang');
                    
                    if (window.UILanguage) {
                        // Set the language and update UI
                        window.UILanguage.setLanguage(selectedLang);
                        
                        // Update active button styling
                        langButtons.forEach(btn => btn.classList.remove('active'));
                        this.classList.add('active');
                    }
                });
            });
            
            // If UILanguage is already loaded, translate and show the page
            if (window.UILanguage) {
                setTimeout(() => {
                    showPageContent();
                }, 100);
            }
        });
    </script>
</body>
</html>