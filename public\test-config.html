<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置测试</title>
</head>
<body>
    <h1>Supabase 配置测试</h1>
    <div id="config-info"></div>
    <div id="test-results"></div>
    
    <script src="/env.js"></script>
    <script src="/js/lib/supabase.min.js"></script>
    <script>
        // 显示配置信息
        document.getElementById('config-info').innerHTML = `
            <h2>当前配置:</h2>
            <p><strong>SUPABASE_URL:</strong> ${window.ENV_CONFIG.SUPABASE_URL}</p>
            <p><strong>ANON_KEY:</strong> ${window.ENV_CONFIG.SUPABASE_ANON_KEY.substring(0, 50)}...</p>
        `;
        
        // 测试连接
        async function testConnection() {
            try {
                const { createClient } = supabase;
                const supabaseClient = createClient(
                    window.ENV_CONFIG.SUPABASE_URL,
                    window.ENV_CONFIG.SUPABASE_ANON_KEY
                );
                
                // 测试基本连接
                const { data, error } = await supabaseClient.auth.getSession();
                
                document.getElementById('test-results').innerHTML = `
                    <h2>连接测试结果:</h2>
                    <p><strong>状态:</strong> ✅ 连接成功</p>
                    <p><strong>Session:</strong> ${data.session ? '有活动会话' : '无会话'}</p>
                    ${error ? `<p><strong>错误:</strong> ${error.message}</p>` : ''}
                `;
            } catch (err) {
                document.getElementById('test-results').innerHTML = `
                    <h2>连接测试结果:</h2>
                    <p><strong>状态:</strong> ❌ 连接失败</p>
                    <p><strong>错误:</strong> ${err.message}</p>
                `;
            }
        }
        
        // 页面加载后测试
        window.addEventListener('load', testConnection);
    </script>
</body>
</html>
