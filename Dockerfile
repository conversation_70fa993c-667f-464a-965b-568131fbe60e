# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set the working directory
WORKDIR /app

# Install build dependencies for libraries with native extensions
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    python3-dev \
    libxml2-dev \
    libxslt-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements file and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt gunicorn

# Copy the application code
COPY . .

# Make sure the application port is exposed
EXPOSE 8080

# Set environment variable for port
ENV PORT=8080

# Command to run the application using Gun<PERSON> with increased timeout and worker settings
CMD ["gunicorn", "--bind", "0.0.0.0:8080", "--timeout", "300", "--workers", "2", "--threads", "4", "--worker-class", "gthread", "--keep-alive", "120", "--log-level", "info", "app:app"]
