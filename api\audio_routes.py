"""
音频相关路由处理器
将音频上传、分析、问答等路由逻辑从app.py中分离
"""

import os
import uuid
import json
import logging
import re
from pathlib import Path
from flask import Blueprint, request, jsonify, send_file, Response, stream_with_context
from pydub import AudioSegment
from api.rate_limit_config import create_rate_limiter
from api.audio_manager import AudioFileManager

logger = logging.getLogger(__name__)

# 创建蓝图
audio_bp = Blueprint('audio', __name__)

# 全局音频管理器实例
audio_manager = None

def init_audio_routes(app, manager):
    """初始化音频路由"""
    global audio_manager
    audio_manager = manager
    app.register_blueprint(audio_bp, url_prefix='/api')

@audio_bp.route('/upload-audio', methods=['POST'])
@create_rate_limiter("content", "upload_audio")
def upload_audio():
    """上传音频文件"""
    try:
        if 'file' not in request.files:
            return jsonify({"error": "没有文件"}), 400
            
        file = request.files['file']
        file_type = request.form.get('file_type', 'upload')
        
        if file.filename == '':
            return jsonify({"error": "没有选择文件"}), 400
        
        # 支持更多音频格式
        supported_formats = ('.mp3', '.wav', '.webm', '.mp4', '.m4a', '.ogg')
        if not file.filename.lower().endswith(supported_formats):
            return jsonify({"error": "支持的音频格式：MP3, WAV, WebM, MP4, M4A, OGG"}), 400
        
        # 生成唯一文件ID
        file_id = str(uuid.uuid4())
        file_extension = Path(file.filename).suffix
        original_name = file.filename
        
        # 确保目录存在
        temp_dir = "temp"
        os.makedirs(temp_dir, exist_ok=True)
        
        # 根据文件类型确定文件名
        if file_type == "recording":
            audio_filename = f"{file_id}{file_extension}"
        else:
            safe_name = re.sub(r'[<>:"/\\|?*]', '_', Path(original_name).stem)[:50]
            audio_filename = f"{safe_name}_{file_id}{file_extension}"
        
        audio_path = os.path.join(temp_dir, audio_filename)
        absolute_audio_path = os.path.abspath(audio_path)
        
        # 保存文件
        file.save(absolute_audio_path)
        
        # 获取音频文件信息
        try:
            audio_segment = AudioSegment.from_file(absolute_audio_path)
            file_size = os.path.getsize(absolute_audio_path)
            
            audio_info = {
                "duration": len(audio_segment) / 1000.0,
                "channels": audio_segment.channels,
                "sample_rate": audio_segment.frame_rate,
                "format": file_extension.upper().replace('.', ''),
                "file_size": file_size
            }
            
            # 使用音频管理器注册文件
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                if file_type == "recording":
                    loop.run_until_complete(audio_manager.register_recording_file(file_id, absolute_audio_path))
                else:
                    loop.run_until_complete(audio_manager.register_upload_file(file_id, absolute_audio_path, original_name))
            finally:
                loop.close()
            
            logger.info(f"音频文件上传成功: {file_id}, 路径: {absolute_audio_path}")
            
            return jsonify({
                "success": True,
                "file_id": file_id,
                "filename": file.filename,
                "audio_info": audio_info,
                "audio_url": f"/api/audio-file/{file_id}"
            })
            
        except Exception as audio_error:
            logger.error(f"获取音频信息失败: {audio_error}")
            return jsonify({"error": f"音频文件处理失败: {str(audio_error)}"}), 400
            
    except Exception as e:
        logger.error(f"音频文件上传失败: {e}")
        return jsonify({"error": f"音频文件上传失败: {str(e)}"}), 500

@audio_bp.route('/audio-file/<file_id>')
def serve_audio_file(file_id):
    """提供音频文件服务"""
    try:
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            audio_path = loop.run_until_complete(audio_manager.get_audio_path(file_id))
        finally:
            loop.close()
        
        if not os.path.exists(audio_path):
            return jsonify({"error": "音频文件不存在"}), 404
        
        return send_file(audio_path)
    except Exception as e:
        logger.error(f"获取音频文件失败: {e}")
        return jsonify({"error": f"获取音频文件失败: {str(e)}"}), 500

@audio_bp.route('/analyze-audio-stream', methods=['POST'])
@create_rate_limiter("content", "audio_analysis")
def analyze_audio_stream():
    """流式分析音频内容"""
    try:
        data = request.get_json()
        if not data:
            logger.error("录音分析请求: 没有JSON数据")
            return jsonify({"error": "没有请求数据"}), 400
            
        file_id = data.get('file_id')
        prompt = data.get('prompt', '请分析这段音频')
        skip_credits_check = data.get('skip_credits_check', False)  # 前端阈值跳过标志
        
        logger.info(f"录音分析请求: file_id={file_id}, prompt长度={len(prompt) if prompt else 0}, 跳过积分检查={skip_credits_check}")
        
        if not file_id:
            logger.error("录音分析请求: 缺少file_id参数")
            return jsonify({"error": "缺少file_id参数"}), 400
            
        # 移除预先积分检查，将其移到音频处理完成后
        from api.credits import check_user_credits_for_operation, deduct_user_credits
        auth_token = request.headers.get('Authorization', '').replace('Bearer ', '')

        def on_completion(user_id, usage_info):
            """AI操作完成后的回调，用于扣减积分"""
            try:
                result = deduct_user_credits(
                    user_id, 'audio_analysis', 'qwen_omni_turbo',
                    token_usage=usage_info
                )
                if result.get('success'):
                    logger.info(f"音频分析积分扣减成功: {result}")
                else:
                    logger.error(f"音频分析积分扣减失败: {result}")
            except Exception as e:
                logger.error(f"音频分析积分扣减异常: {e}")

        def generate():
            import asyncio
            async def async_generate():
                import re
                accumulated_content = ""
                try:
                    # 检查文件是否存在
                    try:
                        audio_path = await audio_manager.get_audio_path(file_id)
                    except FileNotFoundError:
                        yield f"data: {json.dumps({'type': 'error', 'error': '音频文件不存在'}, ensure_ascii=False)}\n\n"
                        return
                    
                    if not os.path.exists(audio_path):
                        yield f"data: {json.dumps({'type': 'error', 'error': '音频文件已被删除'}, ensure_ascii=False)}\n\n"
                        return
                    
                    # 检查音频文件大小
                    file_size = os.path.getsize(audio_path)
                    filename = os.path.basename(audio_path)
                    
                    # 识别录音文件：文件名包含recording或文件大小小于5MB
                    is_recording_file = "recording" in filename.lower() or file_size < 5 * 1024 * 1024
                    
                    # 只对非录音的大文件进行100MB限制
                    max_size = 100 * 1024 * 1024  # 100MB限制
                    if not is_recording_file and file_size > max_size:
                        yield f"data: {json.dumps({'type': 'error', 'error': f'音频文件太大 ({file_size/1024/1024:.1f}MB)，请使用小于100MB的文件'}, ensure_ascii=False)}\n\n"
                        return
                    
                    logger.info(f"开始处理音频文件，大小: {file_size/1024/1024:.2f}MB")
                    
                    # 获取音频时长信息用于积分估算 - 音频处理完成后进行积分检查
                    try:
                        audio_segment = AudioSegment.from_file(audio_path)
                        audio_duration = len(audio_segment) / 1000.0  # 转换为秒
                        logger.info(f"音频时长: {audio_duration:.1f}秒")
                        
                        # 后端三层积分检查：缓存 -> 数据库 -> 完整API
                        if skip_credits_check:
                            logger.info("🔒 [录音分析] 前端请求跳过检查，后端执行三层验证...")
                            
                            # 估算所需积分（录音分析固定估算5积分）
                            estimated_required = 5
                            threshold = estimated_required + 10  # 阈值：需要 + 10缓冲
                            
                            # 获取用户ID
                            from api.auth import get_user_from_token
                            user_data = get_user_from_token(auth_token)
                            user_id = user_data['id'] if user_data else None
                            
                            if not user_id:
                                yield f"data: {json.dumps({'type': 'error', 'error': '用户认证失败'}, ensure_ascii=False)}\n\n"
                                return
                            
                            # 第一层：快速缓存检查
                            from api.credits import get_user_credits_from_cache
                            cached_credits = get_user_credits_from_cache(user_id)
                            logger.info(f"🔒 [录音分析] 第一层检查-后端缓存: 积分={cached_credits}, 阈值={threshold}")
                            
                            if cached_credits is not None and cached_credits >= threshold:
                                logger.info(f"🚀 [录音分析] 第一层通过-后端缓存充足({cached_credits}>={threshold})，跳过详细检查")
                            else:
                                # 第二层：直接数据库查询
                                logger.info("⚡ [录音分析] 第一层失败，执行第二层检查-数据库查询...")
                                try:
                                    from api.credits import service_supabase_client
                                    if not service_supabase_client:
                                        raise Exception("服务端Supabase客户端未初始化")
                                    
                                    # 直接查询数据库获取最新积分
                                    response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
                                    if response.data and len(response.data) > 0:
                                        db_credits = response.data[0]['credits']
                                        logger.info(f"🔒 [录音分析] 第二层检查-数据库查询: 积分={db_credits}, 阈值={threshold}")
                                        
                                        if db_credits >= threshold:
                                            logger.info(f"🚀 [录音分析] 第二层通过-数据库积分充足({db_credits}>={threshold})，跳过详细检查")
                                            # 更新缓存以便下次快速检查
                                            from api.credits_cache import cache_user_credits
                                            cache_user_credits(user_id, db_credits)
                                        else:
                                            logger.warning(f"⚠️ [录音分析] 第二层失败-数据库积分不足({db_credits}<{threshold})，执行第三层完整检查")
                                            raise Exception(f"数据库积分不足: {db_credits} < {threshold}")
                                    else:
                                        logger.warning("⚠️ [录音分析] 第二层失败-数据库中无用户积分记录，执行第三层完整检查")
                                        raise Exception("数据库中无用户积分记录")
                                        
                                except Exception as db_error:
                                    # 第三层：完整API检查
                                    logger.warning(f"⚠️ [录音分析] 第二层失败({str(db_error)})，执行第三层检查-完整API验证")
                                    multimodal_info = {
                                        "audio_duration": audio_duration,
                                        "audio_file_size": file_size,
                                        "has_audio": True,
                                        "has_image": False
                                    }
                                    
                                    has_credits, user_id, credits_info = check_user_credits_for_operation(
                                        auth_token, 'audio_analysis', 'qwen_omni_turbo',
                                        content=prompt,
                                        multimodal_info=multimodal_info
                                    )
                                    
                                    if not has_credits:
                                        yield f"data: {json.dumps({'type': 'error', 'error': '积分不足', **credits_info}, ensure_ascii=False)}\n\n"
                                        return
                                    
                                    logger.info("🔒 [录音分析] 第三层通过-完整API验证成功")
                        else:
                            # 前端要求完整检查，执行标准流程
                            logger.info("📋 [录音分析] 执行完整积分检查")
                            multimodal_info = {
                                "audio_duration": audio_duration,
                                "audio_file_size": file_size,
                                "has_audio": True,
                                "has_image": False
                            }
                            
                            has_credits, user_id, credits_info = check_user_credits_for_operation(
                                auth_token, 'audio_analysis', 'qwen_omni_turbo',
                                content=prompt,
                                multimodal_info=multimodal_info
                            )
                            
                            if not has_credits:
                                yield f"data: {json.dumps({'type': 'error', 'error': '积分不足', **credits_info}, ensure_ascii=False)}\n\n"
                                return
                            
                    except Exception as duration_error:
                        logger.error(f"获取音频时长失败: {duration_error}")
                        # 执行完整积分检查（异常情况）
                        if not skip_credits_check:
                            has_credits, user_id, credits_info = check_user_credits_for_operation(
                                auth_token, 'audio_analysis', 'qwen_omni_turbo',
                                content=prompt
                            )
                            
                            if not has_credits:
                                yield f"data: {json.dumps({'type': 'error', 'error': '积分不足', **credits_info}, ensure_ascii=False)}\n\n"
                                return
                    
                    # 获取千问API配置
                    api_key = os.getenv('DASHSCOPE_API_KEY')
                    if not api_key:
                        yield f"data: {json.dumps({'type': 'error', 'error': '千问API配置缺失'}, ensure_ascii=False)}\n\n"
                        return
                    
                    # 编码音频文件
                    import base64
                    with open(audio_path, "rb") as audio_file:
                        base64_audio = base64.b64encode(audio_file.read()).decode("utf-8")
                    
                    logger.info(f"音频文件编码完成，base64长度: {len(base64_audio)}")
                    
                    # 确定音频格式
                    ext = os.path.splitext(audio_path)[1].lower()
                    audio_format = {
                        '.mp3': 'mp3', '.wav': 'wav', '.mp4': 'mp4', 
                        '.m4a': 'aac', '.ogg': 'ogg', '.webm': 'mp3'
                    }.get(ext, 'mp3')
                    
                    # 构建API请求
                    from openai import OpenAI
                    import time
                    
                    client = OpenAI(
                        api_key=api_key,
                        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                        timeout=120.0,
                        max_retries=2
                    )
                    
                    system_prompt = "你是一个专业的英汉口译专家，能够准确理解并翻译音频内容，请先给出音频的transcript，然后进行翻译和解释，并就其中涉及的语法结构、习语和习惯用法、文化背景提供解释和分析。"
                    
                    messages = [
                        {"role": "user", "content": system_prompt},
                        {"role": "assistant", "content": "好的，我记住了你的设定。"},
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "input_audio",
                                    "input_audio": {
                                        "data": f"data:;base64,{base64_audio}",
                                        "format": audio_format,
                                    },
                                },
                                {"type": "text", "text": prompt},
                            ],
                        }
                    ]
                    
                    # 流式请求 - 添加usage信息收集
                    stream = client.chat.completions.create(
                        model="qwen-omni-turbo",
                        messages=messages,
                        modalities=["text"],
                        stream=True,
                        stream_options={"include_usage": True},
                        timeout=60.0
                    )
                    
                    # 发送开始事件
                    yield f"data: {json.dumps({'type': 'start', 'message': '开始分析音频...'}, ensure_ascii=False)}\n\n"
                    
                    accumulated_content = ""
                    actual_usage = None  # 存储实际的usage信息
                    for chunk in stream:
                        # 检查并打印usage信息
                        if hasattr(chunk, 'usage') and chunk.usage:
                            logger.info(f"API Usage信息 (音频分析): {chunk.usage}")
                            actual_usage = chunk.usage  # 保存实际usage信息
                        
                        if chunk.choices and chunk.choices[0].delta.content:
                            content = chunk.choices[0].delta.content
                            accumulated_content += content
                            yield f"data: {json.dumps({'type': 'chunk', 'accumulated': accumulated_content}, ensure_ascii=False)}\n\n"
                    
                    # 发送完成事件
                    yield f"data: {json.dumps({'type': 'complete', 'final_content': accumulated_content}, ensure_ascii=False)}\n\n"
                    
                    # 扣减积分 - 使用实际usage信息
                    try:
                        if user_id and actual_usage:
                            # 转换实际usage信息为我们需要的格式，包括详细的token分解
                            usage_info = {
                                'input_tokens': actual_usage.prompt_tokens,
                                'output_tokens': actual_usage.completion_tokens,
                                'total_tokens': actual_usage.total_tokens
                            }
                            
                            # 如果有详细的token分解信息，也包括进来
                            if hasattr(actual_usage, 'prompt_tokens_details') and actual_usage.prompt_tokens_details:
                                usage_info['prompt_tokens_details'] = {
                                    'audio_tokens': actual_usage.prompt_tokens_details.audio_tokens or 0,
                                    'text_tokens': actual_usage.prompt_tokens_details.text_tokens or 0,
                                    'cached_tokens': actual_usage.prompt_tokens_details.cached_tokens or 0
                                }
                            
                            if hasattr(actual_usage, 'completion_tokens_details') and actual_usage.completion_tokens_details:
                                usage_info['completion_tokens_details'] = {
                                    'text_tokens': actual_usage.completion_tokens_details.text_tokens or 0,
                                    'audio_tokens': actual_usage.completion_tokens_details.audio_tokens or 0
                                }
                            
                            logger.info(f"音频分析使用实际usage信息扣减积分: {usage_info}")
                            on_completion(user_id, usage_info)
                        elif user_id:
                            # 如果没有actual_usage，使用估算（不应该发生）
                            token_count = len(accumulated_content) // 2
                            logger.warning(f"音频分析未获取到实际usage信息，使用估算: input/output={token_count}")
                            on_completion(user_id, {'input_tokens': token_count, 'output_tokens': token_count})
                        else:
                            logger.error("无法扣减积分：未获取到user_id")
                    except Exception as credit_error:
                        logger.error(f"音频分析积分扣减失败: {credit_error}")
                        
                except Exception as e:
                    logger.error(f"音频分析失败: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"
            
            # 异步循环处理
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                async_gen = async_generate()
                while True:
                    try:
                        chunk = loop.run_until_complete(async_gen.__anext__())
                        yield chunk
                    except StopAsyncIteration:
                        break
            finally:
                loop.close()
        
        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
        })
        
    except Exception as e:
        logger.error(f"流式音频分析失败: {e}")
        return jsonify({"error": f"流式音频分析失败: {str(e)}"}), 500

# NOTE: 禁用重复的 /analyze-local-audio-stream 路由，避免覆盖 app.py 中的完整实现
# def analyze_local_audio_stream_disabled():
#     """已禁用。请使用 app.py 中的 analyze_local_audio_stream"""
#     return jsonify({"error": "此端点已禁用，请使用 /api/analyze-local-audio-stream"}), 410

@audio_bp.route('/audio-stats', methods=['GET'])
def get_audio_stats():
    """获取音频文件统计信息"""
    try:
        stats = audio_manager.get_file_stats()
        return jsonify({
            "success": True,
            "stats": stats,
            "management_rules": {
                "uploaded_files": "运行期间永不清理，用户可以持续使用",
                "recording_files": "智能管理，新录音时自动清理旧录音",
                "snippet_files": "智能管理，新片段时自动清理旧片段",
                "resource_control": f"最多{audio_manager.max_total_files}个文件并存，防止存储滥用",
                "startup_cleanup": "确保每次启动都是干净状态，自动清理temp文件夹所有音频"
            }
        })
    except Exception as e:
        logger.error(f"获取音频统计失败: {e}")
        return jsonify({"error": f"获取音频统计失败: {str(e)}"}), 500

@audio_bp.route('/cleanup-orphaned', methods=['POST'])
def cleanup_orphaned():
    """手动清理孤立文件"""
    try:
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(audio_manager.cleanup_orphaned_files())
        finally:
            loop.close()
            
        return jsonify({
            "success": True,
            "message": "孤立文件清理完成",
            "cleaned_files": result.get("cleaned_files", 0)
        })
    except Exception as e:
        logger.error(f"清理孤立文件失败: {e}")
        return jsonify({"error": f"清理孤立文件失败: {str(e)}"}), 500

@audio_bp.route('/check-resource-limits', methods=['POST'])
def check_resource_limits():
    """手动检查并执行资源限制"""
    try:
        # 获取检查前的状态
        before_stats = audio_manager.get_file_stats()
        
        # 执行资源限制检查
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(audio_manager.check_resource_limits())
        finally:
            loop.close()
        
        # 获取检查后的状态
        after_stats = audio_manager.get_file_stats()
        
        return jsonify({
            "success": True,
            "before": before_stats,
            "after": after_stats,
            "cleaned": before_stats["total_active_files"] - after_stats["total_active_files"],
            "message": "资源限制检查完成"
        })
    except Exception as e:
        logger.error(f"资源限制检查失败: {e}")
        return jsonify({"error": f"检查失败: {str(e)}"}), 500 