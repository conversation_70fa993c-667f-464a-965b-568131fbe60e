-- ========================================
-- 缺失表的创建脚本
-- 解决支付流程中的表缺失问题
-- ========================================

-- ========================================
-- 1. 创建支付订单表 (payment_orders)
-- ========================================
CREATE TABLE IF NOT EXISTS public.payment_orders (
    id BIGSERIAL PRIMARY KEY,
    order_id VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    user_email VARCHAR(255) NOT NULL,
    package_id UUID,  -- 可选，用于关联套餐
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(10) NOT NULL DEFAULT 'USD',
    credits INTEGER NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',  -- pending, completed, failed, cancelled
    creem_checkout_id VARCHAR(255),  -- Creem 的 checkout ID
    creem_order_id VARCHAR(255),     -- Creem 的 order ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB  -- 存储额外的订单信息
);

-- 启用行级安全
ALTER TABLE public.payment_orders ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略 - 用户只能访问自己的订单
CREATE POLICY "Users can view own orders" ON public.payment_orders
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own orders" ON public.payment_orders
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_payment_orders_user_id ON public.payment_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_orders_order_id ON public.payment_orders(order_id);
CREATE INDEX IF NOT EXISTS idx_payment_orders_status ON public.payment_orders(status);
CREATE INDEX IF NOT EXISTS idx_payment_orders_created_at ON public.payment_orders(created_at DESC);

-- ========================================
-- 2. 创建安全日志表 (security_logs)
-- ========================================
CREATE TABLE IF NOT EXISTS public.security_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    event_type VARCHAR(100) NOT NULL,  -- credits_tampering, product_id_tampering, etc.
    details TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    severity VARCHAR(20) DEFAULT 'medium'  -- low, medium, high, critical
);

-- 启用行级安全 (仅管理员可访问)
ALTER TABLE public.security_logs ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略 - 只有 service_role 可以访问
CREATE POLICY "Only service role can access security logs" ON public.security_logs
    FOR ALL USING (false);  -- 普通用户无权访问

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON public.security_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON public.security_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON public.security_logs(created_at DESC);

-- ========================================
-- 3. 创建用户配置表 (user_profiles)
-- ========================================
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    display_name VARCHAR(255),
    avatar_url TEXT,
    bio TEXT,
    language_preference VARCHAR(10) DEFAULT 'en',
    timezone VARCHAR(50),
    notification_settings JSONB DEFAULT '{"email": true, "push": true}',
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 启用行级安全
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略 - 用户只能访问自己的配置
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_profiles_created_at ON public.user_profiles(created_at DESC);

-- ========================================
-- 4. 创建更新时间触发器函数
-- ========================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为 payment_orders 表添加更新时间触发器
DROP TRIGGER IF EXISTS update_payment_orders_updated_at ON public.payment_orders;
CREATE TRIGGER update_payment_orders_updated_at
    BEFORE UPDATE ON public.payment_orders
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 为 user_profiles 表添加更新时间触发器
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 5. 验证表创建
-- ========================================
SELECT 
    'payment_orders' as table_name,
    COUNT(*) as record_count
FROM public.payment_orders
UNION ALL
SELECT 
    'security_logs' as table_name,
    COUNT(*) as record_count
FROM public.security_logs
UNION ALL
SELECT 
    'user_profiles' as table_name,
    COUNT(*) as record_count
FROM public.user_profiles;

-- 显示成功消息
SELECT 'Missing tables created successfully!' as status; 