@c:\Users\<USER>\tool\web_音频理解\音频理解助手文档\0704 当前句子语法可视化方案.txt 我这个项目实施了对字幕行句子进行语法分析可视化，但是加载字幕后，有的字幕行句子，右上角的语法可视化按钮可以点击，有的不可以，如图。请参考我提供的文档，分析确定问题本质。给出最小化修改方案。

我需要分析您的语法可视化功能中为什么有些字幕行的语法可视化按钮可以点击，而有些不可以。让我先查看相关的代码实现。
Search files...
Ran tool
Read file: public/js/subtitle-manager.js
现在我找到了问题的本质原因。让我再查看一下语法可视化按钮状态更新的逻辑：
Ran tool
Read file: public/js/subtitle-manager.js
现在我已经找到了问题的本质原因。让我分析一下：

## 🎯 问题本质分析

根据代码分析，语法可视化按钮能否点击取决于以下条件：

1. **当前字幕高亮状态**：必须有音频正在播放并且有字幕高亮
2. **英文句子检测**：`hasEnglishSentence(text)` 方法检测字幕是否包含英文

### 关键问题在于 `hasEnglishSentence` 方法的检测逻辑：

```javascript
hasEnglishSentence(text) {
    // 简单的英文句子检测：包含英文字母和基本标点符号
    const englishPattern = /[a-zA-Z]+.*[.!?]/;
    return englishPattern.test(text);
}
```

这个正则表达式要求：
1. 至少包含一个英文字母
2. **必须以句号、感叹号或问号结尾**

**问题分析**：
- 很多字幕行可能包含英文，但不以 `.!?` 结尾
- 比如对话中的片段、不完整的句子、以逗号结尾的句子等
- 这导致有些明明包含英文的字幕行按钮无法点击

## 🔧 最小化修改方案

修改 `hasEnglishSentence` 方法的检测逻辑，让它更宽松地识别包含英文的内容：
## 📝 修改总结

**修改的文件**: `public/js/subtitle-manager.js`

**修改的内容**: 
- 将 `hasEnglishSentence` 方法的英文检测正则表达式从严格的句子检测改为宽松的英文内容检测
- **原来**: `/[a-zA-Z]+.*[.!?]/` - 必须以句号、感叹号或问号结尾
- **现在**: `/[a-zA-Z]+(?:\s+[a-zA-Z]+)+/` - 只要包含至少两个英文单词（用空格分隔）

**修改逻辑**:
1. **问题根源**: 原来的正则表达式过于严格，要求字幕必须以特定标点符号结尾才能被识别为英文句子
2. **解决方案**: 改为检测是否包含连续的英文单词，这样可以覆盖更多场景：
   - 完整的句子（如："This is a sentence."）
   - 对话片段（如："you know what I mean"）
   - 短语或从句（如："in the morning, when we"）
   - 没有结尾标点的内容

**效果**: 现在只要字幕行包含至少两个英文单词，语法可视化按钮就可以点击，大大提高了功能的可用性。

这是一个最小化的修改，只改动了一个方法中的一行代码，但能有效解决您遇到的问题。