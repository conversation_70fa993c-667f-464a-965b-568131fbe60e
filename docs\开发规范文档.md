# AudioPilot - 项目开发规范文档

## 1. 代码规范

### 1.1 Python后端规范

#### 1.1.1 代码风格
```python
# 遵循PEP 8规范
# 导入顺序：标准库 -> 第三方库 -> 本地模块
import os
import logging
import time

from flask import Flask, request, jsonify
from flask_cors import CORS

from api.config import get_supabase_config
from api.model_facade import call_model_api

# 类命名：驼峰命名法
class AudioFileManager:
    """音频文件管理器
    
    负责管理上传、录制和片段音频文件的生命周期
    实现智能清理和资源控制机制
    """
    
    def __init__(self):
        self.audio_files = {}  # 明确的类型注释
        self.max_total_files = 3
        
    def cleanup_old_files(self) -> int:
        """清理过期文件
        
        Returns:
            int: 清理的文件数量
        """
        cleaned_count = 0
        # 实现逻辑...
        return cleaned_count
```

#### 1.1.2 错误处理
```python
# 统一的错误处理模式
import logging

logger = logging.getLogger(__name__)

def safe_api_call(func):
    """API调用装饰器 - 统一错误处理"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValueError as e:
            logger.error(f"参数错误: {str(e)}")
            return {"error": "invalid_parameter", "message": str(e)}
        except ConnectionError as e:
            logger.error(f"连接错误: {str(e)}")
            return {"error": "connection_failed", "message": "服务暂时不可用"}
        except Exception as e:
            logger.error(f"未知错误: {str(e)}")
            return {"error": "internal_error", "message": "系统内部错误"}
    return wrapper
```

#### 1.1.3 日志规范
```python
# 日志级别使用规范
logger.debug("详细调试信息 - 仅开发环境")
logger.info("正常业务流程信息")
logger.warning("警告信息 - 需要关注但不影响功能")
logger.error("错误信息 - 影响功能但不崩溃")
logger.critical("严重错误 - 系统可能崩溃")

# 日志格式规范
logger.info(f"用户 {user_id} 上传音频文件: {filename}, 大小: {file_size}MB")
logger.error(f"AI模型调用失败: model={model_id}, error={str(e)}")
```

### 1.2 JavaScript前端规范

#### 1.2.1 ES6+现代语法
```javascript
// 使用const/let，避免var
const API_BASE_URL = '/api';
let currentUser = null;

// 箭头函数优先
const processAudioFile = async (file) => {
    try {
        const result = await uploadAudio(file);
        return result;
    } catch (error) {
        console.error('音频处理失败:', error);
        throw error;
    }
};

// 解构赋值
const { userId, credits, email } = userData;
const [isRecording, setRecording] = useState(false);

// 模板字符串
const message = `用户 ${userName} 的积分余额为 ${credits}`;
```

#### 1.2.2 类和模块设计
```javascript
// AudioApp主类 - 单一职责原则
class AudioApp {
    constructor() {
        this.state = new Map();
        this.initializeComponents();
    }
    
    // 公共方法 - 驼峰命名
    async uploadAudio(file) {
        return this._handleFileUpload(file);
    }
    
    // 私有方法 - 下划线前缀
    _handleFileUpload(file) {
        // 实现逻辑
    }
    
    // 事件处理 - on前缀
    onRecordingStart() {
        this.state.set('isRecording', true);
        this.updateUI();
    }
}

// 模块导出
export { AudioApp };
export default AudioApp;
```

#### 1.2.3 异步处理规范
```javascript
// 优先使用async/await
async function analyzeAudio(fileId, modelId) {
    try {
        showLoading(true);
        
        const response = await fetch(`/api/analyze`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getToken()}`
            },
            body: JSON.stringify({ fileId, modelId })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        return result;
        
    } catch (error) {
        handleError('音频分析失败', error);
        throw error;
    } finally {
        showLoading(false);
    }
}

// Promise链式调用（特定场景）
fetchUserData()
    .then(user => updateUserUI(user))
    .then(() => loadUserPreferences())
    .catch(error => showErrorMessage(error))
    .finally(() => hideLoadingSpinner());
```

## 2. API设计规范

### 2.1 RESTful API设计
```yaml
# 资源命名规范
GET /api/users/{id}           # 获取用户信息
PUT /api/users/{id}           # 更新用户信息
DELETE /api/users/{id}        # 删除用户

GET /api/audio/files          # 获取音频文件列表
POST /api/audio/upload        # 上传音频文件
GET /api/audio/files/{id}     # 获取特定音频文件
DELETE /api/audio/files/{id}  # 删除音频文件

POST /api/ai/analyze          # AI分析请求
POST /api/ai/chat             # AI对话请求
GET /api/ai/models            # 获取可用模型列表
```

### 2.2 请求响应格式
```json
// 成功响应格式
{
    "success": true,
    "data": {
        "fileId": "audio_123456",
        "duration": 180,
        "analysis": {
            "summary": "音频内容摘要...",
            "keywords": ["关键词1", "关键词2"]
        }
    },
    "message": "分析完成",
    "timestamp": "2025-06-14T10:30:00Z"
}

// 错误响应格式
{
    "success": false,
    "error": {
        "code": "INSUFFICIENT_CREDITS",
        "message": "积分不足，请充值后重试",
        "details": {
            "required": 10,
            "available": 5
        }
    },
    "timestamp": "2025-06-14T10:30:00Z"
}
```

### 2.3 流式响应规范
```python
# 流式响应格式
def create_streaming_response(content_generator):
    """创建标准流式响应"""
    
    def generate():
        try:
            for chunk in content_generator:
                # 内容块
                if chunk.get('type') == 'content':
                    yield f"data: {json.dumps(chunk)}\n\n"
                
                # Token使用信息
                elif chunk.get('type') == 'usage':
                    yield f"data: {json.dumps(chunk)}\n\n"
                
                # 错误信息
                elif chunk.get('type') == 'error':
                    yield f"data: {json.dumps(chunk)}\n\n"
                    break
            
            # 结束标记
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            error_chunk = {
                "type": "error",
                "message": str(e)
            }
            yield f"data: {json.dumps(error_chunk)}\n\n"
    
    return Response(
        stream_with_context(generate()),
        content_type='text/plain; charset=utf-8'
    )
```

## 3. 数据库设计规范

### 3.1 表设计原则
```sql
-- 表命名：复数形式，下划线分隔
CREATE TABLE user_credits (
    -- 主键：UUID类型，自动生成
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 外键：明确引用关系
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- 字段命名：下划线分隔，语义明确
    current_balance INTEGER NOT NULL DEFAULT 0,
    total_earned INTEGER NOT NULL DEFAULT 0,
    total_spent INTEGER NOT NULL DEFAULT 0,
    
    -- 时间戳：统一使用TIMESTAMP WITH TIME ZONE
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 约束：业务规则保证数据完整性
    CONSTRAINT positive_balance CHECK (current_balance >= 0),
    CONSTRAINT balance_consistency CHECK (current_balance = total_earned - total_spent)
);

-- 索引：性能优化
CREATE INDEX CONCURRENTLY idx_user_credits_user_id ON user_credits(user_id);
CREATE INDEX CONCURRENTLY idx_user_credits_updated_at ON user_credits(updated_at);
```

### 3.2 数据迁移规范
```sql
-- 迁移脚本命名：YYYYMMDD_HHMMSS_description.sql
-- 2025061401_create_audio_analysis_table.sql

BEGIN;

-- 创建新表
CREATE TABLE audio_analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    audio_file_id UUID NOT NULL REFERENCES audio_files(id),
    model_id VARCHAR(100) NOT NULL,
    analysis_type VARCHAR(50) NOT NULL,
    result_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX CONCURRENTLY idx_audio_analysis_file_id 
    ON audio_analysis_results(audio_file_id);
CREATE INDEX CONCURRENTLY idx_audio_analysis_model_type 
    ON audio_analysis_results(model_id, analysis_type);

-- 更新版本号
INSERT INTO schema_migrations (version, applied_at) 
VALUES ('2025061401', NOW());

COMMIT;
```

## 4. 测试规范

### 4.1 单元测试
```python
# test_audio_manager.py
import unittest
import tempfile
import os
from unittest.mock import patch, MagicMock

from app import AudioFileManager

class TestAudioFileManager(unittest.TestCase):
    """音频文件管理器测试"""
    
    def setUp(self):
        """测试前准备"""
        self.manager = AudioFileManager()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_file_upload_success(self):
        """测试文件上传成功"""
        # 准备测试数据
        test_file = self._create_test_audio_file()
        
        # 执行测试
        result = self.manager.upload_audio(test_file)
        
        # 验证结果
        self.assertTrue(result['success'])
        self.assertIsNotNone(result['file_id'])
        self.assertIn(result['file_id'], self.manager.audio_files)
    
    def test_resource_limit_enforcement(self):
        """测试资源限制执行"""
        # 模拟上传多个文件
        for i in range(5):
            self.manager.upload_audio(self._create_test_audio_file())
        
        # 验证文件数量限制
        self.assertLessEqual(len(self.manager.audio_files), 3)
    
    @patch('app.logger')
    def test_cleanup_error_handling(self, mock_logger):
        """测试清理过程错误处理"""
        # 模拟文件删除失败
        with patch('os.remove', side_effect=OSError("Permission denied")):
            self.manager.cleanup_old_files()
        
        # 验证错误被正确记录
        mock_logger.warning.assert_called()
```

### 4.2 集成测试
```python
# test_api_integration.py
import pytest
import json
from app import app

@pytest.fixture
def client():
    """测试客户端"""
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

@pytest.fixture
def auth_headers():
    """认证头"""
    token = "test_jwt_token"
    return {'Authorization': f'Bearer {token}'}

class TestAudioAPI:
    """音频API集成测试"""
    
    def test_upload_audio_success(self, client, auth_headers):
        """测试音频上传成功流程"""
        # 准备测试文件
        data = {
            'audio': (BytesIO(b'fake audio data'), 'test.mp3')
        }
        
        # 发送请求
        response = client.post('/api/audio/upload', 
                             data=data, 
                             headers=auth_headers)
        
        # 验证响应
        assert response.status_code == 200
        result = json.loads(response.data)
        assert result['success'] is True
        assert 'file_id' in result['data']
    
    def test_analyze_audio_insufficient_credits(self, client, auth_headers):
        """测试积分不足场景"""
        # 模拟积分不足的用户
        with patch('api.credits.get_user_credits', return_value=0):
            response = client.post('/api/ai/analyze', 
                                 json={'fileId': 'test_file', 'model': 'test_model'},
                                 headers=auth_headers)
        
        # 验证响应
        assert response.status_code == 402
        result = json.loads(response.data)
        assert result['error']['code'] == 'INSUFFICIENT_CREDITS'
```

### 4.3 前端测试
```javascript
// test/audio-app.test.js
describe('AudioApp', () => {
    let audioApp;
    
    beforeEach(() => {
        // 模拟DOM元素
        document.body.innerHTML = `
            <div id="uploadBtn"></div>
            <div id="recordBtn"></div>
            <div id="chatMessages"></div>
        `;
        
        audioApp = new AudioApp();
    });
    
    afterEach(() => {
        document.body.innerHTML = '';
    });
    
    describe('文件上传', () => {
        test('应该正确处理音频文件上传', async () => {
            // 模拟文件
            const mockFile = new File(['audio data'], 'test.mp3', {
                type: 'audio/mpeg'
            });
            
            // 模拟API响应
            global.fetch = jest.fn(() =>
                Promise.resolve({
                    ok: true,
                    json: () => Promise.resolve({
                        success: true,
                        data: { fileId: 'test_123' }
                    })
                })
            );
            
            // 执行上传
            const result = await audioApp.uploadAudio(mockFile);
            
            // 验证结果
            expect(result.success).toBe(true);
            expect(audioApp.uploadedFileId).toBe('test_123');
        });
        
        test('应该正确处理上传错误', async () => {
            const mockFile = new File(['audio data'], 'test.mp3');
            
            // 模拟API错误
            global.fetch = jest.fn(() =>
                Promise.resolve({
                    ok: false,
                    status: 400,
                    json: () => Promise.resolve({
                        success: false,
                        error: { message: '文件格式不支持' }
                    })
                })
            );
            
            // 执行上传并验证错误
            await expect(audioApp.uploadAudio(mockFile))
                .rejects.toThrow('文件格式不支持');
        });
    });
});
```

## 5. 版本控制规范

### 5.1 Git分支策略
```bash
# 主分支
main          # 生产环境代码
develop       # 开发环境代码

# 功能分支
feature/audio-upload-optimization    # 功能开发
feature/multi-language-support      # 新功能

# 修复分支
hotfix/critical-security-fix        # 紧急修复
bugfix/audio-playback-issue         # Bug修复

# 发布分支
release/v1.2.0                      # 版本发布
```

### 5.2 提交规范
```bash
# 提交消息格式：<type>(<scope>): <description>

# 功能提交
feat(audio): 添加音频格式自动检测功能
feat(ai): 集成Claude 3.5 Sonnet模型

# 修复提交
fix(credits): 修复积分扣减重复问题
fix(ui): 修复移动端播放器样式问题

# 文档提交
docs(api): 更新API文档和示例代码
docs(readme): 添加部署指南

# 样式提交
style(ui): 统一按钮样式和间距
style(code): 修复代码格式问题

# 重构提交
refactor(auth): 重构用户认证模块
refactor(audio): 优化音频文件管理逻辑

# 测试提交
test(api): 添加音频上传接口测试
test(units): 完善积分系统单元测试
```

### 5.3 代码审查清单
```markdown
## 代码审查清单

### 功能性
- [ ] 功能是否按需求正确实现
- [ ] 边界条件和异常情况是否处理
- [ ] 用户体验是否友好

### 代码质量
- [ ] 代码风格是否符合规范
- [ ] 命名是否清晰易懂
- [ ] 函数是否遵循单一职责原则
- [ ] 是否有适当的注释和文档

### 性能
- [ ] 是否存在性能问题
- [ ] 数据库查询是否优化
- [ ] 缓存策略是否合理

### 安全性
- [ ] 输入验证是否完善
- [ ] 权限控制是否正确
- [ ] 敏感信息是否安全处理

### 测试
- [ ] 是否有对应的测试用例
- [ ] 测试覆盖率是否足够
- [ ] 测试用例是否有效
```

## 6. 部署规范

### 6.1 环境配置
```bash
# .env.example - 环境变量模板
# 数据库配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key

# AI模型API密钥
ZHIPU_API_KEY=your-zhipu-key
DASHSCOPE_API_KEY=your-qwen-key
ANTHROPIC_API_KEY=your-claude-key
DEEPSEEK_API_KEY=your-deepseek-key
XAI_API_KEY=your-grok-key

# 应用配置
FLASK_SECRET_KEY=your-secret-key
FLASK_ENV=production
DEBUG=false

# 限流配置
RATE_LIMIT_STORAGE_URL=redis://localhost:6379
```

### 6.2 Docker部署
```dockerfile
# 多阶段构建
FROM node:18-alpine AS frontend-build
WORKDIR /app/frontend
COPY public/ .
RUN npm run build

FROM python:3.10-slim AS backend
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .
COPY --from=frontend-build /app/frontend/dist ./public

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 非root用户运行
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8080
CMD ["gunicorn", "--bind", "0.0.0.0:8080", "--workers", "4", "app:app"]
```

### 6.3 监控配置
```python
# 健康检查端点
@app.route('/health')
def health_check():
    """系统健康检查"""
    try:
        # 检查数据库连接
        db_status = check_database_connection()
        
        # 检查AI模型可用性
        models_status = check_ai_models_status()
        
        # 检查磁盘空间
        disk_status = check_disk_space()
        
        status = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'checks': {
                'database': db_status,
                'ai_models': models_status,
                'disk_space': disk_status
            }
        }
        
        return jsonify(status), 200
        
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 503
```

---

这份开发规范文档涵盖了AudioPilot项目的各个开发环节，请团队成员严格遵循以确保代码质量和项目的可维护性。

**文档版本**: v1.0  
**生效日期**: 2025年6月14日  
**维护责任**: 技术负责人
