<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证状态一致性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .success { border-color: #28a745; background-color: #d4edda; }
        .warning { border-color: #ffc107; background-color: #fff3cd; }
        .error { border-color: #dc3545; background-color: #f8d7da; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log { 
            background: #f8f9fa; 
            border: 1px solid #dee2e6; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>认证状态一致性测试</h1>
        <p>这个页面用于测试不同模块之间localStorage认证数据的一致性</p>

        <div class="status-box" id="authStatus">
            <h3>当前认证状态</h3>
            <div id="statusContent">检查中...</div>
        </div>

        <div class="status-box">
            <h3>控制按钮</h3>
            <button onclick="checkConsistency()">检查一致性</button>
            <button onclick="simulateLanguageSwitch()">模拟语言切换</button>
            <button onclick="cleanupInconsistentData()">清理不一致数据</button>
            <button onclick="testCreditsHistoryCheck()">测试积分历史检查</button>
            <button onclick="testUnifiedAuthCheck()">测试统一认证检查</button>
            <button onclick="clearAllAuthData()">清除所有认证数据</button>
        </div>

        <div class="log" id="logOutput"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/lib/supabase.min.js"></script>
    <script src="js/global-utils.js"></script>
    
    <script>
        let logContainer = document.getElementById('logOutput');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'warning' ? 'orange' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        function getLocalStorageAuthData() {
            return {
                authToken: localStorage.getItem('authToken'),
                refreshToken: localStorage.getItem('refreshToken'),
                userData: localStorage.getItem('userData'),
                isAuthenticated: localStorage.getItem('isAuthenticated'),
                userId: localStorage.getItem('userId')
            };
        }

        function displayAuthStatus() {
            const data = getLocalStorageAuthData();
            const statusDiv = document.getElementById('statusContent');
            
            let html = '<table style="width:100%; border-collapse: collapse;">';
            html += '<tr><th style="border:1px solid #ccc; padding:5px;">字段</th><th style="border:1px solid #ccc; padding:5px;">值</th><th style="border:1px solid #ccc; padding:5px;">状态</th></tr>';
            
            for (const [key, value] of Object.entries(data)) {
                const status = value && value !== 'null' ? '✅' : '❌';
                const displayValue = value ? (value.length > 50 ? value.substring(0, 50) + '...' : value) : 'null';
                html += `<tr><td style="border:1px solid #ccc; padding:5px;">${key}</td><td style="border:1px solid #ccc; padding:5px;">${displayValue}</td><td style="border:1px solid #ccc; padding:5px;">${status}</td></tr>`;
            }
            html += '</table>';
            
            statusDiv.innerHTML = html;
        }

        function checkConsistency() {
            log('=== 开始检查localStorage认证数据一致性 ===');
            const data = getLocalStorageAuthData();
            
            // 检查统一认证管理器的逻辑
            const hasAuthToken = !!(data.authToken && data.authToken !== 'null');
            const hasUserData = !!(data.userData && data.userData !== 'null');
            const unifiedAuthValid = hasAuthToken && hasUserData;
            
            // 检查积分历史管理器的逻辑
            const hasIsAuthenticated = data.isAuthenticated === 'true';
            const hasUserId = !!(data.userId && data.userId !== 'null');
            const creditsHistoryValid = hasIsAuthenticated && hasUserId;
            
            log(`统一认证管理器检查: ${unifiedAuthValid ? '✅通过' : '❌失败'} (authToken: ${hasAuthToken}, userData: ${hasUserData})`);
            log(`积分历史管理器检查: ${creditsHistoryValid ? '✅通过' : '❌失败'} (isAuthenticated: ${hasIsAuthenticated}, userId: ${hasUserId})`);
            
            if (unifiedAuthValid && creditsHistoryValid) {
                log('✅ 所有检查都通过，数据一致', 'success');
                document.getElementById('authStatus').className = 'status-box success';
            } else if (unifiedAuthValid || creditsHistoryValid) {
                log('⚠️ 部分检查通过，存在数据不一致', 'warning');
                document.getElementById('authStatus').className = 'status-box warning';
            } else {
                log('❌ 所有检查都失败，无认证数据', 'error');
                document.getElementById('authStatus').className = 'status-box error';
            }
            
            displayAuthStatus();
        }

        function simulateLanguageSwitch() {
            log('=== 模拟UI语言切换 ===');
            // 模拟某些场景下可能发生的localStorage部分清理
            if (Math.random() > 0.5) {
                localStorage.removeItem('isAuthenticated');
                log('模拟: isAuthenticated字段被清理', 'warning');
            }
            if (Math.random() > 0.5) {
                localStorage.removeItem('userId');
                log('模拟: userId字段被清理', 'warning');
            }
            
            setTimeout(() => {
                checkConsistency();
            }, 100);
        }

        function cleanupInconsistentData() {
            log('=== 清理不一致的认证数据 ===');
            const data = getLocalStorageAuthData();
            
            const hasMainAuth = !!(data.authToken && data.userData && 
                                   data.authToken !== 'null' && data.userData !== 'null');
                                   
            if (hasMainAuth) {
                // 如果主要认证数据存在，补全缺失的字段
                if (data.isAuthenticated !== 'true') {
                    localStorage.setItem('isAuthenticated', 'true');
                    log('补全: isAuthenticated = true');
                }
                
                if (!data.userId || data.userId === 'null') {
                    try {
                        const userObj = JSON.parse(data.userData);
                        if (userObj && userObj.id) {
                            localStorage.setItem('userId', userObj.id);
                            log(`补全: userId = ${userObj.id}`);
                        }
                    } catch (e) {
                        log('解析userData失败', 'error');
                    }
                }
            } else {
                // 如果主要认证数据不存在，清理所有相关数据
                const authKeys = ['authToken', 'refreshToken', 'userData', 'isAuthenticated', 'userId'];
                authKeys.forEach(key => {
                    if (localStorage.getItem(key)) {
                        localStorage.removeItem(key);
                        log(`清理: ${key}`);
                    }
                });
            }
            
            setTimeout(() => {
                checkConsistency();
            }, 100);
        }

        function testCreditsHistoryCheck() {
            log('=== 测试积分历史管理器的检查逻辑 ===');
            const isAuthenticated = localStorage.getItem('isAuthenticated');
            const userId = localStorage.getItem('userId');
            const shouldInit = (isAuthenticated === 'true' && userId);
            
            log(`积分历史管理器判断: shouldInit = ${shouldInit}`);
            log(`- isAuthenticated: "${isAuthenticated}" (${isAuthenticated === 'true' ? '✅' : '❌'})`);
            log(`- userId: "${userId}" (${!!userId ? '✅' : '❌'})`);
        }

        function testUnifiedAuthCheck() {
            log('=== 测试统一认证管理器的检查逻辑 ===');
            const authToken = localStorage.getItem('authToken');
            const userData = localStorage.getItem('userData');
            const isAuthenticated = localStorage.getItem('isAuthenticated');
            const userId = localStorage.getItem('userId');
            
            const hasAuthToken = !!(authToken && authToken !== 'null');
            const hasUserData = !!(userData && userData !== 'null');
            const hasIsAuthenticated = isAuthenticated === 'true';
            const hasUserId = !!(userId && userId !== 'null');
            
            const hasLocalAuth = (hasAuthToken && hasUserData) || (hasIsAuthenticated && hasUserId);
            
            log(`统一认证管理器判断: hasLocalAuth = ${hasLocalAuth}`);
            log(`- hasAuthToken: ${hasAuthToken} (${authToken ? '存在' : '不存在'})`);
            log(`- hasUserData: ${hasUserData} (${userData ? '存在' : '不存在'})`);
            log(`- hasIsAuthenticated: ${hasIsAuthenticated} (${isAuthenticated})`);
            log(`- hasUserId: ${hasUserId} (${userId ? '存在' : '不存在'})`);
        }

        function clearAllAuthData() {
            log('=== 清除所有认证数据 ===');
            const authKeys = ['authToken', 'refreshToken', 'userData', 'isAuthenticated', 'userId', 'userCredits'];
            authKeys.forEach(key => {
                localStorage.removeItem(key);
                log(`清除: ${key}`);
            });
            
            setTimeout(() => {
                checkConsistency();
            }, 100);
        }

        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，开始检查认证状态一致性');
            checkConsistency();
        });
    </script>
</body>
</html>
