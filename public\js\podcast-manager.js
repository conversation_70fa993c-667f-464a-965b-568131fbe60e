/**
 * 播客管理器
 * 实现播客搜索、收藏、节目列表、下载功能
 */
class PodcastManager {
    constructor() {
        this.favorites = JSON.parse(localStorage.getItem('podcastFavorites') || '[]');
        this.defaultPodcasts = []; // 默认播客列表
        this.removedDefaults = JSON.parse(localStorage.getItem('removedDefaultPodcasts') || '[]');
        this.currentPodcast = null;
        this.currentEpisodes = [];
        this.allEpisodes = [];
        this.currentSearchResults = [];
        this.currentPage = 0;
        this.episodesPerPage = 3;
        this.totalEpisodes = 0;
        this.hasMoreEpisodes = false;
        
        // 独立的播客音频播放器
        this.podcastPlayer = null;
        this.currentPlayingIndex = -1;
        
        this.isLoading = false;
        
        // RSS Feed URL修复将由配置加载器统一管理
        // 不再需要在这里硬编码URL映射表
        
        this.init();
    }

    async init() {
        this.setupTabSwitching();
        this.setupSearchEvents();
        this.setupModalEvents();
        await this.loadDefaultPodcasts(); // 加载默认播客
        this.loadFavorites();
        this.updatePlayButtons();
        this.setupPlayerEventListeners();
        console.log('播客管理器初始化完成');
    }

    // 设置模态框事件
    setupModalEvents() {
        const closeBtn = document.getElementById('podcastModalCloseBtn');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeSearchModal();
            });
        }
    }

    // Tab 切换功能
    setupTabSwitching() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetTab = btn.dataset.tab;
                
                // 移除所有活跃状态
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(content => content.classList.add('hidden'));
                
                // 激活当前tab
                btn.classList.add('active');
                const targetContent = document.getElementById(targetTab + 'Tab');
                if (targetContent) {
                    targetContent.classList.remove('hidden');
                }
            });
        });
    }

    // 设置搜索事件
    setupSearchEvents() {
        const searchBox = document.getElementById('podcastSearchBox');
        const searchBtn = document.getElementById('podcastSearchBtn');

        if (searchBox && searchBtn) {
            searchBtn.addEventListener('click', () => this.searchPodcasts());
            searchBox.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.searchPodcasts();
                }
            });
        }
    }

    // 搜索播客
    async searchPodcasts() {
        const searchBox = document.getElementById('podcastSearchBox');
        const query = searchBox.value.trim();
        
        if (!query) {
            this.showMessage('请输入搜索关键词', 'warning');
            return;
        }

        // 初始化 skipCreditsCheck 变量
        let skipCreditsCheck = false;
        // 检查是否需要进行积分检查
        const needCreditsCheck = window.ENV && window.ENV.CREDITS_CHECK && window.ENV.CREDITS_CHECK.PODCAST_SEARCH;
        
        if (needCreditsCheck) {
            // 前端缓存积分初查：缓存积分不足立即弹框
            if (window.CreditsManager) {
                let cached = window.CreditsManager.getCachedCredits();
                if (cached === null) cached = 0;
                if (cached < 1) {
                    window.CreditsManager.showInsufficientCreditsAlert(cached);
                    return;
                }
            }
            
            // 新增：确保积分已同步（防止前端缓存为0但后端有积分的情况）
            if (window.CreditsManager) {
                const currentCredits = window.CreditsManager.getCachedCredits();
                console.log(`🔍 [播客搜索] 当前缓存积分: ${currentCredits}`);
                
                // 如果积分为0或null，先尝试从后端同步
                if (currentCredits === null || currentCredits === 0) {
                    console.log(`🔄 [播客搜索] 积分为${currentCredits}，尝试从后端同步...`);
                    try {
                        const backendCredits = await window.CreditsManager.fetchCreditsFromBackendCache();
                        console.log(`📊 [播客搜索] 后端同步结果: ${backendCredits}积分`);
                        
                        // 如果后端有积分，检查是否可以设置免检查
                        if (backendCredits > 0) {
                            console.log(`✅ [播客搜索] 发现后端积分${backendCredits}，设置动态免检查时间`);
                            window.CreditsManager.setAuxiliarySkipCheckTime(60);
                            
                            // 启动动态缓存管理（如果还没启动）
                            if (!window.CreditsManager.dynamicCacheManagementStarted) {
                                await window.CreditsManager.startDynamicCacheManagement();
                            }
                        }
                    } catch (error) {
                        console.warn(`⚠️ [播客搜索] 后端同步失败:`, error);
                    }
                }
            }
            
            // 新增：使用动态免检查机制
            if (window.CreditsManager && typeof window.CreditsManager.checkAuxiliarySkipStatus === 'function') {
                const skipStatus = window.CreditsManager.checkAuxiliarySkipStatus();
                skipCreditsCheck = skipStatus.canSkip;
                
                if (skipCreditsCheck) {
                    console.log(`🚀 [播客搜索] 辅助功能免检查生效，剩余${skipStatus.remainingMinutes}分钟，跳过积分检查`);
                } else {
                    console.log(`🔍 [播客搜索] 辅助功能免检查不生效(${skipStatus.reason})，将进行积分验证`);
                    
                    // 降级到原有的双阶段积分校验
                    const creditsManager = window.CreditsManager;
                    const required = 1;
                    let cached = creditsManager.getCachedCredits();
                    if (cached === null) cached = 0;
                    if (cached < required) {
                        creditsManager.showInsufficientCreditsAlert(cached);
                        return;
                    }
                    
                    // 全局后端积分检查
                    if (typeof window.checkCredits === 'function') {
                        const hasCredits = await window.checkCredits('podcast_search');
                        if (!hasCredits) {
                            return;
                        }
                    }
                }
            } else {
                console.log(`🔍 [播客搜索] 动态免检查机制不可用，使用传统积分验证`);
                
                // 降级到原有的双阶段积分校验
                const creditsManager = window.CreditsManager;
                const required = 1;
                let cached = creditsManager.getCachedCredits();
                if (cached === null) cached = 0;
                if (cached < required) {
                    creditsManager.showInsufficientCreditsAlert(cached);
                    return;
                }
                
                // 全局后端积分检查
                if (typeof window.checkCredits === 'function') {
                    const hasCredits = await window.checkCredits('podcast_search');
                    if (!hasCredits) {
                        return;
                    }
                }
            }
        }
        
        // 积分检查通过，显示加载状态
        this.showSearchLoading(true);
        try {
            // 构建搜索URL，传递免检查参数
            let searchUrl = `/podcast/search?q=${encodeURIComponent(query)}`;
            if (skipCreditsCheck) {
                searchUrl += '&skip_credits_check=true';
                console.log(`🚀 [播客搜索] 向后端传递免检查标志: skip_credits_check=true`);
            }
            
            const response = await fetch(searchUrl);
            const data = await response.json();

            // 后端响应中再次处理积分不足错误
            if (data.success === false && data.error && data.error.includes('积分')) {
                if (window.CreditsManager && typeof window.CreditsManager.showInsufficientCreditsAlert === 'function') {
                    const currentCredits = data.current_credits !== undefined ? data.current_credits : 0;
                    window.CreditsManager.showInsufficientCreditsAlert(currentCredits);
                } else {
                    alert(data.error);
                }
                return;
            }

            if (data.resultCount === 0) {
                this.showMessage('未找到相关播客，请尝试其他关键词', 'info');
                return;
            }

            this.currentSearchResults = data.results;
            this.displaySearchResults(data.results);
            this.showSearchModal();
        } catch (error) {
            console.error('搜索播客失败:', error);
            this.showMessage(`搜索失败: ${error.message}`, 'error');
        } finally {
            this.showSearchLoading(false);
        }
    }

    // 显示搜索加载状态
    showSearchLoading(show) {
        const loading = document.getElementById('podcastSearchLoading');
        const modalLoading = document.getElementById('modalSearchLoading');
        
        if (loading) {
            loading.classList.toggle('hidden', !show);
        }
        if (modalLoading) {
            modalLoading.classList.toggle('hidden', !show);
        }
    }

    // 显示搜索结果
    displaySearchResults(results) {
        const container = document.getElementById('searchResultsList');
        if (!container) return;

        container.innerHTML = '';

        results.forEach(podcast => {
            const item = this.createSearchResultItem(podcast);
            container.appendChild(item);
        });
    }

    // 创建搜索结果项
    createSearchResultItem(podcast) {
        const item = document.createElement('div');
        item.className = 'search-result-item';
        
        const isUserFavorited = this.isUserFavorited(podcast.collectionId);
        const isDefault = this.isDefaultPodcast(podcast.collectionId);
        const showAsFavorited = isUserFavorited || isDefault;
        
        item.innerHTML = `
            <img src="/images/podcast-covers/${podcast.collectionId}.jpg" alt="${podcast.collectionName}" class="search-result-artwork"
                 onerror="this.onerror=null;this.src='${this.getArtworkUrl(podcast)}';">
            <div class="search-result-info">
                <div class="search-result-name">${this.escapeHtml(podcast.collectionName)}</div>
                <div class="search-result-author">${this.escapeHtml(podcast.artistName || '未知作者')}</div>
                <div class="search-result-genre">${this.escapeHtml(podcast.primaryGenreName || '')}</div>
            </div>
            <div class="search-result-actions">
                <button class="favorite-toggle-btn ${showAsFavorited ? 'favorited' : ''}" 
                        data-collection-id="${podcast.collectionId}">
                    <i class="fas fa-heart"></i>
                </button>
            </div>
        `;

        // 添加收藏按钮事件
        const favoriteBtn = item.querySelector('.favorite-toggle-btn');
        favoriteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleFavorite(podcast.collectionId, favoriteBtn);
        });

        // 点击项目查看节目列表
        item.addEventListener('click', (e) => {
            if (!e.target.closest('.favorite-toggle-btn')) {
                this.selectPodcast(podcast);
                this.closeSearchModal();
            }
        });

        return item;
    }

    // 切换收藏状态
    toggleFavorite(collectionId, btnElement) {
        const podcast = this.currentSearchResults.find(p => p.collectionId == collectionId);
        if (!podcast) return;

        const index = this.favorites.findIndex(fav => fav.collectionId == collectionId);
        const isDefaultPodcast = this.isDefaultPodcast(collectionId);
        
        if (index > -1) {
            // 取消收藏
            this.favorites.splice(index, 1);
            btnElement.classList.remove('favorited');
            
            // 如果是默认播客，更新收藏按钮的样式
            if (isDefaultPodcast) {
                this.showMessage('已从个人收藏中移除，但仍在推荐列表中显示', 'info');
            }
        } else {
            // 添加收藏（置顶最新收藏）
            this.favorites.unshift(podcast);
            btnElement.classList.add('favorited');
            
            if (isDefaultPodcast) {
                this.showMessage('已添加到个人收藏', 'success');
            }
        }

        this.saveFavorites();
        this.loadFavorites();
    }

    // 保存收藏到本地存储
    saveFavorites() {
        localStorage.setItem('podcastFavorites', JSON.stringify(this.favorites));
    }

    // 保存已移除的默认播客列表
    saveRemovedDefaults() {
        localStorage.setItem('removedDefaultPodcasts', JSON.stringify(this.removedDefaults));
    }

    // 加载默认播客配置
    async loadDefaultPodcasts() {
        try {
            const response = await fetch('/config/default-podcasts.json?v=' + Date.now());
            if (!response.ok) {
                console.warn('无法加载默认播客配置，使用空列表');
                this.defaultPodcasts = [];
                return;
            }
            
            const config = await response.json();
            this.defaultPodcasts = config.defaultPodcasts || [];
            console.log(`✅ 默认播客配置加载完成，共 ${this.defaultPodcasts.length} 个播客`);
        } catch (error) {
            console.error('加载默认播客配置失败:', error);
            this.defaultPodcasts = [];
        }
    }

    // 获取显示的播客列表：用户收藏置顶，默认推荐（过滤已移除）置后，且去重
    getMergedPodcastList() {
        const merged = [];
        // 1. 添加用户收藏，放在顶部
        this.favorites.forEach(fav => merged.push(fav));
        // 2. 添加默认推荐（过滤已移除），避免重复
        this.defaultPodcasts.forEach(podcast => {
            if (!this.removedDefaults.includes(podcast.collectionId) && !merged.some(p => p.collectionId === podcast.collectionId)) {
                merged.push(podcast);
            }
        });
        console.log(`加载合并播客列表，共 ${merged.length} 个播客 (收藏 ${this.favorites.length}, 默认 ${this.defaultPodcasts.length - this.removedDefaults.length})`);
        return merged;
    }

    // 检查播客是否被用户收藏（不包括默认播客）
    isUserFavorited(collectionId) {
        return this.favorites.some(fav => fav.collectionId == collectionId);
    }

    // 检查是否为默认播客（且未被用户移除）
    isDefaultPodcast(collectionId) {
        // 先判断是否在默认列表中
        const isDefault = this.defaultPodcasts.some(podcast => podcast.collectionId == collectionId);
        if (!isDefault) return false;
        // 排除用户已移除的默认播客
        return !this.removedDefaults.includes(collectionId);
    }

    // 加载收藏列表
    loadFavorites() {
        const container = document.getElementById('podcastFavoritesList');
        if (!container) return;

        const mergedPodcasts = this.getMergedPodcastList();

        if (mergedPodcasts.length === 0) {
            container.innerHTML = '<div class="no-favorites" data-i18n="podcast.no_favorites">暂无收藏的播客</div>';
            return;
        }

        container.innerHTML = '';
        mergedPodcasts.forEach(podcast => {
            const item = this.createFavoriteItem(podcast);
            container.appendChild(item);
        });
        // 将滚动条重置到顶部，确保新添加的播客可见
        container.scrollTop = 0;
    }

    // 创建收藏项
    createFavoriteItem(podcast) {
        const item = document.createElement('div');
        item.className = 'podcast-item';
        item.dataset.collectionId = podcast.collectionId;
        
        const isDefault = this.isDefaultPodcast(podcast.collectionId);
        const isUserFavorited = this.isUserFavorited(podcast.collectionId);
        
        // 为默认播客添加标识
        if (isDefault) {
            item.classList.add('default-podcast');
        }
        
        item.innerHTML = `
            <img src="/images/podcast-covers/${podcast.collectionId}.jpg" alt="${podcast.collectionName}" class="podcast-artwork"
                 onerror="this.onerror=null;this.src='${this.getArtworkUrl(podcast)}';">
            <div class="podcast-info">
                <div class="podcast-name">${this.escapeHtml(podcast.collectionName)}</div>
                <div class="podcast-author">${this.escapeHtml(podcast.artistName || '未知作者')}</div>
            </div>
            <button class="favorite-btn ${isDefault && !isUserFavorited ? 'default-heart' : ''}" data-collection-id="${podcast.collectionId}">
                <i class="fas fa-heart"></i>
            </button>
        `;

        // 添加收藏按钮事件
        const favoriteBtn = item.querySelector('.favorite-btn');
        favoriteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.removeFavorite(podcast.collectionId);
        });

        // 添加选择事件
        item.addEventListener('click', (e) => {
            if (!e.target.closest('.favorite-btn')) {
                this.selectPodcast(podcast);
            }
        });

        return item;
    }

    // 移除收藏
    removeFavorite(collectionId) {
        const isDefaultPodcast = this.isDefaultPodcast(collectionId);
        const index = this.favorites.findIndex(fav => fav.collectionId == collectionId);
        
        if (index > -1) {
            // 用户收藏项移除
            this.favorites.splice(index, 1);
            this.saveFavorites();
            this.showMessage(this.getText('podcast.unfavorited'), 'info');
            this.loadFavorites();
            // 如果取消的是当前选中播客，则清空节目列表
            if (this.currentPodcast && this.currentPodcast.collectionId == collectionId) {
                this.currentPodcast = null;
                const epContainer = document.getElementById('podcastEpisodesList');
                if (epContainer) {
                    epContainer.innerHTML = `<div class="no-episodes">${this.getText('podcast.select_podcast')}</div>`;
                }
                const selectedItem = document.querySelector('.podcast-item.selected');
                if (selectedItem && selectedItem.dataset.collectionId == collectionId) {
                    selectedItem.classList.remove('selected');
                }
            }
        } else if (isDefaultPodcast) {
            // 默认播客移除
            if (!this.removedDefaults.includes(collectionId)) {
                this.removedDefaults.push(collectionId);
                this.saveRemovedDefaults();
            }
            this.showMessage('已从推荐列表中移除', 'info');
            this.loadFavorites();
            // 同样清理右侧选中状态
            if (this.currentPodcast && this.currentPodcast.collectionId == collectionId) {
                this.currentPodcast = null;
                const epContainer = document.getElementById('podcastEpisodesList');
                if (epContainer) {
                    epContainer.innerHTML = `<div class="no-episodes">${this.getText('podcast.select_podcast')}</div>`;
                }
                const selectedItem = document.querySelector('.podcast-item.selected');
                if (selectedItem && selectedItem.dataset.collectionId == collectionId) {
                    selectedItem.classList.remove('selected');
                }
            }
        }
    }

    // 选择播客
    async selectPodcast(podcast) {
        // 停止当前播放（如果有）
        if (this.podcastPlayer) {
            this.podcastPlayer.pause();
            this.podcastPlayer.currentTime = 0;
            this.podcastPlayer = null;
        }
        this.currentPlayingIndex = -1;
        
        // 应用RSS URL修复
        const originalFeedUrl = podcast.feedUrl;
        const fixedFeedUrl = await this.fixRssUrl(originalFeedUrl, podcast.collectionName);
        
        // 创建修复后的播客对象
        this.currentPodcast = {
            ...podcast,
            feedUrl: fixedFeedUrl,
            originalFeedUrl: originalFeedUrl // 保留原始URL用于调试
        };
        
        this.currentPage = 0;
        this.currentEpisodes = [];
        this.allEpisodes = [];
        this.totalEpisodes = 0;
        this.hasMoreEpisodes = false;
        
        // 更新选中状态
        const favoriteItems = document.querySelectorAll('.podcast-item');
        favoriteItems.forEach(item => item.classList.remove('selected'));
        
        // 找到并选中当前项
        const currentItem = document.querySelector(`[data-collection-id="${podcast.collectionId}"]`);
        if (currentItem) {
            currentItem.classList.add('selected');
        }

        await this.loadEpisodes();
    }

    // 加载节目列表
    async loadEpisodes() {
        if (!this.currentPodcast || !this.currentPodcast.feedUrl) {
            this.showMessage('无法获取播客节目列表', 'error');
            return;
        }

        console.log(`===== 前端加载节目列表 =====`);
        console.log(`播客: ${this.currentPodcast.collectionName}`);
        console.log(`Feed URL: ${this.currentPodcast.feedUrl}`);
        console.log(`页码: ${this.currentPage}, 每页: ${this.episodesPerPage}`);

        this.showEpisodesLoading(true);

        try {
            const apiUrl = `/podcast/episodes?feedUrl=${encodeURIComponent(this.currentPodcast.feedUrl)}&page=${this.currentPage}&per_page=${this.episodesPerPage}`;
            console.log(`API调用URL: ${apiUrl}`);
            
            const response = await fetch(apiUrl);
            const data = await response.json();

            console.log(`API响应状态: ${response.status}`);
            console.log(`API响应数据:`, data);

            if (!response.ok) {
                throw new Error(data.error || '获取节目列表失败');
            }

            // 检查数据格式
            if (data.episodes) {
                // 新格式
                console.log(`✅ 检测到新API格式`);
                console.log(`总节目数: ${data.total}, 当前页: ${data.page}, 每页: ${data.per_page}, 还有更多: ${data.has_more}`);
                console.log(`当前页节目数: ${data.episodes.length}`);
                
                this.currentEpisodes = data.episodes;
                this.totalEpisodes = data.total;
                this.hasMoreEpisodes = data.has_more;
                
                // 打印前3个节目标题
                console.log(`前3个节目:`, data.episodes.slice(0, 3).map(ep => ep.title));
            } else if (Array.isArray(data)) {
                // 旧格式（降级模式）
                console.log(`⚠️ 检测到旧API格式（降级模式）`);
                console.log(`节目数: ${data.length}`);
                
                this.currentEpisodes = data;
                this.totalEpisodes = data.length;
                this.hasMoreEpisodes = false;
                
                console.log(`前3个节目:`, data.slice(0, 3).map(ep => ep.title));
            } else {
                console.error(`❌ 未知的API响应格式:`, data);
                throw new Error('API响应格式错误');
            }
            
            console.log(`准备渲染节目列表...`);
            this.displayEpisodes();
            console.log(`===== 节目列表加载完成 =====`);
            
        } catch (error) {
            console.error('❌ 获取节目列表失败:', error);
            this.showMessage(`获取节目列表失败: ${error.message}`, 'error');
        } finally {
            this.showEpisodesLoading(false);
        }
    }

    // 显示节目加载状态
    showEpisodesLoading(show) {
        const container = document.getElementById('podcastEpisodesList');
        if (!container) return;

        if (show && this.currentPage === 0) {
            // 只有第一页加载时才显示顶部加载状态
            container.innerHTML = `
                <div class="loading-container" style="text-align: center; padding: 40px 0; display: flex; flex-direction: column; align-items: center; gap: 5px;">
                    <div class="loading-spinner"></div>
                    <div style="font-size: 12px; color: #718096;">${this.getText('podcast.loading_episodes')}</div>
                </div>
            `;
        }
        // 对于"加载更多"，加载状态已经在loadMoreEpisodes中处理了
    }

    // 显示节目列表
    displayEpisodes() {
        const listContainer = document.getElementById('podcastEpisodesList');
        if (!listContainer) return;

        if (this.currentEpisodes.length === 0 && this.currentPage === 0) {
            listContainer.innerHTML = `<div class="no-episodes">${this.getText('podcast.no_episodes')}</div>`;
            return;
        }

        // 如果是第一页，清空容器并重置累积列表
        if (this.currentPage === 0) {
            listContainer.innerHTML = '';
            this.allEpisodes = [];
        }
        
        // 将新加载的节目添加到总列表中
        if (!this.allEpisodes) this.allEpisodes = [];
        this.allEpisodes.push(...this.currentEpisodes);
        
        console.log(`渲染节目: 当前页=${this.currentPage}, 新增${this.currentEpisodes.length}期, 总计${this.allEpisodes.length}期`);
        
        // 只渲染新加载的节目（追加到现有内容后面）
        this.currentEpisodes.forEach((episode, index) => {
            const globalIndex = this.allEpisodes.length - this.currentEpisodes.length + index;
            const item = this.createEpisodeItem(episode, globalIndex);
            listContainer.appendChild(item);
        });

        // 添加或更新加载更多按钮
        this.updateLoadMoreButton();
    }

    // 更新加载更多按钮
    updateLoadMoreButton() {
        const loadMoreContainer = document.getElementById('loadMoreContainer');
        if (!loadMoreContainer) {
            console.error('"加载更多"按钮的容器未找到');
            return;
        }
        
        // 先清空容器
        loadMoreContainer.innerHTML = '';

        // 如果还有更多节目，添加加载更多按钮
        if (this.hasMoreEpisodes) {
            const remainingCount = this.totalEpisodes - this.allEpisodes.length;
            if (remainingCount <= 0) {
                console.log(`所有节目已加载完毕`);
                return;
            }

            const loadMoreBtn = document.createElement('button');
            loadMoreBtn.className = 'load-more-btn';
            loadMoreBtn.innerHTML = `<span>${this.getText('podcast.load_more_count').replace('{count}', remainingCount)}</span>`;
            loadMoreBtn.addEventListener('click', () => this.loadMoreEpisodes());
            
            loadMoreContainer.appendChild(loadMoreBtn);
            
            console.log(`添加"加载更多"按钮，剩余${remainingCount}集`);
        } else {
            console.log(`所有节目已加载完毕`);
        }
    }

    // 加载更多节目
    async loadMoreEpisodes() {
        if (!this.hasMoreEpisodes) {
            console.log('没有更多节目可加载');
            return;
        }
        
        console.log(`点击"加载更多"，准备加载第${this.currentPage + 1}页`);
        
        const loadMoreContainer = document.getElementById('loadMoreContainer');
        const loadMoreBtn = loadMoreContainer ? loadMoreContainer.querySelector('.load-more-btn') : null;
        if (loadMoreBtn) {
            // 保持按钮原有的结构和样式，只更改文字和添加加载图标
            loadMoreBtn.innerHTML = `<i class="fas fa-spinner loading-spin-animation"></i> ${this.getText('podcast.loading_episodes')}`;
            loadMoreBtn.disabled = true;
        }
        
        this.currentPage++;
        await this.loadEpisodes();
    }

    // 创建节目项
    createEpisodeItem(episode, index) {
        const item = document.createElement('div');
        item.className = 'episode-item';
        
        const publishedDate = this.formatDate(episode.published);
        const duration = episode.duration || '';
        
        item.innerHTML = `
            <div class="episode-header">
                <div class="episode-title">${this.escapeHtml(episode.title)}</div>
                <div class="episode-meta">
                    ${publishedDate} ${duration ? `• ${duration}` : ''}
                </div>
            </div>
            ${episode.description ? `
                <div class="episode-description">${this.escapeHtml(episode.description.substring(0, 200))}${episode.description.length > 200 ? '...' : ''}</div>
            ` : ''}
            <div class="episode-actions">
                ${episode.audio_url ? `
                    <button class="episode-download-btn" data-audio-url="${episode.audio_url}" data-title="${this.escapeHtml(episode.title)}" data-index="${index}">
                        <i class="fas fa-download"></i> ${this.getText('podcast.download')}
                    </button>
                    <div class="episode-player-controls">
                        <button class="episode-rewind-btn" data-index="${index}" title="快退5秒">
                            <i class="fas fa-backward"></i>
                        </button>
                        <button class="episode-play-btn" data-audio-url="${episode.audio_url}" data-index="${index}">
                            <i class="fas fa-play"></i> ${this.getText('podcast.play')}
                        </button>
                        <button class="episode-forward-btn" data-index="${index}" title="快进10秒">
                            <i class="fas fa-forward"></i>
                        </button>
                    </div>
                ` : `<span style="color: #999; font-size: 11px;">${this.getText('podcast.no_audio')}</span>`}
            </div>
            <div class="episode-progress hidden" id="episodeProgress${index}">
                <div class="episode-progress-fill" id="episodeProgressFill${index}"></div>
            </div>
        `;

        // 添加下载事件
        const downloadBtn = item.querySelector('.episode-download-btn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => {
                this.downloadEpisode(
                    downloadBtn.dataset.audioUrl,
                    downloadBtn.dataset.title,
                    parseInt(downloadBtn.dataset.index)
                );
            });
        }

        // 添加播放事件
        const playBtn = item.querySelector('.episode-play-btn');
        if (playBtn) {
            playBtn.addEventListener('click', () => {
                const title = item.querySelector('.episode-title').textContent;
                this.playEpisode(
                    playBtn.dataset.audioUrl,
                    title,
                    playBtn.dataset.index
                );
            });
        }

        // 添加快退事件
        const rewindBtn = item.querySelector('.episode-rewind-btn');
        if (rewindBtn) {
            rewindBtn.addEventListener('click', () => {
                this.rewindEpisode(parseInt(rewindBtn.dataset.index));
            });
        }

        // 添加快进事件
        const forwardBtn = item.querySelector('.episode-forward-btn');
        if (forwardBtn) {
            forwardBtn.addEventListener('click', () => {
                this.forwardEpisode(parseInt(forwardBtn.dataset.index));
            });
        }

        return item;
    }

    // 播放节目 - 使用独立播放器
    async playEpisode(audioUrl, title, index) {
        const playBtn = document.querySelector(`[data-index="${index}"].episode-play-btn`);
        if (!playBtn) return;
        
        // 如果当前是同一个节目，则切换播放/暂停
        if (this.currentPlayingIndex === index && this.podcastPlayer) {
            if (this.podcastPlayer.paused) {
                this.podcastPlayer.play();
            } else {
                this.podcastPlayer.pause();
            }
            return;
        }

        // 停止当前播放（如果有）
        if (this.podcastPlayer) {
            this.podcastPlayer.pause();
            this.podcastPlayer.currentTime = 0;
        }

        // 显示加载状态
        this.showPlayLoading(true, playBtn);
        const loadingStartTime = Date.now();
        let loadingFinished = false;

        try {
            // 创建新的音频播放器实例
            this.podcastPlayer = new Audio(audioUrl);
            this.currentPlayingIndex = index;
            
            // 设置事件监听器
            this.podcastPlayer.addEventListener('loadstart', () => {
                this.showPlayLoading(true, playBtn);
            });
            
            this.podcastPlayer.addEventListener('canplay', async () => {
                // 确保最少显示500ms的加载状态，提供一致的用户体验
                const elapsedTime = Date.now() - loadingStartTime;
                const minLoadingTime = 500; // 最少500ms
                
                if (elapsedTime < minLoadingTime && !loadingFinished) {
                    await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime));
                }
                
                loadingFinished = true;
                this.showPlayLoading(false, playBtn);
                this.updatePlayButtons();
            });
            
            this.podcastPlayer.addEventListener('play', () => {
                this.updatePlayButtons();
            });
            
            this.podcastPlayer.addEventListener('pause', () => {
                this.updatePlayButtons();
            });
            
            this.podcastPlayer.addEventListener('ended', () => {
                this.currentPlayingIndex = -1;
                this.updatePlayButtons();
            });
            
            this.podcastPlayer.addEventListener('error', (e) => {
                console.error('播客播放失败:', e);
                this.showMessage(this.getText('podcast.play_failed'), 'error');
                loadingFinished = true;
                this.showPlayLoading(false, playBtn);
                this.currentPlayingIndex = -1;
                this.updatePlayButtons();
            });

            // 开始播放
            await this.podcastPlayer.play();
            
        } catch (error) {
            console.error('播放播客失败:', error);
            this.showMessage(this.getText('podcast.play_failed'), 'error');
            loadingFinished = true;
            this.showPlayLoading(false, playBtn);
            this.currentPlayingIndex = -1;
            this.updatePlayButtons();
        }
    }
    
    // 显示播放加载状态
    showPlayLoading(isLoading, button) {
        if (!button) return;
        if (isLoading) {
            button.disabled = true;
            button.classList.add('loading-state');
            button.innerHTML = '<i class="fas fa-spinner loading-spin-animation"></i>';
        } else {
            button.disabled = false;
            button.classList.remove('loading-state');
            // 状态将由 AudioApp 中的事件回调更新
        }
    }

    // 更新所有播放按钮的状态
    updatePlayButtons(playingIndex = this.currentPlayingIndex) {
        const playingIdxInt = parseInt(playingIndex, 10);

        const allPlayButtons = document.querySelectorAll('.episode-play-btn');
        const allRewindButtons = document.querySelectorAll('.episode-rewind-btn');
        const allForwardButtons = document.querySelectorAll('.episode-forward-btn');
        
        allPlayButtons.forEach(btn => {
            const btnIndex = parseInt(btn.dataset.index, 10);
            btn.disabled = false;
            
            if (btnIndex === playingIdxInt && this.podcastPlayer) {
                if (!this.podcastPlayer.paused) {
                    btn.innerHTML = `<i class="fas fa-pause"></i> ${this.getText('podcast.pause')}`;
                } else {
                    btn.innerHTML = `<i class="fas fa-play"></i> ${this.getText('podcast.play')}`;
                }
            } else {
                btn.innerHTML = `<i class="fas fa-play"></i> ${this.getText('podcast.play')}`;
            }
        });

        // 更新快退和快进按钮状态 - 所有按钮都保持可用状态
        allRewindButtons.forEach(btn => {
            const btnIndex = parseInt(btn.dataset.index, 10);
            // 只有当前播放的episode才能实际使用快退快进功能，但所有按钮都保持启用状态
            btn.disabled = false;
            btn.style.opacity = '1';
        });

        allForwardButtons.forEach(btn => {
            const btnIndex = parseInt(btn.dataset.index, 10);
            // 只有当前播放的episode才能实际使用快退快进功能，但所有按钮都保持启用状态
            btn.disabled = false;
            btn.style.opacity = '1';
        });
    }

    // 快退5秒
    rewindEpisode(index) {
        console.log(`快退请求: index=${index}, currentPlayingIndex=${this.currentPlayingIndex}, hasPlayer=${!!this.podcastPlayer}`);
        
        if (parseInt(this.currentPlayingIndex) !== parseInt(index) || !this.podcastPlayer) {
            console.log('没有正在播放的音频或索引不匹配');
            return;
        }
        
        const currentTime = this.podcastPlayer.currentTime;
        const newTime = Math.max(0, currentTime - 5); // 快退5秒，但不能小于0
        this.podcastPlayer.currentTime = newTime;
        
        console.log(`快退5秒: ${currentTime.toFixed(1)}s -> ${newTime.toFixed(1)}s`);
        
        // 添加视觉反馈
        const btn = document.querySelector(`[data-index="${index}"].episode-rewind-btn`);
        if (btn) {
            btn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                btn.style.transform = 'scale(1)';
                btn.blur(); // 快速移除焦点，消除边框
            }, 150);
        }
    }

    // 快进10秒
    forwardEpisode(index) {
        console.log(`快进请求: index=${index}, currentPlayingIndex=${this.currentPlayingIndex}, hasPlayer=${!!this.podcastPlayer}`);
        
        if (parseInt(this.currentPlayingIndex) !== parseInt(index) || !this.podcastPlayer) {
            console.log('没有正在播放的音频或索引不匹配');
            return;
        }
        
        const currentTime = this.podcastPlayer.currentTime;
        const duration = this.podcastPlayer.duration || 0;
        const newTime = Math.min(duration, currentTime + 10); // 快进10秒，但不能超过总时长
        this.podcastPlayer.currentTime = newTime;
        
        console.log(`快进10秒: ${currentTime.toFixed(1)}s -> ${newTime.toFixed(1)}s (总时长: ${duration.toFixed(1)}s)`);
        
        // 添加视觉反馈
        const btn = document.querySelector(`[data-index="${index}"].episode-forward-btn`);
        if (btn) {
            btn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                btn.style.transform = 'scale(1)';
                btn.blur(); // 快速移除焦点，消除边框
            }, 150);
        }
    }

    // 下载节目
    async downloadEpisode(audioUrl, title, index) {
        if (!audioUrl) {
            this.showMessage(this.getText('podcast.invalid_audio_link'), 'error');
            return;
        }

        const progressElement = document.getElementById(`episodeProgress${index}`);
        const progressFill = document.getElementById(`episodeProgressFill${index}`);
        const downloadBtn = document.querySelector(`[data-index="${index}"].episode-download-btn`);

        if (downloadBtn.disabled) return;

        try {
            // 禁用下载按钮，显示加载状态
            downloadBtn.disabled = true;
            downloadBtn.classList.add('loading-state');
            downloadBtn.innerHTML = '<i class="fas fa-spinner loading-spin-animation"></i>';

            // 使用 fetch 流式下载，移除前端重试，只依赖后端重试
            const response = await fetch(`/podcast/proxy_download?audio_url=${encodeURIComponent(audioUrl)}`);
            
            // 连接成功后，保持加载状态，等待完整下载完成后再恢复按钮

            const contentLength = +response.headers.get('Content-Length');
            let receivedLength = 0;
            const chunks = [];
            
            // 显示进度条
            if (progressElement) {
                progressElement.classList.remove('hidden');
            }
            
            const reader = response.body.getReader();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                chunks.push(value);
                receivedLength += value.length;

                if (contentLength > 0) {
                    const percent = Math.round((receivedLength / contentLength) * 100);
                    if (progressFill) {
                        progressFill.style.width = `${percent}%`;
                    }
                }
            }

            // 创建下载链接
            const blob = new Blob(chunks);
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.sanitizeFilename(title)}.mp3`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showMessage(this.getText('podcast.download_complete'), 'success');
            // 下载完成，恢复按钮到初始状态
            downloadBtn.disabled = false;
            downloadBtn.classList.remove('loading-state');
            downloadBtn.innerHTML = `<i class="fas fa-download"></i> ${this.getText('podcast.download')}`;

        } catch (error) {
            console.error('下载失败:', error);
            this.showMessage(`${this.getText('podcast.download_failed')}: ${error.message}`, 'error');
            
            // 错误时立即恢复按钮状态
            if (downloadBtn) {
                downloadBtn.disabled = false;
                downloadBtn.classList.remove('loading-state');
                downloadBtn.innerHTML = `<i class="fas fa-download"></i> ${this.getText('podcast.download')}`;
            }
            
            // 错误时立即隐藏进度条
            if (progressElement) {
                progressElement.classList.add('hidden');
            }
            if (progressFill) {
                progressFill.style.width = `0%`;
            }
        } finally {
            // 成功时的延时隐藏进度条（只有成功时才需要延时）
            if (!downloadBtn.disabled) {
                setTimeout(() => {
                    if (progressElement) {
                        progressElement.classList.add('hidden');
                    }
                    if (progressFill) {
                        progressFill.style.width = `0%`;
                    }
                }, 2000);
            }
        }
    }

    // 显示/隐藏搜索模态框
    showSearchModal() {
        const modal = document.getElementById('podcastSearchModal');
        if (modal) {
            modal.classList.add('show');
            
            // 添加点击背景关闭功能（避免重复添加）
            if (!modal.hasAttribute('data-events-bound')) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        this.closeSearchModal();
                    }
                });
                modal.setAttribute('data-events-bound', 'true');
            }
            
            // 添加ESC键关闭功能
            const handleEscKey = (e) => {
                if (e.key === 'Escape') {
                    this.closeSearchModal();
                    document.removeEventListener('keydown', handleEscKey);
                }
            };
            document.addEventListener('keydown', handleEscKey);
        }
    }

    closeSearchModal() {
        const modal = document.getElementById('podcastSearchModal');
        if (modal) {
            modal.classList.remove('show');
        }
    }

    // 工具函数
    getArtworkUrl(podcast) {
        return podcast.artworkUrl100 || podcast.artworkUrl60 || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik01MCAyNUMyOC4wNTg5IDI1IDEwIDQzLjA1ODkgMTAgNjVDMTAgODYuOTQxMSAyOC4wNTg5IDEwNSA1MCAxMDVDNzEuOTQxMSAxMDUgOTAgODYuOTQxMSA5MCA2NUM5MCA0My4wNTg5IDcxLjk0MTEgMjUgNTAgMjVaIiBmaWxsPSIjQ0NDIi8+CjxwYXRoIGQ9Ik00MCA0MFY4MEw3MCA2MFY0MEw0MCA0MFoiIGZpbGw9IiM5OTkiLz4KPC9zdmc+';
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(dateString) {
        if (!dateString) return this.getText('podcast.unknown_date');
        try {
            const date = new Date(dateString);
            const lang = localStorage.getItem('language') || 'zh';
            return date.toLocaleDateString(lang === 'zh' ? 'zh-CN' : 'en-US');
        } catch (error) {
            return this.getText('podcast.unknown_date');
        }
    }

    sanitizeFilename(filename) {
        return filename.replace(/[^\w\s-]/gi, '').replace(/\s+/g, '_');
    }

    showMessage(message, type = 'info') {
        // 可以集成到现有的消息系统
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // 简单的消息提示
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            alert(message);
        }
    }

    // 新增：设置播放器事件监听
    setupPlayerEventListeners() {
        // 由于使用独立播放器，不再需要监听主播放器事件
        console.log('播客使用独立播放器，无需设置主播放器监听');
    }

    // 获取国际化文本的辅助方法
    getText(key) {
        // 使用UI语言系统的方法
        if (window.UILanguage && window.UILanguage.getText) {
            return window.UILanguage.getText(key, key);
        }
        
        // 备用方法
        const lang = localStorage.getItem('uiLanguage') || localStorage.getItem('language') || 'zh';
        
        // 尝试从全局语言数据获取
        let translations = window.languageData || window.translations || {};
        
        // 如果没有找到，尝试从UILanguage系统获取
        if (Object.keys(translations).length === 0 && window.UILanguage) {
            translations = window.UILanguage.getLanguageData() || {};
        }
        
        const currentLang = translations[lang] || translations['zh'] || translations;
        
        // 支持嵌套key，如 'podcast.download'
        const keys = key.split('.');
        let value = currentLang;
        for (const k of keys) {
            value = value ? value[k] : undefined;
        }
        
        return value || key;
    }

    // 修复RSS Feed URL - 使用配置加载器
    async fixRssUrl(originalUrl, podcastName = '') {
        if (!originalUrl) return originalUrl;
        
        // 确保配置已加载
        if (window.podcastRSSConfig && !window.podcastRSSConfig.loaded) {
            console.log('🔄 等待RSS修复配置加载...');
            await window.podcastRSSConfig.loadConfig();
        }
        
        // 使用配置加载器的修复功能
        if (window.podcastRSSConfig) {
            return window.podcastRSSConfig.fixRssUrl(originalUrl, podcastName);
        } else {
            console.warn('⚠️ RSS配置加载器不可用，使用原始URL');
            return originalUrl;
        }
    }
}

// 创建全局实例
window.PodcastManager = null;

// 等待语言加载完成后初始化播客管理器
function initPodcastManager() {
    if (!window.PodcastManager) {
        window.PodcastManager = new PodcastManager();
        console.log('播客管理器已初始化，支持国际化');
    }
}

// 等待页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果语言已经加载，直接初始化
    if (window.UILanguage && window.UILanguage.getLanguageData()) {
        initPodcastManager();
    } else {
        // 否则等待语言加载完成
        document.addEventListener('ui-language-loaded', initPodcastManager);
    }
}); 