/* UI改进样式 - 优化AI标签页和模型选择器 */

/* 1. 桌面端AI标签按钮与模型选择器在同一行显示 */
@media (min-width: 769px) {
    .ai-tabs {
        display: flex !important;
        flex-direction: row !important; /* 改为行布局 */
        justify-content: space-between !important; /* 两端对齐 */
        align-items: center !important;
        gap: 5px !important;
        margin-bottom: 5px;
    }
    
    .tab-buttons {
        display: flex;
        justify-content: flex-start !important; /* 左对齐 */
        width: auto !important; /* 不再占满整行 */
        gap: 5px;
        margin-bottom: 0;
        flex-wrap: nowrap;
    }
}

/* 所有屏幕尺寸下的按钮样式 */
.ai-tab-button {
    padding: 6px 12px !important; /* 调整内边距 */
    font-size: 15px !important; /* 调整字号 */
    font-weight: 600;
    border-radius: 4px;
    background-color: #f0f7ff;
    border: none;
    transition: all 0.2s ease;
    height: auto !important;
    text-align: center;
    white-space: nowrap;
    overflow: visible;
    flex: 0 0 auto !important; /* 不拉伸，按照内容宽度显示 */
}

/* 移除默认选中第一个按钮的样式 */
.ai-tab-button {
    background-color: #f0f7ff;
    color: #333;
}

/* 只有active状态的按钮显示蓝色 */
.ai-tab-button.active {
    background-color: #2196F3;
    color: white;
    border: none;
}

.ai-tab-button:hover:not(.active) {
    background-color: #e3f2fd;
}

/* 2. 移动端布局 - 垂直排列且优化间距 */
@media (max-width: 768px) {
    .ai-tabs {
        display: flex !important;
        flex-direction: column !important; /* 移动端垂直排列 */
        gap: 8px !important;
        margin-bottom: 8px !important;
    }
    
    .tab-buttons {
        display: flex;
        justify-content: center !important; /* 居中显示 */
        width: 100%;
        gap: 3px !important; /* 减小间距以适应更多按钮 */
        margin-bottom: 0;
        flex-wrap: nowrap;
        overflow-x: auto;
        padding: 0 2px;
    }
    
    .ai-tab-button {
        font-size: 12px !important; /* 更小的字体 */
        padding: 6px 8px !important; /* 减小内边距 */
    }
    
    /* 模型选择器移动端样式 */
    .model-selector {
        margin-top: 0 !important;
        margin-bottom: 5px !important;
        width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    .current-model {
        font-size: 12px !important; /* 更小的字体 */
        padding: 8px 10px !important;
        border-radius: 4px;
        background-color: #f5f8fa;
        border: 1px solid #e1e8ed;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    #currentModelName {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

/* 3. 调整容器高度 - 修复输入框被挤占问题 */
.container {
    min-height: 400px;
    height: calc(100vh - 200px) !important;
    overflow: auto;
}

.column {
    max-height: 100%;
}

/* 确保内容滚动区域使用额外空间 */
#aiSummary, #aiMindmap, #chapterContent, #aiKnowledgeGraph, .knowledgegraph-container {
    max-height: calc(100% - 50px) !important;
}

/* 聊天输入区域样式 - 确保不与主样式冲突 */
.chat-input-container {
    /* 移除可能冲突的样式，让主样式文件生效 */
}

.chat-input-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    max-width: 100%;
    padding: 0 5px;
}

.chat-input {
    flex: 1;
    min-width: 0; /* 允许输入框缩小 */
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 14px;
}

.send-button {
    flex-shrink: 0;
    padding: 8px 16px;
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.send-button:hover {
    background-color: #1976D2;
}

/* 超小屏幕的特殊调整 */
@media (max-width: 480px) {
    .tab-buttons {
        gap: 2px !important; /* 进一步减小间距 */
    }
    
    .ai-tab-button {
        font-size: 11px !important; /* 进一步减小字体 */
        padding: 5px 5px !important; /* 最小化内边距 */
    }
    
    .current-model {
        font-size: 11px !important;
    }
} 