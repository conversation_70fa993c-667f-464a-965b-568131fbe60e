/**
 * 积分套餐配置
 * 定义可用的充值套餐选项
 */
console.log('payment-packages.js 开始加载');

// 根据不同语言的套餐配置
const PAYMENT_PACKAGES_BY_LANG = {
    // 中文套餐配置
    zh: [
        {
            id: "package_basic",
            name: "Basic Package",
            description: "Suitable for light use",
            credits: 3000,
            price: 5,
            currency: "USD",
            currencySymbol: "$"
        },
        {
            id: "package_standard",
            name: "Standard Package",
            description: "Most popular choice",
            credits: 6500,
            price: 10,
            currency: "USD",
            currencySymbol: "$",
            popular: true
        },
        {
            id: "package_premium",
            name: "Premium Package",
            description: "Ideal for heavy use",
            credits: 10000,
            price: 15,
            currency: "USD",
            currencySymbol: "$"
        }
    ],
    // 英文套餐配置
    en: [
        {
            id: "package_basic",
            name: "Basic Package",
            description: "Suitable for light use",
            credits: 3000,
            price: 5,
            currency: "USD",
            currencySymbol: "$"
        },
        {
            id: "package_standard",
            name: "Standard Package",
            description: "Most popular choice",
            credits: 6500,
            price: 10,
            currency: "USD",
            currencySymbol: "$",
            popular: true
        },
        {
            id: "package_premium",
            name: "Premium Package",
            description: "Ideal for heavy use",
            credits: 10000,
            price: 15,
            currency: "USD",
            currencySymbol: "$"
        }
    ]
};

// 默认使用中文套餐
const PAYMENT_PACKAGES = PAYMENT_PACKAGES_BY_LANG.zh;

/**
 * 获取所有可用的支付套餐
 * @returns {Array} 支付套餐数组
 */
function getPaymentPackages() {
    // 检查 UI 语言系统是否可用
    const hasUILanguage = window.UILanguage && typeof window.UILanguage.getCurrentLanguage === 'function';
    
    // 获取当前语言
    let currentLang = 'zh';  // 默认中文
    if (hasUILanguage) {
        currentLang = window.UILanguage.getCurrentLanguage();
    }
    
    // 确保语言代码有效，只支持中英文
    if (!['zh', 'en'].includes(currentLang)) {
        currentLang = 'zh'; // 不支持的语言默认使用中文
    }
    
    // 返回对应语言的套餐
    return PAYMENT_PACKAGES_BY_LANG[currentLang] || PAYMENT_PACKAGES_BY_LANG.zh;
}

/**
 * 根据ID获取特定套餐
 * @param {string} packageId 套餐ID
 * @returns {Object|null} 套餐对象或null
 */
function getPackageById(packageId) {
    return PAYMENT_PACKAGES.find(pkg => pkg.id === packageId) || null;
}

/**
 * 创建并显示统一的充值模态框
 * @param {Function} onPackageSelected 套餐选择回调
 */
function createRechargeModal(onPackageSelected) {
    // 检查是否已存在充值弹窗
    if (document.getElementById('rechargeModal')) {
        return;
    }
    
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'recharge-modal';
    modal.id = 'rechargeModal';
    
    // 获取套餐列表
    const packages = getPaymentPackages();
    
    // 检查UI语言系统是否可用
    const hasUILanguage = window.UILanguage && typeof window.UILanguage.getText === 'function';
    
    // 获取翻译文本的函数
    const getText = (key) => {
        if (hasUILanguage) {
            return window.UILanguage.getText(key);
        }
        
        // 默认中文文本，如果UI语言系统不可用
        const defaultTexts = {
            'payment.title': '积分套餐',
            'payment.subtitle': '选择适合您的积分套餐',
            'payment.credits': '积分',
            'payment.select': '选择',
            'payment.payment_wechat': '微信支付',
            'payment.popular_tag': '最受欢迎',
            // 修正这两个键名以匹配JSON文件中的键
            'payment.payment_subtitle_qr': '扫描二维码完成支付',
            'payment.payment_subtitle_footer': '完成支付后请刷新页面'
        };
        
        return defaultTexts[key] || key;
    };
    
    // 构建模态框内容 - 使用横向布局和国际化文本
    modal.innerHTML = `
        <div class="recharge-modal-content">
            <div class="recharge-modal-header">
                <h2>${getText('payment.title')}</h2>
                <span class="close-modal">&times;</span>
            </div>
            
            <!-- 添加微信二维码区域 -->
            <div class="wechat-qrcode-section">
                <img src="/assets/wechat-qrcode.png" alt="${getText('payment.payment_wechat')}" class="wechat-qrcode">
                <p class="qrcode-prompt">${getText('payment.payment_subtitle_qr')}</p>
            </div>
            
            <div class="recharge-modal-body">
                <p class="recharge-description">${getText('payment.subtitle')}</p>
                <div class="package-container horizontal">
                    ${packages.map(pkg => `
                        <div class="package-card ${pkg.popular ? 'popular' : ''}" data-package-id="${pkg.id}">
                            ${pkg.popular ? `<div class="popular-badge">${getText('payment.popular_tag')}</div>` : ''}
                            <h3>${pkg.name}</h3>
                            <p class="package-description">${pkg.description}</p>
                            <div class="package-credits">${pkg.credits} ${getText('payment.credits')}</div>
                            <div class="package-price">${pkg.currencySymbol}${pkg.price}</div>
                            <button class="select-package-btn" data-package-id="${pkg.id}">${getText('payment.select')}</button>
                        </div>
                    `).join('')}
                </div>
            </div>
            <div class="recharge-modal-footer">
                <p class="payment-note">${getText('payment.payment_subtitle_footer')}</p>
            </div>
        </div>
    `;
    
    // 添加到文档
    document.body.appendChild(modal);
    
    // 显示模态框
    setTimeout(() => {
        modal.style.display = 'flex';
        modal.style.opacity = '1'; // 确保模态框可见
    }, 10);
    
    // 关闭按钮事件
    const closeBtn = modal.querySelector('.close-modal');
    closeBtn.addEventListener('click', () => {
        closeRechargeModal();
    });
    
    // 点击模态框外部关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeRechargeModal();
        }
    });
    
    // 套餐选择事件
    const selectButtons = modal.querySelectorAll('.select-package-btn');
    selectButtons.forEach(button => {
        button.addEventListener('click', () => {
            const packageId = button.getAttribute('data-package-id');
            const selectedPackage = getPackageById(packageId);
            
            if (selectedPackage && typeof onPackageSelected === 'function') {
                // 显示处理状态，并禁用按钮防止重复点击
                showProcessingState(modal, '正在处理您的选择...');
                button.disabled = true;
                
                // 调用回调函数，但不关闭模态框
                onPackageSelected(selectedPackage);
                // 注意：此处不再自动关闭模态框
                // 关闭操作将由回调函数中根据订单创建成功与否决定
            }
        });
    });
}

/**
 * 显示处理状态
 * @param {HTMLElement} modal 模态框元素
 * @param {string} message 显示的消息
 */
function showProcessingState(modal, message) {
    // 查找是否已存在处理状态元素
    let processingElement = modal.querySelector('.payment-processing-state');
    
    if (!processingElement) {
        // 创建处理状态元素
        processingElement = document.createElement('div');
        processingElement.className = 'payment-processing-state';
        
        // 创建处理状态内容
        processingElement.innerHTML = `
            <div class="processing-spinner"></div>
            <p class="processing-message">${message}</p>
        `;
        
        // 找到套餐容器并隐藏它
        const packageContainer = modal.querySelector('.package-container');
        if (packageContainer) {
            packageContainer.style.display = 'none';
        }
        
        // 找到模态框主体并添加处理状态
        const modalBody = modal.querySelector('.recharge-modal-body');
        if (modalBody) {
            modalBody.appendChild(processingElement);
        }
    } else {
        // 更新已存在的处理状态消息
        const messageElement = processingElement.querySelector('.processing-message');
        if (messageElement) {
            messageElement.textContent = message;
        }
    }
    
    // 禁用关闭按钮
    const closeBtn = modal.querySelector('.close-modal');
    if (closeBtn) {
        closeBtn.style.opacity = '0.5';
        closeBtn.style.pointerEvents = 'none';
    }
}

/**
 * 关闭充值弹窗
 */
function closeRechargeModal() {
    const modal = document.getElementById('rechargeModal');
    if (modal) {
        // 先设置透明度为0，实现淡出效果
        modal.style.opacity = '0';
        // 等待动画完成后移除元素
        setTimeout(() => {
            if (modal.parentNode) {
                document.body.removeChild(modal);
            }
        }, 300);
    }
}

// 导出模块函数
window.paymentPackages = {
    getPaymentPackages,
    getPackageById,
    createRechargeModal,
    closeRechargeModal,
    showProcessingState
};

console.log('payment-packages.js 加载完成，函数已导出到 window.paymentPackages');