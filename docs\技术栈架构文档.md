# AudioPilot - 技术栈架构文档

## 1. 架构概览

### 1.1 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Frontend)                      │
├─────────────────────────────────────────────────────────────┤
│  Web浏览器 (HTML5 + CSS3 + JavaScript ES6+)                  │
│  ├── 音频录制与播放 (Web Audio API + MediaRecorder)         │
│  ├── 用户界面 (原生JavaScript + 响应式CSS)                   │
│  ├── 状态管理 (本地存储 + 内存状态)                         │
│  └── 多语言支持 (i18n动态加载)                              │
└─────────────────────────────────────────────────────────────┘
                                │
                                │ HTTPS/WebSocket
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      应用服务层 (Backend)                     │
├─────────────────────────────────────────────────────────────┤
│  Flask Web框架 (Python 3.10+)                              │
│  ├── 路由控制 (Blueprint模块化)                             │
│  ├── 中间件 (CORS + 限流 + 认证)                           │
│  ├── 音频处理 (pydub + 文件管理)                           │
│  └── 流式响应 (Server-Sent Events)                         │
└─────────────────────────────────────────────────────────────┘
                                │
                                │ HTTP/HTTPS
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      AI模型层 (AI Services)                   │
├─────────────────────────────────────────────────────────────┤
│  多模型集成架构                                              │
│  ├── 智谱AI (GLM-4-Flash/Air) - 中文优化                    │
│  ├── 千问 (Qwen-Turbo/Plus) - 通用模型                     │
│  ├── Claude 3.5 (Haiku/Sonnet) - 推理能力                  │
│  ├── DeepSeek-V3 - 代码理解                                │
│  ├── Grok-2 - 实时信息                                     │
│  └── Gemini 2.0 Flash - 多模态                             │
└─────────────────────────────────────────────────────────────┘
                                │
                                │ PostgreSQL协议
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据层 (Database)                        │
├─────────────────────────────────────────────────────────────┤
│  Supabase (PostgreSQL + 实时功能)                           │
│  ├── 用户管理 (Authentication + 权限)                       │
│  ├── 积分系统 (实时余额 + 使用记录)                         │
│  ├── 音频元数据 (文件信息 + 分析结果)                       │
│  └── 系统配置 (多语言 + 模型配置)                           │
└─────────────────────────────────────────────────────────────┘
                                │
                                │ 文件存储
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      存储层 (Storage)                         │
├─────────────────────────────────────────────────────────────┤
│  临时文件存储 (本地temp目录)                                 │
│  ├── 音频文件缓存 (上传/录制/片段)                          │
│  ├── 智能清理机制 (启动清理 + 资源限制)                     │
│  └── 文件类型管理 (分类存储 + 生命周期)                     │
└─────────────────────────────────────────────────────────────┘
```

## 2. 前端技术栈

### 2.1 核心技术
```yaml
基础框架:
  - HTML5: 语义化标签、Web Components
  - CSS3: Grid布局、Flexbox、自定义属性
  - JavaScript: ES6+、模块化、异步编程

音频处理:
  - Web Audio API: 音频录制与实时处理
  - MediaRecorder API: 浏览器原生录音
  - Audio元素: 音频播放控制

用户界面:
  - 原生JavaScript: 无第三方框架依赖
  - 响应式设计: Mobile-First方法
  - 组件化: 模块化JavaScript类设计

状态管理:
  - localStorage: 用户偏好持久化
  - sessionStorage: 临时会话数据
  - 内存状态: 实时音频状态管理
```

### 2.2 关键模块
```javascript
// 核心应用类
class AudioApp {
  constructor() {
    this.uploadedFileId = null;
    this.recordingFileId = null;
    this.currentSnippetFileId = null;
    this.activePlayer = null;
    this.isRecording = false;
    // ... 状态管理
  }
  
  // 音频文件管理
  async uploadAudio(file) { /* ... */ }
  async startRecording() { /* ... */ }
  async stopRecording() { /* ... */ }
  
  // AI交互
  async analyzeAudio(fileId, model) { /* ... */ }
  async askQuestion(question, context) { /* ... */ }
}

// 多语言支持
class LanguageManager {
  constructor() {
    this.currentLanguage = 'zh';
    this.translations = new Map();
  }
  
  async loadLanguage(lang) { /* ... */ }
  getText(key, defaultText) { /* ... */ }
}
```

### 2.3 文件结构
```
public/
├── index.html              # 主页面
├── main.html              # 应用主界面
├── css/
│   ├── styles.css         # 主样式文件
│   ├── landing.css        # 落地页样式
│   └── components/        # 组件样式
├── js/
│   ├── audio-app.js       # 音频应用核心
│   ├── auth.js           # 用户认证
│   ├── credits.js        # 积分管理
│   ├── language-settings.js # 多语言
│   └── components/       # 功能组件
├── lang/
│   ├── zh.json           # 中文语言包
│   ├── en.json           # 英文语言包
│   └── ...               # 其他语言包
└── assets/
    ├── images/           # 图片资源
    └── icons/            # 图标资源
```

## 3. 后端技术栈

### 3.1 核心框架
```yaml
Web框架:
  - Flask: 2.3.3 (轻量级Web框架)
  - Flask-CORS: 4.0.0 (跨域请求处理)
  - Flask-Limiter: 3.5.0 (API限流控制)

音频处理:
  - pydub: 0.25.1 (音频格式转换与处理)
  - 支持格式: MP3, WAV, M4A, OGG, FLAC

HTTP客户端:
  - requests: 2.31.0 (AI模型API调用)
  - urllib3: HTTP连接池管理

数据处理:
  - python-dotenv: 1.0.0 (环境变量管理)
  - python-dateutil: 2.8.2 (日期时间处理)
  - uuid: 1.30 (唯一标识符生成)
```

### 3.2 应用架构
```python
# 主应用结构
app/
├── app.py                 # Flask应用入口
├── requirements.txt       # 依赖包管理
├── api/
│   ├── __init__.py
│   ├── config.py         # 配置管理
│   ├── auth.py           # 用户认证
│   ├── credits.py        # 积分系统
│   ├── model_facade.py   # AI模型统一接口
│   ├── models_config.py  # 模型配置管理
│   └── rate_limit.py     # 限流控制
├── models/               # AI模型集成
│   ├── zhipu_api.py     # 智谱AI
│   ├── qwen_api.py      # 千问
│   ├── claude_api.py    # Claude
│   ├── deepseek_api.py  # DeepSeek
│   └── grok_api.py      # Grok
└── temp/                # 临时文件存储
```

### 3.3 核心功能模块

#### 3.3.1 音频文件管理器
```python
class AudioFileManager:
    """音频文件管理器 - 实现正确的文件分类和清理逻辑"""
    
    def __init__(self):
        self.audio_files = {}  # file_id -> file_path
        self.current_recording_file = None
        self.current_snippet_file = None
        self.uploaded_files = []
        self.max_total_files = 3  # 资源控制
    
    async def startup_cleanup(self):
        """应用启动时的清理工作"""
        # 启动清理所有临时文件
        
    async def check_resource_limits(self):
        """检查资源限制，确保最多3个文件并存"""
        # 按优先级清理文件
```

#### 3.3.2 AI模型门面
```python
class ModelFacade:
    """统一的AI模型调用接口"""
    
    def __init__(self):
        self.model_configs = MODEL_CONFIG
    
    def call_model_api(self, model_id, prompt, system_message):
        """统一的模型调用接口"""
        model_config = self.get_model_config(model_id)
        module = importlib.import_module(model_config['module'])
        func = getattr(module, model_config['function'])
        return func(prompt, system_message, model_id)
```

#### 3.3.3 积分系统
```python
class CreditsSystem:
    """积分系统 - 支持缓存和实时同步"""
    
    def __init__(self):
        self.cache = {}  # 用户积分缓存
        self.cache_ttl = 300  # 5分钟缓存
    
    async def get_user_credits(self, user_id):
        """获取用户积分（支持缓存）"""
        
    async def deduct_credits(self, user_id, amount, operation_type):
        """扣减积分并记录使用"""
        
    async def sync_credits(self, user_id):
        """同步积分到数据库"""
```

## 4. AI模型集成架构

### 4.1 多模型支持策略
```yaml
模型配置管理:
  - 中心化配置: models_config.py统一管理
  - 动态路由: 根据模型ID动态调用对应API
  - 错误回退: 模型不可用时的智能降级

支持的模型:
  智谱AI:
    - GLM-4-Flash: 高速响应，中文优化
    - GLM-4-Air: 轻量级模型，成本优化
  
  千问系列:
    - Qwen-Turbo: 通用对话模型
    - Qwen-Plus: 高级推理模型
  
  Claude 3.5:
    - Haiku: 快速响应
    - Sonnet: 平衡性能
  
  其他模型:
    - DeepSeek-V3: 代码理解专家
    - Grok-2: 实时信息处理
    - Gemini 2.0 Flash: 多模态支持
```

### 4.2 模型调用流程
```python
# 统一的模型调用接口
def call_model_api(model_id, prompt, system_message):
    """
    统一模型调用流程:
    1. 验证模型配置
    2. 检查环境变量
    3. 限流控制
    4. API调用
    5. 流式响应处理
    6. 错误处理和重试
    """
    
    # 1. 获取模型配置
    model_config = MODEL_CONFIG.get(model_id)
    
    # 2. 动态导入模块
    module = importlib.import_module(model_config['module'])
    func = getattr(module, model_config['function'])
    
    # 3. 调用API
    return func(prompt, system_message, model_id)
```

### 4.3 流式响应处理
```python
# 流式响应统一格式
def create_streaming_response(prompt, system_message, frontend_model_id):
    """创建流式响应生成器"""
    
    def generate():
        # Token使用统计
        token_usage = {"prompt_tokens": 0, "completion_tokens": 0}
        
        try:
            # API调用
            for chunk in api_stream:
                if chunk:
                    yield chunk  # 实时输出内容
                    
            # 最后输出Token使用情况
            yield format_token_usage(token_usage)
            
        except Exception as e:
            yield format_error_message(str(e))
    
    return Response(stream_with_context(generate()), 
                   content_type='text/plain; charset=utf-8')
```

## 5. 数据库设计

### 5.1 Supabase集成
```yaml
数据库类型: PostgreSQL (Supabase托管)
特性支持:
  - 实时订阅: 积分变动实时通知
  - 行级安全: RLS策略保护用户数据
  - 自动备份: 多地域备份保护
  - 扩展支持: PostGIS、全文搜索等

连接配置:
  - 连接池: 自动管理连接
  - SSL加密: 数据传输安全
  - 读写分离: 提升查询性能
```

### 5.2 数据表设计
```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    profile JSONB
);

-- 积分账户表
CREATE TABLE user_credits (
    user_id UUID REFERENCES users(id),
    current_balance INTEGER DEFAULT 30,
    total_earned INTEGER DEFAULT 30,
    total_spent INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (user_id)
);

-- 积分使用记录表
CREATE TABLE credit_usage_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    operation_type VARCHAR(50) NOT NULL,
    model_id VARCHAR(100),
    credits_used INTEGER NOT NULL,
    token_usage JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 音频文件元数据表
CREATE TABLE audio_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    original_filename VARCHAR(255),
    file_size INTEGER,
    duration_seconds INTEGER,
    file_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    analysis_results JSONB
);
```

### 5.3 缓存策略
```python
# 积分缓存管理
class CreditsCache:
    def __init__(self):
        self.cache = {}  # {user_id: {balance: int, timestamp: float}}
        self.cache_ttl = 300  # 5分钟
    
    async def get_cached_credits(self, user_id):
        """获取缓存的积分"""
        if user_id in self.cache:
            cached_data = self.cache[user_id]
            if time.time() - cached_data['timestamp'] < self.cache_ttl:
                return cached_data['balance']
        return None
    
    async def update_cache(self, user_id, balance):
        """更新积分缓存"""
        self.cache[user_id] = {
            'balance': balance,
            'timestamp': time.time()
        }
```

## 6. 系统安全架构

### 6.1 认证授权
```yaml
认证方式:
  - JWT Token: 无状态会话管理
  - OAuth集成: 支持第三方登录
  - 会话管理: HttpOnly Cookie + CSRF保护

授权控制:
  - 角色权限: 基于角色的访问控制
  - 资源隔离: 用户数据严格隔离
  - API密钥: 模型调用密钥管理
```

### 6.2 安全措施
```python
# 限流控制
from flask_limiter import Limiter

limiter = Limiter(
    app,
    key_func=get_user_identifier,
    default_limits=["1000 per hour", "100 per minute"]
)

# 特定端点限流
@app.route('/api/analyze', methods=['POST'])
@limiter.limit("10 per minute")
def analyze_audio():
    """音频分析接口 - 限制频率防止滥用"""
    pass

# CSRF保护
@app.before_request
def csrf_protect():
    if request.method == "POST":
        token = request.headers.get('X-CSRF-Token')
        if not token or not validate_csrf_token(token):
            abort(403)
```

### 6.3 数据安全
```yaml
传输加密:
  - HTTPS: 强制SSL/TLS加密
  - WSS: WebSocket安全连接
  - 证书管理: 自动续期和更新

存储安全:
  - 数据库加密: 敏感字段加密存储
  - 临时文件: 定期清理和安全删除
  - 备份加密: 备份数据加密保护

隐私保护:
  - 数据最小化: 只收集必要数据
  - 数据匿名: 分析数据去标识化
  - 合规性: GDPR/CCPA合规
```

## 7. 部署架构

### 7.1 容器化部署
```dockerfile
# Dockerfile
FROM python:3.10-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:8080", "app:app"]
```

### 7.2 云平台支持
```yaml
Fly.io部署:
  - 自动扩缩容: 基于负载自动调整实例
  - 全球分发: 多地域部署优化延迟
  - 健康检查: 自动故障检测和恢复
  - 零停机部署: 滚动更新保证服务连续性

Docker部署:
  - 环境一致性: 开发、测试、生产环境统一
  - 资源隔离: 容器级别的资源管理
  - 快速部署: 自动化CI/CD流程
  - 版本管理: 镜像版本控制和回滚

本地部署:
  - 开发环境: 快速本地调试
  - 私有化部署: 企业内网环境支持
  - 配置管理: 环境变量和配置文件管理
```

### 7.3 监控和日志
```python
# 日志配置
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

# 性能监控
@app.before_request
def before_request():
    g.start_time = time.time()

@app.after_request
def after_request(response):
    duration = time.time() - g.start_time
    logger.info(f"请求处理时间: {duration:.3f}s")
    return response
```

## 8. 性能优化

### 8.1 前端优化
```yaml
资源优化:
  - 代码分割: 按需加载减少初始包大小
  - 资源压缩: Gzip/Brotli压缩传输
  - 缓存策略: 浏览器缓存和CDN加速
  - 图片优化: WebP格式和响应式图片

性能优化:
  - 懒加载: 图片和组件按需加载
  - 防抖节流: 用户交互优化
  - 虚拟滚动: 大列表性能优化
  - Web Workers: 音频处理后台执行
```

### 8.2 后端优化
```python
# 异步处理
import asyncio
from concurrent.futures import ThreadPoolExecutor

class AsyncProcessor:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=10)
    
    async def process_audio(self, file_path):
        """异步音频处理"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor, 
            self._process_audio_sync, 
            file_path
        )

# 缓存优化
from functools import lru_cache

@lru_cache(maxsize=128)
def get_model_config(model_id):
    """模型配置缓存"""
    return MODEL_CONFIG.get(model_id)
```

### 8.3 数据库优化
```sql
-- 索引优化
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_credit_usage_user_id ON credit_usage_records(user_id);
CREATE INDEX CONCURRENTLY idx_credit_usage_created_at ON credit_usage_records(created_at);

-- 分区表（大量数据时）
CREATE TABLE credit_usage_records_y2025m01 
    PARTITION OF credit_usage_records 
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

## 9. 可扩展性设计

### 9.1 水平扩展
```yaml
应用层扩展:
  - 无状态设计: 应用实例可任意增减
  - 负载均衡: 智能流量分发
  - 会话共享: Redis集群会话存储

数据层扩展:
  - 读写分离: 主从复制分担读压力
  - 分库分表: 按用户或时间维度分片
  - 缓存集群: Redis Cluster高可用
```

### 9.2 功能扩展
```python
# 插件化架构
class PluginManager:
    def __init__(self):
        self.plugins = {}
    
    def register_plugin(self, name, plugin_class):
        """注册插件"""
        self.plugins[name] = plugin_class
    
    def get_plugin(self, name):
        """获取插件实例"""
        return self.plugins.get(name)

# 模型扩展接口
class BaseModelAPI:
    """模型API基类"""
    
    def __init__(self, api_key, base_url):
        self.api_key = api_key
        self.base_url = base_url
    
    def create_streaming_response(self, prompt, system_message):
        """创建流式响应 - 子类实现"""
        raise NotImplementedError
```

## 10. 维护和运维

### 10.1 自动化运维
```yaml
CI/CD流程:
  - 代码检查: ESLint, Flake8自动检查
  - 单元测试: 自动化测试覆盖
  - 构建部署: Docker镜像自动构建
  - 健康检查: 部署后自动验证

监控告警:
  - 系统监控: CPU、内存、磁盘使用率
  - 应用监控: 响应时间、错误率、吞吐量
  - 业务监控: 用户活跃、转化率、收入
  - 告警通知: 邮件、短信、钉钉通知
```

### 10.2 数据备份
```bash
#!/bin/bash
# 数据库备份脚本
pg_dump \
  --host=$DB_HOST \
  --port=$DB_PORT \
  --username=$DB_USER \
  --dbname=$DB_NAME \
  --format=custom \
  --file=backup_$(date +%Y%m%d_%H%M%S).sql

# 上传到云存储
aws s3 cp backup_*.sql s3://backup-bucket/database/
```

---

## 技术栈总结

### 前端技术栈
- **核心**: HTML5 + CSS3 + JavaScript ES6+
- **音频**: Web Audio API + MediaRecorder API
- **工具**: 原生JavaScript、响应式设计、模块化架构

### 后端技术栈
- **框架**: Flask 2.3.3 + Python 3.10+
- **音频**: pydub 0.25.1
- **HTTP**: requests 2.31.0 + Flask-CORS + Flask-Limiter

### AI模型集成
- **智谱AI**: GLM-4-Flash, GLM-4-Air
- **千问**: Qwen-Turbo, Qwen-Plus  
- **Claude**: 3.5-Haiku, 3.5-Sonnet
- **其他**: DeepSeek-V3, Grok-2, Gemini 2.0 Flash

### 数据存储
- **数据库**: Supabase (PostgreSQL)
- **缓存**: 内存缓存 + 积分缓存系统
- **文件**: 本地临时存储 + 智能清理

### 部署运维
- **容器**: Docker + Dockerfile
- **云平台**: Fly.io + 自动扩缩容
- **监控**: 日志系统 + 性能监控 + 健康检查

**文档版本**: v1.0  
**创建日期**: 2025年6月14日  
**维护团队**: 技术架构组
