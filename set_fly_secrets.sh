#!/bin/bash
# 设置fly.io的环境变量

# 确保已登录fly.io
echo "确保您已经登录到fly.io (flyctl auth login)"

# 设置生产环境变量
echo "正在设置fly.io环境变量..."

# 基本环境配置
flyctl secrets set ENVIRONMENT=production \
                  APP_URL=https://reader-app.fly.dev \
                  PORT=8080 \
                  DEBUG=false

# 支付相关配置
# 注意: 请替换下面的XXX为您的实际API密钥
# flyctl secrets set CREEM_API_KEY=XXX \
#                  CREEM_WEBHOOK_SECRET=XXX

echo "环境变量设置完成。"
echo "注意: 请手动设置敏感的API密钥:"
echo "flyctl secrets set CREEM_API_KEY=你的密钥值"
echo "flyctl secrets set CREEM_WEBHOOK_SECRET=你的密钥值"
