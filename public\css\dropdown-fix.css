/* 修复模型选择器显示问题 */
.model-selector {
    position: relative;
    margin-left: auto;
    margin-right: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    height: 40px;
    width: 170px;
    z-index: 1000; /* 提高z-index确保显示在最上层 */
    visibility: visible;
    opacity: 1;
}

.current-model {
    padding: 4px 8px; /* 原为 8px 15px，缩小内边距 */
    font-size: 13px;
    color: #555;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s;
    visibility: visible;
    opacity: 1;
}

.current-model #currentModelName {
    flex: 1 1 auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
}

.model-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    border-radius: 6px;
    width: 170px;
    z-index: 1500; /* 提高z-index确保下拉菜单显示在最上层 */
    margin-top: 5px;
    border: 1px solid #e0e0e0;
    overflow: visible;
    display: none; /* 默认隐藏，由JS控制显示 */
}

.model-dropdown.active {
    display: block;
}

/* 确保AI标签页布局正确 */
.ai-tabs {
    display: flex;
    margin-bottom: 5px;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.tab-buttons {
    display: flex;
    gap: 5px;
}

.ai-tab-button {
    padding: 6px 12px;
    background-color: #f0f0f0;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #555;
}

.ai-tab-button.active {
    background-color: #2196F3;
    color: white;
}

/* 模型列表样式 */
.model-list {
    max-height: 300px;
    overflow-y: auto;
}

.model-item {
    padding: 8px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 13px; /* 调整这里的字号 */
}

.model-item:hover {
    background-color: #f5f5f5;
}

.model-item.selected {
    background-color: #e3f2fd;
    color: #2196F3;
}

/* 确保下拉图标可见 */
.dropdown-icon {
    margin-left: 5px;
    display: inline-block;
    visibility: visible;
}
