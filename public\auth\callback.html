<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录处理中...</title>
    <style>
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .loading-container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="spinner"></div>
        <h2>登录处理中...</h2>
        <p>正在验证您的Google账户，请稍候...</p>
    </div>

    <!-- 加载必要的配置和库 -->
    <script src="../js/env-config.js"></script>
    <script src="../js/lib/supabase.min.js"></script>
    <script src="../js/supabase-client.js"></script>
    
    <script>
        (async function() {
            try {
                console.log('OAuth回调页面加载，开始处理认证...');
                
                // 等待Supabase客户端初始化
                let attempts = 0;
                while (!window.supabaseClient && attempts < 50) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }
                
                if (!window.supabaseClient) {
                    throw new Error('Supabase客户端初始化失败');
                }
                
                // 让Supabase处理OAuth回调
                const { data, error } = await window.supabaseClient.auth.getSession();
                
                if (error) {
                    console.error('获取会话失败:', error);
                    throw error;
                }
                
                if (data.session) {
                    console.log('OAuth登录成功，会话已建立');
                    
                    // 保存认证信息到localStorage
                    localStorage.setItem('authToken', data.session.access_token);
                    localStorage.setItem('refreshToken', data.session.refresh_token);
                    localStorage.setItem('userData', JSON.stringify(data.session.user));
                    localStorage.setItem('isAuthenticated', 'true');
                    localStorage.setItem('userEmail', data.session.user.email);
                    localStorage.setItem('userId', data.session.user.id);
                    
                    // 关键修复：与后端同步session
                    try {
                        console.log('正在与后端同步认证状态...');
                        const syncResponse = await fetch('/api/auth/set-session', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${data.session.access_token}`
                            },
                            body: JSON.stringify({
                                access_token: data.session.access_token,
                                refresh_token: data.session.refresh_token,
                                user: data.session.user,
                                session: data.session
                            }),
                            credentials: 'include'
                        });
                        
                        if (syncResponse.ok) {
                            console.log('✅ 后端session同步成功');
                        } else {
                            console.warn('⚠️ 后端session同步失败，但继续登录流程');
                        }
                    } catch (syncError) {
                        console.warn('⚠️ 后端session同步出错，但继续登录流程:', syncError);
                    }
                    
                    // 获取最终重定向URL
                    const urlParams = new URLSearchParams(window.location.search);
                    const finalRedirect = urlParams.get('final_redirect') || '/main.html';
                    
                    console.log(`认证成功，重定向到: ${finalRedirect}`);
                    window.location.replace(finalRedirect);
                } else {
                    console.error('未找到有效会话');
                    throw new Error('认证失败：未找到有效会话');
                }
            } catch (error) {
                console.error('OAuth回调处理失败:', error);
                
                // 显示错误信息
                document.querySelector('.loading-container').innerHTML = `
                    <h2>登录失败</h2>
                    <p>错误：${error.message}</p>
                    <p><a href="/" style="color: white; text-decoration: underline;">返回首页重试</a></p>
                `;
            }
        })();
    </script>
</body>
</html> 