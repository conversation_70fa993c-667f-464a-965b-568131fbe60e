/**
 * 启动Google登录流程
 * 此函数由登录页面的按钮调用
 */
async function signInWithGoogle() {
    console.log('🚀 [Google Auth] 启动Google登录流程... v3.0');
    
    try {
        // 确保Supabase客户端已加载
        if (!window.supabaseClient) {
            throw new Error('Supabase客户端未初始化');
        }
        
        // 正确的重定向URL：直接到main.html，让Supabase处理OAuth回调
        const redirectUrl = `${window.location.origin}/main.html`;
        console.log(`🚀 [Google Auth] 重定向URL: ${redirectUrl}`);
        
        const { data, error } = await window.supabaseClient.auth.signInWithOAuth({
            provider: 'google',
            options: {
                // 修复：直接重定向到main.html，Supabase会自动处理OAuth
                redirectTo: redirectUrl
            }
        });

        if (error) {
            console.error('❌ [Google Auth] OAuth请求失败:', error.message);
            alert(`Google登录请求失败: ${error.message}`);
        } else {
            console.log('✅ [Google Auth] OAuth请求已发送，等待重定向...');
        }
    } catch (e) {
        console.error('💥 [Google Auth] 严重错误:', e);
        alert('启动Google登录时发生未知错误，请检查控制台。');
    }
}

/**
 * OAuth回调处理器
 * 优化版：去除DOM依赖，异步处理积分预加载，提升性能
 */
(function() {
    // 定义一个全局Promise，主应用可以等待此Promise完成
    window.oAuthCallbackPromise = new Promise((resolve) => {
        // 如果URL的#hash部分不包含access_token，说明不是OAuth回调
        if (!window.location.hash.includes('access_token')) {
            return resolve();
        }

        console.log('⚡ [Google Auth] 检测到OAuth回调，开始快速处理...');
        
        // 从URL的#hash中解析出参数
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = hashParams.get('access_token');
        const refreshToken = hashParams.get('refresh_token');

        if (!accessToken) {
            console.error('❌ [Google Auth] OAuth回调错误：URL中未找到access_token');
            return resolve();
        }

        // 使用获取到的令牌在Supabase客户端中设置会话
        window.supabaseClient.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken,
        }).then(async ({ data, error }) => {
            if (error || !data.session) {
                console.error('❌ [Google Auth] 设置会话失败:', error);
                return resolve();
            }

            console.log('✅ [Google Auth] 成功建立会话');
            const { session } = data;

            // 立即保存认证信息到localStorage
            localStorage.setItem('authToken', session.access_token);
            localStorage.setItem('refreshToken', session.refresh_token);
            localStorage.setItem('userData', JSON.stringify(session.user));
            localStorage.setItem('isAuthenticated', 'true');
            localStorage.setItem('userEmail', session.user.email);
            localStorage.setItem('userId', session.user.id);
            console.log('⚡ [Google Auth] 认证信息已快速保存');

            // 立即清除URL hash，避免重复处理
            if (window.history.replaceState) {
                const url = new URL(window.location.href);
                window.history.replaceState({}, document.title, url.origin + url.pathname);
                console.log('⚡ [Google Auth] URL hash已清除');
            }
            
            // 异步预加载积分，不阻塞页面流程
            Promise.resolve().then(async () => {
                try {
                    console.log('🔄 [Google Auth] 开始异步预加载积分...');
                    const resp = await fetch('/api/credits/get-fast', {
                        credentials: 'include',
                        headers: {
                            'Authorization': `Bearer ${session.access_token}`
                        }
                    });
                    const creditsJson = await resp.json();
                    if (resp.ok && creditsJson.success) {
                        // 使用与CreditsManager一致的格式和key存储积分
                        const creditsData = {
                            credits: creditsJson.credits,
                            userId: session.user.id,
                            timestamp: Date.now()
                        };
                        localStorage.setItem('userCredits', JSON.stringify(creditsData));
                        console.log(`✅ [Google Auth] 积分预加载成功: ${creditsJson.credits}`);
                        
                        // 触发积分预加载完成事件
                        document.dispatchEvent(new CustomEvent('credits-preloaded', {
                            detail: { credits: creditsJson.credits }
                        }));
                    } else {
                        console.warn('⚠️ [Google Auth] 积分预加载失败:', creditsJson.error || creditsJson.message);
                    }
                } catch (e) {
                    console.warn('⚠️ [Google Auth] 积分预加载异常:', e);
                }
            });
            
            // 立即resolve，不等待积分预加载
            resolve();
        }).catch(err => {
            console.error('❌ [Google Auth] 设置会话过程中发生严重错误:', err);
            resolve();
        });
    });
})(); 