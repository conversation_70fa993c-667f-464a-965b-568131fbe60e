<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录重构测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status-box {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .log-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 登录重构测试</h1>
    
    <div class="status-box info">
        <h3>重构说明</h3>
        <p><strong>问题诊断</strong>：原系统存在多个认证管理器同时运行，造成竞态条件，导致登录后自动退出。</p>
        <p><strong>解决方案</strong>：创建统一认证管理器，移除冲突的认证检查，基于Supabase事件驱动模式。</p>
    </div>

    <div class="status-box" id="authStatus">
        <h3>🔐 认证状态</h3>
        <p>状态：<span id="authStatusText">检查中...</span></p>
        <p>用户：<span id="userInfo">-</span></p>
        <p>最后更新：<span id="lastUpdate">-</span></p>
    </div>

    <div class="status-box">
        <h3>🧪 测试操作</h3>
        <button class="button" onclick="testAuthState()">检查认证状态</button>
        <button class="button" onclick="refreshAuthState()">刷新认证状态</button>
        <button class="button" onclick="goToMain()">前往主页面</button>
        <button class="button" onclick="clearLogs()">清除日志</button>
    </div>

    <div class="status-box">
        <h3>📋 重构改动</h3>
        <ul>
            <li>✅ 新增：unified-auth-manager.js（统一认证管理器）</li>
            <li>✅ 修改：main.html（移除多重认证检查，简化初始化逻辑）</li>
            <li>✅ 修改：logout.js（支持统一认证管理器）</li>
            <li>❌ 移除：auth-state-manager.js、oauth-manager.js、token-refresh.js 的引用</li>
            <li>✅ 简化：防跳转保护机制（不再需要复杂的延迟和重试）</li>
        </ul>
    </div>

    <div class="status-box">
        <h3>📈 预期效果</h3>
        <ul>
            <li>🎯 彻底解决"登录后自动退出"问题</li>
            <li>🚀 提高登录稳定性和响应速度</li>
            <li>🔧 简化代码维护和调试</li>
            <li>📊 基于事件驱动，状态一致性更强</li>
        </ul>
    </div>

    <div class="log-box" id="logBox">
        <strong>实时日志：</strong><br>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="/env.js"></script>
    <script src="js/lib/supabase.min.js"></script>
    <script src="js/supabase-client.js"></script>
    <script src="js/unified-auth-manager.js"></script>

    <script>
        // 日志函数
        function log(message) {
            const logBox = document.getElementById('logBox');
            const timestamp = new Date().toLocaleTimeString();
            logBox.innerHTML += `<span style="color: #666">[${timestamp}]</span> ${message}<br>`;
            logBox.scrollTop = logBox.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logBox').innerHTML = '<strong>实时日志：</strong><br>';
        }

        // 更新认证状态显示
        function updateAuthDisplay(authState) {
            const statusElement = document.getElementById('authStatus');
            const statusText = document.getElementById('authStatusText');
            const userInfo = document.getElementById('userInfo');
            const lastUpdate = document.getElementById('lastUpdate');

            if (authState.isAuthenticated) {
                statusElement.className = 'status-box success';
                statusText.textContent = '✅ 已登录';
                userInfo.textContent = authState.user ? authState.user.email : '用户信息不可用';
            } else {
                statusElement.className = 'status-box error';
                statusText.textContent = '❌ 未登录';
                userInfo.textContent = '-';
            }

            lastUpdate.textContent = authState.lastUpdate || '-';
        }

        // 测试函数
        async function testAuthState() {
            log('🧪 开始测试认证状态...');
            
            if (!window.unifiedAuthManager) {
                log('❌ 统一认证管理器不可用');
                return;
            }

            try {
                const authState = window.unifiedAuthManager.getAuthState();
                log(`📋 认证状态: ${JSON.stringify(authState, null, 2)}`);
                updateAuthDisplay(authState);
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
            }
        }

        async function refreshAuthState() {
            log('🔄 刷新认证状态...');
            
            if (!window.unifiedAuthManager) {
                log('❌ 统一认证管理器不可用');
                return;
            }

            try {
                const authState = await window.unifiedAuthManager.refreshAuthState();
                log(`🔄 刷新完成: ${JSON.stringify(authState, null, 2)}`);
                updateAuthDisplay(authState);
            } catch (error) {
                log(`❌ 刷新失败: ${error.message}`);
            }
        }

        function goToMain() {
            log('🚀 跳转到主页面...');
            window.location.href = '/main.html';
        }

        // 监听认证状态变化
        document.addEventListener('DOMContentLoaded', async function() {
            log('📋 测试页面开始初始化...');

            // 等待统一认证管理器初始化
            let attempts = 0;
            while (!window.unifiedAuthManager?.isInitialized && attempts < 50) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }

            if (window.unifiedAuthManager?.isInitialized) {
                log('✅ 统一认证管理器已初始化');
                
                // 添加认证状态监听器
                window.unifiedAuthManager.addListener((event, session, authState) => {
                    log(`🔔 认证状态变化: ${event}, 已认证: ${authState.isAuthenticated}`);
                    updateAuthDisplay(authState);
                });

                // 获取初始状态
                const authState = window.unifiedAuthManager.getAuthState();
                updateAuthDisplay(authState);
                log(`📋 初始认证状态: ${JSON.stringify(authState, null, 2)}`);
            } else {
                log('❌ 统一认证管理器初始化失败');
            }
        });

        // 页面加载完成时显示重构完成提示
        window.addEventListener('load', function() {
            log('🎉 登录重构测试页面加载完成！');
            log('📝 您可以使用上面的按钮测试新的认证系统');
        });
    </script>
</body>
</html>
