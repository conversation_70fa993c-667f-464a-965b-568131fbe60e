// 极端书签显示测试
console.log('🔥 极端书签显示测试脚本已加载');

window.extremeBookmarkTest = function() {
    console.log('🔥 开始极端书签显示测试');
    
    const progressBar = document.getElementById('uploadedProgressBar');
    const progressContainer = progressBar?.parentElement;
    
    console.log('🔥 进度条元素:', progressBar);
    console.log('🔥 进度条容器:', progressContainer);
    
    if (!progressBar) {
        console.error('🔥 未找到进度条');
        return;
    }
    
    // 强制修改进度条和容器样式确保可见
    if (progressContainer) {
        progressContainer.style.cssText += `
            overflow: visible !important;
            position: relative !important;
            z-index: 1000 !important;
            background: yellow !important;
            border: 2px solid red !important;
            min-height: 50px !important;
            padding: 20px 0 !important;
        `;
        console.log('🔥 进度条容器样式已强制修改');
    }
    
    progressBar.style.cssText += `
        overflow: visible !important;
        position: relative !important;
        z-index: 1001 !important;
        background: lime !important;
        border: 2px solid blue !important;
        height: 8px !important;
        margin: 15px 4px !important;
    `;
    console.log('🔥 进度条样式已强制修改');
    
    // 清除现有测试标记
    const existingTests = progressBar.querySelectorAll('.extreme-test-marker');
    existingTests.forEach(marker => marker.remove());
    
    // 创建超大超明显的测试标记
    const marker = document.createElement('div');
    marker.className = 'extreme-test-marker';
    marker.style.cssText = `
        position: absolute !important;
        top: -30px !important;
        left: 50% !important;
        width: 40px !important;
        height: 60px !important;
        background: red !important;
        border: 5px solid black !important;
        border-radius: 8px !important;
        z-index: 99999 !important;
        transform: translateX(-50%) !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        box-shadow: 0 0 20px rgba(255,0,0,1) !important;
        font-size: 12px !important;
        color: white !important;
        text-align: center !important;
        line-height: 60px !important;
    `;
    marker.textContent = 'TEST';
    marker.title = '极端测试标记';
    
    progressBar.appendChild(marker);
    console.log('🔥 超大测试标记已添加');
    
    // 也在body上添加一个绝对定位的标记作为对比
    const bodyMarker = document.createElement('div');
    bodyMarker.className = 'body-test-marker';
    bodyMarker.style.cssText = `
        position: fixed !important;
        top: 100px !important;
        left: 100px !important;
        width: 60px !important;
        height: 40px !important;
        background: blue !important;
        color: white !important;
        z-index: 99999 !important;
        border: 3px solid yellow !important;
        text-align: center !important;
        line-height: 40px !important;
        font-weight: bold !important;
    `;
    bodyMarker.textContent = 'BODY';
    document.body.appendChild(bodyMarker);
    console.log('🔥 Body测试标记已添加');
    
    // 检查进度条的计算样式
    const computedStyle = window.getComputedStyle(progressBar);
    console.log('🔥 进度条计算样式:');
    console.log('  - position:', computedStyle.position);
    console.log('  - overflow:', computedStyle.overflow);
    console.log('  - z-index:', computedStyle.zIndex);
    console.log('  - display:', computedStyle.display);
    console.log('  - visibility:', computedStyle.visibility);
    
    if (progressContainer) {
        const containerStyle = window.getComputedStyle(progressContainer);
        console.log('🔥 容器计算样式:');
        console.log('  - position:', containerStyle.position);
        console.log('  - overflow:', containerStyle.overflow);
        console.log('  - z-index:', containerStyle.zIndex);
        console.log('  - display:', containerStyle.display);
        console.log('  - visibility:', containerStyle.visibility);
    }
    
    console.log('🔥 如果仍然看不到红色TEST和蓝色BODY标记，请检查浏览器开发者工具');
    
    // 15秒后清除测试
    setTimeout(() => {
        marker.remove();
        bodyMarker.remove();
        console.log('🔥 极端测试标记已清除');
    }, 15000);
};

// 检查DOM层级
window.checkDOMHierarchy = function() {
    const progressBar = document.getElementById('uploadedProgressBar');
    if (!progressBar) {
        console.log('❌ 未找到进度条');
        return;
    }
    
    console.log('🔍 DOM层级检查:');
    let element = progressBar;
    let level = 0;
    
    while (element && level < 10) {
        const style = window.getComputedStyle(element);
        console.log(`  ${level}: ${element.tagName}.${element.className} - overflow:${style.overflow}, position:${style.position}, z-index:${style.zIndex}`);
        element = element.parentElement;
        level++;
    }
};

console.log('🔥 极端测试函数已准备：');
console.log('🔥 - extremeBookmarkTest() : 极端显示测试');
console.log('🔥 - checkDOMHierarchy() : 检查DOM层级'); 