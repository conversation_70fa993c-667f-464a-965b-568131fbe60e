/**
 * 资源加载恢复相关样式
 * 用于资源加载状态提示、恢复对话框和帮助内容
 */

#troubleshooting-helper {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
}

#troubleshooting-helper .help-toggle {
    background-color: #4285f4;
    color: white;
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

#troubleshooting-helper .help-toggle:hover {
    background-color: #3367d6;
    transform: scale(1.1);
}

#troubleshooting-helper .help-content {
    position: absolute;
    bottom: 60px;
    right: 0;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    padding: 15px;
    width: 300px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, opacity 0.3s ease;
    opacity: 0;
    transform-origin: bottom right;
    transform: scale(0.9);
}

#troubleshooting-helper.active .help-content {
    max-height: 500px;
    opacity: 1;
    transform: scale(1);
}

#troubleshooting-helper .help-content h4 {
    margin-top: 0;
    color: #4285f4;
    font-size: 16px;
    margin-bottom: 10px;
}

#troubleshooting-helper .help-content ul {
    padding-left: 20px;
    margin-bottom: 15px;
}

#troubleshooting-helper .help-content li {
    margin-bottom: 8px;
}

#troubleshooting-helper .help-content button {
    background-color: #4285f4;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-right: 8px;
    margin-bottom: 8px;
    transition: background-color 0.2s ease;
}

#troubleshooting-helper .help-content button:hover {
    background-color: #3367d6;
}

.resource-loading-indicator {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
    max-width: 80%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.resource-loading-indicator.success {
    background-color: #43a047;
    animation: scaleIn 0.3s ease;
}

.resource-loading-indicator.error {
    background-color: #e53935;
    animation: scaleIn 0.3s ease;
}

.fallback-mode-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: #ff9800;
    color: white;
    text-align: center;
    padding: 10px;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.fallback-mode-banner button {
    background-color: white;
    color: #ff9800;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.fallback-mode-banner button:hover {
    background-color: #f5f5f5;
}

.resource-recovery-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.4s ease;
}

.resource-recovery-dialog .recovery-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    animation: scaleIn 0.3s ease;
}

.resource-recovery-dialog h3 {
    color: #4285f4;
    margin-top: 0;
    font-size: 18px;
}

.resource-recovery-dialog p {
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.5;
}

.resource-recovery-dialog .recovery-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.resource-recovery-dialog .action-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    background-color: #4285f4;
    color: white;
    cursor: pointer;
    font-weight: bold;
    flex-grow: 1;
    min-width: 120px;
    font-size: 14px;
    transition: background-color 0.2s ease, transform 0.2s ease;
}

.resource-recovery-dialog .action-button:hover {
    background-color: #3367d6;
}

.resource-recovery-dialog #dismissDialog {
    background-color: #757575;
}

.resource-recovery-dialog #dismissDialog:hover {
    background-color: #616161;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes scaleIn {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    70% {
        transform: scale(1.05);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@media (max-width: 768px) {
    .resource-loading-indicator {
        left: 50%;
        transform: translateX(-50%);
        bottom: 10px;
        font-size: 12px;
        padding: 6px 12px;
    }
    
    #troubleshooting-helper .help-toggle {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
    
    .resource-recovery-dialog .recovery-content {
        width: 95%;
        padding: 15px;
    }
} 