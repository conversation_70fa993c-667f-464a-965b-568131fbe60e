"""
集中配置的速率限制模块 - 1000用户规模版本

此配置适用于月活跃用户约1000人的应用规模。
基于用户分析：80%免费用户(800人，20次操作/月)，20%付费用户(200人，600次操作/月)
预计月AI操作总量：136,000次
"""
from functools import wraps
from flask import request, current_app
import logging

logger = logging.getLogger(__name__)

# 速率限制配置 - 1000用户规模
# 月AI操作：136,000次，日均：4,533次，时均：189次，分均：3.2次
RATE_LIMITS = {
    # 认证相关 - 进一步放宽以支持更多用户
    "auth": {
        # 注册/邮箱验证 - 支持更多新用户注册
        "register": "15 per minute, 80 per hour, 350 per day",
        "email_verification": "15 per minute, 80 per hour, 350 per day",
        # 登录 - 支持用户正常登录频率
        "login": "25 per minute, 180 per hour, 700 per day",
        # 密码重置
        "password_reset": "8 per minute, 35 per hour, 70 per day",
    },
    
    # 内容处理相关 - 核心AI功能，大幅提升限制
    "content": {
        # 上传epub - 付费用户需要频繁上传
        "upload_epub": "18 per minute, 120 per hour, 500 per day",
        # AI摘要 - 主要操作，需要支持高频使用
        "summarize": "35 per minute, 250 per hour, 1400 per day",
        # 脑图生成 - 主要操作
        "mindmap": "35 per minute, 250 per hour, 1400 per day",
        # 知识图谱生成 - 主要操作
        "knowledgegraph": "35 per minute, 250 per hour, 1400 per day",
    },
    
    # 积分相关 - 支持高频的积分操作
    "credits": {
        # 积分查询 - 用户会频繁查看
        "get_credits": "50 per minute, 350 per hour, 1800 per day",
        # 积分扣除 - 每次AI操作都需要
        "deduct_credits": "45 per minute, 320 per hour, 1600 per day",
        # 积分添加 - 支付成功后添加
        "add_credits": "8 per minute, 35 per hour, 80 per day",
        # 积分历史
        "credits_history": "25 per minute, 150 per hour, 500 per day",
    },
    
    # 支付相关 - 支持更多付费用户
    "payment": {
        # 支付套餐查询
        "payment_packages": "18 per minute, 80 per hour, 350 per day",
        # 支付订单创建 - 支持付费用户购买
        "create_order": "8 per minute, 40 per hour, 80 per day",
        # 支付状态检查
        "check_status": "18 per minute, 70 per hour, 180 per day",
        # 支付成功回调
        "payment_success": "18 per minute, 70 per hour, 180 per day",
        # 支付取消回调
        "payment_cancel": "18 per minute, 70 per hour, 180 per day",
        # 支付回调（webhook）
        "payment_webhook": "25 per minute, 180 per hour",
        # 重定向到应用
        "redirect_to_app": "18 per minute, 70 per hour, 180 per day",
    },
    
    # 其他API
    "other": {
        # 默认限制 - 大幅提高基础限制
        "default": "80 per minute, 800 per hour, 3500 per day",
        # 模型配置获取
        "model_config": "35 per minute, 180 per hour, 700 per day",
        # CSRF令牌
        "csrf_token": "35 per minute, 150 per hour, 500 per day",
    }
}

def get_limit(category, operation):
    """
    获取指定类别和操作的速率限制
    
    Args:
        category: 限制类别，如 'auth', 'content', 'credits', 'payment', 'other'
        operation: 操作名称，如 'login', 'summarize', 'get_credits' 等
        
    Returns:
        str: 速率限制字符串，如 "5 per minute, 40 per hour, 200 per day"
    """
    category_limits = RATE_LIMITS.get(category, {})
    return category_limits.get(operation, RATE_LIMITS["other"]["default"])

def create_rate_limiter(category, operation):
    """
    创建适合特定操作类型的速率限制装饰器
    
    根据操作类型使用不同的标识符策略：
    - 认证相关操作（auth类别）：始终使用IP地址作为标识符
    - 其他操作（内容、积分、支付等）：优先使用用户ID，无法获取时回退到IP地址
    
    Args:
        category: 限制类别，如 'auth', 'content', 'credits', 'payment', 'other'
        operation: 操作名称，如 'login', 'summarize', 'get_credits' 等
        
    Returns:
        function: 装饰器函数
    """
    # 获取限制值
    limit_value = get_limit(category, operation)
    
    # 返回一个简单的装饰器，它只保存操作类型和限制值
    def decorator(f):
        # 在函数上保存操作类型和限制值，供后续使用
        f._rate_limit_category = category
        f._rate_limit_operation = operation
        f._rate_limit_value = limit_value
        return f
    
    return decorator

def get_auth_limits():
    """
    返回认证相关操作的速率限制配置（兼容旧版API）
    
    Returns:
        dict: 包含各种认证操作的速率限制
    """
    return {
        # 登录尝试限制 - 防止暴力破解
        'login': {
            'ip': RATE_LIMITS["auth"]["login"],  # IP级别限制
            'email': "35 per hour, 70 per day"  # 每个邮箱的限制
        },
        # 注册限制 - 防止批量注册
        'register': {
            'ip': RATE_LIMITS["auth"]["register"],
            'email_domain': "70 per hour, 180 per day"  # 每个邮箱域名的限制
        },
        # 密码重置限制 - 防止滥用
        'password_reset': {
            'ip': RATE_LIMITS["auth"]["password_reset"],
            'email': "8 per hour, 18 per day"
        },
        # 邮箱验证限制
        'email_verification': {
            'ip': RATE_LIMITS["auth"]["email_verification"],
            'email': "18 per hour, 35 per day"
        }
    } 