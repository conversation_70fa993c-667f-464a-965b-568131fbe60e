字幕问答框，AI在返回结果前，有一个提示文字，动态省略号应该放在提示文字后，而不是放在文字之前。请请修改。

Read file: public/js/subtitle-chat.js

// ... existing code ...
    addLoadingMessage() {
        const messagesContainer = document.getElementById('subtitleChatMessages');
        const messageId = 'loading_' + Date.now();
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message ai loading';
        messageDiv.id = messageId;
        
        const loadingText = this.getLocalizedText('subtitle.chat.thinking') || 'AI思考中...';
        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="thinking-indicator">
                    <span class="thinking-text">${loadingText}</span>
                    <span class="thinking-dots">
                        <span></span><span></span><span></span>
                    </span>
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
        return messageId;
    }


### 修改说明

**涉及文件**：
- `public/js/subtitle-chat.js`

**修改内容**：
- 将 `addLoadingMessage` 方法中“AI思考中...”提示和动态省略号的顺序调整为：  
  **先显示提示文字，再显示动态省略号**。

**原逻辑**：
```
<div class="thinking-indicator">
    <span class="thinking-dots">...</span>
    <span class="thinking-text">AI思考中...</span>
</div>
```
**现逻辑**：
```
<div class="thinking-indicator">
    <span class="thinking-text">AI思考中...</span>
    <span class="thinking-dots">...</span>
</div>
```

**效果**：
- 现在“AI思考中...”在前，动态省略号在后，符合中文阅读习惯和用户体验。

如需进一步微调样式，可继续告知！
---------------------------------------------------------
删除AI思考中后面的省略号，需要调整en/zh.json