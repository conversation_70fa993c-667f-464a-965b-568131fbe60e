<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AudioPilot - AI Audio Learning Assistant</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎧</text></svg>">
    
    <link rel="stylesheet" href="css/landing.css">
    <link rel="stylesheet" href="css/grammar-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <style>
        /* Initial loading overlay to prevent FOUC (Flash of Unstyled Content) */
        #initial-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #007bff;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        /* Hide page content initially */
        body > *:not(#initial-loading) {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        body.loaded > *:not(#initial-loading) {
            opacity: 1;
        }
        
        /* 语言切换按钮样式 - 简化为单个按钮 */
        .language-selector {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .language-selector:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }

        /* Ensure consistent button width in the language selection modal */
        .language-modal button {
            width: 100%;
            max-width: 300px; /* Adjust as needed */
            margin: 5px auto;
            display: block;
            text-align: center;
        }

        /* 用户头像样式 */
        .testimonial-card .user img {
            width: 50px;
            height: 50px;
            border-radius: 50%; /* 使头像呈现为圆形 */
            object-fit: cover; /* 确保图片填满区域且不变形 */
            border: 2px solid #007bff; /* 添加边框 */
        }

        /* 或者，如果您想要方形头像，可以使用这个样式（取消上面的圆形样式注释） */
        /*
        .testimonial-card .user img {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border: 2px solid #007bff;
        }
        */

        /* 价格样式 - 使USD字体小一点 */
        .price {
            margin: 20px 0;
            display: flex;
            align-items: flex-end; /* 对齐底部 */
            justify-content: center;
        }
        
        /* 标题栏下方tagline样式 */
        .logo .tagline {
            font-size: 0.7rem;
            color: #666;
            margin-top: -5px;
            margin-bottom: 5px;
            text-align: center;
        }
        
        .price .currency {
            font-size: 2.5rem; /* 增大字号 */
            font-weight: bold;
            color: #333; /* 与plan-name颜色一致 */
        }
        
        .price .unit {
            font-size: 1rem;
            font-weight: normal;
            margin-left: 2px;
            margin-bottom: 6px; /* 微调底部对齐位置 */
            color: #666; /* 保持灰色 */
        }
    </style>
    <!-- Preload only the detected language file -->
    <script>
        // Create a preload link for only the detected language
        const detectedLang = localStorage.getItem('uiLanguage') || 
            (navigator.language && navigator.language.toLowerCase().startsWith('zh') ? 'zh' : 'en');
        const preloadLink = document.createElement('link');
        preloadLink.rel = 'preload';
        preloadLink.href = `lang/${detectedLang}.json`;
        preloadLink.as = 'fetch';
        preloadLink.crossOrigin = 'anonymous';
        document.head.appendChild(preloadLink);
    </script>
    
    <!-- Update the language initialization script in the head section -->
    <script>
        // 立即检查是否需要加载特定语言
        const urlParams = new URLSearchParams(window.location.search);
        const urlLang = urlParams.get('lang');
        const storedLang = localStorage.getItem('uiLanguage');
        const browserLang = navigator.language || navigator.userLanguage;
        
        // 确定要使用的语言 - 与index.html完全保持一致
        const supportedLanguages = ['zh', 'en'];
        let initialLang;
        
        if (urlLang && supportedLanguages.includes(urlLang)) {
            initialLang = urlLang;
            console.log(`使用URL参数指定的语言: ${initialLang}`);
        } else if (storedLang && supportedLanguages.includes(storedLang)) {
            initialLang = storedLang;
            console.log(`使用存储的语言偏好: ${initialLang}`);
        } else if (browserLang && browserLang.toLowerCase().startsWith('zh')) {
            // 如果浏览器语言是中文，使用中文
            initialLang = 'zh';
            console.log(`使用浏览器设置的中文`);
        } else {
            // 其他所有语言默认使用英文
            initialLang = 'en';
            console.log(`使用英文作为默认语言`);
        }
        
        // 根据检测到的语言设置html lang属性
        document.documentElement.lang = initialLang === 'en' ? 'en' : 'zh-CN';
        
        // 根据检测到的语言立即设置页面标题
        if (initialLang === 'en') {
            document.title = 'AudioPilot - AI Audio Learning Assistant';
        } else {
            document.title = 'AudioPilot - AI音频学习助手';
        }
        
        // 存储检测到的语言供ui-language.js使用
        window.initialDetectedLang = initialLang;
    </script>
    
    <!-- Load UI language system -->
    <script src="js/ui-language.js"></script>

    <!-- 资源加载器 - 用于处理CDN资源加载失败的情况 -->
    <script src="js/resource-loader.js"></script>
</head>
<body>
    <!-- Initial loading overlay -->
    <div id="initial-loading">
        <div class="loading-spinner"></div>
    </div>
    
    <header>
        <nav>
            <div class="logo">
                <h1><span class="highlight">Audio</span>Pilot</h1>
                <p class="tagline" data-i18n="landing.hero.tagline">AI语言学习助手</p>
            </div>
            <ul class="nav-links">
                <li><a href="#features" data-i18n="nav.features">功能</a></li>
                <li><a href="#pricing" data-i18n="nav.pricing">价格</a></li>
                <li><a href="#faq" data-i18n="nav.faq">常见问题</a></li>
                <li><a href="log.html" class="btn-secondary" data-i18n="nav.getStarted">开始使用</a></li>
            </ul>
            <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
                <i class="fas fa-bars"></i>
            </button>
        </nav>
    </header>

    <section class="hero" style="background-color: #ffffff;">
        <div class="hero-container">
            <div class="hero-content">
                <h1 data-i18n="landing.hero.subtitle">快速掌握英语音频内容，AI助力英语学习</h1>
                <p data-i18n="landing.description">上传英语音频或在线录音，获取AI智能分析、发音纠正和语言学习指导，高效提升英语水平。</p>
                <div class="cta-buttons">
                    <a href="log.html" class="btn-primary" data-i18n="landing.hero.cta">立即开始学习</a>
                </div>
            </div>
        </div>
    </section>

    <section id="features" class="features" style="background-color: #ffffff;">
        <h2 data-i18n="landing.features.title">强大功能，简单操作</h2>
        <div class="feature-cards">
            <div class="feature-card">
                <div class="icon">
                    <i class="fas fa-microphone"></i>
                </div>
                <h3 data-i18n="landing.features.upload.title">音频上传</h3>
                <p data-i18n="landing.features.upload.desc">支持多种英语音频格式，轻松上传或在线录音</p>
            </div>
            <div class="feature-card">
                <div class="icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3 data-i18n="landing.features.ai.title">智能分析</h3>
                <p data-i18n="landing.features.ai.desc">AI深度分析英语音频，提供发音、语法和词汇指导</p>
            </div>
            <div class="feature-card">
                <div class="icon">
                    <i class="fas fa-comments"></i>
                </div>
                <h3 data-i18n="landing.features.qa.title">智能问答</h3>
                <p data-i18n="landing.features.qa.desc">基于音频内容进行智能问答，巩固英语学习效果</p>
            </div>
            <div class="feature-card">
                <div class="icon">
                    <i class="fas fa-sync"></i>
                </div>
                <h3 data-i18n="landing.features.paygo.title">按需付费</h3>
                <p data-i18n="landing.features.paygo.desc">无月费，仅为使用的功能付费</p>
            </div>
        </div>
    </section>


    <section id="pricing" class="pricing" style="background-color: #ffffff;">
        <h2 data-i18n="pricing.title">选择适合您的计划</h2>
        <div class="pricing-cards">
            <div class="pricing-card">
                <div class="plan-name" data-i18n="pricing.basic.name">基础版</div>
                <div class="price">
                    <span class="currency">$5</span>
                    <span class="unit">USD</span>
                </div>
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.basic.credits">3000积分</span></li>
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.basic.analysis">AI音频分析</span></li>
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.basic.qa">智能问答</span></li>
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.basic.perOperation">10-20个积分/AI操作</span></li>
                </ul>
                <a href="log.html" class="btn-secondary button-link" data-i18n="pricing.getStarted">开始使用</a>
            </div>
            <div class="pricing-card">
                <div class="plan-name" data-i18n="pricing.standard.name">标准版</div>
                <div class="price">
                    <span class="currency">$10</span>
                    <span class="unit">USD</span>
                </div>
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.standard.credits">6500积分</span></li>
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.standard.analysis">AI音频分析</span></li>
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.standard.qa">智能问答</span></li>
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.standard.perOperation">10-20个积分/AI操作</span></li>
                </ul>
                <a href="log.html" class="btn-secondary button-link" data-i18n="pricing.getStarted">开始使用</a>
            </div>
            <div class="pricing-card">
                <div class="plan-name" data-i18n="pricing.premium.name">高级版</div>
                <div class="price">
                    <span class="currency">$15</span>
                    <span class="unit">USD</span>
                </div>
                <ul class="features-list">
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.premium.credits">10000积分</span></li>
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.premium.analysis">AI音频分析</span></li>
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.premium.qa">智能问答</span></li>
                    <li><i class="fas fa-check"></i> <span data-i18n="pricing.premium.perOperation">10-20个积分/AI操作</span></li>
                </ul>
                <a href="log.html" class="btn-secondary button-link" data-i18n="pricing.getStarted">开始使用</a>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="faq">
        <div class="container">
            <h2 data-i18n="landing.faq.title">常见问题</h2>
            <p class="faq-subtitle" data-i18n="landing.faq.subtitle">查找关于使用AudioPilot的常见问题答案。</p>
            
            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question" data-i18n="landing.faq.q1.question">
                        <span>AudioPilot是什么？</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p data-i18n="landing.faq.q1.answer">AudioPilot是一个AI驱动的英语学习助手，可以帮您分析英语音频内容，提供发音纠正、语法指导和词汇学习建议。</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-i18n="landing.faq.q2.question">
                        <span>如何开始使用AudioPilot？</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p data-i18n="landing.faq.q2.answer">使用非常简单：注册账户后，上传英语音频文件或使用在线录音功能，AI会自动分析音频内容，提供个性化的英语学习指导。</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-i18n="landing.faq.q3.question">
                        <span>支持哪些音频格式？</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p data-i18n="landing.faq.q3.answer">目前支持MP3、WAV、M4A等主流音频格式，文件大小限制为最大100MB。</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-i18n="landing.faq.q4.question">
                        <span>英语分析准确度如何？</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p data-i18n="landing.faq.q4.answer">我们使用先进的AI语音识别和自然语言处理技术，在英语语音分析方面准确率超过95%，持续优化中。</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" data-i18n="landing.faq.q5.question">
                        <span>如何计费？</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p data-i18n="landing.faq.q5.answer">AudioPilot采用积分付费模式，每次AI操作（音频分析、发音指导、智能问答）都会消耗积分。不同功能消耗的积分数量不同，新用户注册赠送免费试用积分。</p>
                    </div>
                </div>




            </div>
        </div>
    </section>

    <section class="cta">
        <h2 data-i18n="landing.hero.cta">立即开始学习</h2>
        <p data-i18n="landing.demo.register_button">免费注册</p>
        <a href="log.html" class="btn-primary" data-i18n="landing.demo.upload_button">立即体验</a>
    </section>

    <footer>
        <div class="footer-content">
            <div class="footer-logo">
                <h2><span class="highlight">Audio</span>Pilot</h2>
                <p data-i18n="landing.hero.tagline">AI语言学习助手</p>
            </div>
            <div class="footer-links">
                <div class="link-group">
                    <h3 data-i18n="footer.product">产品</h3>
                    <ul>
                        <li><a href="#features" data-i18n="footer.features">功能</a></li>
                        <li><a href="#pricing" data-i18n="footer.pricing">价格</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h3 data-i18n="footer.support">支持</h3>
                    <ul>
                        <li><a href="#faq" data-i18n="footer.faq">常见问题</a></li>
                        <li><a href="mailto: <EMAIL>" data-i18n="footer.contactUs">联系我们</a></li>
                    </ul>
                </div>
                <div class="link-group">
                    <h3 data-i18n="footer.legal">法律</h3>
                    <ul>
                        <li><a href="terms.html" data-i18n="footer.terms">服务条款</a></li>
                        <li><a href="privacy.html" data-i18n="footer.privacy">隐私政策</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p data-i18n="landing.footer.copyright">&copy; 2025 AudioPilot. 保留所有权利。</p>
            <div class="social-links">
                <a href="mailto:<EMAIL>" title="<EMAIL>"><i class="fas fa-envelope"></i></a>
                <a href="#"><i class="fab fa-twitter"></i></a>
                <a href="#"><i class="fab fa-weixin"></i></a>
            </div>
        </div>
    </footer>
    
    <!-- Simplified language selector button -->
    <button class="language-selector" data-lang-selector title="选择语言 / Language">
        <i class="fas fa-globe"></i>
    </button>
    
    <!-- Script to hide loading overlay once language is loaded -->
    <script>
        document.addEventListener('ui-language-loaded', function() {
            console.log('Language loaded, showing page content');
            document.body.classList.add('loaded');
            setTimeout(() => {
                const loadingEl = document.getElementById('initial-loading');
                if (loadingEl) {
                    loadingEl.style.display = 'none';
                }
            }, 300);
        });

        // Mobile menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
            const navLinks = document.querySelector('.nav-links');
            
            if (mobileMenuToggle && navLinks) {
                mobileMenuToggle.addEventListener('click', function() {
                    navLinks.classList.toggle('active');
                });
                
                // Close menu when clicking on close button (::before pseudo-element)
                navLinks.addEventListener('click', function(e) {
                    if (e.target === navLinks) {
                        navLinks.classList.remove('active');
                    }
                });
                
                // Close menu when clicking on a link
                const navLinksItems = navLinks.querySelectorAll('a');
                navLinksItems.forEach(link => {
                    link.addEventListener('click', function() {
                        navLinks.classList.remove('active');
                    });
                });
                
                // Close menu when clicking outside
                document.addEventListener('click', function(e) {
                    if (!navLinks.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                        navLinks.classList.remove('active');
                    }
                });
            }

            // FAQ functionality
            const faqItems = document.querySelectorAll('.faq-item');
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question');
                if (question) {
                    question.addEventListener('click', function() {
                        // Close other items
                        faqItems.forEach(otherItem => {
                            if (otherItem !== item && otherItem.classList.contains('active')) {
                                otherItem.classList.remove('active');
                            }
                        });
                        
                        // Toggle current item
                        item.classList.toggle('active');
                    });
                }
            });
        });
    </script>

    <!-- 资源加载状态指示器 -->
    <div id="resource-loading-status" class="resource-loading-indicator" style="display:none;">
        <span id="loading-message">正在加载必要的资源...</span>
    </div>

    <!-- 降级模式提示条 -->
    <div id="fallback-mode-banner" class="fallback-mode-banner" style="display:none;">
        <span data-i18n="fallback.message">部分资源加载失败，应用正在降级模式下运行。某些功能可能受限。</span>
        <button id="try-reload" data-i18n="fallback.reload">尝试重新加载</button>
    </div>
     
        <script>
            // 监听资源加载事件
            window.addEventListener('resource-loading-started', (event) => {
                const statusElem = document.getElementById('resource-loading-status');
                const messageElem = document.getElementById('loading-message');
                if (statusElem && messageElem) {
                    messageElem.textContent = event.detail.message || '正在加载资源...';
                    statusElem.className = 'resource-loading-indicator';
                    statusElem.style.display = 'block';
                    // 5秒后自动隐藏
                    setTimeout(() => {
                        statusElem.style.display = 'none';
                    }, 5000);
                }
            });
        
            window.addEventListener('resource-loading-complete', (event) => {
                const statusElem = document.getElementById('resource-loading-status');
                const messageElem = document.getElementById('loading-message');
                if (statusElem && messageElem) {
                    messageElem.textContent = event.detail.message || '资源加载完成';
                    statusElem.className = 'resource-loading-indicator success';
                    // 3秒后隐藏
                    setTimeout(() => {
                        statusElem.style.display = 'none';
                    }, 3000);
                }
            });
        
            window.addEventListener('resource-loading-error', (event) => {
                const statusElem = document.getElementById('resource-loading-status');
                const messageElem = document.getElementById('loading-message');
                const fallbackBanner = document.getElementById('fallback-mode-banner');
                if (statusElem && messageElem) {
                    messageElem.textContent = event.detail.message || '资源加载失败';
                    statusElem.className = 'resource-loading-indicator error';
                    statusElem.style.display = 'block';
                    // 5秒后隐藏状态指示器
                    setTimeout(() => {
                        statusElem.style.display = 'none';
                    }, 5000);
                }
                // 显示降级模式提示条
                if (fallbackBanner && event.detail.fallbackMode) {
                    fallbackBanner.style.display = 'block';
                }
            });
        </script>
        
    <!-- Microsoft Clarity (放在body结束标签前，确保不干扰页面渲染) -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "rimwd810jk");
        
        // 添加环境标记，帮助区分本地与生产数据
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            clarity('set', '_environment', 'development');
        }
    </script>

    <!-- 添加基础脚本，但不处理OAuth回调 -->
    <script src="js/env-config.js"></script>
    <script src="env.js"></script>
    <script src="js/app-config.js"></script>
    <script src="js/lib/supabase.min.js"></script>
    <script src="js/supabase-client.js"></script>
    
    <!-- 语法分析器模块 -->
    <script src="js/grammar-analyzer.js"></script>
    
    <!-- 落地页简化脚本 - 不需要Google Auth -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 [Index] 落地页DOM加载完成');
            console.log('ℹ️ [Index] 落地页模式 - 无需Google登录功能');
        });
    </script>
</body>
</html>