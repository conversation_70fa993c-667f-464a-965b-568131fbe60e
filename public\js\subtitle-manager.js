// SRT字幕管理器
class SubtitleManager {
    constructor() {
        this.subtitles = []; // 存储解析后的字幕数据
        this.currentIndex = -1; // 当前激活的字幕索引
        this.syncInterval = null; // 同步定时器
        this.isActive = false; // 字幕是否激活状态
        this.scrollAnimation = null; // 滚动动画句柄
        this.currentTimeUpdateHandler = null; // 用于字幕片段播放停止
        this.subtitleFileId = null; // 服务器上的字幕文件ID
        
        this.initializeElements();
        this.attachEventListeners();
    }
    
    initializeElements() {
        // Tab控制元素
        this.qaTabBtn = document.getElementById('qaTabBtn');
        this.subtitleTabBtn = document.getElementById('subtitleTabBtn');
        this.qaTabContent = document.getElementById('qaTabContent');
        this.subtitleTabContent = document.getElementById('subtitleTabContent');
        
        // 字幕上传相关元素
        this.subtitleDropZone = document.getElementById('subtitleDropZone');
        this.subtitleFileInput = document.getElementById('subtitleFileInput');
        this.subtitleUploadArea = document.getElementById('subtitleUploadArea');
        this.subtitleDisplayArea = document.getElementById('subtitleDisplayArea');
        
        // 字幕显示相关元素
        this.subtitleContainer = document.getElementById('subtitleContainer');
        this.clearSubtitleBtn = document.getElementById('clearSubtitleBtn');
        // 添加字幕搜索相关元素
        this.subtitleSearchBox = document.getElementById('subtitleSearchBox');
        this.subtitleSearchBtn = document.getElementById('subtitleSearchBtn');
    }
    
    attachEventListeners() {
        // Tab切换事件
        if (this.qaTabBtn) {
            this.qaTabBtn.addEventListener('click', () => this.switchTab('qa'));
        }
        
        if (this.subtitleTabBtn) {
            this.subtitleTabBtn.addEventListener('click', () => this.switchTab('subtitle'));
        }
        
        // 字幕文件上传事件
        if (this.subtitleDropZone) {
            this.subtitleDropZone.addEventListener('click', () => {
                this.subtitleFileInput.click();
            });
            
            // 拖拽事件
            this.subtitleDropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                this.subtitleDropZone.classList.add('drag-over');
            });
            
            this.subtitleDropZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                this.subtitleDropZone.classList.remove('drag-over');
            });
            
            this.subtitleDropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                this.subtitleDropZone.classList.remove('drag-over');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleFileSelect(files[0]);
                }
            });
        }
        
        if (this.subtitleFileInput) {
            this.subtitleFileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handleFileSelect(file);
                }
                // 重置文件输入，确保重复选择同一文件时触发change事件
                e.target.value = '';
            });
        }
        
        // 清除字幕按钮
        if (this.clearSubtitleBtn) {
            this.clearSubtitleBtn.addEventListener('click', () => {
                this.clearSubtitles();
            });
        }
        // 添加字幕搜索过滤事件
        if (this.subtitleSearchBox) {
            this.subtitleSearchBox.addEventListener('input', () => this.filterSubtitles());
        }
        if (this.subtitleSearchBtn) {
            this.subtitleSearchBtn.addEventListener('click', () => this.filterSubtitles());
        }
        
        // 添加键盘事件支持上下切换字幕
        document.addEventListener('keydown', (e) => {
            // 仅在字幕标签页激活时生效
            if (!this.subtitleTabContent.classList.contains('active')) return;
            // 忽略在输入框中操作
            const activeTag = document.activeElement.tagName;
            if (activeTag === 'INPUT' || activeTag === 'TEXTAREA' || document.activeElement.isContentEditable) {
                return;
            }
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                if (this.subtitles.length === 0) return;
                let newIndex = this.currentIndex + 1;
                if (newIndex >= this.subtitles.length) newIndex = this.subtitles.length - 1;
                // 移除旧高亮
                if (this.currentIndex >= 0 && this.subtitles[this.currentIndex]) {
                    this.subtitles[this.currentIndex].element.classList.remove('active');
                }
                this.currentIndex = newIndex;
                this.subtitles[newIndex].element.classList.add('active');
                this.scrollToActiveSubtitle();
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                if (this.subtitles.length === 0) return;
                let newIndex = this.currentIndex - 1;
                if (newIndex < 0) newIndex = 0;
                if (this.currentIndex >= 0 && this.subtitles[this.currentIndex]) {
                    this.subtitles[this.currentIndex].element.classList.remove('active');
                }
                this.currentIndex = newIndex;
                this.subtitles[newIndex].element.classList.add('active');
                this.scrollToActiveSubtitle();
            }
        });
    }
    
    // Tab切换功能
    switchTab(tabType) {
        if (tabType === 'qa') {
            this.qaTabBtn.classList.add('active');
            this.subtitleTabBtn.classList.remove('active');
            this.qaTabContent.classList.add('active');
            this.subtitleTabContent.classList.remove('active');
            
            // 停止字幕同步
            this.stopSync();
        } else if (tabType === 'subtitle') {
            this.qaTabBtn.classList.remove('active');
            this.subtitleTabBtn.classList.add('active');
            this.qaTabContent.classList.remove('active');
            this.subtitleTabContent.classList.add('active');
            
            // 如果有字幕数据，开始同步
            if (this.subtitles.length > 0) {
                this.startSync();
            }
        }
    }
    
    // 处理文件选择
    async handleFileSelect(file) {
        if (!file.name.toLowerCase().endsWith('.srt')) {
            this.showError(this.getI18nText('subtitle.invalid_format', '请选择SRT格式的字幕文件'));
            return;
        }
        
        try {
            const content = await this.readFileContent(file);
            const parsedSubtitles = this.parseSRT(content);
            
            if (parsedSubtitles.length === 0) {
                this.showError(this.getI18nText('subtitle.parse_failed', '字幕文件解析失败，请检查文件格式'));
                return;
            }

            // 初始化skipCreditsCheck变量，默认为false
            let skipCreditsCheck = false;

            // 检查是否需要进行积分检查
            const needCreditsCheck = window.ENV && window.ENV.CREDITS_CHECK && window.ENV.CREDITS_CHECK.UPLOAD_SUBTITLE;
            
            if (needCreditsCheck) {
                // 前端缓存积分初查：缓存积分不足立即弹框
                if (window.CreditsManager) {
                    let cached = window.CreditsManager.getCachedCredits();
                    if (cached === null) cached = 0;
                    if (cached < 1) {
                        window.CreditsManager.showInsufficientCreditsAlert(cached);
                        return;
                    }
                }
                
                // 新增：确保积分已同步（防止前端缓存为0但后端有积分的情况）
                if (window.CreditsManager) {
                    const currentCredits = window.CreditsManager.getCachedCredits();
                    console.log(`🔍 [字幕上传] 当前缓存积分: ${currentCredits}`);
                    
                    // 如果积分为0或null，先尝试从后端同步
                    if (currentCredits === null || currentCredits === 0) {
                        console.log(`🔄 [字幕上传] 积分为${currentCredits}，尝试从后端同步...`);
                        try {
                            const backendCredits = await window.CreditsManager.fetchCreditsFromBackendCache();
                            console.log(`📊 [字幕上传] 后端同步结果: ${backendCredits}积分`);
                            
                            // 如果后端有积分，检查是否可以设置免检查
                            if (backendCredits > 0) {
                                console.log(`✅ [字幕上传] 发现后端积分${backendCredits}，设置动态免检查时间`);
                                window.CreditsManager.setAuxiliarySkipCheckTime(60);
                                
                                // 启动动态缓存管理（如果还没启动）
                                if (!window.CreditsManager.dynamicCacheManagementStarted) {
                                    await window.CreditsManager.startDynamicCacheManagement();
                                }
                            }
                        } catch (error) {
                            console.warn(`⚠️ [字幕上传] 后端同步失败:`, error);
                        }
                    }
                }
                
                // 新增：使用动态免检查机制
                if (window.CreditsManager && typeof window.CreditsManager.checkAuxiliarySkipStatus === 'function') {
                    const skipStatus = window.CreditsManager.checkAuxiliarySkipStatus();
                    skipCreditsCheck = skipStatus.canSkip;
                    
                    if (skipCreditsCheck) {
                        console.log(`🚀 [字幕上传] 辅助功能免检查生效，剩余${skipStatus.remainingMinutes}分钟，跳过积分检查`);
                    } else {
                        console.log(`🔍 [字幕上传] 辅助功能免检查不生效(${skipStatus.reason})，将进行积分验证`);
                        
                        // 降级到原有的双阶段积分校验
                        const creditsManager = window.CreditsManager;
                        const required = 1;
                        let cached = creditsManager.getCachedCredits();
                        if (cached === null) cached = 0;
                        if (cached < required) {
                            creditsManager.showInsufficientCreditsAlert(cached);
                            return;
                        }
                        try {
                            const fastCredits = await creditsManager.fetchCreditsFromBackendCache();
                            if (fastCredits < required) {
                                creditsManager.showInsufficientCreditsAlert(fastCredits);
                                return;
                            }
                        } catch (err) {
                            // 如果后端查询失败，使用前端缓存的积分显示
                            creditsManager.showInsufficientCreditsAlert(cached);
                            return;
                        }
                    }
                } else {
                    console.log(`🔍 [字幕上传] 动态免检查机制不可用，使用传统积分验证`);
                    
                    // 降级到原有的双阶段积分校验
                    const creditsManager = window.CreditsManager;
                    const required = 1;
                    let cached = creditsManager.getCachedCredits();
                    if (cached === null) cached = 0;
                    if (cached < required) {
                        creditsManager.showInsufficientCreditsAlert(cached);
                        return;
                    }
                    try {
                        const fastCredits = await creditsManager.fetchCreditsFromBackendCache();
                        if (fastCredits < required) {
                            creditsManager.showInsufficientCreditsAlert(fastCredits);
                            return;
                        }
                    } catch (err) {
                        // 如果后端查询失败，使用前端缓存的积分显示
                        creditsManager.showInsufficientCreditsAlert(cached);
                        return;
                    }
                }
            }

            // 通过积分校验后，加载字幕并上传
            this.subtitles = parsedSubtitles;
            this.displaySubtitles();
            // 上传字幕后初始高亮第一行字幕，启用可视化按钮
            if (this.subtitles.length > 0) {
                this.currentIndex = 0;
                const firstEl = this.subtitles[0].element;
                firstEl.classList.add('active');
                this.scrollToActiveSubtitle();
                this.updateGrammarButtonState();
            }
            this.showSubtitleArea(file.name);

            // 上传字幕文件到服务器
            await this.uploadSubtitleToServer(file, content, skipCreditsCheck);
            
            // 如果当前在字幕tab，开始同步
            if (this.subtitleTabContent.classList.contains('active')) {
                this.startSync();
            }

            // 触发字幕文件上传事件，供时间戳管理器使用
            document.dispatchEvent(new CustomEvent('subtitleFileUploaded', {
                detail: {
                    fileName: file.name,
                    subtitleCount: this.subtitles.length,
                    fileId: this.subtitleFileId
                }
            }));
        } catch (error) {
            console.error('字幕文件处理失败:', error);
            this.showError(this.getI18nText('subtitle.upload_failed', '字幕文件读取失败，请重试'));
        }
    }
    
    // 读取文件内容
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(reader.error);
            reader.readAsText(file, 'UTF-8');
        });
    }
    
    // SRT格式解析
    parseSRT(content) {
        const subtitles = [];
        const blocks = content.trim().split(/\n\s*\n/);
        
        blocks.forEach((block, index) => {
            const lines = block.trim().split('\n');
            if (lines.length >= 3) {
                const timeMatch = lines[1].match(/(\d{2}):(\d{2}):(\d{2}),(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2}),(\d{3})/);
                
                if (timeMatch) {
                    const startTime = this.parseTime(timeMatch[1], timeMatch[2], timeMatch[3], timeMatch[4]);
                    const endTime = this.parseTime(timeMatch[5], timeMatch[6], timeMatch[7], timeMatch[8]);
                    
                    const text = lines.slice(2).join(' ').trim();
                    
                    subtitles.push({
                        index: index + 1,
                        startTime: startTime,
                        endTime: endTime,
                        text: text,
                        element: null // 将在显示时设置
                    });
                }
            }
        });
        
        return subtitles.sort((a, b) => a.startTime - b.startTime);
    }
    
    // 时间解析（转换为秒）
    parseTime(hours, minutes, seconds, milliseconds) {
        return parseInt(hours) * 3600 + 
               parseInt(minutes) * 60 + 
               parseInt(seconds) + 
               parseInt(milliseconds) / 1000;
    }
    
    // 显示字幕列表
    displaySubtitles() {
        if (!this.subtitleContainer) return;
        
        this.subtitleContainer.innerHTML = '';
        
        this.subtitles.forEach((subtitle, index) => {
            const subtitleItem = document.createElement('div');
            subtitleItem.className = 'subtitle-item';
            subtitleItem.dataset.index = index;
            
            const timeSpan = document.createElement('div');
            timeSpan.className = 'subtitle-time';
            timeSpan.textContent = `${this.formatTime(subtitle.startTime)} --> ${this.formatTime(subtitle.endTime)}`;
            
            const textSpan = document.createElement('div');
            textSpan.className = 'subtitle-text';
            textSpan.textContent = subtitle.text;
            
            subtitleItem.appendChild(timeSpan);
            subtitleItem.appendChild(textSpan);
            
            // 点击字幕项高亮并播放音频（如果有）
            subtitleItem.addEventListener('click', () => {
                // 取消上一个高亮
                if (this.currentIndex >= 0 && this.subtitles[this.currentIndex]) {
                    this.subtitles[this.currentIndex].element.classList.remove('active');
                }
                // 设置当前高亮
                this.currentIndex = index;
                subtitleItem.classList.add('active');
                this.scrollToActiveSubtitle();
                this.updateGrammarButtonState();
                // 播放对应音频片段
                this.playSubtitle(index);
            });
            
            // 双击字幕项打开问答模态框
            subtitleItem.addEventListener('dblclick', () => {
                this.openSubtitleChat(index);
            });
            
            this.subtitleContainer.appendChild(subtitleItem);
            subtitle.element = subtitleItem;
        });
        
        // 添加语法可视化按钮到字幕控制区域
        this.addGrammarVisualizationButton();
    }
    
    // 过滤字幕，根据搜索关键字展示匹配项
    filterSubtitles() {
        const keyword = (this.subtitleSearchBox && this.subtitleSearchBox.value.trim().toLowerCase()) || '';
        this.subtitles.forEach(sub => {
            if (!keyword || sub.text.toLowerCase().includes(keyword)) {
                sub.element.style.display = '';
            } else {
                sub.element.style.display = 'none';
            }
        });
    }
    
    // 播放指定字幕片段并在结束时停止
    playSubtitle(index) {
        const subtitle = this.subtitles[index];
        const activePlayer = this.getActiveAudioPlayer();
        if (!activePlayer) return;
        // 移除旧的 timeupdate 监听
        if (this.currentTimeUpdateHandler) {
            activePlayer.removeEventListener('timeupdate', this.currentTimeUpdateHandler);
            this.currentTimeUpdateHandler = null;
        }
        activePlayer.currentTime = subtitle.startTime;
        activePlayer.play().catch(console.error);
        this.currentTimeUpdateHandler = () => {
            if (activePlayer.currentTime >= subtitle.endTime) {
                activePlayer.pause();
                activePlayer.removeEventListener('timeupdate', this.currentTimeUpdateHandler);
                this.currentTimeUpdateHandler = null;
            }
        };
        activePlayer.addEventListener('timeupdate', this.currentTimeUpdateHandler);
    }
    
    // 显示字幕区域
    showSubtitleArea(fileName) {
        if (this.subtitleUploadArea) {
            this.subtitleUploadArea.style.display = 'none';
        }
        
        if (this.subtitleDisplayArea) {
            this.subtitleDisplayArea.classList.remove('hidden');
        }
        
        // 移除文件名显示相关代码，因为已经不需要显示文件名了
    }
    
    // 跳转到指定时间
    seekToTime(time) {
        // 获取当前活跃的音频播放器
        const activePlayer = this.getActiveAudioPlayer();
        if (activePlayer) {
            activePlayer.currentTime = time;
            // 如果音频暂停，则播放
            if (activePlayer.paused) {
                activePlayer.play().catch(console.error);
            }
        }
    }
    
    // 获取当前活跃的音频播放器
    getActiveAudioPlayer() {
        const uploadedPlayer = document.getElementById('uploadedPlayer');
        const recordingPlayer = document.getElementById('recordingPlayer');
        
        // 优先检查上传的音频播放器
        if (uploadedPlayer && uploadedPlayer.src && !uploadedPlayer.paused) {
            return uploadedPlayer;
        }
        
        // 检查录音播放器
        if (recordingPlayer && recordingPlayer.src && !recordingPlayer.paused) {
            return recordingPlayer;
        }
        
        // 如果都暂停，返回有内容的播放器
        if (uploadedPlayer && uploadedPlayer.src) {
            return uploadedPlayer;
        }
        
        if (recordingPlayer && recordingPlayer.src) {
            return recordingPlayer;
        }
        
        return null;
    }
    
    // 开始同步
    startSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }
        
        this.isActive = true;
        this.syncInterval = setInterval(() => {
            this.updateSubtitleSync();
        }, 100); // 每100ms检查一次
    }
    
    // 停止同步
    stopSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
        
        // 取消滚动动画
        if (this.scrollAnimation) {
            cancelAnimationFrame(this.scrollAnimation);
            this.scrollAnimation = null;
        }
        
        this.isActive = false;
        
        // 清除当前高亮
        if (this.currentIndex >= 0 && this.subtitles[this.currentIndex]) {
            this.subtitles[this.currentIndex].element?.classList.remove('active');
        }
        this.currentIndex = -1;
    }
    
    // 更新字幕同步
    updateSubtitleSync() {
        if (!this.isActive || this.subtitles.length === 0) return;
        
        const activePlayer = this.getActiveAudioPlayer();
        if (!activePlayer) return;
        
        const currentTime = activePlayer.currentTime;
        
        // 找到当前时间对应的字幕
        let newIndex = -1;
        for (let i = 0; i < this.subtitles.length; i++) {
            const subtitle = this.subtitles[i];
            if (currentTime >= subtitle.startTime && currentTime <= subtitle.endTime) {
                newIndex = i;
                break;
            }
        }
        
        // 如果字幕索引发生变化
        if (newIndex !== this.currentIndex) {
            // 移除之前的高亮
            if (this.currentIndex >= 0 && this.subtitles[this.currentIndex]) {
                this.subtitles[this.currentIndex].element?.classList.remove('active');
            }
            
            // 设置新的高亮
            this.currentIndex = newIndex;
            if (this.currentIndex >= 0 && this.subtitles[this.currentIndex]) {
                const activeElement = this.subtitles[this.currentIndex].element;
                activeElement?.classList.add('active');
                
                // 自动滚动到当前字幕
                this.scrollToActiveSubtitle();
                
                // 更新语法可视化按钮状态
                this.updateGrammarButtonState();
            }
        }
    }
    
    // 平滑滚动到指定位置
    smoothScrollToPosition(container, targetScrollTop) {
        // 取消之前的滚动动画
        if (this.scrollAnimation) {
            cancelAnimationFrame(this.scrollAnimation);
        }
        
        const startScrollTop = container.scrollTop;
        const distance = targetScrollTop - startScrollTop;
        const duration = 300; // 300ms动画时长
        const startTime = performance.now();
        
        const animateScroll = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用easeOutCubic缓动函数
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            
            container.scrollTop = startScrollTop + (distance * easeOutCubic);
            
            if (progress < 1) {
                this.scrollAnimation = requestAnimationFrame(animateScroll);
            } else {
                this.scrollAnimation = null;
            }
        };
        
        this.scrollAnimation = requestAnimationFrame(animateScroll);
    }
    
    // 滚动到当前激活的字幕
    scrollToActiveSubtitle() {
        if (this.currentIndex < 0 || !this.subtitles[this.currentIndex]) return;
        
        const activeElement = this.subtitles[this.currentIndex].element;
        const container = this.subtitleContainer;
        
        if (activeElement && container) {
            // 使用requestAnimationFrame确保DOM更新完成
            requestAnimationFrame(() => {
                const containerHeight = container.clientHeight;
                const elementTop = activeElement.offsetTop;
                const elementHeight = activeElement.clientHeight;
                
                // 计算使元素位于容器中心偏上的位置（约1/3处）
                const elementCenter = elementTop + (elementHeight / 2);
                const containerTargetPosition = containerHeight * 0.35; // 从中心移到35%位置
                const targetScrollTop = elementCenter - containerTargetPosition;
                
                // 边界检查
                const maxScrollTop = container.scrollHeight - containerHeight;
                const finalScrollTop = Math.min(Math.max(0, targetScrollTop), maxScrollTop);
                
                // 使用平滑滚动动画，但添加防抖逻辑
                this.smoothScrollToPosition(container, finalScrollTop);
                
                console.log(`字幕居中滚动: 元素${this.currentIndex}, 滚动到${finalScrollTop}px`);
            });
        }
    }
    
    // 清除字幕
    clearSubtitles() {
        this.stopSync();
        this.subtitles = [];
        this.currentIndex = -1;
        
        if (this.subtitleContainer) {
            this.subtitleContainer.innerHTML = '';
        }
        
        if (this.subtitleDisplayArea) {
            this.subtitleDisplayArea.classList.add('hidden');
        }
        
        if (this.subtitleUploadArea) {
            this.subtitleUploadArea.style.display = 'block';
        }
        
        // 重置文件输入
        if (this.subtitleFileInput) {
            this.subtitleFileInput.value = '';
        }
    }
    
    // 格式化时间显示
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        const ms = Math.floor((seconds % 1) * 1000);
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`;
    }
    
    // 显示错误信息
    showError(message) {
        console.error('字幕管理器错误:', message);
        
        // 如果有toast显示功能，使用它
        if (window.audioApp && typeof window.audioApp.showToast === 'function') {
            window.audioApp.showToast(message, 'error');
        } else {
            alert(message);
        }
    }
    
    // 通用toast显示方法，确保 this.showToast 可用
    showToast(message, type = 'info') {
        if (window.audioApp && typeof window.audioApp.showToast === 'function') {
            window.audioApp.showToast(message, type);
        } else {
            console.log(`Toast (${type}): ${message}`);
        }
    }
    
    // 获取国际化文本
    getI18nText(key, fallback = '') {
        if (window.UILanguage && typeof window.UILanguage.getText === 'function') {
            return window.UILanguage.getText(key) || fallback;
        }
        return fallback;
    }
    
    // 打开字幕问答对话框
    openSubtitleChat(subtitleIndex) {
        if (!window.subtitleChatManager) {
            console.error('字幕问答管理器未初始化');
            return;
        }
        
        if (subtitleIndex < 0 || subtitleIndex >= this.subtitles.length) {
            console.error('无效的字幕索引:', subtitleIndex);
            return;
        }
        
        // 打开字幕问答模态框
        window.subtitleChatManager.openModal(subtitleIndex, this.subtitles);
    }
    
    // 上传字幕文件到服务器
    async uploadSubtitleToServer(file, content, skipCreditsCheck = false) {
        if (!file) {
            console.error('没有可上传的字幕文件');
            this.showToast('没有可上传的字幕文件', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('subtitleFile', file);
        formData.append('filename', file.name);
        
        const fileId = `subtitle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        formData.append('file_id', fileId);
        
        // 新增：如果前端免检查生效，传递给后端
        if (skipCreditsCheck) {
            formData.append('skip_credits_check', 'true');
            console.log('🚀 [字幕上传] 传递免检查标志给后端: skip_credits_check=true');
        }

        // 获取认证信息
        let headers = {};
        
        try {
            // 优先尝试从统一认证管理器获取token
            if (window.unifiedAuthManager && typeof window.unifiedAuthManager.getAccessToken === 'function') {
                const token = window.unifiedAuthManager.getAccessToken();
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                    console.log('🔐 [字幕上传] 使用统一认证管理器token');
                }
            }
            // 备用方案：使用全局方法
            else if (window.getAuthToken && typeof window.getAuthToken === 'function') {
                const token = window.getAuthToken();
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                    console.log('🔐 [字幕上传] 使用全局getAuthToken方法');
                }
            }
            // 备用方案：从localStorage获取token
            else if (window.localStorage) {
                const authData = localStorage.getItem('supabase.auth.token');
                if (authData) {
                    const parsed = JSON.parse(authData);
                    if (parsed.access_token) {
                        headers['Authorization'] = `Bearer ${parsed.access_token}`;
                        console.log('🔐 [字幕上传] 使用localStorage token');
                    }
                }
            }
        } catch (authError) {
            console.warn('🔐 [字幕上传] 获取认证信息失败:', authError);
        }

        try {
            const response = await fetch('/api/upload-subtitle', {
                method: 'POST',
                headers: headers,
                body: formData,
                credentials: 'include', // 确保发送cookies
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.subtitleFileId = result.file_id;
                console.log('字幕文件上传成功, ID:', this.subtitleFileId);

                // 触发事件前先派发文件ID，确保TimestampManager能够捕获
                document.dispatchEvent(new CustomEvent('subtitleFileUploaded', { 
                    detail: { 
                        fileId: this.subtitleFileId,
                        filename: result.filename
                    } 
                }));

                // 显示成功提示
                const successMsg = this.getI18nText('subtitle.upload_success', '字幕文件上传成功');
                this.showToast(successMsg, 'success');
            } else {
                // 检查是否是积分不足错误
                if (result.success === false && result.error && result.error.includes('积分')) {
                    if (window.CreditsManager && typeof window.CreditsManager.showInsufficientCreditsAlert === 'function') {
                        // 使用前端缓存的积分显示，而不是后端返回的验证失败值
                        const frontendCredits = window.CreditsManager.getCachedCredits();
                        const creditsToShow = frontendCredits !== null ? frontendCredits : 0;
                        console.log(`🔍 [字幕上传] 积分不足，显示前端积分: ${creditsToShow}`);
                        window.CreditsManager.showInsufficientCreditsAlert(creditsToShow);
                    } else {
                        alert(result.error);
                    }
                    return;
                }
                throw new Error(result.error || '上传失败');
            }
        } catch (error) {
            console.error('上传字幕文件失败:', error);

            // 显示错误提示
            const failMsg = this.getI18nText('subtitle.upload_failed', '字幕文件上传失败');
            this.showToast(`${failMsg}: ${error.message}`, 'error');
        }
    }
    
    // 检查是否有字幕数据
    hasSubtitles() {
        return this.subtitles && this.subtitles.length > 0;
    }
    
    // 获取当前字幕状态
    getStatus() {
        return {
            hasSubtitles: this.hasSubtitles(),
            subtitleCount: this.subtitles.length,
            isActive: this.isActive
        };
    }
    
    // 添加语法可视化按钮到字幕控制区域
    addGrammarVisualizationButton() {
        // 查找字幕搜索框容器
        const searchContainer = document.querySelector('.qa-controls .search-box');
        if (!searchContainer) return;
        
        // 检查是否已经添加了按钮
        if (document.getElementById('grammarVizBtn')) {
            return;
        }
        
        // 创建语法可视化按钮
        const grammarBtn = document.createElement('button');
        grammarBtn.id = 'grammarVizBtn';
        // 与"随时问"按钮保持一致的样式
        grammarBtn.className = 'btn btn-info grammar-viz-btn';
        grammarBtn.title = '分析当前高亮字幕的语法结构';
        grammarBtn.innerHTML = '<i class="fas fa-project-diagram"></i> <span>语法可视化</span>';
        grammarBtn.style.marginLeft = '0px';  // 左侧与上一步按钮间距
        grammarBtn.style.marginRight = '10px';  // 右侧与下一按钮间距
        grammarBtn.disabled = true; // 初始禁用
        
        // 添加点击事件
        grammarBtn.addEventListener('click', () => {
            this.analyzeCurrentHighlightedSubtitle();
        });
        
        // 插入到搜索框后面
        searchContainer.parentNode.insertBefore(grammarBtn, searchContainer.nextSibling);
        
        // 监听字幕高亮变化，更新按钮状态
        this.updateGrammarButtonState();
    }
    
    // 更新语法可视化按钮状态
    updateGrammarButtonState() {
        const grammarBtn = document.getElementById('grammarVizBtn');
        if (!grammarBtn) return;
        
        // 检查当前是否有高亮的字幕且包含英文
        const currentSubtitle = this.getCurrentHighlightedSubtitle();
        if (currentSubtitle && this.hasEnglishSentence(currentSubtitle.text)) {
            grammarBtn.disabled = false;
            grammarBtn.title = `分析: "${currentSubtitle.text.substring(0, 30)}${currentSubtitle.text.length > 30 ? '...' : ''}"`;
        } else {
            grammarBtn.disabled = true;
            grammarBtn.title = '请先播放音频，当前高亮字幕中没有英文句子';
        }
    }
    
    // 获取当前高亮的字幕
    getCurrentHighlightedSubtitle() {
        if (this.currentIndex >= 0 && this.currentIndex < this.subtitles.length) {
            return this.subtitles[this.currentIndex];
        }
        return null;
    }
    
    // 分析当前高亮字幕的语法结构
    async analyzeCurrentHighlightedSubtitle() {
        const currentSubtitle = this.getCurrentHighlightedSubtitle();
        if (!currentSubtitle) {
            this.showToast('没有当前高亮的字幕', 'warning');
            return;
        }
        
        if (!this.hasEnglishSentence(currentSubtitle.text)) {
            this.showToast('当前字幕中没有英文句子', 'warning');
            return;
        }
        
        // 检查积分
        if (window.Credits && typeof window.Credits.checkCredits === 'function') {
            const hasCredits = await window.Credits.checkCredits('grammar_analysis', 2);
            if (!hasCredits) {
                this.showToast('积分不足，无法进行语法分析', 'error');
                return;
            }
        }
        
        // 显示语法可视化模态框
        this.showSidebarGrammar(currentSubtitle.text, this.currentIndex);
    }
    
    // 检测文本是否包含英文句子
    hasEnglishSentence(text) {
        // 简单的英文句子检测：包含英文字母和基本标点符号
        const englishPattern = /[a-zA-Z]+.*[.!?]/;
        return englishPattern.test(text);
    }
    
    // 侧边栏展示
    showSidebarGrammar(text, subtitleIndex) {
        // 检查是否已存在侧边栏
        const existing = document.getElementById('grammarSidebar');
        if (existing) {
            existing.remove();
        }
        
        const sidebar = document.createElement('div');
        sidebar.id = 'grammarSidebar';
        sidebar.className = 'grammar-sidebar';
        sidebar.innerHTML = `
            <div class="grammar-sidebar-header">
                <h3><i class="fas fa-project-diagram"></i> 语法树 - 第 ${subtitleIndex + 1} 行</h3>
                <button class="grammar-sidebar-close">&times;</button>
            </div>
            <div class="grammar-sidebar-body">
                <div id="grammarVizContainer"></div>
            </div>
        `;
        
        document.body.appendChild(sidebar);
        
        // 调整主内容区域
        const mainContent = document.querySelector('.main-content') || document.body;
        if (mainContent !== document.body) {
            mainContent.style.marginRight = '550px';
            mainContent.style.transition = 'margin-right 0.3s ease';
        } else {
            // 如果没有找到 .main-content，则创建一个包装器
            const wrapper = document.createElement('div');
            wrapper.className = 'main-content-wrapper';
            wrapper.style.marginRight = '550px';
            wrapper.style.transition = 'margin-right 0.3s ease';
            
            // 将body的所有子元素（除了侧边栏）移到包装器中
            const bodyChildren = Array.from(document.body.children);
            bodyChildren.forEach(child => {
                if (child.id !== 'grammarSidebar') {
                    wrapper.appendChild(child);
                }
            });
            
            document.body.appendChild(wrapper);
        }
        
        // 添加关闭事件
        sidebar.querySelector('.grammar-sidebar-close').addEventListener('click', () => {
            sidebar.classList.remove('show');
            
            // 恢复主内容区域布局
            const currentMainContent = document.querySelector('.main-content') || document.querySelector('.main-content-wrapper');
            if (currentMainContent) {
                currentMainContent.style.marginRight = '';
                
                // 如果是我们创建的包装器，则移除包装器并恢复原始结构
                if (currentMainContent.className === 'main-content-wrapper') {
                    const wrapperChildren = Array.from(currentMainContent.children);
                    wrapperChildren.forEach(child => {
                        document.body.appendChild(child);
                    });
                    currentMainContent.remove();
                }
            }
            
            setTimeout(() => {
                if (sidebar.parentNode) {
                    sidebar.parentNode.removeChild(sidebar);
                }
            }, 300);
        });
        
        // 显示动画
        setTimeout(() => sidebar.classList.add('show'), 10);
        
        // 执行句子成分分析并可视化
        (async () => {
            const vizContainer = document.getElementById('grammarVizContainer');
            if (!vizContainer) return;
            
            // 显示加载动画
            vizContainer.innerHTML = `
                <div class="loading-container" style="text-align: center; padding: 40px;">
                    <div class="loading-spinner" style="
                        border: 4px solid #f3f3f3;
                        border-top: 4px solid #4CAF50;
                        border-radius: 50%;
                        width: 40px;
                        height: 40px;
                        margin: 0 auto 20px;
                        animation: spin 2s linear infinite;
                    "></div>
                    <p style="color: #666;">AI正在分析句子成分，请稍候...</p>
                    <style>
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    </style>
                </div>
            `;
            
            try {
                // 使用语法分析器进行分析
                const analyzer = new window.GrammarAnalyzer();
                await analyzer.initialize();
                
                // 提取英文句子
                const sentences = analyzer.extractEnglishSentences(text);
                if (sentences.length === 0) {
                    vizContainer.innerHTML = '<p style="color:#666;text-align:center;">未检测到需要分析的英语句子</p>';
                    return;
                }
                
                // 调用新的流式API接口
                const response = await analyzer.callGrammarAnalysisAPI(sentences[0]);
                
                // 处理流式响应
                await analyzer.handleStreamResponse(response, vizContainer);
                
            } catch (e) {
                console.error('句子成分分析失败:', e);
                const vizContainer = document.getElementById('grammarVizContainer');
                if (vizContainer) vizContainer.innerHTML = '<p style="color:#e74c3c;text-align:center;">语法分析失败，请重试</p>';
            }
        })();
    }
    
    // 执行语法分析
    async performGrammarAnalysis(text) {
        // 在可视化容器中渲染结构图
        const vizContainer = document.getElementById('grammarVizContainer');
        if (!vizContainer) return;
        
        try {
            const analyzer = new window.GrammarAnalyzer();
            await analyzer.initialize();
            // 提取首个英文句子
            const [sentence] = analyzer.extractEnglishSentences(text);
            if (!sentence) {
                vizContainer.innerHTML = '<p style="text-align:center;color:#888;">未检测到英文句子</p>';
                return;
            }
            // 调用API获取流并使用新的流式渲染
            const response = await analyzer.callGrammarAnalysisAPI(sentence);
            // 使用新的流式处理方法
            await analyzer.handleStreamResponse(response, vizContainer);
        } catch (e) {
            console.error('可视化失败:', e);
            vizContainer.innerHTML = '<p style="text-align:center;color:#e74c3c;">可视化出错</p>';
        }
    }
}

// 全局实例
window.SubtitleManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    const subtitleManagerInstance = new SubtitleManager();
    window.SubtitleManager = subtitleManagerInstance;
    window.subtitleManager = subtitleManagerInstance; // 兼容小写命名
    console.log('字幕管理器已初始化');
}); 