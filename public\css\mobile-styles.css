/* 移动端专用样式文件 */
/* File: public/css/mobile-styles.css */

/* 默认显示桌面端布局，隐藏移动端布局 */
.desktop-header {
    display: block !important;
    width: 100%;
}

.mobile-header {
    display: none;
}

/* 默认隐藏移动端菜单相关元素 */
.mobile-menu-overlay,
.mobile-language-overlay {
    display: none !important;
}

/* 移动端适配 - 媒体查询 */
@media (max-width: 768px) {
    /* 基础布局调整 - 允许页面滚动 */
    html, body {
        height: auto !important;
        overflow: visible !important;
        overflow-x: hidden;
    }
    
    /* 隐藏桌面端布局，显示移动端布局 */
    .desktop-header {
        display: none !important;
    }
    
    .mobile-header {
        display: block !important;
        width: 100%;
    }
    
    /* 头部移动端适配 */
    header {
        padding: 12px !important; /* 增加顶部和底部内边距 */
        height: auto;
        min-height: 48px !important; /* 增加最小高度使标题栏更高 */
        display: block;
        grid-template-columns: none;
        grid-template-areas: none;
        position: relative;
        margin-bottom: 10px !important; /* 增加底部外边距 */
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important; /* 添加轻微阴影增强专业感 */
    }
    
    /* 移动端标题栏主容器 */
    .mobile-header-main {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        width: 100% !important;
        padding: 0 !important;
        min-height: 42px !important; /* 增加最小高度使标题栏更高 */
    }
    
    /* 移动端上传区域 */
    .mobile-upload-section {
        flex: 1 !important;
        display: flex !important;
    }
    
    /* 移动端上传按钮 */
    .mobile-upload-btn {
        display: flex !important;
        align-items: center !important;
        gap: 6px !important; /* 减小图标和文字的间距 */
        padding: 8px 10px !important; /* 减小左右内边距使按钮更窄 */
        background-color: #2196F3 !important;
        color: white !important;
        border: none !important;
        border-radius: 6px !important;
        cursor: pointer !important;
        font-size: 15px !important; /* 略微增大字体 */
        font-weight: 500 !important; /* 添加适当的字重 */
        transition: background-color 0.2s ease !important;
        text-decoration: none !important;
        width: auto !important;
        min-width: auto !important; /* 允许按钮根据内容自动调整宽度 */
        max-width: none !important; /* 移除最大宽度限制 */
        height: 42px !important; /* 增加高度 */
        position: relative !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important; /* 添加轻微阴影增强立体感 */
    }
    
    .mobile-upload-btn:hover {
        background-color: #0b7dda !important;
        transform: translateY(-1px) !important; /* 添加轻微悬浮效果 */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) !important; /* 悬浮时增强阴影 */
    }
    
    /* 移动端菜单区域 */
    .mobile-menu-section {
        margin-left: 18px !important; /* 增加左侧间距 */
        display: flex !important;
    }
    
    /* 移动端菜单按钮 */
    .mobile-menu-btn {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 48px !important; /* 增加宽度使按钮更宽 */
        height: 42px !important; /* 增加高度与上传按钮一致 */
        background-color: #f8f8f8 !important; /* 略微调亮背景色 */
        border: 1px solid #e0e0e0 !important; /* 调整边框颜色 */
        border-radius: 6px !important;
        cursor: pointer !important;
        font-size: 18px !important; /* 增大图标尺寸 */
        color: #333 !important;
        transition: all 0.2s ease !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important; /* 添加轻微阴影增强立体感 */
    }
    
    .mobile-menu-btn:hover {
        background-color: #e8e8e8 !important;
        transform: translateY(-1px) !important; /* 添加轻微悬浮效果 */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important; /* 悬浮时增强阴影 */
    }
    
    /* 重新显示移动端菜单相关元素 */
    .mobile-menu-overlay,
    .mobile-language-overlay,
    .mobile-upload-section,
    .mobile-menu-section,
    .mobile-upload-btn,
    .mobile-menu-btn {
        display: flex !important;
    }
    
    /* 但默认隐藏弹层 */
    .mobile-menu-overlay,
    .mobile-language-overlay {
        display: none !important;
    }
    
    .mobile-menu-overlay.active {
        display: flex !important;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        align-items: flex-start;
        justify-content: flex-end;
        padding-top: 70px;
        padding-right: 15px;
    }
    
    /* 移动端菜单内容 */
    .mobile-menu-content {
        background-color: white;
        border-radius: 8px;
        min-width: 250px;
        max-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        overflow: hidden;
    }
    
    /* 移动端菜单头部 */
    .mobile-menu-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 9px 16px; /* 减少上下内边距，原来是12px，减少1/4 */
        border-bottom: 1px solid #eee;
        background-color: #f8f9fa;
    }
    
    .mobile-menu-header h3 {
        margin: 0;
        font-size: 14px;
        color: #333;
        font-weight: 600;
    }
    
    .close-menu-btn {
        background: none;
        border: none;
        font-size: 16px;
        cursor: pointer;
        color: #666;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
    }
    
    .close-menu-btn:hover {
        background-color: #e9ecef;
        color: #333;
    }
    
    /* 移动端菜单项目 */
    .mobile-menu-items {
        padding: 0;
    }
    
    .mobile-menu-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s ease;
        gap: 10px;
        font-size: 14px;
    }
    
    .mobile-menu-item:last-child {
        border-bottom: none;
    }
    
    .mobile-menu-item:hover {
        background-color: #f8f9fa;
    }
    
    .mobile-menu-item i {
        width: 16px;
        font-size: 14px;
        color: #666;
        text-align: center;
    }
    
    .menu-item-content {
        display: grid;
        grid-template-columns: 1fr 60px;
        align-items: center;
        width: 100%;
        font-size: 15px; /* 增大字号，原来是14px */
        gap: 10px;
    }
    
    .credits-amount, .current-language {
        color: #2196F3;
        font-weight: 500;
        font-size: 15px; /* 增大字号，原来是13px */
        text-align: right !important;
        width: 60px !important;
        padding-right: 10px !important;
        box-sizing: border-box !important;
        justify-self: end !important;
        font-family: inherit !important; /* 使用与左侧一致的字体 */
    }
    
    /* 汉堡菜单图标样式 */
    .mobile-menu-btn i {
        transition: transform 0.2s ease;
    }
    
    .mobile-menu-btn:active i {
        transform: scale(0.9);
    }
    
    /* 移动端语言选择弹层 */
    .mobile-language-overlay.active {
        display: flex !important;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        align-items: flex-start;
        justify-content: flex-end;
        padding-top: 70px;
        padding-right: 15px;
    }
    
    /* 按钮容器移动端样式 */
    .buttons-container {
        position: static;
        display: flex;
        gap: 10px;
        align-items: center;
        height: auto;
        grid-area: none;
        justify-self: auto;
    }
    
    /* 标题移动端样式 */
    header h1 {
        grid-area: none;
        text-align: center;
        font-size: 20px;
        margin: 0;
    }
    
    /* 退出登录按钮移动端样式 */
    #logoutButton, #logoutButtonMobile {
        position: static;
        transform: none;
        right: auto;
        top: auto;
        padding: 8px 12px;
        font-size: 13px;
        height: auto;
    }
    
    /* 语言选择器移动端样式 */
    .language-selector, #aiLanguageSelector, #aiLanguageSelectorMobile {
        grid-area: none;
        justify-self: auto;
        margin: 0;
        position: relative;
    }
    
    /* 语言按钮移动端样式 */
    .language-button {
        width: 90px;
        padding: 8px 10px;
        font-size: 13px;
    }
    
    /* 下拉菜单移动端样式 */
    .language-dropdown, #aiLanguageDropdown, #aiLanguageDropdownMobile {
        width: 90px;
    }
    
    /* 积分和充值容器移动端样式 */
    .credits-and-recharge-container {
        gap: 8px;
    }
    
    /* 上传标签移动端样式 */
    .upload-label {
        padding: 12px 20px;
        font-size: 14px;
        width: 100%;
        max-width: 300px;
        text-align: center;
        border-radius: 8px;
        margin-bottom: 10px;
        position: relative;
        overflow: hidden;
    }
    
    /* 隐藏移动端文件输入框 - 限制在label范围内 */
    #epubInputMobile {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        opacity: 0 !important;
        cursor: pointer !important;
        z-index: 2 !important;
        pointer-events: auto !important;
        border: none !important;
        outline: none !important;
    }
    
    /* 确保上传按钮容器有相对定位 */
    .mobile-upload-btn {
        position: relative !important;
    }
    
    /* 充值按钮移动端样式 */
    #rechargeButton, #rechargeButtonMobile {
        padding: 10px 16px;
        font-size: 14px;
    }
    
    /* 移动端积分和充值容器样式 */
    .mobile-header .user-credits-container {
        font-size: 13px;
        margin-right: 8px;
    }
    
    .mobile-header .logout-button {
        font-size: 13px;
        padding: 6px 10px;
    }
    
    /* 主容器移动端布局 */
    .container {
        display: flex !important;
        flex-direction: column !important;
        height: auto !important;
        width: calc(100% - 20px) !important;
        margin: 10px !important;
        gap: 10px !important;
        overflow: visible !important;
        min-height: auto !important;
        max-height: none !important;
    }
    
    /* 列移动端样式 */
    .column {
        width: 100% !important;
        flex: none !important;
        max-width: 100% !important;
        min-width: 0 !important;
        max-height: none !important;
        margin-bottom: 0 !important;
        overflow-y: visible !important;
        height: auto !important;
        padding: 12px !important;
    }
    
    /* 章节列表移动端样式 */
    #chapters {
        order: 1;
        min-height: 225px; /* 从300px减少到225px (减少1/4) */
        max-height: 600px !important; /* 从800px减少到600px (减少1/4) */
        height: 600px !important; /* 从800px减少到600px (减少1/4) */
        overflow-y: auto !important;
    }
    
    /* 内容区域移动端样式 */
    #content {
        order: 2;
        min-height: 262px; /* 从350px减少到262px (减少1/4) */
        max-height: 600px !important; /* 从800px减少到600px (减少1/4) */
        height: 600px !important; /* 从800px减少到600px (减少1/4) */
        overflow-y: auto !important;
        position: relative !important;
    }
    
    /* AI内容区域移动端样式 - 修复输入框被挤占 */
    #aiContent {
        order: 3;
        min-height: 200px; /* 大幅减少最小高度 */
        max-height: calc(100vh - 300px) !important; /* 限制最大高度 */
        height: auto !important; /* 使用自动高度 */
        display: flex !important;
        flex-direction: column !important;
        overflow-y: hidden !important;
    }
    
    /* AI标签页移动端样式 - 重新布局 */
    .ai-tabs {
        display: flex !important;
        flex-direction: column !important;
        gap: 8px !important;
        margin-bottom: 8px !important;
    }
    
    /* 标签按钮容器移动端样式 */
    .tab-buttons {
        display: flex;
        justify-content: center !important; /* 居中显示 */
        gap: 3px !important; /* 减小间距以适应更多按钮 */
        margin-bottom: 0;
        flex-wrap: nowrap;
        overflow-x: auto;
        width: 100%;
        padding: 0 2px;
    }
    
    /* AI标签按钮移动端样式 */
    .ai-tab-button {
        flex: 1 1 auto !important; /* 允许按钮拉伸 */
        padding: 8px 6px !important;
        font-size: 13px !important;
        min-width: 60px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        border-radius: 4px !important;
        background-color: #f0f7ff;
        color: #333;
        border: none;
        text-align: center;
        justify-content: center;
        margin: 0 1px !important;
        transition: all 0.2s ease !important;
    }
    
    /* 移除默认选中第一个按钮 */
    .ai-tab-button:first-child {
        background-color: #f0f7ff;
        color: #333;
    }
    
    /* 只有active状态的按钮显示蓝色 */
    .ai-tab-button.active {
        background-color: #2196F3 !important;
        color: white !important;
        border: none;
        font-weight: 600;
    }
    
    /* 模型选择器移动端样式 - 保持已更新的样式 */
    .model-selector {
        width: 100% !important;
        margin: 3px 0 8px 0 !important;
        flex: 1;
        min-width: 0;
    }
    
    /* 当前模型移动端样式 - 优化宽度显示 */
    .current-model {
        width: 100% !important;
        padding: 6px 10px !important;
        font-size: 13px !important;
        height: auto !important;
        min-height: 35px !important;
        box-sizing: border-box !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        min-width: 0;
        overflow: hidden;
        background-color: #f5f8fa;
        border: 1px solid #e1e8ed;
    }
    
    /* 模型名称文字优化 */
    .current-model #currentModelName {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        min-width: 0;
    }
    
    /* 模型下拉菜单移动端样式 */
    .model-dropdown {
        width: 100%;
        left: 0;
        right: 0;
    }
    
    /* AI标签内容移动端样式 - 修复输入框被挤占 */
    .ai-tab-content {
        min-height: 150px !important; /* 减少最小高度 */
        max-height: calc(100vh - 400px) !important; /* 限制最大高度 */
        overflow-y: hidden !important;
        flex: 1 !important;
        height: auto !important; /* 使用自动高度 */
        display: none !important;
    }
    
    .ai-tab-content.active {
        display: flex !important;
        flex-direction: column !important;
    }
    
    /* AI摘要和脑图移动端样式 */
    #aiSummary, #aiMindmap, #aiKnowledgeGraph {
        min-height: 350px !important;
        max-height: calc(100% - 50px) !important;
        overflow-y: auto !important;
        font-size: 14px;
        line-height: 1.5;
        flex: 1 !important;
        height: calc(100% - 50px) !important;
        margin-bottom: 3px !important;
    }
    
    /* 知识图谱容器移动端样式 */
    .knowledgegraph-container {
        height: calc(100% - 50px) !important;
        min-height: 420px !important;
        max-height: calc(100% - 50px) !important;
        margin-bottom: 3px !important;
        overflow: hidden !important;
        flex: 1 !important;
    }
    
    /* 生成按钮移动端样式 */
    #generateSummaryBtn, 
    #generateMindmapBtn, 
    #generateKnowledgeGraphBtn {
        width: 100% !important;
        padding: 12px !important;
        font-size: 14px !important;
        margin-top: auto !important;
        margin-bottom: 3px !important;
        border-radius: 6px !important;
        background-color: #2196F3 !important;
        color: white !important;
        border: none !important;
        cursor: pointer !important;
        display: block !important;
        position: sticky !important;
        bottom: 3px !important;
    }
    
    #generateSummaryBtn:hover, 
    #generateMindmapBtn:hover, 
    #generateKnowledgeGraphBtn:hover {
        background-color: #0b7dda !important;
    }
    
    /* 内容头部移动端样式 */
    .content-header {
        flex-direction: row !important;
        gap: 10px !important;
        align-items: center !important;
        justify-content: space-between !important;
        position: relative !important;
        margin-bottom: 12px !important;
        padding-right: 0 !important;
        height: 32px !important;
    }
    
    /* 粘贴按钮移动端样式 */
    .paste-button {
        position: absolute !important;
        top: 2px !important;
        right: 15px !important;
        z-index: 1000 !important;
        padding: 6px 12px !important;
        margin: 0 !important;
        font-size: 14px !important;
        border-radius: 4px !important;
        background-color: #f0f8ff !important;
        color: #2196F3 !important;
        border: 1px solid #2196F3 !important;
        cursor: pointer !important;
    }
    
    /* 章节内容移动端样式 */
    #chapterContent {
        font-size: 14px;
        line-height: 1.6;
        padding: 10px;
    }
    
    /* 粘贴内容移动端样式 */
    .pasted-content {
        font-size: 14px;
        line-height: 1.5;
        padding: 10px;
        margin: 5px 0 15px 0;
        max-height: 300px;
    }
    
    /* 模态对话框移动端样式 */
    .modal-content {
        width: 95%;
        max-width: none;
        margin: 10px;
        max-height: 90vh;
    }
    
    /* 模态对话框头部移动端样式 */
    .modal-header {
        padding: 12px 15px;
    }
    
    /* 模态对话框主体移动端样式 */
    .modal-body {
        padding: 15px;
    }
    
    /* 模态对话框底部移动端样式 */
    .modal-footer {
        padding: 12px 15px;
        flex-direction: column;
        gap: 8px;
    }
    
    /* 输入组移动端样式 */
    .input-group textarea {
        min-height: 150px;
        font-size: 14px;
    }
    
    /* 按钮移动端样式 */
    .confirm-button, .cancel-button {
        width: 100%;
        padding: 12px;
        font-size: 14px;
    }
    
    /* 图表控制按钮移动端样式 */
    .graph-controls {
        top: 5px;
        right: 5px;
        gap: 3px;
        padding: 3px;
    }
    
    .graph-control-button {
        width: 25px;
        height: 25px;
        font-size: 14px;
    }
    
    /* 脑图控制移动端样式 */
    .mindmap-controls {
        top: 5px;
        right: 5px;
        gap: 3px;
        padding: 3px;
    }
    
    .mindmap-controls button {
        width: 25px;
        height: 25px;
        font-size: 12px;
        padding: 2px;
    }
    
    /* 隐藏一些在移动端不必要的元素 */
    .scroll-hint {
        display: none;
    }
    
    /* API费用信息移动端样式 */
    .api-cost-info {
        font-size: 11px;
        padding: 8px;
        margin-top: 10px;
    }
    
    .api-cost-info h3 {
        font-size: 12px;
        margin-bottom: 6px;
    }
    
    /* 用户积分容器移动端样式 */
    .user-credits-container {
        font-size: 13px;
    }
    
    .credits-icon {
        font-size: 14px;
    }
    
    /* 章节项目移动端样式 */
    .chapter-item {
        padding: 8px 10px;
        font-size: 14px;
        margin-bottom: 5px;
    }
    
    /* 标题移动端样式 */
    h3 {
        font-size: 16px;
        margin-bottom: 10px;
    }
    
    /* 移动端充值模态框样式 */
    .recharge-modal {
        align-items: flex-start !important;
        padding-top: 70px !important;
        z-index: 10000 !important;
    }
    
    .recharge-modal-content {
        width: 95% !important;
        max-width: none !important;
        margin: 0 auto !important;
        padding: 12px !important;
        max-height: calc(100vh - 80px) !important;
    }
    
    .recharge-modal-header h2 {
        font-size: 16px !important;
        margin: 0 !important;
    }
    
    .recharge-description {
        font-size: 13px !important;
        margin: 8px 0 !important;
    }
    
    .package-card h3 {
        font-size: 14px !important;
        margin: 0 0 6px 0 !important;
    }
    
    .package-description {
        font-size: 12px !important;
        margin: 4px 0 !important;
    }
    
    .package-credits {
        font-size: 14px !important;
        font-weight: bold !important;
        margin: 6px 0 !important;
    }
    
    .package-price {
        font-size: 16px !important;
        font-weight: bold !important;
        color: #4a90e2 !important;
        margin: 6px 0 !important;
    }
    
    .select-package-btn {
        padding: 8px 14px !important;
        font-size: 13px !important;
        margin-top: 8px !important;
    }
    
    .payment-note {
        font-size: 12px !important;
        margin-top: 12px !important;
        line-height: 1.3 !important;
    }
    
    .qrcode-prompt {
        font-size: 13px !important;
    }
    
    .popular-badge {
        font-size: 10px !important;
        padding: 2px 5px !important;
        top: -6px !important;
        right: 6px !important;
    }
    
    .processing-message, .error-message {
        font-size: 13px !important;
    }
    
    .try-again-btn {
        font-size: 13px !important;
        padding: 6px 14px !important;
        margin-top: 8px !important;
    }
    
    /* 移动端滚动条优化 */
    #aiSummary::-webkit-scrollbar, 
    #aiMindmap::-webkit-scrollbar, 
    .knowledgegraph-container::-webkit-scrollbar,
    #chapterContent::-webkit-scrollbar,
    #chapters::-webkit-scrollbar {
        width: 4px !important;
    }
    
    #aiSummary::-webkit-scrollbar-track, 
    #aiMindmap::-webkit-scrollbar-track, 
    .knowledgegraph-container::-webkit-scrollbar-track,
    #chapterContent::-webkit-scrollbar-track,
    #chapters::-webkit-scrollbar-track {
        background: #f5f5f5 !important;
        border-radius: 2px !important;
    }
    
    #aiSummary::-webkit-scrollbar-thumb, 
    #aiMindmap::-webkit-scrollbar-thumb, 
    .knowledgegraph-container::-webkit-scrollbar-thumb,
    #chapterContent::-webkit-scrollbar-thumb,
    #chapters::-webkit-scrollbar-thumb {
        background: #c8c8c8 !important;
        border-radius: 2px !important;
    }
    
    #aiSummary::-webkit-scrollbar-thumb:hover, 
    #aiMindmap::-webkit-scrollbar-thumb:hover, 
    .knowledgegraph-container::-webkit-scrollbar-thumb:hover,
    #chapterContent::-webkit-scrollbar-thumb:hover,
    #chapters::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8 !important;
    }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
    header {
        padding: 10px !important; /* 调整内边距但保持足够空间 */
        min-height: 46px !important; /* 即使在小屏幕上也保持足够高度 */
    }
    
    /* 移动端上传按钮在小屏幕上的样式 */
    .mobile-upload-btn {
        padding: 6px 10px !important; /* 进一步减小内边距 */
        font-size: 13px !important; /* 稍微减小字体 */
        max-width: 160px !important;
        height: 30px !important; /* 更小的高度 */
    }
    
    /* 移动端菜单按钮在小屏幕上的样式 */
    .mobile-menu-btn {
        width: 32px !important; /* 减小宽度 */
        height: 30px !important; /* 减小高度，与上传按钮一致 */
        font-size: 14px !important; /* 减小图标大小 */
    }
    
    /* 移动端标题栏主容器在小屏幕上的样式 */
    .mobile-header-main {
        min-height: 30px !important; /* 确保最小高度与按钮一致 */
    }
    
    .mobile-upload-btn span {
        display: none;
    }
    
    .mobile-upload-btn::after {
        content: attr(data-after-text, "上传");
    }
    
    /* 移动端菜单内容在小屏幕上的样式 */
    .mobile-menu-overlay.active,
    .mobile-language-overlay.active {
        padding-top: 60px;
        padding-right: 10px;
    }
    
    .mobile-menu-content {
        min-width: 220px;
        max-width: 280px;
    }
    
    .mobile-menu-item {
        padding: 12px 16px;
        font-size: 14px;
    }
    
    .mobile-menu-item i {
        font-size: 14px;
    }
    
    .container {
        margin: 5px !important;
        width: calc(100% - 10px) !important;
        gap: 10px !important;
    }
    
    .column {
        padding: 10px !important;
        margin-bottom: 0 !important;
    }
    
    .ai-tab-button {
        font-size: 11px !important;
        padding: 5px 5px !important;
        height: auto !important;
        flex: 0 0 auto !important;
        white-space: nowrap !important;
        overflow: visible !important;
    }
    
    .upload-label {
        padding: 10px 16px;
        font-size: 13px;
        max-width: 280px;
    }
    
    #rechargeButton, #rechargeButtonMobile, #logoutButton, #logoutButtonMobile {
        font-size: 12px;
        padding: 8px 12px;
    }
    
    .language-button {
        width: 80px;
        font-size: 12px;
        padding: 6px 8px;
    }
    
    .language-dropdown, #aiLanguageDropdown, #aiLanguageDropdownMobile {
        width: 80px;
    }
    
    .current-model {
        font-size: 10px;
        padding: 4px 8px;
        height: 28px !important;
        box-sizing: border-box !important;
        min-width: 0;
        overflow: hidden;
    }
    
    /* 超小屏幕模型名称优化 */
    .current-model #currentModelName {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        min-width: 0;
    }
    
    #chapters {
        min-height: 210px !important; /* 从280px减少到210px (减少1/4) */
        max-height: 600px !important; /* 从800px减少到600px (减少1/4) */
        height: 600px !important; /* 从800px减少到600px (减少1/4) */
    }
    
    #content {
        min-height: 225px !important; /* 从300px减少到225px (减少1/4) */
        max-height: 600px !important; /* 从800px减少到600px (减少1/4) */
        height: 600px !important; /* 从800px减少到600px (减少1/4) */
    }
    
    #aiContent {
        min-height: 200px !important; /* 减少最小高度 */
        max-height: calc(100vh - 300px) !important; /* 限制最大高度 */
        height: auto !important; /* 使用自动高度 */
    }
    
    .ai-tab-content {
        min-height: 150px !important; /* 减少最小高度 */
        max-height: calc(100vh - 400px) !important; /* 限制最大高度 */
        height: auto !important; /* 使用自动高度 */
    }
    
    #aiSummary, #aiMindmap, #aiKnowledgeGraph {
        min-height: 350px !important;
        max-height: calc(100% - 50px) !important;
        height: calc(100% - 50px) !important;
        font-size: 13px;
    }
    
    .knowledgegraph-container {
        height: calc(100% - 50px) !important;
        min-height: 420px !important;
        max-height: calc(100% - 50px) !important;
        flex: 1 !important;
    }
    
    .pasted-content {
        font-size: 13px;
        max-height: none !important;
        height: auto !important;
        padding: 8px;
    }
    
    #chapterContent {
        font-size: 13px;
        padding: 8px;
    }
    
    /* 超小屏幕充值模态框样式 */
    .recharge-modal {
        padding-top: 60px !important;
    }
    
    .recharge-modal-content {
        width: 98% !important;
        padding: 10px !important;
        max-height: calc(100vh - 70px) !important;
    }
    
    .recharge-modal-header h2 {
        font-size: 15px !important;
    }
    
    .recharge-description {
        font-size: 12px !important;
        margin: 6px 0 !important;
    }
    
    .package-card {
        padding: 12px !important;
    }
    
    .package-card h3 {
        font-size: 13px !important;
        margin: 0 0 5px 0 !important;
    }
    
    .package-description {
        font-size: 11px !important;
        margin: 3px 0 !important;
    }
    
    .package-credits {
        font-size: 13px !important;
        margin: 5px 0 !important;
    }
    
    .package-price {
        font-size: 15px !important;
        margin: 5px 0 !important;
    }
    
    .select-package-btn {
        padding: 6px 12px !important;
        font-size: 12px !important;
        margin-top: 6px !important;
    }
    
    .payment-note {
        font-size: 11px !important;
        margin-top: 10px !important;
        line-height: 1.2 !important;
    }
    
    .qrcode-prompt {
        font-size: 12px !important;
    }
    
    .wechat-qrcode {
        width: 100px !important;
        height: 100px !important;
    }
    
    .popular-badge {
        font-size: 9px !important;
        padding: 1px 4px !important;
        top: -5px !important;
        right: 5px !important;
    }
    
    .processing-message, .error-message {
        font-size: 12px !important;
    }
    
    .try-again-btn {
        font-size: 12px !important;
        padding: 5px 12px !important;
        margin-top: 6px !important;
    }
    
    /* 移动端滚动条优化 */
    #aiSummary::-webkit-scrollbar, 
    #aiMindmap::-webkit-scrollbar, 
    .knowledgegraph-container::-webkit-scrollbar,
    #chapterContent::-webkit-scrollbar,
    #chapters::-webkit-scrollbar {
        width: 3px !important;
    }
    
    #aiSummary::-webkit-scrollbar-track, 
    #aiMindmap::-webkit-scrollbar-track, 
    .knowledgegraph-container::-webkit-scrollbar-track,
    #chapterContent::-webkit-scrollbar-track,
    #chapters::-webkit-scrollbar-track {
        background: #f8f8f8 !important;
        border-radius: 2px !important;
    }
    
    #aiSummary::-webkit-scrollbar-thumb, 
    #aiMindmap::-webkit-scrollbar-thumb, 
    .knowledgegraph-container::-webkit-scrollbar-thumb,
    #chapterContent::-webkit-scrollbar-thumb,
    #chapters::-webkit-scrollbar-thumb {
        background: #d0d0d0 !important;
        border-radius: 2px !important;
    }
    
    #aiSummary::-webkit-scrollbar-thumb:hover, 
    #aiMindmap::-webkit-scrollbar-thumb:hover, 
    .knowledgegraph-container::-webkit-scrollbar-thumb:hover,
    #chapterContent::-webkit-scrollbar-thumb:hover,
    #chapters::-webkit-scrollbar-thumb:hover {
        background: #b0b0b0 !important;
    }
    
    /* 调整标签按钮间距 */
    .tab-buttons {
        gap: 2px !important;
    }
} 