/* 桌面端菜单优化 - 减少标题栏高度，调整字体 */

/* 减少桌面端菜单标题栏高度并优化垂直居中 */
.desktop-menu-header {
    padding: 9px 16px !important; /* 减少上下内边距，原来是12px，减少1/4 */
    display: flex !important;
    align-items: center !important; /* 确保垂直居中 */
    height: 40px !important; /* 设置固定高度 */
    box-sizing: border-box !important;
}

/* 桌面端菜单标题文本垂直居中 */
.desktop-menu-header h3 {
    margin: 0 !important;
    padding: 0 !important;
    line-height: 40px !important; /* 使用与容器相同的高度确保完全垂直居中 */
    height: 40px !important; /* 确保高度与容器一致 */
    display: flex !important;
    align-items: center !important;
    font-size: 15px !important; /* 增大标题字体大小 */
}

/* 关闭按钮垂直居中 */
.desktop-menu-header .close-menu-btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 30px !important;
    width: 30px !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 调整积分数字和语言选择字体 */
.desktop-menu-content .credits-amount, 
.desktop-menu-content .current-language {
    font-size: 15px !important; /* 增大字体大小 */
    font-family: inherit !important; /* 使用与左侧一致的字体 */
    text-align: right !important;
    justify-self: end !important;
}

/* 确保菜单项内容正确布局 */
.desktop-menu-content .menu-item-content {
    display: grid !important;
    grid-template-columns: 1fr 60px !important;
    align-items: center !important;
    width: 100% !important;
}

/* 桌面端AI显示区域优化 */

/* 1. 将TAB的加粗显示改为非加粗 */
.ai-tab-button {
    font-weight: normal !important; /* 移除加粗显示 */
}

/* 活动状态下的按钮也不加粗 */
.ai-tab-button.active {
    font-weight: normal !important;
}
