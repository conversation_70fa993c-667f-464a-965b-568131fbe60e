/**
 * UI Language System
 * Automatically detects browser language and applies UI translations across all pages
 */
const UILanguage = (function() {
    // Default language (Chinese)
    const DEFAULT_LANGUAGE = 'zh';
    
    // Supported languages
    const SUPPORTED_LANGUAGES = ['zh', 'en'];
    
    // Language display names
    const LANGUAGE_NAMES = {
        'zh': '中文',
        'en': 'English'
    };
    
    // Store loaded language data
    let languageData = null;
    
    // Flag to track if we're loading language data
    let isLoadingLanguage = false;
    
    // Store current language
    let currentLanguage = null;
    
    // Language selector modal
    let languageModal = null;
    
    /**
     * Initialize the language system
     * @param {boolean} forceDetect - Whether to force browser language detection (ignore localStorage)
     */
    function init(forceDetect = false) {
        // If there's a pre-detected language from the page script, use it
        let userLang;
        
        if (window.initialDetectedLang && !forceDetect) {
            userLang = window.initialDetectedLang;
            console.log(`Using pre-detected language: ${userLang}`);
        } else {
            // Check localStorage for previously saved preference (if not forcing detection)
            const storedLang = !forceDetect ? localStorage.getItem('uiLanguage') : null;
            
            // Get browser language directly
            const browserLang = navigator.language || navigator.userLanguage;
            console.log(`Current browser language: ${browserLang}`);
            
            // 修改的语言检测逻辑：
            // 优先级: 存储的语言偏好 > 浏览器语言 > 默认语言
            if (storedLang && SUPPORTED_LANGUAGES.includes(storedLang)) {
                userLang = storedLang;
                console.log(`Using language from localStorage: ${userLang}`);
            } else if (browserLang && browserLang.toLowerCase().startsWith('zh')) {
                // 如果浏览器语言是中文，使用中文
                userLang = 'zh';
                console.log(`Using Chinese from browser settings: ${userLang}`);
            } else if (browserLang) {
                // 如果浏览器语言不是中文，使用英文
                userLang = 'en';
                console.log(`Using English for non-Chinese browser: ${userLang}`);
            } else {
                userLang = DEFAULT_LANGUAGE;
                console.log(`Using default language: ${userLang}`);
            }
        }
        
        // 检查当前URL，如果包含lang参数，清理URL
        if (window.location.search.includes('lang=')) {
            try {
                // 使用 history API 清理URL参数而不刷新页面
                const cleanUrl = window.location.pathname + window.location.hash;
                window.history.replaceState({}, document.title, cleanUrl);
                console.log('已清理URL中的语言参数');
            } catch (error) {
                console.error('清理URL参数失败:', error);
            }
        }
        
        // 继续加载语言
        loadLanguage(userLang).then(() => {
            translatePage();
            console.log(`Page translated to: ${userLang}`);
            // Dispatch an event that translation is complete and content can be shown
            document.dispatchEvent(new CustomEvent('ui-language-loaded'));
        }).catch(error => {
            console.error('Error loading language:', error);
            // Fallback to default language if error occurs
            if (userLang !== DEFAULT_LANGUAGE) {
                console.log(`Fallback to default language: ${DEFAULT_LANGUAGE}`);
                loadLanguage(DEFAULT_LANGUAGE).then(() => {
                    translatePage();
                    document.dispatchEvent(new CustomEvent('ui-language-loaded'));
                });
            } else {
                // Still dispatch the event even if we couldn't load any language
                document.dispatchEvent(new CustomEvent('ui-language-loaded'));
            }
        });
    }
    
    /**
     * Load language file
     * @param {string} lang - Language code ('zh', 'en')
     * @returns {Promise} - Promise resolving with language data
     */
    function loadLanguage(lang) {
        if (isLoadingLanguage) {
            console.warn('[UI Language] Language loading is already in progress. Request ignored.');
            // 返回一个立即解析的Promise，避免调用链中断
            return Promise.resolve(); 
        }
        
        isLoadingLanguage = true;
        
        // 添加语言切换保护机制
        const originalPageInitializing = window.pageInitializing;
        const originalPageInitialized = window.pageInitialized;
        
        // 设置语言切换保护标志
        window.languageSwitching = true;
        console.log('🛡️ [UI Language] 语言切换保护已启用');
        
        return new Promise((resolve, reject) => {
            // Ensure we're using a supported language
            if (!SUPPORTED_LANGUAGES.includes(lang)) {
                lang = DEFAULT_LANGUAGE;
            }
            
            // Store current language
            currentLanguage = lang;
            
            // Set html lang attribute based on detected language
            document.documentElement.lang = lang === 'en' ? 'en' : 'zh-CN';
            
            // Load the language file with relative path
            fetch(`lang/${lang}.json`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Failed to load language file: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    languageData = data;
                    // Store current language in localStorage for persistence
                    localStorage.setItem('uiLanguage', lang);
                    isLoadingLanguage = false;
                    
                    // 新增：在加载语言数据后重置翻译完成标志，确保能够再次执行翻译
                    console.log(`[UI Language] Language data for '${lang}' loaded successfully. Applying translation.`);
                    // 重置翻译完成标志，允许重新翻译所有元素
                    window.translatedPageDone = false;
                    translatePage();
                    
                    // 延迟移除语言切换保护，给页面翻译一些时间
                    setTimeout(() => {
                        window.languageSwitching = false;
                        console.log('🛡️ [UI Language] 语言切换保护已移除');
                    }, 2000); // 给2秒时间完成页面翻译
                    
                    resolve(data);
                })
                .catch(error => {
                    console.error('Error loading language file:', error);
                    isLoadingLanguage = false;
                    
                    // 出错时也要移除保护
                    window.languageSwitching = false;
                    console.log('🛡️ [UI Language] 语言切换保护已移除（出错）');
                    
                    reject(error);
                });
        });
    }
    
    /**
     * Apply translations to the page
     */
    function translatePage() {
        // 防止重复调用
        if (window.translatedPageDone) {
            console.log('[UI Language] translatePage已执行过，跳过重复调用');
            return;
        }
        window.translatedPageDone = true;
        if (!languageData || isLoadingLanguage) {
            console.warn('[UI Language] translatePage called but language data is not ready or is currently loading. Aborting.');
            return;
        }
        
        // 设置翻译保护标志
        window.translatingPage = true;
        console.log('🛡️ [UI Language] 页面翻译保护已启用');
        
        // Translate elements with data-i18n attribute
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = getNestedTranslation(key);
            
            if (translation) {
                // Handle different element types
                if (element.tagName === 'INPUT' && element.getAttribute('placeholder')) {
                    element.setAttribute('placeholder', translation);
                } else if (element.tagName === 'A' && element.getAttribute('title')) {
                    // Also set title for links if they have one
                    element.setAttribute('title', translation);
                } else {
                    element.textContent = translation;
                }
            }
        });
        
        // Translate placeholders with data-i18n-placeholder attribute
        document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            const translation = getNestedTranslation(key);
            
            if (translation) {
                element.setAttribute('placeholder', translation);
            }
        });
        
        // Translate title attributes with data-i18n-title attribute
        document.querySelectorAll('[data-i18n-title]').forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            const translation = getNestedTranslation(key);
            
            if (translation) {
                element.setAttribute('title', translation);
            }
        });
        
        // Translate page title
        if (getNestedTranslation('title')) {
            document.title = getNestedTranslation('title');
        }
        
        // 新增：手动翻译特定元素的placeholder，确保总是生效
        const questionInput = document.getElementById('questionInput');
        if (questionInput) {
            const placeholderText = getNestedTranslation('audio.enter_question');
            if (placeholderText) {
                questionInput.setAttribute('placeholder', placeholderText);
                console.log('手动翻译输入框placeholder:', placeholderText);
            }
        }
        const podcastSearchBox = document.getElementById('podcastSearchBox');
        if (podcastSearchBox) {
            const placeholderText = getNestedTranslation('podcast.search_placeholder');
            if (placeholderText) {
                podcastSearchBox.setAttribute('placeholder', placeholderText);
                console.log('手动翻译播客搜索框placeholder:', placeholderText);
            }
        }
        const subtitleSearchBox = document.getElementById('subtitleSearchBox');
        if (subtitleSearchBox) {
            const placeholderText = getNestedTranslation('subtitle.search_placeholder');
            if (placeholderText) {
                subtitleSearchBox.setAttribute('placeholder', placeholderText);
                console.log('手动翻译字幕搜索框placeholder:', placeholderText);
            }
        }
        
        // 延迟移除翻译保护
        setTimeout(() => {
            window.translatingPage = false;
            console.log('🛡️ [UI Language] 页面翻译保护已移除');
        }, 1000); // 给1秒时间完成所有翻译操作
        
        // Dispatch event that translation is complete
        document.dispatchEvent(new CustomEvent('ui-language-changed'));
    }
    
    /**
     * Get a nested translation value from a dot-notation key
     * @param {string} key - Dot notation key (e.g., "auth.login")
     * @returns {string|null} - Translation or null if not found
     */
    function getNestedTranslation(key) {
        if (!key || !languageData) return null;
        
        const keys = key.split('.');
        let value = languageData;
        
        for (const k of keys) {
            value = value[k];
            if (value === undefined) return null;
        }
        
        return value;
    }
    
    /**
     * Get a translation by key with fallback handling
     * @param {string} key - Translation key (can be dot notation)
     * @param {string} [fallback] - Optional fallback text if translation is not found
     * @returns {string} - Translation or fallback or the key itself if not found
     */
    function getText(key, fallback) {
        if (!key) return fallback || '';
        
        // Use dot notation to navigate the language data
        const translation = getNestedTranslation(key);
        
        // If a translation was found, return it
        if (translation !== null && translation !== undefined) {
            return translation;
        }
        
        // If no translation found but fallback provided, return fallback
        if (fallback !== undefined) {
            return fallback;
        }
        
        // Last resort: return the key itself
        return key;
    }
    
    /**
     * Get current language data
     * @returns {object} - Current language data object
     */
    function getLanguageData() {
        return languageData;
    }
    
    /**
     * Get current language code
     * @returns {string} - Current language code
     */
    function getCurrentLanguage() {
        return currentLanguage;
    }
    
    /**
     * Create and show the language selection modal
     */
    function showLanguageSelector() {
        // Remove existing modal if any
        if (languageModal) {
            document.body.removeChild(languageModal);
        }
        
        // Create modal container
        languageModal = document.createElement('div');
        languageModal.className = 'language-modal';
        languageModal.style.position = 'fixed';
        languageModal.style.top = '0';
        languageModal.style.left = '0';
        languageModal.style.width = '100%';
        languageModal.style.height = '100%';
        languageModal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        languageModal.style.display = 'flex';
        languageModal.style.justifyContent = 'center';
        languageModal.style.alignItems = 'center';
        languageModal.style.zIndex = '10000';
        
        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'language-modal-content';
        modalContent.style.backgroundColor = 'white';
        modalContent.style.borderRadius = '8px';
        modalContent.style.padding = '20px';
        modalContent.style.width = '300px';
        modalContent.style.maxWidth = '90%';
        modalContent.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
        
        // Get translations from language file if available
        const modalTitle = languageData && getNestedTranslation('ui.languageSelector.title') 
            ? getNestedTranslation('ui.languageSelector.title') 
            : (currentLanguage === 'en' ? 'Select Language' : '选择语言');
        
        // Create title
        const title = document.createElement('h3');
        title.textContent = modalTitle;
        title.style.marginTop = '0';
        title.style.marginBottom = '20px';
        title.style.textAlign = 'center';
        
        // Create language options
        const optionsList = document.createElement('div');
        optionsList.style.display = 'flex';
        optionsList.style.flexDirection = 'column';
        optionsList.style.gap = '10px';
        
        // Add language options with translated names
        SUPPORTED_LANGUAGES.forEach(lang => {
            const option = document.createElement('button');
            
            // Get translated language name based on current UI language
            let displayName = '';
            if (lang === 'zh') {
                displayName = getNestedTranslation('ui.languageSelector.chinese') || '中文';
            } else if (lang === 'en') {
                displayName = getNestedTranslation('ui.languageSelector.english') || 'English';
            } else {
                displayName = LANGUAGE_NAMES[lang] || lang.toUpperCase();
            }
            
            option.textContent = displayName;
            option.style.padding = '12px';
            option.style.backgroundColor = lang === currentLanguage ? '#007bff' : '#f0f0f0';
            option.style.color = lang === currentLanguage ? 'white' : 'black';
            option.style.border = 'none';
            option.style.borderRadius = '4px';
            option.style.cursor = 'pointer';
            option.style.fontWeight = lang === currentLanguage ? 'bold' : 'normal';
            option.style.transition = 'all 0.2s ease';
            
            option.addEventListener('mouseover', () => {
                if (lang !== currentLanguage) {
                    option.style.backgroundColor = '#e0e0e0';
                }
            });
            
            option.addEventListener('mouseout', () => {
                if (lang !== currentLanguage) {
                    option.style.backgroundColor = '#f0f0f0';
                }
            });
            
            option.addEventListener('click', () => {
                // Close modal
                document.body.removeChild(languageModal);
                languageModal = null;
                
                // Load selected language if different
                if (lang !== currentLanguage) {
                    loadLanguage(lang).then(() => {
                        translatePage();
                    });
                }
            });
            
            optionsList.appendChild(option);
        });
        
        // Add "Use Browser Language" option with translation
        const browserOptionText = getNestedTranslation('ui.languageSelector.useBrowser') || 
            (currentLanguage === 'en' ? 'Use Browser Language' : '使用浏览器语言');
            
        const browserOption = document.createElement('button');
        browserOption.textContent = browserOptionText;
        browserOption.style.padding = '12px';
        browserOption.style.backgroundColor = '#f8f9fa';
        browserOption.style.color = '#6c757d';
        browserOption.style.border = '1px dashed #dee2e6';
        browserOption.style.borderRadius = '4px';
        browserOption.style.cursor = 'pointer';
        browserOption.style.marginTop = '10px';
        browserOption.style.transition = 'all 0.2s ease';
        
        browserOption.addEventListener('mouseover', () => {
            browserOption.style.backgroundColor = '#e9ecef';
        });
        
        browserOption.addEventListener('mouseout', () => {
            browserOption.style.backgroundColor = '#f8f9fa';
        });
        
        browserOption.addEventListener('click', () => {
            // Close modal
            document.body.removeChild(languageModal);
            languageModal = null;
            
            // Reset to browser language
            resetToSystemLanguage();
        });
        
        // Create close button
        const closeButton = document.createElement('button');
        closeButton.textContent = '×';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '10px';
        closeButton.style.right = '10px';
        closeButton.style.border = 'none';
        closeButton.style.background = 'none';
        closeButton.style.fontSize = '24px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#666';
        
        closeButton.addEventListener('click', () => {
            document.body.removeChild(languageModal);
            languageModal = null;
        });
        
        // Add click event to close when clicking outside modal
        languageModal.addEventListener('click', (e) => {
            if (e.target === languageModal) {
                document.body.removeChild(languageModal);
                languageModal = null;
            }
        });
        
        // Assemble modal
        modalContent.appendChild(closeButton);
        modalContent.appendChild(title);
        modalContent.appendChild(optionsList);
        modalContent.appendChild(browserOption);
        languageModal.appendChild(modalContent);
        
        // Add to body
        document.body.appendChild(languageModal);
    }
    
    /**
     * Reset language to browser setting (force detection)
     */
    function resetToSystemLanguage() {
        // Clear language from localStorage so we'll detect from browser
        localStorage.removeItem('uiLanguage');
        // Re-initialize with force detection flag
        init(true);
    }
    
    /**
     * Set a specific language
     * @param {string} lang - The language code to set
     */
    function setLanguage(lang) {
        if (!SUPPORTED_LANGUAGES.includes(lang)) {
            console.warn(`Unsupported language: ${lang}, falling back to default`);
            lang = DEFAULT_LANGUAGE;
        }
        
        // Only reload if different from current
        if (lang !== currentLanguage) {
            console.log(`Changing language from ${currentLanguage} to ${lang}`);
            loadLanguage(lang).then(() => {
                translatePage();
                // Notify any AI language settings to update
                document.dispatchEvent(new CustomEvent('ui-language-changed', {
                    detail: { language: lang }
                }));
            });
        }
    }
    
    // Initialize immediately, not waiting for DOMContentLoaded
    init();
    
    // Also listen for DOMContentLoaded to translate any elements that might be added after initial loading
    document.addEventListener('DOMContentLoaded', function() {
        if (languageData) {
            translatePage();
        }
        
        // Setup any language toggle buttons (new unified approach)
        document.querySelectorAll('[data-lang-selector]').forEach(button => {
            button.addEventListener('click', showLanguageSelector);
        });
    });
    
    // Public API
    return {
        getText,
        translatePage,
        showLanguageSelector,
        resetToSystemLanguage,
        getCurrentLanguage: () => currentLanguage,
        getLanguageData: () => languageData,
        setLanguage
    };
})();

// Make it globally accessible
window.UILanguage = UILanguage;