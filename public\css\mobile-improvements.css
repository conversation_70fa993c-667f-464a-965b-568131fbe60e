/* 移动端 main.html 页面UI改进 - 增大字号和优化间距 */

/* 移动端脑图容器样式优化 */
@media (max-width: 768px) {
    /* 移动端脑图容器增加底部空间 */
    #aiMindmap {
        padding-bottom: 100px !important; /* 增加底部空间，确保拖拽时可以看到完整内容 */
        min-height: 200px !important; /* 减少最小高度，避免挤占输入框 */
    }
    
    /* AI工具栏样式 */
    .ai-toolbar {
        flex-direction: column !important;
        align-items: stretch !important;
        gap: 10px !important;
        margin-bottom: 15px !important;
    }

    .ai-toolbar button {
        width: 100% !important;
        font-size: 14px !important;
        padding: 10px 15px !important;
        margin: 0 !important;
    }

    /* AI内容区域样式 */
    #aiSummary, #aiMindmap, #aiKnowledgeGraph {
        min-height: 150px !important; /* 减少最小高度，避免挤占输入框 */
        padding: 15px !important;
        font-size: 14px !important;
        line-height: 1.5 !important;
    }
}


/* 移动端菜单优化 - 减少标题栏高度，增大字号 */
@media (max-width: 768px) {
    /* 减少菜单标题栏高度并优化垂直居中 */
    .mobile-menu-header {
        padding: 0 16px !important; /* 移除上下内边距，使用固定高度代替 */
        display: flex !important;
        align-items: center !important; /* 确保垂直居中 */
        height: 40px !important; /* 设置固定高度而不是最小高度 */
        box-sizing: border-box !important;
    }
    
    /* 菜单标题文本垂直居中 */
    .mobile-menu-header h3 {
        margin: 0 !important;
        padding: 0 !important;
        line-height: 40px !important; /* 使用与容器相同的高度确保完全垂直居中 */
        height: 40px !important; /* 确保高度与容器一致 */
        display: flex !important;
        align-items: center !important;
        font-size: 15px !important; /* 增大标题字体大小 */
    }
    
    /* 关闭按钮垂直居中 */
    .mobile-menu-header .close-menu-btn {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        height: 30px !important;
        width: 30px !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    /* 增大菜单项字体大小并统一字体 */
    .menu-item-content {
        font-size: 15px !important; /* 增大字体大小 */
    }
    
    /* 增大积分数字和语言选择字体大小并统一字体 */
    .credits-amount, .current-language {
        font-size: 15px !important; /* 增大字体大小 */
        font-family: inherit !important; /* 使用与左侧一致的字体 */
        text-align: right !important;
        justify-self: end !important;
    }
    
    /* 确保菜单项内容正确布局 */
    .menu-item-content {
        display: grid !important;
        grid-template-columns: 1fr 60px !important;
        align-items: center !important;
        width: 100% !important;
    }
}

/* 移动端布局下的标签页和模型选择器字体大小调整 */
@media (max-width: 768px) {
    /* 增大AI标签按钮字体大小，与Generate Summary按钮一致 */
    .ai-tab-button {
        font-size: 14px !important; /* 增大字体大小 */
        padding: 6px 12px !important; /* 增加内边距，提供更好的点击区域和文字间距 */
        height: auto !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-weight: normal !important; /* 添加正常字重，避免加粗显示 */
    }
    
    /* 模型选择器文字大小调整 */
    .current-model {
        font-size: 14px !important; /* 增大字体大小，与标签页一致 */
        padding: 8px 12px !important; /* 增加内边距 */
    }
    
    #currentModelName {
        font-size: 14px !important;
    }
    
    /* 调整粘贴按钮的右侧间距，使其更靠近右边缘 */
    .paste-button {
        right: 5px !important; /* 将右侧间距从15px减小到5px */
    }
    
    /* 调整标签页与上下部分的间距，使其保持一致 */
    .ai-tabs {
        margin-top: 5px !important;
        margin-bottom: 5px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
    
    .tab-buttons {
        margin-top: 0 !important;
        margin-bottom: 5px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
    
    /* 生成按钮的样式保持不变，无需修改 */
}

/* 超小屏幕的特殊调整 */
@media (max-width: 480px) {
    /* 超小屏幕下保持菜单样式一致 */
    .mobile-menu-header {
        padding: 0 16px !important; /* 移除上下内边距，使用固定高度代替 */
        display: flex !important;
        align-items: center !important; /* 确保垂直居中 */
        height: 40px !important; /* 设置固定高度而不是最小高度 */
        box-sizing: border-box !important;
    }
    
    /* 超小屏幕下的菜单标题文本垂直居中 */
    .mobile-menu-header h3 {
        margin: 0 !important;
        padding: 0 !important;
        line-height: 40px !important; /* 使用与容器相同的高度确保完全垂直居中 */
        height: 40px !important; /* 确保高度与容器一致 */
        display: flex !important;
        align-items: center !important;
        font-size: 15px !important; /* 增大标题字体大小 */
    }
    
    /* 超小屏幕下的关闭按钮垂直居中 */
    .mobile-menu-header .close-menu-btn {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        height: 30px !important;
        width: 30px !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .menu-item-content {
        font-size: 15px !important; /* 保持与大屏幕相同的字体大小 */
        display: grid !important;
        grid-template-columns: 1fr 60px !important;
        align-items: center !important;
        width: 100% !important;
    }
    
    .credits-amount, .current-language {
        font-size: 15px !important; /* 保持与大屏幕相同的字体大小 */
        font-family: inherit !important; /* 保持与大屏幕相同的字体 */
        text-align: right !important;
        justify-self: end !important;
    }
    
    /* 即使在更小的屏幕上也保持相同的字体大小 */
    .ai-tab-button {
        font-size: 14px !important;
        padding: 6px 10px !important; /* 略微减小内边距，但保持足够的空间 */
    }
    
    .current-model {
        font-size: 14px !important;
    }
    
    #currentModelName {
        font-size: 14px !important;
    }
}

/* 知识图谱区域滚动条移除 */
/* 全局强制设置，适用于所有屏幕尺寸 */
#aiKnowledgeGraph,
.knowledgegraph-container {
    overflow: hidden !important;
}

/* 针对不同浏览器的滚动条隐藏 */
/* WebKit浏览器（Chrome、Safari、新版Edge） */
#aiKnowledgeGraph::-webkit-scrollbar,
.knowledgegraph-container::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    display: none !important;
}

/* Firefox浏览器 */
#aiKnowledgeGraph,
.knowledgegraph-container {
    scrollbar-width: none !important;
}

/* IE和旧版Edge */
#aiKnowledgeGraph,
.knowledgegraph-container {
    -ms-overflow-style: none !important;
}
