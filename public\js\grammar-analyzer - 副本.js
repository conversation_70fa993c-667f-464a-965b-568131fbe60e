/**
 * 英语语法结构可视化分析器
 * 集成到智能问答系统，支持句法树、依存图等多种可视化
 * 基于项目现有的技术栈：D3.js + 智谱模型
 */
class GrammarAnalyzer {
    constructor() {
        this.apiEndpoint = '/api/grammar-analysis-stream';
        this.initialized = false;
        this.supportedLanguages = ['en']; // 主要支持英语
        this.grammarKeywords = [
            // 英文关键词
            'grammar', 'syntax', 'sentence structure', 'parse', 'parsing',
            'subject', 'predicate', 'object', 'clause', 'phrase',
            'noun phrase', 'verb phrase', 'dependency', 'dependencies',
            'part of speech', 'pos', 'tense', 'passive voice', 'active voice',
            'gerund', 'participle', 'infinitive', 'adjective', 'adverb',
            'preposition', 'conjunction', 'article', 'determiner',
            'morphology', 'phonology', 'semantics', 'pragmatics',
            'analyze', 'analysis', 'structure', 'syntactic', 'grammatical',
            // 中文关键词
            '语法', '句法', '语法结构', '句子结构', '语法分析',
            '主语', '谓语', '宾语', '从句', '短语',
            '名词短语', '动词短语', '依存关系', '依存',
            '词性', '词性标注', '时态', '被动语态', '主动语态',
            '动名词', '分词', '不定式', '形容词', '副词',
            '介词', '连词', '冠词', '限定词',
            '形态学', '语音学', '语义学', '语用学',
            '分析', '结构', '句法的', '语法的',
            '分析一下', '解释语法', '语法解释', '语法分析'
        ];
    }

    /**
     * 初始化语法分析器
     */
    async initialize() {
        if (this.initialized) return true;
        
        try {
            // 确保D3.js已加载
            if (typeof d3 === 'undefined') {
                console.warn('D3.js未加载，语法可视化功能可能受限');
                return false;
            }
            
            this.initialized = true;
            console.log('语法分析器初始化完成');
            return true;
        } catch (error) {
            console.error('语法分析器初始化失败:', error);
            return false;
        }
    }

    /**
     * 检测文本是否需要语法分析
     * @param {string} text - 用户输入的文本
     * @returns {boolean} 是否需要语法分析
     */
    shouldAnalyzeGrammar(text) {
        const lowerText = text.toLowerCase();
        return this.grammarKeywords.some(keyword => lowerText.includes(keyword));
    }

    /**
     * 从文本中提取英语句子
     * @param {string} text - 输入文本
     * @returns {Array} 英语句子数组
     */
    extractEnglishSentences(text) {
        // 改进的英语句子识别正则表达式
        const englishPattern = /[A-Za-z][A-Za-z\s\.,;:!?'"()\-\d]*[.!?]/g;
        const sentences = text.match(englishPattern) || [];
        
        return sentences
            .map(s => s.trim())
            .filter(s => {
                // 过滤条件：
                // 1. 长度至少10个字符
                // 2. 包含至少5个英文字母
                // 3. 不包含中文字符
                const hasEnoughLetters = (s.match(/[A-Za-z]/g) || []).length >= 5;
                const noChinese = !/[\u4e00-\u9fff]/.test(s);
                return s.length >= 10 && hasEnoughLetters && noChinese;
            })
            .slice(0, 3); // 最多分析3个句子
    }

    // Prompt配置已统一到后端管理，前端不再维护prompt内容

    /**
     * 调用API进行语法分析
     * @param {string} sentence - 待分析的句子
     * @param {string} analysisType - 分析类型
     * @returns {Promise<Response>} API响应
     */
    async callGrammarAnalysisAPI(sentence, analysisType = 'comprehensive') {
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify({
                    sentence: sentence,
                    analysis_type: analysisType,
                    model: this.getCurrentModel() || 'zhipu_flash',
                    temperature: 0.3
                })
            });

            return response;
        } catch (error) {
            console.error('语法分析API调用失败:', error);
            throw error;
        }
    }

    /**
     * 获取当前选择的模型
     * @returns {string} 模型ID
     */
    getCurrentModel() {
        // 优先从全局变量获取
        if (window.currentModel) {
            return window.currentModel;
        }
        
        // 从DOM元素获取
        const modelElement = document.getElementById('currentModel');
        if (modelElement) {
            const modelText = modelElement.textContent.trim();
            // 将显示名称转换为模型ID
            if (modelText.includes('GLM-4-Flash')) return 'zhipu_flash';
            if (modelText.includes('GLM-4-Air')) return 'zhipu_glm_4_air';
            if (modelText.includes('GLM-4-FlashX')) return 'zhipu_glm_4_flashX';
        }
        
        // 默认返回
        return 'zhipu_flash';
    }

    /**
     * 创建语法分析的可视化容器
     * @param {HTMLElement} container - 容器元素
     * @param {string} sentence - 分析的句子
     * @returns {HTMLElement} 可视化容器
     */
    createVisualizationContainer(container, sentence) {
        // 清空容器
        container.innerHTML = '';
        
        // 创建主容器
        const mainContainer = document.createElement('div');
        mainContainer.className = 'grammar-analysis-container';
        
        // 创建标题
        const title = document.createElement('h3');
        title.className = 'grammar-analysis-title';
        title.innerHTML = '<i class="fas fa-tree"></i> 语法结构分析';
        mainContainer.appendChild(title);
        
        // 创建句子显示区域
        const sentenceDisplay = document.createElement('div');
        sentenceDisplay.className = 'analyzed-sentence';
        sentenceDisplay.innerHTML = `<strong>分析句子:</strong> "${sentence}"`;
        mainContainer.appendChild(sentenceDisplay);
        
        // 创建标签页容器
        const tabsContainer = document.createElement('div');
        tabsContainer.className = 'grammar-tabs';
        
        // 创建标签按钮
        const tabs = [
            { id: 'analysis', label: '语法分析', icon: 'fas fa-search' },
            { id: 'visualization', label: '结构图', icon: 'fas fa-project-diagram' },
            { id: 'learning', label: '学习要点', icon: 'fas fa-lightbulb' }
        ];
        
        tabs.forEach((tab, index) => {
            const button = document.createElement('button');
            button.className = `grammar-tab-btn ${index === 0 ? 'active' : ''}`;
            button.setAttribute('data-tab', tab.id);
            button.innerHTML = `<i class="${tab.icon}"></i> ${tab.label}`;
            button.onclick = () => this.switchTab(button, tab.id, mainContainer);
            tabsContainer.appendChild(button);
        });
        
        mainContainer.appendChild(tabsContainer);
        
        // 创建内容容器
        const contentContainer = document.createElement('div');
        contentContainer.className = 'grammar-content';
        contentContainer.id = 'grammar-content';
        mainContainer.appendChild(contentContainer);
        
        container.appendChild(mainContainer);
        
        // 默认显示第一个标签
        this.switchTab(tabsContainer.querySelector('.grammar-tab-btn'), 'analysis', mainContainer);
        
        return mainContainer;
    }

    /**
     * 切换标签页
     * @param {HTMLElement} activeButton - 激活的按钮
     * @param {string} tabId - 标签ID
     * @param {HTMLElement} container - 主容器
     */
    switchTab(activeButton, tabId, container) {
        // 移除所有按钮的active类
        container.querySelectorAll('.grammar-tab-btn').forEach(btn => 
            btn.classList.remove('active'));
        
        // 激活当前按钮
        activeButton.classList.add('active');
        
        // 更新内容区域
        const contentContainer = container.querySelector('#grammar-content');
        this.renderTabContent(contentContainer, tabId);
    }

    /**
     * 渲染标签页内容
     * @param {HTMLElement} container - 内容容器
     * @param {string} tabId - 标签ID
     */
    renderTabContent(container, tabId) {
        container.innerHTML = '';
        
        switch(tabId) {
            case 'analysis':
                this.renderAnalysisTab(container);
                break;
            case 'visualization':
                this.renderVisualizationTab(container);
                break;
            case 'learning':
                this.renderLearningTab(container);
                break;
        }
    }

    /**
     * 渲染语法分析标签页
     * @param {HTMLElement} container - 容器
     */
    renderAnalysisTab(container) {
        const analysisDiv = document.createElement('div');
        analysisDiv.className = 'grammar-analysis-content';
        analysisDiv.id = 'grammar-analysis-text';
        analysisDiv.innerHTML = '<div class="loading">正在分析语法结构...</div>';
        container.appendChild(analysisDiv);
    }

    /**
     * 渲染可视化标签页
     * @param {HTMLElement} container - 容器
     */
    renderVisualizationTab(container) {
        const vizDiv = document.createElement('div');
        vizDiv.className = 'grammar-visualization';
        vizDiv.innerHTML = `
            <div class="viz-placeholder">
                <i class="fas fa-chart-network fa-3x"></i>
                <p>语法结构图将在分析完成后显示</p>
            </div>
        `;
        container.appendChild(vizDiv);
    }

    /**
     * 渲染学习要点标签页
     * @param {HTMLElement} container - 容器
     */
    renderLearningTab(container) {
        const learningDiv = document.createElement('div');
        learningDiv.className = 'grammar-learning-points';
        learningDiv.innerHTML = `
            <div class="learning-placeholder">
                <i class="fas fa-graduation-cap fa-3x"></i>
                <p>学习要点将在分析完成后显示</p>
            </div>
        `;
        container.appendChild(learningDiv);
    }

    /**
     * 处理流式响应（新版本，支持语法树流式渲染）
     * @param {Response} response - API响应
     * @param {HTMLElement} container - 容器元素
     */
    async handleStreamResponse(response, container) {
        if (!response.ok) {
            throw new Error(`语法分析请求失败: ${response.status}`);
        }
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let fullContent = '';
        let lastTreeUpdate = 0; // 用于控制树的更新频率
        
        // 创建初始的可视化容器
        this.initializeGrammarVisualization(container);
        
        try {
            while (true) {
                const { value, done } = await reader.read();
                
                if (done) break;
                
                buffer += decoder.decode(value, { stream: true });
                
                // 按行分割处理，确保逐行输出
                let lines = buffer.split('\n');
                
                // 如果最后一行没有换行符，保留在buffer中
                if (!buffer.endsWith('\n')) {
                    buffer = lines.pop() || '';
                } else {
                    buffer = '';
                }
                
                // 处理完整的行
                for (const line of lines) {
                    if (line.trim()) {
                        // 逐行追加内容
                        fullContent += line + '\n';
                        
                        // 实时更新分析内容
                        this.updateAnalysisContent(container, fullContent);
                        
                        // 控制语法树更新频率，避免过于频繁的重绘
                        const now = Date.now();
                        if (now - lastTreeUpdate > 300) { // 每300ms最多更新一次
                            this.updateGrammarTreeStreaming(container, fullContent);
                            lastTreeUpdate = now;
                        }
                        
                        // 添加小延迟，模拟逐行打字效果
                        await new Promise(resolve => setTimeout(resolve, 50));
                    }
                }
            }
            
            // 处理剩余的buffer内容
            if (buffer.trim()) {
                fullContent += buffer;
                this.updateAnalysisContent(container, fullContent);
            }
            
            // 最终更新：确保完整的语法树渲染
            this.finalizeGrammarVisualization(container, fullContent);
            
        } catch (error) {
            console.error('处理流式响应时出错:', error);
            this.showError(container, '语法分析过程中出现错误');
        }
    }

    /**
     * 初始化语法可视化容器
     * @param {HTMLElement} container - 主容器
     */
    initializeGrammarVisualization(container) {
        // 清空容器
        container.innerHTML = '';
        
        // 创建语法树容器结构
        const treeSection = document.createElement('div');
        treeSection.className = 'grammar-tree-section';
        
        // 创建语法树容器（与updateGrammarTreeStreaming保持一致）
        const treeContainer = document.createElement('div');
        treeContainer.className = 'grammar-tree-container';
        treeContainer.innerHTML = '<div class="grammar-tree-placeholder">正在分析语法结构...</div>';
        
        treeSection.appendChild(treeContainer);
        container.appendChild(treeSection);
    }

    /**
     * 流式更新语法树可视化
     * @param {HTMLElement} container - 容器
     * @param {string} content - 内容
     */
    updateGrammarTreeStreaming(container, content) {
        const vizContainer = container.querySelector('.grammar-tree-container');
        if (!vizContainer) return;
        
        // 检查内容是否足够进行可视化
        if (!content || content.trim().length < 50) {
            return;
        }
        
        try {
            // 使用语法分析特定的解析和渲染方法（参考脑图的updateContent）
            const treeData = this.parseMarkdownToGrammarTree(content);
            
            // 验证树数据是否有效
            if (!treeData || !treeData.name) {
                return;
            }
            
            // 检查是否只有加载节点，如果是则不更新
            if (treeData.children && treeData.children.length === 1 && 
                treeData.children[0].type === 'loading') {
                return;
            }
            
            // 渲染语法树
            this.renderGrammarTree(vizContainer, treeData);
            
            // 等待DOM更新完成后再进行滚动
            requestAnimationFrame(() => {
                // 流式输出过程中使用渐进式滚动
                this.scrollToLatestContent(vizContainer, true);
            });
        } catch (error) {
            console.error('流式更新语法树时出错:', error);
            // 保持当前的placeholder状态，不显示错误
        }
    }

    /**
     * 最终完成语法树可视化
     * @param {HTMLElement} container - 容器
     * @param {string} fullContent - 完整内容
     */
    finalizeGrammarVisualization(container, fullContent) {
        const vizContainer = container.querySelector('.grammar-tree-container');
        if (!vizContainer) return;
       
        try {
            // 清理内容，移除HIDDEN_TOKEN_DATA和其他不需要的信息
            const cleanContent = fullContent
                .replace(/<!-- HIDDEN_TOKEN_DATA: .+? -->/g, '')
                .replace(/==== Token使用情况.*?====/gs, '')
                .replace(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3} - INFO.*$/gm, '')
                .replace(/数据流接收完成/g, '')
                .trim();
            
            // 解析清理后的内容
            const treeData = this.parseMarkdownToGrammarTree(cleanContent);

            
            // 验证树数据是否有效
            if (!treeData || !treeData.name) {
                vizContainer.innerHTML = '<div class="error">语法分析完成，但未能解析出有效的语法结构</div>';
                return;
            }
            
            // 检查是否只有加载节点，如果是则显示降级内容
            if (treeData.children && treeData.children.length === 1 && 
                treeData.children[0].type === 'loading') {
                this.showFallbackContent(vizContainer, fullContent);
                return;
            }
            
            // 最终渲染
            this.renderGrammarTree(vizContainer, treeData);
            
            // 等待DOM更新完成后再进行滚动
            requestAnimationFrame(() => {
                // 最终完成时使用精确的滚动
                this.scrollToLatestContent(vizContainer, false);
            });
            
            // 如果有有效的语法树数据，显示下载工具栏
            if (treeData && treeData.children && treeData.children.length > 0 &&
                !(treeData.children.length === 1 && treeData.children[0].type === 'loading')) {
                this.createDownloadToolbar(container, 'grammar', fullContent);
            }
            
        } catch (error) {
            console.error('最终化语法树时出错:', error);
            // 如果渲染失败，显示降级内容而不是错误
            this.showFallbackContent(vizContainer, fullContent);
        }
    }

    /**
     * 滚动到最新内容（参考脑图实现）
     * @param {HTMLElement} container - 容器
     * @param {boolean} isProgressiveRender - 是否为渐进式渲染
     */
    scrollToLatestContent(container, isProgressiveRender = false) {
        if (!container) return;
        
        try {
            // 如果是渐进式渲染，使用较温和的滚动
            if (isProgressiveRender) {
                const currentScrollTop = container.scrollTop;
                const scrollHeight = container.scrollHeight;
                const clientHeight = container.clientHeight;
                
                // 只有在接近底部时才继续自动滚动
                if (currentScrollTop + clientHeight >= scrollHeight - 100) {
                    container.scrollTo({
                        top: scrollHeight,
                        behavior: 'smooth'
                    });
                }
            } else {
                // 最终完成时，精确滚动到底部
                container.scrollTo({
                    top: container.scrollHeight,
                    behavior: 'smooth'
                });
            }
        } catch (error) {
            console.error('滚动到最新内容失败:', error);
        }
    }

    /**
     * 创建下载工具栏（简化版本）
     * @param {HTMLElement} container - 容器
     * @param {string} type - 类型
     * @param {string} content - 内容
     */
    createDownloadToolbar(container, type, content) {
        // 移除现有的工具栏
        const existingToolbar = container.querySelector('.download-toolbar');
        if (existingToolbar) {
            existingToolbar.remove();
        }
        
        // 创建新的工具栏
        const toolbar = document.createElement('div');
        toolbar.className = 'download-toolbar';
        toolbar.style.cssText = `
            display: flex;
            gap: 10px;
            margin-top: 10px;
            padding: 5px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        `;
        
        // 下载为文本按钮
        const downloadTextBtn = document.createElement('button');
        downloadTextBtn.className = 'download-button';
        downloadTextBtn.innerHTML = '<i class="fas fa-download"></i> 下载分析结果';
        downloadTextBtn.style.cssText = `
            padding: 8px 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        `;
        
        downloadTextBtn.onclick = () => {
            const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `语法分析_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        };
        
        toolbar.appendChild(downloadTextBtn);
        container.appendChild(toolbar);
    }

    /**
     * 格式化要点文本
     * @param {string} text - 原始文本
     * @returns {string} 格式化后的HTML
     */
    formatPoints(text) {
        if (!text) return '';
        
        // 将列表项转换为HTML列表
        const lines = text.split('\n').filter(line => line.trim());
        const items = lines.map(line => {
            if (line.trim().startsWith('- ')) {
                return `<li>${line.substring(2).trim()}</li>`;
            } else if (line.trim().startsWith('* ')) {
                return `<li>${line.substring(2).trim()}</li>`;
            } else {
                return `<p>${line.trim()}</p>`;
            }
        });
        
        if (items.some(item => item.startsWith('<li>'))) {
            return `<ul>${items.filter(item => item.startsWith('<li>')).join('')}</ul>`;
        } else {
            return items.join('');
        }
    }

    /**
     * 显示降级内容（当解析失败时）
     * @param {HTMLElement} container - 容器
     * @param {string} content - 内容
     */
    showFallbackContent(container, content) {
        container.innerHTML = `
            <div class="grammar-fallback">
                <h4><i class="fas fa-exclamation-triangle"></i> 语法分析结果</h4>
                <div class="fallback-content">
                    <p>AI已完成语法分析，但结构化解析遇到问题。原始分析内容如下：</p>
                    <div class="raw-content">${content.replace(/\n/g, '<br>')}</div>
                </div>
            </div>
        `;
    }

    /**
     * 更新分析内容显示
     * @param {HTMLElement} container - 容器
     * @param {string} content - 内容
     */
    updateAnalysisContent(container, content) {
        const analysisContent = container.querySelector('#grammar-analysis-text');
        if (analysisContent) {
            // 移除加载状态
            analysisContent.classList.remove('loading');
            
            // 使用marked库渲染Markdown（如果可用）
            if (window.marked && window.marked.parse) {
                analysisContent.innerHTML = window.marked.parse(content);
            } else {
                // 简单的格式化
                const formattedContent = content
                    .replace(/\n\n/g, '<br><br>')
                    .replace(/\n/g, '<br>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>');
                analysisContent.innerHTML = formattedContent;
            }
        }
    }

    /**
     * 处理分析结果，尝试提取结构化信息
     * @param {HTMLElement} container - 容器
     * @param {string} content - 分析内容
     */
    processAnalysisResult(container, content) {
        try {
            // 尝试从内容中提取结构化信息
            const structuredData = this.parseGrammarTreeStructure(content);
            
            if (structuredData) {
                this.updateVisualization(container, structuredData);
                this.updateLearningPoints(container, structuredData);
            } else {
                // 如果无法提取结构化数据，显示基本可视化
                this.createBasicVisualization(container, content);
            }
        } catch (error) {
            console.error('处理分析结果时出错:', error);
        }
    }

    /**
     * 从AI分析内容中提取语法树结构
     * @param {string} content - AI返回的分析内容
     * @returns {Object|null} 语法树结构数据
     */
    parseGrammarTreeStructure(content) {
        try {
            // 直接使用整个内容作为语法树内容
            const treeData = this.parseMarkdownToGrammarTree(content);
            
            return {
                treeData: treeData,
                fullContent: content
            };
        } catch (error) {
            console.error('解析语法树结构失败:', error);
            return null;
        }
    }

    /**
     * 解析Markdown格式的语法树为D3可用的树状结构
     * @param {string} markdown - Markdown格式的语法树内容
     * @returns {Object} D3树状结构数据
     */
    parseMarkdownToGrammarTree(markdown) {
        // 确保每个Markdown标题独占一行，便于后续解析
        markdown = markdown.replace(/(#{1,4})(?=\s)/g, '\n$1');
        
        // 创建根节点，使用句子本身作为根节点
        const rootNode = { name: '语法成分分析', children: [] };
        
        try {
            // 分割行并过滤空行
            const lines = markdown.split('\n').filter(line => line.trim() !== '');
            if (lines.length === 0) {
                return rootNode;
            }

            // 如果找到了一级标题（句子），使用它作为根节点名称
            for (let i = 0; i < lines.length; i++) {
                if (lines[i].trim().startsWith('# ')) {
                    const sentence = lines[i].substring(2).trim();
                    if (sentence && sentence !== '语法成分分析') {
                        rootNode.name = `语法成分分析: ${sentence}`;
                    }
                    lines.splice(i, 1); // 移除已处理的标题行
                    break;
                }
            }

            let currentComponent = null;  // 当前句子成分节点（如主语成分、谓语成分等）
            
            // 处理剩余的行
            for (const line of lines) {
                let trimmedLine = line.trim();
                
                // 跳过代码块标记和空行
                if (trimmedLine.startsWith('```') || trimmedLine === '') {
                    continue;
                }
                
                if (trimmedLine.startsWith('## ') && !trimmedLine.startsWith('### ')) {
                    // 二级标题 - 句子成分（主语成分、谓语成分等）
                    const componentName = trimmedLine.substring(3).trim();
                    
                    // 跳过空的或无效的成分
                    if (!componentName || 
                        componentName.toLowerCase() === 'none' || 
                        componentName.toLowerCase() === '无' ||
                        componentName.toLowerCase() === 'null' ||
                        componentName.toLowerCase() === 'empty' ||
                        componentName === '') {
                        currentComponent = null;
                        continue;
                    }
                    
                    // 检查是否已存在相同类型的成分（如多个状语部分）
                    const existingComponent = rootNode.children.find(child => 
                        child.componentType === componentName
                    );
                    
                    if (existingComponent) {
                        // 如果已存在，复用现有组件
                        currentComponent = existingComponent;
                    } else {
                        // 创建新的成分节点
                        currentComponent = { 
                            name: componentName, 
                            children: [],
                            type: 'component',
                            componentType: componentName,  // 保存成分类型用于后续处理
                            subItems: []  // 用于存储同类型的多个子项
                        };
                        // 暂时不添加到根节点，等确认有内容后再添加
                    }
                } else if (trimmedLine.startsWith('### ')) {
                    // 三级标题 - 新格式的句子成分（主语:、谓语:、宾语:等）
                    if (!currentComponent) {
                        continue; // 如果没有有效的当前成分，跳过
                    }
                    
                    const infoLine = trimmedLine.substring(4).trim();
                    
                    // 跳过空的信息行
                    if (!infoLine || infoLine.toLowerCase() === 'none' || infoLine.toLowerCase() === '无') {
                        continue;
                    }
                    
                    // 解析新格式：主语: [文本] - [说明]
                    if (infoLine.includes(':') && infoLine.includes('-')) {
                        const parts = infoLine.split(':');
                        if (parts.length >= 2) {
                            const componentType = parts[0].trim(); // 主语、谓语、宾语等
                            const contentPart = parts.slice(1).join(':').trim(); // 后面的内容
                            
                            // 解析内容部分：[文本] - [说明]
                            const dashIndex = contentPart.indexOf(' - ');
                            if (dashIndex > 0) {
                                const content = contentPart.substring(0, dashIndex).trim();
                                const description = contentPart.substring(dashIndex + 3).trim();
                                
                                if (content && content.toLowerCase() !== 'none' && content.toLowerCase() !== '无') {
                                    // 创建成分节点
                                    const componentNode = {
                                        name: content,
                                        type: 'content',
                                        children: []
                                    };
                                    
                                    // 添加成分标签
                                    const labelNode = {
                                        name: `[${componentType}]`,
                                        type: 'component_label'
                                    };
                                    componentNode.children.push(labelNode);
                                    
                                    // 添加说明
                                    if (description && description.toLowerCase() !== 'none') {
                                        const descNode = {
                                            name: description,
                                            type: 'info'
                                        };
                                        componentNode.children.push(descNode);
                                    }
                                    
                                    currentComponent.children.push(componentNode);
                                    
                                    // 确保当前成分已添加到根节点
                                    if (!rootNode.children.includes(currentComponent)) {
                                        rootNode.children.push(currentComponent);
                                    }
                                }
                            }
                        }
                    }
                    // 保持旧格式兼容性
                    else if (infoLine.startsWith('文本内容:') || infoLine.startsWith('文本内容：')) {
                        const content = infoLine.split(/[：:]/)[1]?.trim();
                        
                        // 跳过空的文本内容
                        if (!content || content.toLowerCase() === 'none' || content.toLowerCase() === '无') {
                            continue;
                        }
                        
                        // 如果是第一个文本内容，设置为主要名称
                        if (!currentComponent.actualContent) {
                            currentComponent.name = content;
                            currentComponent.actualContent = content;
                            
                            // 添加成分类型标签作为子节点
                            if (currentComponent.componentType && currentComponent.componentType !== content) {
                                const labelNode = {
                                    name: `[${currentComponent.componentType}]`,
                                    type: 'component_label'
                                };
                                currentComponent.children.push(labelNode);
                            }
                        } else {
                            // 如果已经有文本内容，作为子项添加
                            const contentNode = {
                                name: content,
                                type: 'content'
                            };
                            currentComponent.children.push(contentNode);
                        }
                        
                        // 现在有了实际内容，将成分添加到根节点
                        if (!rootNode.children.includes(currentComponent)) {
                            rootNode.children.push(currentComponent);
                        }
                    } else if (infoLine.startsWith('语法作用:') || infoLine.startsWith('语法作用：')) {
                        const role = infoLine.split(/[：:]/)[1]?.trim();
                        if (role && role.toLowerCase() !== 'none' && role.toLowerCase() !== '无') {
                            const roleNode = {
                                name: `语法作用: ${role}`,
                                type: 'role'
                            };
                            currentComponent.children.push(roleNode);
                            
                            // 确保成分已添加到根节点
                            if (!rootNode.children.includes(currentComponent)) {
                                rootNode.children.push(currentComponent);
                            }
                        }
                    } else {
                        // 通用信息节点
                        const infoNode = {
                            name: infoLine,
                            type: 'info'
                        };
                        currentComponent.children.push(infoNode);
                        
                        // 确保成分已添加到根节点
                        if (!rootNode.children.includes(currentComponent)) {
                            rootNode.children.push(currentComponent);
                        }
                    }
                } else if (trimmedLine.startsWith('- ') || trimmedLine.startsWith('* ')) {
                    // 列表项 - 添加到当前成分的子节点
                    const listContent = trimmedLine.substring(2).trim();
                    if (listContent && listContent.toLowerCase() !== 'none' && listContent.toLowerCase() !== '无') {
                        const listItem = { 
                            name: listContent,
                            type: 'list'
                        };
                        
                        if (currentComponent) {
                            currentComponent.children.push(listItem);
                            // 确保成分已添加到根节点
                            if (!rootNode.children.includes(currentComponent)) {
                                rootNode.children.push(currentComponent);
                            }
                        } else {
                            rootNode.children.push(listItem);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('解析句子成分Markdown时出错:', error);
        }
        
        // 如果根节点没有子节点，添加一个默认提示
        if (rootNode.children.length === 0) {
            rootNode.children.push({ name: '正在解析中...', type: 'loading' });
        }

        return rootNode;
    }

    /**
     * 从内容中提取指定章节
     * @param {string} content - 完整内容
     * @param {string} sectionName - 章节名称
     * @returns {string} 提取的章节内容
     */
    extractSection(content, sectionName) {
        const pattern = new RegExp(`## ${sectionName}([\\s\\S]*?)(?=## |$)`, 'i');
        const match = content.match(pattern);
            if (match) {
            return match[1].trim();
            }
        return '';
    }

    /**
     * 更新可视化标签页
     * @param {HTMLElement} container - 容器
     * @param {Object} data - 结构化数据
     */
    updateVisualization(container, data) {
        const vizContainer = container.querySelector('.grammar-visualization');
        if (!vizContainer) return;
        
        vizContainer.innerHTML = '';
        
        if (data.posData && data.posData.length > 0) {
            this.createPOSVisualization(vizContainer, data.posData);
        } else {
            this.createBasicVisualization(vizContainer, data.content);
        }
    }

    /**
     * 创建词性标注可视化
     * @param {HTMLElement} container - 容器
     * @param {Array} posData - 词性数据
     */
    createPOSVisualization(container, posData) {
        const posContainer = document.createElement('div');
        posContainer.className = 'pos-visualization';
        
        const title = document.createElement('h4');
        title.textContent = '词性标注可视化';
        posContainer.appendChild(title);
        
        const wordsContainer = document.createElement('div');
        wordsContainer.className = 'pos-words-container';
        
        posData.forEach(item => {
            const wordDiv = document.createElement('div');
            wordDiv.className = 'pos-word-item';
            
            const wordSpan = document.createElement('span');
            wordSpan.className = 'word-text';
            wordSpan.textContent = item.word;
            
            const posSpan = document.createElement('span');
            posSpan.className = 'pos-tag';
            posSpan.textContent = item.pos;
            posSpan.style.backgroundColor = this.getPOSColor(item.pos);
            
            wordDiv.appendChild(wordSpan);
            wordDiv.appendChild(posSpan);
            wordsContainer.appendChild(wordDiv);
        });
        
        posContainer.appendChild(wordsContainer);
        container.appendChild(posContainer);
    }

    /**
     * 获取词性对应的颜色
     * @param {string} pos - 词性标签
     * @returns {string} 颜色值
     */
    getPOSColor(pos) {
        const colorMap = {
            '名词': '#e3f2fd',
            '动词': '#f3e5f5',
            '形容词': '#e8f5e8',
            '副词': '#fff3e0',
            '介词': '#fce4ec',
            '代词': '#f1f8e9',
            '连词': '#fff8e1',
            'noun': '#e3f2fd',
            'verb': '#f3e5f5',
            'adjective': '#e8f5e8',
            'adverb': '#fff3e0',
            'preposition': '#fce4ec',
            'pronoun': '#f1f8e9',
            'conjunction': '#fff8e1'
        };
        
        // 查找匹配的颜色
        for (const [key, color] of Object.entries(colorMap)) {
            if (pos.toLowerCase().includes(key.toLowerCase())) {
                return color;
            }
        }
        
        return '#f5f5f5'; // 默认颜色
    }

    /**
     * 创建基本可视化
     * @param {HTMLElement} container - 容器
     * @param {string} content - 内容
     */
    createBasicVisualization(container, content) {
        const basicViz = document.createElement('div');
        basicViz.className = 'basic-grammar-viz';
        basicViz.innerHTML = `
            <div class="viz-info">
                <i class="fas fa-info-circle"></i>
                <p>基于AI分析结果的语法结构展示</p>
            </div>
            <div class="content-summary">
                <h4>分析摘要</h4>
                <p>AI已完成对句子的详细语法分析，请查看"语法分析"标签页获取完整信息。</p>
            </div>
        `;
        container.appendChild(basicViz);
    }

    /**
     * 更新学习要点标签页
     * @param {HTMLElement} container - 容器
     * @param {Object} data - 结构化数据
     */
    updateLearningPoints(container, data) {
        const learningContainer = container.querySelector('.grammar-learning-points');
        if (!learningContainer) return;
        
        learningContainer.innerHTML = '';
        
        const title = document.createElement('h4');
        title.innerHTML = '<i class="fas fa-graduation-cap"></i> 学习要点';
        learningContainer.appendChild(title);
        
        // 提取学习要点
        const learningPoints = this.extractLearningPoints(data.content);
        
        if (learningPoints.length > 0) {
            const pointsList = document.createElement('ul');
            pointsList.className = 'learning-points-list';
            
            learningPoints.forEach(point => {
                const listItem = document.createElement('li');
                listItem.textContent = point;
                pointsList.appendChild(listItem);
            });
            
            learningContainer.appendChild(pointsList);
        } else {
            const defaultPoints = document.createElement('div');
            defaultPoints.className = 'default-learning-points';
            defaultPoints.innerHTML = `
                <p><strong>一般语法学习要点：</strong></p>
                <ul>
                    <li>注意句子的主语、谓语、宾语结构</li>
                    <li>识别各种词性及其在句中的作用</li>
                    <li>理解时态和语态的使用</li>
                    <li>掌握修饰语的位置和作用</li>
                </ul>
            `;
            learningContainer.appendChild(defaultPoints);
        }
    }

    /**
     * 从内容中提取学习要点
     * @param {string} content - 分析内容
     * @returns {Array} 学习要点数组
     */
    extractLearningPoints(content) {
        const points = [];
        
        // 查找包含学习要点的段落
        const patterns = [
            /学习要点[：:]([^\\n\\r]*)/gi,
            /要点[：:]([^\\n\\r]*)/gi,
            /注意[：:]([^\\n\\r]*)/gi,
            /重点[：:]([^\\n\\r]*)/gi
        ];
        
        patterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    const point = match.split(/[：:]/)[1].trim();
                    if (point && point.length > 5) {
                        points.push(point);
                    }
                });
            }
        });
        
        return points.slice(0, 5); // 最多5个要点
    }

    /**
     * 显示错误信息
     * @param {HTMLElement} container - 容器
     * @param {string} message - 错误信息
     */
    showError(container, message) {
        const analysisContent = container.querySelector('#grammar-analysis-text');
        if (analysisContent) {
            analysisContent.innerHTML = `
                <div class="grammar-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>${message}</p>
                </div>
            `;
        }
    }

    /**
     * 分析指定句子的语法结构
     * @param {string} sentence - 要分析的句子
     * @param {string} messageId - 消息ID
     * @returns {Promise<boolean>} 是否成功分析
     */
    async analyzeSentence(sentence, messageId) {
        if (!this.initialized) {
            await this.initialize();
        }
        
        try {
            // 获取消息容器
            const messageElement = document.getElementById(messageId);
            if (!messageElement) {
                console.error('找不到消息元素:', messageId);
                return false;
            }
            
            // 创建可视化容器
            const vizContainer = this.createVisualizationContainer(messageElement, sentence);
            
            // 调用API进行语法分析
            const response = await this.callGrammarAnalysisAPI(sentence);
            
            if (!response.ok) {
                throw new Error(`API调用失败: ${response.status}`);
            }
            
            // 处理流式响应
            await this.handleStreamResponse(response, vizContainer);
            
            return true;
        } catch (error) {
            console.error('语法分析失败:', error);
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                this.showError(messageElement, `语法分析失败：${error.message}`);
            }
            return false;
        }
    }

    /**
     * 主要的语法分析入口函数（保留向后兼容）
     * @param {string} text - 用户输入的文本
     * @param {HTMLElement} container - 显示容器
     * @returns {Promise<boolean>} 是否成功分析
     */
    async analyzeGrammar(text, container) {
        if (!this.initialized) {
            await this.initialize();
        }
        
        const sentences = this.extractEnglishSentences(text);
        if (sentences.length === 0) {
            container.innerHTML = `
                <div class="grammar-no-sentences">
                    <i class="fas fa-info-circle"></i>
                    <p>未检测到需要分析的英语句子</p>
                    <p class="hint">请确保输入的文本包含完整的英语句子</p>
                </div>
            `;
            return false;
        }
        
        // 分析第一个句子
        const sentence = sentences[0];
        
        try {
            // 创建可视化容器
            const vizContainer = this.createVisualizationContainer(container, sentence);
            
            // 调用API进行语法分析
            const response = await this.callGrammarAnalysisAPI(sentence);
            
            // 处理流式响应
            await this.handleStreamResponse(response, vizContainer);
            
            return true;
        } catch (error) {
            console.error('语法分析失败:', error);
            this.showError(container, `语法分析失败：${error.message}`);
            return false;
        }
    }

    // 保留向后兼容的方法，但标记为过时
    /**
     * @deprecated 使用 parseGrammarTreeStructure 替代
     */
    extractStructuredData(content) {
        console.warn('extractStructuredData已过时，建议使用parseGrammarTreeStructure');
        return this.parseGrammarTreeStructure(content);
    }

    /**
     * 创建语法树可视化（使用D3.js树状图）
     * @param {HTMLElement} container - 容器
     * @param {Object} treeData - 语法树数据
     */


    /**
     * 渲染语法树（基于脑图项目的D3渲染器改造）
     * @param {HTMLElement} container - 容器
     * @param {Object} data - 树状数据
     */
    renderGrammarTree(container, data) {
        // 如果容器不存在，直接返回
        if (!container) return;
        
        // 清空容器
        container.innerHTML = '';
        
        // 如果数据无效，显示提示
        if (!data || !data.name) {
            container.innerHTML = '<div class="error-message">语法树数据解析失败</div>';
            return;
        }

        // 计算容器宽度
        const width = container.clientWidth || 800;
        
        // 语法树的样式配置（参考脑图配置）
        const CONFIG = {
            nodeHeight: 30,            // 每个节点的总高度
            levelIndent: 40,           // 每一级的缩进距离
            verticalSpacing: 20,       // 节点之间的垂直间距
            leftMargin: 20,            // 左侧边距
            topMargin: 20,             // 顶部边距
            textYOffset: -8,           // 文本相对于横线的垂直偏移量
            lineStrokeWidth: 1.5,      // 线条粗细
            lineColor: '#4CAF50',      // 线条颜色（绿色，语法树特色）
            hoverLineColor: '#2E7D32', // 悬停时的线条颜色
            hoverTextColor: '#2E7D32', // 悬停时的文本颜色
            textColor: '#333'          // 默认文本颜色
        };
        
        // 创建层次结构
        const root = d3.hierarchy(data);
        
        // 直接使用根节点的子节点作为最顶层节点（跳过主节点）
        let processNodes = [];
        if (root.children && root.children.length > 0) {
            processNodes = [...root.children]; // 复制子节点数组
        } else {
            // 如果没有子节点，仍然显示根节点（为了不显示空白）
            processNodes = [root];
        }
        
        // 计算节点数量以确定高度
        let nodeCount = 0;
        
        // 按照前序遍历的顺序为每个节点分配垂直位置（扁平化树结构）
        function assignVerticalPositions(nodes, depth = 0) {
            for (const node of nodes) {
                // 为节点分配水平和垂直位置
                node.x = nodeCount * (CONFIG.nodeHeight + CONFIG.verticalSpacing) + CONFIG.topMargin;
                node.y = depth * CONFIG.levelIndent + CONFIG.leftMargin;
                
                nodeCount++; // 增加计数器
                
                // 递归处理子节点
                if (node.children && node.children.length > 0) {
                    assignVerticalPositions(node.children, depth + 1);
                }
            }
        }
        
        // 从顶层节点开始分配位置
        assignVerticalPositions(processNodes);
        
        // 计算总高度（至少有一个最小高度）
        const totalHeight = Math.max(400, nodeCount * (CONFIG.nodeHeight + CONFIG.verticalSpacing) + CONFIG.topMargin * 2);
        
        // 创建SVG元素
        const svg = d3.select(container)
            .append('svg')
            .attr('width', width)
            .attr('height', totalHeight)
            .attr('class', 'grammar-tree-svg');
        
        // 创建一个包含所有元素的组
        const g = svg.append('g');
        
        // 收集所有节点和连接线数据
        let allNodes = [];
        let allLinks = [];
        let topLevelNodes = []; // 存储顶层节点，用于后续绘制左侧垂直线
        
        processNodes.forEach(node => {
            // 存储顶层节点
            topLevelNodes.push(node);
            
            // 收集所有节点
            node.each(d => {
                allNodes.push(d);
            });
            
            // 收集所有连接线
            node.links().forEach(link => {
                allLinks.push(link);
            });
        });
        
        // 绘制左侧垂直连接线（连接所有顶层节点）
        if (topLevelNodes.length > 1) {
            const startY = topLevelNodes[0].x + CONFIG.nodeHeight / 2;
            const endY = topLevelNodes[topLevelNodes.length - 1].x + CONFIG.nodeHeight / 2;
            
            g.append('line')
                .attr('class', 'main-vertical-line')
                .attr('x1', CONFIG.leftMargin)
                .attr('y1', startY)
                .attr('x2', CONFIG.leftMargin)
                .attr('y2', endY)
                .attr('stroke', CONFIG.lineColor)
                .attr('stroke-width', CONFIG.lineStrokeWidth);
        }
        
        // 绘制连接线 - 确保线条闭合连接
        g.selectAll('.link')
            .data(allLinks)
            .enter()
            .append('path')
            .attr('class', 'link')
            .attr('d', d => {
                // 父节点中心位置
                const parentX = d.source.y;
                const parentY = d.source.x + CONFIG.nodeHeight / 2;
                
                // 子节点左侧位置
                const childX = d.target.y;
                const childY = d.target.x + CONFIG.nodeHeight / 2;
                
                // 创建从父节点到子节点的直角连接线
                return `
                    M${parentX},${parentY}
                    L${childX},${parentY}
                    L${childX},${childY}
                `;
            })
            .attr('fill', 'none')
            .attr('stroke', CONFIG.lineColor)
            .attr('stroke-width', CONFIG.lineStrokeWidth);
        
        // 创建节点组
        const nodeGroup = g.selectAll('.node-group')
            .data(allNodes)
            .enter()
            .append('g')
            .attr('class', d => `node-group depth-${d.depth} ${d.data.type || 'default'}`)
            .attr('transform', d => `translate(${d.y}, ${d.x})`);
        
        // 添加节点水平线
        nodeGroup.append('line')
            .attr('class', 'node-line')
            .attr('x1', 0)
            .attr('y1', CONFIG.nodeHeight / 2)
            .attr('x2', d => {
                // 根据深度设置初始长度，后续会根据文本调整，增加长度以适应更宽的侧边栏
                const initialLength = d.depth === 0 ? 280 : 220;
                const maxLineLength = width - d.y - 20;
                return Math.min(initialLength, maxLineLength);
            })
            .attr('y2', CONFIG.nodeHeight / 2)
            .attr('stroke', CONFIG.lineColor)
            .attr('stroke-width', CONFIG.lineStrokeWidth);

        // 添加节点文本
        const textElements = nodeGroup.append('text')
            .attr('class', 'node-text')
            .attr('x', 5) 
            .attr('y', CONFIG.nodeHeight / 2 + CONFIG.textYOffset)
            .attr('text-anchor', 'start')
            .attr('dominant-baseline', 'middle')
            .text(d => d.data.name)
            .attr('font-family', 'Microsoft YaHei, 微软雅黑, sans-serif')
            .attr('font-size', (d) => this.getNodeFontSize(d.data.type))
            .attr('font-weight', (d) => this.getNodeFontWeight(d.data.type))
            .attr('fill', CONFIG.textColor);

        // 文本截断处理并调整线条长度
        textElements.each(function(d) {
            const text = d3.select(this);
            const textLength = this.getComputedTextLength() + 15; // 增加边距
            const maxTextWidth = width - d.y - 20; // 减少右侧边距，增加可用空间
            
            // 如果文本太长，进行截断
            if (textLength > maxTextWidth) {
                let textContent = d.data.name;
                while (this.getComputedTextLength() > maxTextWidth - 15 && textContent.length > 0) {
                    textContent = textContent.slice(0, -1);
                    text.text(textContent + '...');
                }
            }
            
            // 更新线条长度匹配文本长度或确保最小长度
            const finalTextLength = this.getComputedTextLength() + 15;
            const minLineLength = 40; // 增加最小长度
            const lineLength = Math.max(minLineLength, Math.min(finalTextLength, maxTextWidth));
            d3.select(this.parentNode).select('.node-line')
                .attr('x2', lineLength);
        });

        // 添加鼠标悬停效果
        nodeGroup.on('mouseover', function() {
            d3.select(this).select('.node-line')
                .transition()
                .duration(200)
                .attr('stroke-width', CONFIG.lineStrokeWidth * 1.5)
                .attr('stroke', CONFIG.hoverLineColor);
                
            d3.select(this).select('.node-text')
                .transition()
                .duration(200)
                .attr('fill', CONFIG.hoverTextColor);
        }).on('mouseout', function() {
            d3.select(this).select('.node-line')
                .transition()
                .duration(200)
                .attr('stroke-width', CONFIG.lineStrokeWidth)
                .attr('stroke', CONFIG.lineColor);
                
            d3.select(this).select('.node-text')
                .transition()
                .duration(200)
                .attr('fill', CONFIG.textColor);
        });

        return svg.node();
    }

    /**
     * 获取节点宽度
     * @param {string} text - 节点文本
     * @param {string} type - 节点类型
     * @returns {number} 节点宽度
     */
    getNodeWidth(text, type) {
        // 对于长文本，使用固定的最大宽度以支持换行，增加宽度以适应更宽的侧边栏
        if (text.length > 20) {
            return Math.min(430, Math.max(text.length * 10 + 50, 240));
        }
        
        const baseWidth = text.length * 15 + 30;
        const minWidth = type === 'component' ? 160 : 140;
        return Math.max(baseWidth, minWidth);
    }

    /**
     * 获取节点颜色
     * @param {string} type - 节点类型
     * @returns {string} 颜色值
     */
    getNodeColor(type) {
        const colorMap = {
            'root': '#E1F5FE',         // 浅天蓝色 - 根节点
            'component': '#E8F5E8',    // 浅绿色 - 句子成分（实际内容）
            'component_label': '#F3E5F5', // 浅紫色 - 成分标签（主语、谓语等）
            'content': '#E8F5E8',      // 浅绿色 - 文本内容
            'role': '#FFF3E0',         // 浅橙色 - 语法作用
            'structure': '#FCE4EC',    // 浅粉色 - 内部结构
            'info': '#F1F8E9',         // 浅绿黄色 - 通用信息
            'list': '#FFF8E1',         // 浅黄色 - 列表项
            'loading': '#EEEEEE'       // 浅灰色 - 加载中
        };
        return colorMap[type] || '#F5F5F5';
    }

    /**
     * 获取节点字体大小
     * @param {string} type - 节点类型
     * @returns {string} 字体大小
     */
    getNodeFontSize(type) {
        const sizeMap = {
            'root': '16px',            // 根节点 - 最大
            'component': '15px',       // 句子成分（实际内容）- 较大
            'component_label': '14px', // 成分标签 - 统一加大
            'content': '14px',         // 文本内容 - 统一加大
            'role': '14px',            // 语法作用 - 统一加大
            'structure': '14px',       // 内部结构 - 统一加大
            'info': '14px',            // 通用信息 - 统一加大
            'list': '14px',            // 列表项 - 统一加大
            'loading': '14px'          // 加载中 - 统一加大
        };
        return sizeMap[type] || '14px';
    }

    /**
     * 获取节点字体粗细
     * @param {string} type - 节点类型
     * @returns {string} 字体粗细
     */
    getNodeFontWeight(type) {
        const weightMap = {
            'root': 'bold',            // 根节点 - 粗体
            'component': 'bold',       // 句子成分（实际内容）- 粗体
            'component_label': '600',  // 成分标签 - 半粗体
            'content': '600',          // 文本内容 - 半粗体
            'role': '600',             // 语法作用 - 半粗体
            'structure': 'normal',     // 内部结构 - 正常
            'info': 'normal',          // 通用信息 - 正常
            'list': 'normal',          // 列表项 - 正常
            'loading': 'italic'        // 加载中 - 斜体
        };
        return weightMap[type] || 'normal';
    }

    /**
     * 调用后端句子成分分析API（已弃用，使用新的流式API）
     * @param {string} sentence - 待分析的句子
     * @returns {Promise<Object>} 分析结果JSON
     * @deprecated 使用 callGrammarAnalysisAPI 替代
     */
    async callComponentAnalysisAPI(sentence) {
        console.warn('callComponentAnalysisAPI已弃用，建议使用callGrammarAnalysisAPI');
        try {
            // 使用新的流式API
            const response = await this.callGrammarAnalysisAPI(sentence);
            if (!response.ok) throw new Error(`Grammar API错误: ${response.status}`);
            
            // 返回兼容格式（简化版）
            return {
                nodes: [
                    { label: '正在使用新的流式分析API，请使用完整的语法分析功能' }
                ]
            };
        } catch (e) {
            console.error('调用语法分析API失败:', e);
            throw e;
        }
    }

    /**
     * 执行句子成分级别分析并渲染结果为树形图
     * @param {string} sentence - 待分析的句子
     */
    async performComponentAnalysis(sentence) {
        const container = document.getElementById('grammarVizContainer');
        if (!container) return;
        container.innerHTML = '';
        try {
            const result = await this.callComponentAnalysisAPI(sentence);
            // 构建层级数据，仅使用nodes
            const treeData = {
                name: '句子成分分析',
                children: result.nodes.map(n => ({ name: n.label, children: [] }))
            };
            // 直接使用renderGrammarTree渲染，避免额外嵌套
            container.innerHTML = '';
            this.renderGrammarTree(container, treeData);
        } catch (e) {
            container.innerHTML = '<p class="error">句子成分分析失败</p>';
        }
    }
}
// 导出语法分析器
window.GrammarAnalyzer = GrammarAnalyzer;