"""
通用路由处理器
包含字幕问答、语法分析、播客搜索等功能
"""

import os
import json
import logging
import requests
from datetime import datetime
from flask import Blueprint, request, jsonify, Response, stream_with_context
from api.rate_limit_config import create_rate_limiter
from api.streaming_handler import StreamingHandler

logger = logging.getLogger(__name__)

# 创建蓝图
common_bp = Blueprint('common', __name__)

def init_common_routes(app):
    """初始化通用路由"""
    app.register_blueprint(common_bp, url_prefix='/api')

@common_bp.route('/subtitle-chat-stream', methods=['POST'])
def subtitle_chat_stream():
    """字幕问答流式响应API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据无效"}), 400
        
        # 获取请求参数
        current_subtitle = data.get('current_subtitle', '')
        previous_subtitle = data.get('previous_subtitle', '')
        next_subtitle = data.get('next_subtitle', '')
        user_question = data.get('user_question', '')
        model_id = data.get('model', 'glm-4-flash')
        
        if not current_subtitle:
            return jsonify({"error": "缺少当前字幕内容"}), 400
        
        auth_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        
        # 使用字幕问答处理器
        from api.streaming_handler import SubtitleChatHandler
        handler = SubtitleChatHandler(
            auth_token, current_subtitle, previous_subtitle, 
            next_subtitle, user_question, model_id
        )
        return handler.process_subtitle_chat()
        
    except Exception as e:
        logger.error(f"字幕问答处理失败: {e}")
        return jsonify({"error": "字幕问答处理失败，请重试"}), 500

@common_bp.route('/grammar-analysis-stream', methods=['POST'])
def grammar_analysis_stream():
    """英语语法结构分析流式响应API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据无效"}), 400
        
        # 获取请求参数
        sentence = data.get('sentence', '')
        system_message = data.get('system_message', '')
        user_prompt = data.get('user_prompt', '')
        model_id = data.get('model', 'zhipu_flash')
        
        if not sentence:
            return jsonify({"error": "缺少待分析的句子"}), 400
        
        auth_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        
        # 使用语法分析处理器
        from api.streaming_handler import GrammarAnalysisHandler
        handler = GrammarAnalysisHandler(
            auth_token, sentence, system_message, 
            user_prompt, model_id
        )
        return handler.process_grammar_analysis()
        
    except Exception as e:
        logger.error(f"语法分析处理失败: {e}")
        return jsonify({"error": "语法分析处理失败，请重试"}), 500

@common_bp.route('/generate-timestamps', methods=['POST'])
@create_rate_limiter("content", "generate_timestamps")
def generate_timestamps():
    """从字幕文件生成时间戳"""
    try:
        data = request.json
        subtitle_file_id = data.get('subtitle_file_id')
        if not subtitle_file_id:
            return jsonify({"error": "缺少字幕文件ID"}), 400
        auth_token = request.headers.get('Authorization', '').replace('Bearer ', '')

        # ====== 新增：积分预检查，读取实际字幕内容进行token估算 ======
        import os, math
        from api.token_utils import estimate_tokens
        from api.models_config import get_model_by_id
        from api.credits import CREDIT_COST_RATE
        temp_dir = "temp"
        subtitle_path = os.path.join(temp_dir, f"{subtitle_file_id}.srt")
        if os.path.exists(subtitle_path):
            with open(subtitle_path, 'r', encoding='utf-8') as f:
                subtitle_content = f.read()
            input_tokens = estimate_tokens(subtitle_content)
            output_tokens = max(50, input_tokens // 3)
            model_config = get_model_by_id('zhipu_flash')
            input_price = model_config['pricing']['input']
            output_price = model_config['pricing']['output']
            usd_cost = input_tokens * input_price + output_tokens * output_price
            required_credits = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))
            # 这里可加日志
            print(f"[积分预检查] 字幕内容token={input_tokens}, 预估积分={required_credits}")
            # TODO: 实际项目中应调用积分检查API，传递required_credits
        # ====== 预检查结束 ======

        # 使用时间戳生成处理器
        from api.streaming_handler import TimestampGenerationHandler
        handler = TimestampGenerationHandler(auth_token, subtitle_file_id)
        return handler.process_timestamp_generation()
    except Exception as e:
        logger.error(f"生成时间戳API失败: {e}")
        return jsonify({"error": f"生成时间戳失败: {str(e)}"}), 500

@common_bp.route('/upload-subtitle', methods=['POST'])
@create_rate_limiter("content", "upload_subtitle")
def upload_subtitle():
    """上传字幕文件到服务器"""
    try:
        skip_credits_check = request.form.get('skip_credits_check', 'false').lower() == 'true'
        
        # 积分检查逻辑
        from api.config import CHECK_CREDITS_UPLOAD_SUBTITLE
        if CHECK_CREDITS_UPLOAD_SUBTITLE and not skip_credits_check:
            from api.credits import get_user_id_from_request, get_user_credits_from_cache, service_supabase_client
            from api.credits_cache import cache_user_credits
            
            user_id = get_user_id_from_request()
            if not user_id:
                return jsonify({"success": False, "error": "需要登录才能使用此功能"}), 401
            
            current_credits = get_user_credits_from_cache(user_id)
            if current_credits is None:
                credits_resp = service_supabase_client.table("user_credits").select("credits").eq("user_id", user_id).execute()
                data = credits_resp.data or []
                current_credits = data[0].get("credits", 0) if data else 0
                cache_user_credits(user_id, current_credits)
            
            if current_credits < 1:
                return jsonify({"success": False, "error": "积分不足，请充值", "current_credits": current_credits, "required_credits": 1}), 200
        
        if 'subtitleFile' not in request.files:
            return jsonify({"error": "没有找到字幕文件"}), 400

        file = request.files['subtitleFile']
        file_id = request.form.get('file_id')
        
        if not file_id or file.filename == '':
            return jsonify({"error": "缺少必要参数或文件名"}), 400
        
        # 确保temp目录存在
        temp_dir = "temp"
        os.makedirs(temp_dir, exist_ok=True)
        
        # 直接保存原始文件
        subtitle_path = os.path.join(temp_dir, f'{file_id}.srt')
        file.save(subtitle_path)
        
        logger.info(f"字幕文件保存成功: {file_id}, 路径: {subtitle_path}")
        
        return jsonify({
            "success": True,
            "file_id": file_id,
            "filename": file.filename,
            "message": "字幕文件上传成功"
        })
        
    except Exception as e:
        logger.error(f"字幕文件上传失败: {e}")
        return jsonify({"error": f"字幕文件上传失败: {str(e)}"}), 500

# 播客功能路由
@common_bp.route('/podcast/search', methods=['GET'])
@create_rate_limiter("content", "podcast_search")
def podcast_search():
    """搜索播客"""
    try:
        skip_credits_check = request.args.get('skip_credits_check', 'false').lower() == 'true'
        
        # 积分检查
        from api.config import CHECK_CREDITS_PODCAST_SEARCH
        if CHECK_CREDITS_PODCAST_SEARCH and not skip_credits_check:
            from api.credits import get_user_id_from_request, get_user_credits_from_cache, service_supabase_client
            from api.credits_cache import cache_user_credits
            
            user_id = get_user_id_from_request()
            if not user_id:
                return jsonify({"success": False, "error": "需要登录才能使用此功能"}), 401
            
            current_credits = get_user_credits_from_cache(user_id)
            if current_credits is None:
                credits_resp = service_supabase_client.table("user_credits").select("credits").eq("user_id", user_id).execute()
                data = credits_resp.data or []
                current_credits = data[0].get("credits", 0) if data else 0
                cache_user_credits(user_id, current_credits)
            
            if current_credits < 1:
                return jsonify({"success": False, "error": "积分不足，请充值", "current_credits": current_credits, "required_credits": 1}), 200
        
        query = request.args.get('q', '')
        if not query:
            return jsonify({'error': '请提供搜索关键词'}), 400
        
        # 调用iTunes Search API
        from urllib.parse import quote
        url = f'https://itunes.apple.com/search?term={quote(query)}&media=podcast&limit=20'
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        return jsonify(data)
        
    except requests.RequestException as e:
        return jsonify({'error': f'搜索请求失败: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500

@common_bp.route('/podcast/episodes', methods=['GET'])
@create_rate_limiter("content", "podcast_episodes")
def podcast_episodes():
    """获取播客节目列表"""
    try:
        feed_url = request.args.get('feedUrl', '')
        page = int(request.args.get('page', 0))
        per_page = int(request.args.get('per_page', 20))
        
        if not feed_url:
            return jsonify({'error': '请提供播客feed URL'}), 400
        
        # 使用播客处理器
        from api.podcast_handler import PodcastHandler
        handler = PodcastHandler()
        return handler.get_episodes(feed_url, page, per_page)
        
    except Exception as e:
        logger.error(f'获取节目列表失败: {e}')
        return jsonify({'error': f'获取节目列表失败: {str(e)}'}), 500

@common_bp.route('/podcast/proxy_download', methods=['GET'])
@create_rate_limiter("content", "podcast_download")
def podcast_proxy_download():
    """代理下载播客音频"""
    try:
        audio_url = request.args.get('audio_url', '')
        if not audio_url:
            return jsonify({'error': '请提供音频URL'}), 400
        
        # 使用播客处理器
        from api.podcast_handler import PodcastHandler
        handler = PodcastHandler()
        return handler.proxy_download(audio_url)
        
    except Exception as e:
        logger.error(f"播客代理下载函数发生严重错误: {e}")
        return jsonify({'error': f'播客下载服务异常: {str(e)}'}), 500

# 书签相关API
@common_bp.route('/export-bookmarks', methods=['POST'])
@create_rate_limiter("auth", "bookmark_save")
def export_bookmarks():
    """导出书签文件供下载"""
    try:
        # 积分检查
        from api.config import CHECK_CREDITS_BOOKMARK_SAVE
        if CHECK_CREDITS_BOOKMARK_SAVE:
            from api.credits import get_user_id_from_request, get_user_credits_from_cache, service_supabase_client
            from api.credits_cache import cache_user_credits
            
            user_id = get_user_id_from_request()
            if not user_id:
                return jsonify({"success": False, "error": "需要登录才能使用此功能"}), 401
            
            current_credits = get_user_credits_from_cache(user_id)
            if current_credits is None:
                credits_resp = service_supabase_client.table("user_credits").select("credits").eq("user_id", user_id).execute()
                data = credits_resp.data or []
                current_credits = data[0].get("credits", 0) if data else 0
                cache_user_credits(user_id, current_credits)
            
            if current_credits < 1:
                return jsonify({"success": False, "error": "积分不足，请充值", "current_credits": current_credits, "required_credits": 1}), 200
        
        data = request.get_json()
        filename = data.get('filename')
        bookmarks = data.get('bookmarks', [])
        
        if not filename:
            return jsonify({'success': False, 'error': '文件名不能为空'}), 400
        
        # 使用书签处理器
        from api.bookmark_handler import BookmarkHandler
        handler = BookmarkHandler()
        return handler.export_bookmarks(filename, bookmarks)
        
    except Exception as e:
        logger.error(f"导出书签文件失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@common_bp.route('/parse-bookmarks', methods=['POST'])
@create_rate_limiter("auth", "bookmark_load")
def parse_bookmarks():
    """解析上传的书签文件内容"""
    try:
        # 积分检查
        from api.config import CHECK_CREDITS_BOOKMARK_LOAD
        if CHECK_CREDITS_BOOKMARK_LOAD:
            from api.credits import get_user_id_from_request, get_user_credits_from_cache, service_supabase_client
            from api.credits_cache import cache_user_credits
            
            user_id = get_user_id_from_request()
            if not user_id:
                return jsonify({"success": False, "error": "需要登录才能使用此功能"}), 401
            
            current_credits = get_user_credits_from_cache(user_id)
            if current_credits is None:
                credits_resp = service_supabase_client.table("user_credits").select("credits").eq("user_id", user_id).execute()
                data = credits_resp.data or []
                current_credits = data[0].get("credits", 0) if data else 0
                cache_user_credits(user_id, current_credits)
            
            if current_credits < 1:
                return jsonify({"success": False, "error": "积分不足，请充值", "current_credits": current_credits, "required_credits": 1}), 200
        
        data = request.get_json()
        content = data.get('content', '')
        
        if not content:
            return jsonify({'success': False, 'error': '书签文件内容为空'}), 400
        
        # 使用书签处理器
        from api.bookmark_handler import BookmarkHandler
        handler = BookmarkHandler()
        return handler.parse_bookmarks(content)
        
    except Exception as e:
        logger.error(f"解析书签文件失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500 