"""
Token 工具模块 - 使用OpenAI的tiktoken统一处理token计算
"""
import logging

# 获取已配置的logger
logger = logging.getLogger(__name__)

# 全局变量用于单例模式
_tokenizer = None
_tiktoken_available = None
_initialized = False

def _initialize_tokenizer():
    """单例模式初始化tokenizer，只初始化一次 - 优化版：延迟加载"""
    global _tokenizer, _tiktoken_available, _initialized
    
    if _initialized:
        return _tokenizer, _tiktoken_available
    
    try:
        # 延迟导入tiktoken，仅在首次需要时加载
        import tiktoken
        # 使用o200k_base编码器，这是较新的编码器
        _tokenizer = tiktoken.get_encoding("o200k_base")
        _tiktoken_available = True
        logger.info("tiktoken库已延迟加载，使用o200k_base编码器")
    except ImportError:
        _tiktoken_available = False
        _tokenizer = None
        logger.warning("tiktoken库未安装，将使用简单估算方法")
    except Exception as e:
        _tiktoken_available = False
        _tokenizer = None
        logger.warning(f"tiktoken库加载失败: {e}，将使用简单估算方法")
    
    _initialized = True
    return _tokenizer, _tiktoken_available

def get_tokenizer():
    """获取tokenizer实例，使用延迟初始化"""
    return _initialize_tokenizer()

# 为向后兼容性保留的全局变量 - 延迟初始化，避免启动时加载
tokenizer = None
TIKTOKEN_AVAILABLE = None

def _lazy_init_globals():
    """延迟初始化全局变量"""
    global tokenizer, TIKTOKEN_AVAILABLE
    if tokenizer is None or TIKTOKEN_AVAILABLE is None:
        tokenizer, TIKTOKEN_AVAILABLE = get_tokenizer()

def estimate_tokens(text):
    """
    估算文本的token数量 - 统一使用OpenAI的方法，延迟初始化版本
    
    参数:
        text: 要估算的文本
        
    返回:
        估算的token数量
    """
    if not text:
        return 0
    
    # 延迟初始化全局变量
    _lazy_init_globals()
    
    if TIKTOKEN_AVAILABLE and tokenizer:
        try:
            return len(tokenizer.encode(text))
        except Exception as e:
            logger.warning(f"tiktoken计算失败: {e}，使用备用方法")
    
    # 备用方法：简单估算
    chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
    other_chars = len(text) - chinese_chars
    
    # 中文字符按照2.5:1的比例计算，其他字符按照4:1的比例计算
    estimated_tokens = (chinese_chars * 2.5) + (other_chars / 4)
    return max(1, int(estimated_tokens))

def validate_token_limits(text, system_message, model_id, operation_type=None):
    """
    验证输入文本是否超过指定模型的token限制
    
    参数:
        text: 用户输入文本
        system_message: 系统提示文本
        model_id: 模型ID
        operation_type: 操作类型，如'summary', 'mindmap'等，用于估算输出token
        
    返回:
        (is_valid, validation_info): 元组，包含验证结果和详细信息字典
    """
    from api.models_config import get_model_by_id
    
    # 获取模型配置
    model_config = get_model_by_id(model_id)
    
    # 获取模型的最大输入token限制
    max_input_tokens = model_config.get('max_input_tokens', 100000)
    
    # 估算输入token数
    estimated_input_tokens = estimate_tokens(text) + estimate_tokens(system_message)
    
    # 只验证输入是否在限制范围内
    input_valid = estimated_input_tokens <= max_input_tokens
    
    # 创建验证结果
    validation_info = {
        "estimated_input_tokens": estimated_input_tokens,
        "max_input_tokens": max_input_tokens,
        "input_valid": input_valid,
        "failure_reason": "input_exceeded" if not input_valid else None
    }
    
    logger.info(f"Token验证: 模型={model_id}, 输入tokens={estimated_input_tokens}/{max_input_tokens}, "
               f"结果={'通过' if input_valid else '超限'}")
    
    return (input_valid, validation_info)

def validate_tokens_for_model(model_id, prompt, max_output_tokens=None):
    """
    验证输入提示词是否超过指定模型的token限制
    
    参数:
        model_id: 模型ID
        prompt: 提示词文本
        max_output_tokens: 可选，指定最大输出token数
        
    返回:
        验证结果字典，包含valid, message, error_type和details字段
    """
    from api.models_config import get_model_by_id
    
    # 获取模型配置
    model_config = get_model_by_id(model_id)
    
    # 获取模型的最大输入token限制
    max_input_tokens = model_config.get('max_input_tokens', 100000)
    
    # 如果未指定最大输出token数，使用模型配置中的值或默认值
    if max_output_tokens is None:
        max_output_tokens = model_config.get('max_output_tokens', 8000)
    
    # 估算输入token数
    estimated_input_tokens = estimate_tokens(prompt)
    
    # 验证输入是否在限制范围内
    input_valid = estimated_input_tokens <= max_input_tokens
    
    # 创建验证结果
    result = {
        "valid": input_valid,
        "message": "",
        "error_type": None,
        "details": {
            "estimated_input_tokens": estimated_input_tokens,
            "max_input_tokens": max_input_tokens,
            "max_output_tokens": max_output_tokens
        }
    }
    
    # 设置错误信息
    if not input_valid:
        result["error_type"] = "input_exceeded"
        result["message"] = f"输入内容超出模型处理能力 (估计 {estimated_input_tokens} tokens, 最大允许 {max_input_tokens} tokens)。请减少内容量或选择支持更长上下文的模型。"
        logger.warning(f"Token验证失败: {result['message']}")
    
    return result

def format_token_limit_message(validation_info, language='zh'):
    """
    创建用户友好的token限制错误消息
    
    参数:
        validation_info: 验证信息字典，包含估算token数、最大限制和失败原因
        language: 语言代码 ('zh' 或 'en')
        
    返回:
        格式化的错误消息
    """
    failure_reason = validation_info.get("failure_reason")
    
    if language.lower().startswith('zh'):
        if failure_reason == "input_exceeded":
            return f"输入内容超出模型处理能力 (估计 {validation_info['estimated_input_tokens']} tokens, 最大允许 {validation_info['max_input_tokens']} tokens)。请减少内容量或选择支持更长上下文的模型。"
        elif failure_reason == "output_exceeded":
            return f"预计输出内容超出模型处理能力 (估计输出 {validation_info['estimated_output_tokens']} tokens, 最大允许 {validation_info['max_output_tokens']} tokens)。请减少内容量或选择支持更长输出的模型。"
        else:
            return f"内容超出模型处理能力。输入: {validation_info['estimated_input_tokens']}/{validation_info['max_input_tokens']} tokens, 输出: {validation_info['estimated_output_tokens']}/{validation_info['max_output_tokens']} tokens。请减少内容量或选择支持更大容量的模型。"
    else:
        if failure_reason == "input_exceeded":
            return f"Input content exceeds model capacity (estimated {validation_info['estimated_input_tokens']} tokens, maximum allowed {validation_info['max_input_tokens']} tokens). Please reduce content or choose a model that supports longer context."
        elif failure_reason == "output_exceeded":
            return f"Estimated output exceeds model capacity (estimated output {validation_info['estimated_output_tokens']} tokens, maximum allowed {validation_info['max_output_tokens']} tokens). Please reduce content or choose a model that supports longer outputs."
        else:
            return f"Content exceeds model capacity. Input: {validation_info['estimated_input_tokens']}/{validation_info['max_input_tokens']} tokens, Output: {validation_info['estimated_output_tokens']}/{validation_info['max_output_tokens']} tokens. Please reduce content or choose a model with larger capacity."
