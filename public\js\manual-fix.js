/**
 * 手动修复脚本 - 用户可以在控制台直接调用
 */
window.manualFix = {
    
    /**
     * 快速修复认证问题
     */
    async quickFix() {
        console.log('🛠️ [Manual Fix] 开始快速修复...');
        
        try {
            // 1. 从URL获取token
            const url = new URL(window.location.href);
            const accessToken = url.searchParams.get('access_token') || 
                               new URLSearchParams(url.hash.substring(1)).get('access_token');
            const refreshToken = url.searchParams.get('refresh_token') || 
                                new URLSearchParams(url.hash.substring(1)).get('refresh_token');
                                
            console.log('access_token 存在:', !!accessToken);
            console.log('refresh_token 存在:', !!refreshToken);
            
            if (!accessToken) {
                console.log('❌ 没有找到access_token，无法修复');
                return false;
            }
            
            // 2. 设置Supabase Session
            console.log('🔧 正在设置Supabase Session...');
            const { data, error } = await window.supabaseClient.auth.setSession({
                access_token: accessToken,
                refresh_token: refreshToken
            });
            
            if (error) {
                console.log('❌ 设置Session失败:', error);
                return false;
            }
            
            // 3. 保存到localStorage
            console.log('💾 保存认证信息到localStorage...');
            localStorage.setItem('authToken', accessToken);
            localStorage.setItem('refreshToken', refreshToken);
            localStorage.setItem('userData', JSON.stringify(data.session.user));
            localStorage.setItem('isAuthenticated', 'true');
            localStorage.setItem('userEmail', data.session.user?.email || '');
            localStorage.setItem('userId', data.session.user?.id || '');
            
            // 4. 与后端同步
            console.log('🔄 与后端同步Session...');
            const syncResponse = await fetch('/api/auth/set-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${accessToken}`
                },
                body: JSON.stringify({
                    access_token: accessToken,
                    refresh_token: refreshToken,
                    user: data.session.user
                })
            });
            
            if (syncResponse.ok) {
                console.log('✅ 修复成功！正在清理URL...');
                window.history.replaceState({}, document.title, window.location.pathname);
                console.log('✅ 请刷新页面验证修复效果');
                return true;
            } else {
                console.log('❌ 后端同步失败');
                return false;
            }
            
        } catch (e) {
            console.log('❌ 修复过程出错:', e.message);
            return false;
        }
    },
    
    /**
     * 测试API连接
     */
    async testAPI() {
        console.log('🧪 [Manual Fix] 测试API连接...');
        
        try {
            const response = await fetch('/api/credits/get-fast');
            const data = await response.json();
            
            console.log('API响应状态:', response.status);
            console.log('API响应数据:', data);
            
            if (data.success) {
                console.log('✅ API连接正常');
            } else {
                console.log('❌ API返回错误:', data.error);
            }
            
            return data;
        } catch (e) {
            console.log('❌ API请求失败:', e.message);
            return null;
        }
    },
    
    /**
     * 清理认证状态
     */
    clearAuth() {
        console.log('🧹 [Manual Fix] 清理认证状态...');
        
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('userData');
        localStorage.removeItem('isAuthenticated');
        localStorage.removeItem('userEmail');
        localStorage.removeItem('userId');
        
        console.log('✅ 认证状态已清理');
    },
    
    /**
     * 显示当前状态
     */
    showStatus() {
        console.log('📊 [Manual Fix] 当前认证状态:');
        console.log('- URL access_token:', !!new URL(window.location.href).searchParams.get('access_token'));
        console.log('- localStorage authToken:', !!localStorage.getItem('authToken'));
        console.log('- localStorage isAuthenticated:', localStorage.getItem('isAuthenticated'));
        console.log('- localStorage userEmail:', localStorage.getItem('userEmail'));
    },
    
    /**
     * 显示帮助信息
     */
    help() {
        console.log(`
🛠️ 手动修复工具使用说明:

1. manualFix.quickFix()     - 快速修复认证问题
2. manualFix.testAPI()      - 测试API连接
3. manualFix.clearAuth()    - 清理认证状态
4. manualFix.showStatus()   - 显示当前状态
5. manualFix.help()         - 显示此帮助

📝 使用步骤:
1. 首先运行 manualFix.showStatus() 查看状态
2. 如果有问题，运行 manualFix.quickFix() 进行修复
3. 修复后运行 manualFix.testAPI() 验证
4. 如果仍有问题，可运行 manualFix.clearAuth() 清理后重新登录
        `);
    }
};

// 自动显示帮助信息
console.log('🛠️ 手动修复工具已加载！输入 manualFix.help() 查看使用说明'); 