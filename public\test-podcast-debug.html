<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播客API调试测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .episode { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .date { color: #666; font-size: 12px; }
        button { padding: 8px 16px; margin: 5px; }
        .log { background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: scroll; }
    </style>
</head>
<body>
    <h1>播客API调试测试</h1>
    
    <div class="test-section">
        <h2>测试播客: Hard Fork (The New York Times)</h2>
        <button onclick="testAPI(0)">测试第1页</button>
        <button onclick="testAPI(1)">测试第2页</button>
        <button onclick="testAPI(2)">测试第3页</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="test-section">
        <h2>API调用日志</h2>
        <div id="log" class="log"></div>
    </div>

    <div class="test-section">
        <h2>节目列表</h2>
        <div id="episodes"></div>
    </div>

    <script>
        const log = document.getElementById('log');
        const episodesDiv = document.getElementById('episodes');
        
        // Hard Fork 播客的feed URL
        const feedUrl = 'https://feeds.simplecast.com/K2iOb5wR';
        
        function addLog(message) {
            const time = new Date().toLocaleTimeString();
            log.innerHTML += `[${time}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        function clearResults() {
            episodesDiv.innerHTML = '';
            log.innerHTML = '';
        }
        
        async function testAPI(page) {
            addLog(`开始测试第${page + 1}页...`);
            
            try {
                const url = `/podcast/episodes?feedUrl=${encodeURIComponent(feedUrl)}&page=${page}&per_page=3`;
                addLog(`API URL: ${url}`);
                
                const response = await fetch(url);
                const data = await response.json();
                
                addLog(`状态码: ${response.status}`);
                
                if (!response.ok) {
                    addLog(`❌ 错误: ${data.error}`);
                    return;
                }
                
                if (data.episodes) {
                    addLog(`✅ 新格式 - 总数: ${data.total}, 当前页: ${data.page}, 还有更多: ${data.has_more}`);
                    displayEpisodes(data.episodes, `第${page + 1}页`);
                } else if (Array.isArray(data)) {
                    addLog(`⚠️ 旧格式 - 节目数: ${data.length}`);
                    displayEpisodes(data, `第${page + 1}页 (旧格式)`);
                } else {
                    addLog(`❌ 未知格式: ${JSON.stringify(data).substring(0, 100)}...`);
                }
                
            } catch (error) {
                addLog(`❌ 请求失败: ${error.message}`);
            }
        }
        
        function displayEpisodes(episodes, title) {
            const section = document.createElement('div');
            section.innerHTML = `<h3>${title}</h3>`;
            
            episodes.forEach((episode, index) => {
                const div = document.createElement('div');
                div.className = 'episode';
                div.innerHTML = `
                    <strong>${index + 1}. ${episode.title}</strong>
                    <div class="date">发布: ${episode.published}</div>
                    <div>描述: ${episode.description.substring(0, 100)}...</div>
                `;
                section.appendChild(div);
            });
            
            episodesDiv.appendChild(section);
        }
        
        // 页面加载时自动测试第1页
        window.onload = () => {
            addLog('页面加载完成，准备测试...');
            setTimeout(() => testAPI(0), 1000);
        };
    </script>
</body>
</html> 