/**
 * 语言设置管理模块
 * 用于处理AI输出语言的设置和保存
 */
const LanguageSettings = (function() {
    // 语言映射表，用于翻译语言代码到显示名称
    const languageMap = {
        'zh': '中文',
        'en': 'English',
        'ja': '日本語',
        'ko': '한국어'
    };

    // 支持的语言列表
    const supportedLanguages = Object.keys(languageMap);
    
    // 确定默认语言的函数（优先使用与UI语言相同的逻辑）
    function determineDefaultLanguage() {
        // 1. 首先尝试使用UI语言
        if (window.UILanguage && typeof window.UILanguage.getCurrentLanguage === 'function') {
            const uiLang = window.UILanguage.getCurrentLanguage();
            if (supportedLanguages.includes(uiLang)) {
                return uiLang;
            }
        }
        
        // 2. 如果UI语言不可用，使用浏览器语言
        const browserLang = navigator.language || navigator.userLanguage;
        const matchedLang = supportedLanguages.find(lang => 
            browserLang.toLowerCase().startsWith(lang.toLowerCase())
        );
        
        if (matchedLang) {
            return matchedLang;
        }
        
        // 3. 如果前两者都失败，使用中文作为兜底
        return 'zh';
    }

    // 当前选择的语言
    let currentLanguage = determineDefaultLanguage();
    
    /**
     * 初始化语言选择器功能
     */
    function init() {
        const languageSelector = document.getElementById('aiLanguageSelector'); 
        const languageDropdown = document.getElementById('aiLanguageDropdown'); 
        const currentLanguageEl = document.getElementById('currentAiLanguageName'); 
        
        if (!languageSelector || !languageDropdown || !currentLanguageEl) {
            return;
        }
        
        // 设置初始显示的语言名称
        currentLanguageEl.textContent = languageMap[currentLanguage];
        
        // 标记当前选中的语言选项
        updateSelectedOption();
        
        // 点击语言标签时显示/隐藏下拉菜单
        const languageButton = languageSelector.querySelector('.language-button'); 
        if (languageButton) {
            languageButton.addEventListener('click', function(e) {
                e.stopPropagation();
                languageDropdown.classList.toggle('active'); 
            });
        }
        
        // 点击语言选项时更新语言设置
        const languageOptions = languageDropdown.querySelectorAll('.ai-language-option'); 
        languageOptions.forEach(option => {
            option.addEventListener('click', function() {
                const langCode = this.getAttribute('data-lang');
                setLanguage(langCode);
                languageDropdown.classList.remove('active'); 
            });
        });
        
        // 点击页面其他位置时关闭下拉菜单
        document.addEventListener('click', function(e) {
            if (!languageSelector.contains(e.target)) {
                languageDropdown.classList.remove('active'); 
            }
        });
    }
    
    /**
     * 设置当前语言
     * @param {string} langCode - 语言代码
     */
    function setLanguage(langCode) {
        if (languageMap[langCode]) {
            currentLanguage = langCode;
            
            // 更新UI显示
            const currentLanguageEl = document.getElementById('currentAiLanguageName'); 
            if (currentLanguageEl) {
                currentLanguageEl.textContent = languageMap[langCode];
            }
            
            // 更新选中状态
            updateSelectedOption();
        }
    }
    
    /**
     * 更新语言选项的选中状态
     */
    function updateSelectedOption() {
        const options = document.querySelectorAll('.ai-language-option'); 
        options.forEach(option => {
            if (option.getAttribute('data-lang') === currentLanguage) {
                option.classList.add('selected');
            } else {
                option.classList.remove('selected');
            }
        });
    }
    
    /**
     * 获取当前选择的语言代码
     * @returns {string} 当前语言代码
     */
    function getCurrentLanguage() {
        return currentLanguage;
    }
    
    /**
     * 重置为当前UI语言
     */
    function resetToUILanguage() {
        if (window.UILanguage && typeof window.UILanguage.getCurrentLanguage === 'function') {
            const uiLang = window.UILanguage.getCurrentLanguage();
            if (supportedLanguages.includes(uiLang)) {
                setLanguage(uiLang);
                return true;
            }
        }
        return false;
    }
    
    // 当DOM内容加载完成后初始化
    document.addEventListener('DOMContentLoaded', init);
    
    // 监听UI语言变化事件，确保在UI语言切换时重新翻译页面
    document.addEventListener('ui-language-changed', function() {
        // 重新初始化语言设置以更新显示
        setTimeout(init, 50);
    });
    
    // 公开API
    return {
        getCurrentLanguage,
        setLanguage,
        resetToUILanguage
    };
})();

// 确保全局可访问
window.LanguageSettings = LanguageSettings;
