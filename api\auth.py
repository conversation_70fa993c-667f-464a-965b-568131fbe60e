from flask import Blueprint, request, jsonify, make_response
import os
import time
import logging
import traceback
from functools import wraps
from api.rate_limit import get_auth_limits, get_auth_key_function
import requests
from api.config import get_supabase_config
from datetime import datetime, timezone, timedelta
import jwt
from api.payment import supabase_client, service_supabase_client
from api.credits_cache import cache_user_credits  # 添加积分缓存导入

# 设置日志记录
logger = logging.getLogger(__name__)

auth_bp = Blueprint('auth', __name__)

def get_user_from_token(auth_token):
    """
    从JWT令牌解析用户ID（简化版本）
    
    Args:
        auth_token: JWT认证令牌
        
    Returns:
        dict or None: 用户信息，包含id字段；如果失败返回None
    """
    try:
        if not auth_token:
            return None
        
        # 简单从JWT payload中解析用户ID，不进行验证（因为前端已经验证过了）
        import base64
        import json
        
        # JWT格式：header.payload.signature
        parts = auth_token.split('.')
        if len(parts) != 3:
            return None
        
        # 解码payload部分
        payload = parts[1]
        # 添加必要的padding
        payload += '=' * (4 - len(payload) % 4)
        
        decoded_payload = base64.urlsafe_b64decode(payload)
        payload_data = json.loads(decoded_payload)
        
        # 从payload中获取用户ID
        user_id = payload_data.get('sub')  # 'sub' 是JWT标准的用户ID字段
        
        if user_id:
            return {'id': user_id}
        else:
            return None
            
    except Exception as e:
        logger.error(f"从令牌解析用户ID失败: {str(e)}")
        return None

# 安全配置
COOKIE_NAME = 'session_token'
COOKIE_MAX_AGE = 3600  # 1小时
CSRF_HEADER = 'X-CSRF-Token'

# 验证CSRF令牌
def verify_csrf(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 从请求头获取CSRF令牌
        csrf_token = request.headers.get(CSRF_HEADER)
        if not csrf_token:
            return jsonify({'success': False, 'message': 'CSRF令牌缺失'}), 403
        
        # 这里可以添加更复杂的验证逻辑，如从数据库验证令牌
        # 目前简单检查令牌是否存在
        
        return f(*args, **kwargs)
    return decorated_function

# 获取认证限制配置
auth_limits = get_auth_limits()

# 设置会话（登录时调用）
@auth_bp.route('/auth/set-session', methods=['POST'])
def set_session():
    try:
        data = request.json
        token = data.get('token')
        user_id = data.get('userId')
        expires = data.get('expires')
        
        if not token or not user_id or not expires:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
        
        # 创建响应
        resp = make_response(jsonify({'success': True}))
        
        # 设置HttpOnly cookie
        resp.set_cookie(
            COOKIE_NAME,
            token,
            max_age=COOKIE_MAX_AGE,
            httponly=True,
            secure=request.is_secure,  # 在生产环境中应设为True
            samesite='Strict'
        )
        
        # 新增：谷歌登录时强制从数据库查询积分
        try:
            from api.config import FORCE_CREDITS_REFRESH_ON_LOGIN
            if FORCE_CREDITS_REFRESH_ON_LOGIN:
                from api.credits import force_refresh_user_credits_from_db
                logger.info(f"🔄 [登录积分同步] 用户{user_id}登录，强制从数据库刷新积分")
                force_refresh_user_credits_from_db(user_id)
        except Exception as credits_error:
            # 积分刷新失败不影响登录流程
            logger.warning(f"⚠️ [登录积分同步] 刷新用户{user_id}积分失败: {credits_error}")
        
        return resp
    except Exception as e:
        logger.exception(f"设置会话时出错: {str(e)}")
        return jsonify({'success': False, 'message': '设置会话时发生错误'}), 500

# 清除会话（登出时调用）
@auth_bp.route('/auth/clear-session', methods=['POST'])
def clear_session():
    try:
        # 创建响应
        resp = make_response(jsonify({'success': True}))
        
        # 清除cookie
        resp.delete_cookie(COOKIE_NAME)
        
        return resp
    except Exception as e:
        logger.exception(f"清除会话时出错: {str(e)}")
        return jsonify({'success': False, 'message': '清除会话时发生错误'}), 500

# 获取令牌（需要进行API调用时获取）
@auth_bp.route('/auth/get-token', methods=['GET'])
def get_token():
    try:
        # 从cookie获取令牌
        token = request.cookies.get(COOKIE_NAME)
        
        if not token:
            return jsonify({'success': False, 'message': '未找到会话令牌'}), 401
        
        return jsonify({'success': True, 'token': token})
    except Exception as e:
        logger.exception(f"获取令牌时出错: {str(e)}")
        return jsonify({'success': False, 'message': '获取令牌时发生错误'}), 500

# 刷新会话（定期刷新时调用）
@auth_bp.route('/auth/refresh-session', methods=['POST'])
def refresh_session():
    try:
        data = request.json
        token = data.get('token')
        
        if not token:
            return jsonify({'success': False, 'message': '缺少令牌参数'}), 400
        
        # 从cookie获取当前令牌
        current_token = request.cookies.get(COOKIE_NAME)
        
        if not current_token:
            return jsonify({'success': False, 'message': '未找到会话令牌'}), 401
        
        if current_token != token:
            return jsonify({'success': False, 'message': '令牌不匹配'}), 403
        
        # 创建响应
        resp = make_response(jsonify({'success': True}))
        
        # 刷新cookie过期时间
        resp.set_cookie(
            COOKIE_NAME,
            token,
            max_age=COOKIE_MAX_AGE,
            httponly=True,
            secure=request.is_secure,
            samesite='Strict'
        )
        
        return resp
    except Exception as e:
        logger.exception(f"刷新会话时出错: {str(e)}")
        return jsonify({'success': False, 'message': '刷新会话时发生错误'}), 500

# 验证码配置获取（用于前端检查验证码是否启用）
@auth_bp.route('/auth/captcha-config', methods=['GET'])
def get_captcha_config():
    try:
        # 从环境变量获取Cloudflare Turnstile配置
        import os
        cloudflare_enabled = os.getenv('CLOUDFLARE_TURNSTILE_ENABLED', 'true').lower() == 'true'
        
        # 检测当前环境并获取相应密钥
        is_production = not any(host in os.environ.get('APP_URL', '') for host in ['localhost', '127.0.0.1'])
        if is_production:
            cloudflare_site_key = os.getenv('CLOUDFLARE_PROD_SITE_KEY', '0x4AAAAAABkTvsQTpOrFmwv2')
        else:
            cloudflare_site_key = os.getenv('CLOUDFLARE_DEV_SITE_KEY', '0x4AAAAAABkTvsQTpOrFmwv2')
        
        captcha_config = {
            'enabled': cloudflare_enabled,  # 根据环境变量控制是否启用
            'type': 'turnstile',  # 使用Cloudflare Turnstile验证码
            'siteKey': cloudflare_site_key if cloudflare_enabled else '',  # 只有启用时才返回密钥
            'environment': 'production' if is_production else 'development'
        }
        
        return jsonify({'success': True, 'config': captcha_config})
    except Exception as e:
        logger.exception(f"获取验证码配置时出错: {str(e)}")
        return jsonify({'success': False, 'message': '获取验证码配置时发生错误'}), 500

# 密码重置端点（处理重置密码请求）
@auth_bp.route('/auth/reset-password', methods=['POST'])
def reset_password():
    try:
        data = request.json
        token = data.get('token')
        password = data.get('password')
        
        if not token or not password:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
        
        # 这里我们只是传递令牌，实际的密码重置操作将由前端使用Supabase客户端完成
        logger.info(f"收到密码重置请求，将由前端处理")
        
        # 返回成功响应，让前端继续处理
        return jsonify({
            'success': True,
            'message': '密码重置请求已处理'
        })
    except Exception as e:
        logger.error(f"密码重置失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

# 验证令牌有效性端点
@auth_bp.route('/auth/verify', methods=['GET'])
def verify_token():
    try:
        # 从请求头获取令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'message': '未提供有效的认证令牌'}), 401
        
        token = auth_header.split(' ')[1]
        # 提取用户ID供前端一致性检查
        parsed_user = get_user_from_token(token)
        user_id = parsed_user.get('id') if parsed_user else None
        
        # 检查令牌格式是否为有效的JWT
        if len(token.split('.')) != 3:  # 简单检查是否为JWT格式（header.payload.signature）
            return jsonify({'success': False, 'message': '令牌格式无效', 'error_code': 'invalid_format'}), 401
        
        # 添加更严格的JWT验证：尝试调用supabase API验证令牌
        supabase_config = get_supabase_config()
        supabase_url = supabase_config.get('url')
        supabase_key = supabase_config.get('key')
        
        if not supabase_url or not supabase_key:
            # 如果没有配置Supabase，直接返回成功，避免阻断流程
            logger.warning("Supabase配置缺失，跳过严格验证")
            return jsonify({'success': True, 'message': '令牌格式有效（未严格验证）', 'user_id': user_id})
        
        # 调用Supabase API验证令牌
        try:
            verify_url = f"{supabase_url}/auth/v1/user"
            headers = {
                'Authorization': f'Bearer {token}',
                'apikey': supabase_key
            }
            
            # 只在debug模式下记录HTTP请求详情
            logger.debug(f"验证JWT令牌: GET {verify_url}")
            
            resp = requests.get(verify_url, headers=headers, timeout=5)
            
            # 只在debug模式下记录HTTP响应详情
            logger.debug(f'JWT验证响应: {resp.status_code} {resp.reason}')
            
            if resp.status_code == 200:
                # 令牌有效
                return jsonify({'success': True, 'message': '令牌有效', 'user_id': user_id})
            elif resp.status_code == 401 or resp.status_code == 403:
                # 令牌无效或已过期
                error_message = '令牌已过期或无效'
                try:
                    resp_data = resp.json()
                    if 'error' in resp_data and 'message' in resp_data:
                        error_message = f"{resp_data.get('error')}: {resp_data.get('message')}"
                        # 只在出现错误时记录详细信息
                        logger.error(f"验证JWT出错: {error_message}")
                except Exception:
                    logger.error(f"验证JWT时无法解析响应: {resp.text}")
                
                return jsonify({
                    'success': False, 
                    'message': '令牌已过期或无效', 
                    'error_code': 'token_expired',
                    'details': error_message
                }), 401
            else:
                # 其他错误
                logger.error(f"验证JWT时服务器返回意外状态码: {resp.status_code}")
                return jsonify({
                    'success': False,
                    'message': '验证令牌时发生服务器错误',
                    'error_code': 'server_error'
                }), 500
                
        except requests.exceptions.RequestException as e:
            # 请求异常（超时、连接错误等）
            logger.error(f"调用Supabase验证API时出错: {str(e)}")
            # 由于网络问题，我们不确定令牌是否有效，但为了避免阻断用户操作，返回成功
            return jsonify({'success': True, 'message': '令牌验证超时，临时允许操作'})
            
        except Exception as e:
            logger.error(f"验证JWT时发生未知错误: {str(e)}")
            # 为安全起见，将未知错误视为失败
            return jsonify({
                'success': False,
                'message': '验证令牌时发生错误',
                'error_code': 'unknown_error',
                'details': str(e)
            }), 500
            
    except Exception as e:
        logger.error(f"验证令牌时出错: {str(e)}")
        return jsonify({'success': False, 'message': '验证令牌时发生错误', 'error_code': 'system_error'}), 500

# 刷新令牌端点 - 保留第二个实现，移除第一个实现
@auth_bp.route('/auth/refresh', methods=['POST'])
def refresh_token():
    try:
        data = request.json
        refresh_token = data.get('refresh_token')
        
        if not refresh_token:
            return jsonify({'success': False, 'message': '缺少刷新令牌'}), 400
        
        # 调用Supabase实际刷新令牌
        supabase_config = get_supabase_config()
        supabase_url = supabase_config.get('url')
        supabase_key = supabase_config.get('key')
        
        if not supabase_url or not supabase_key:
            logger.error("刷新令牌失败：Supabase配置缺失")
            return jsonify({
                'success': False, 
                'message': '服务器配置错误，请联系管理员',
                'use_client_refresh': True
            }), 500
        
        # 调用Supabase Auth API刷新令牌
        try:
            refresh_url = f"{supabase_url}/auth/v1/token?grant_type=refresh_token"
            headers = {
                'apikey': supabase_key,
                'Content-Type': 'application/json'
            }
            payload = {
                'refresh_token': refresh_token
            }
            
            # 记录HTTP请求（不记录敏感信息）
            logger.info(f"HTTP Request: POST {refresh_url} (refresh token)")
            
            resp = requests.post(refresh_url, json=payload, headers=headers, timeout=5)
            
            # 记录响应状态（不记录敏感内容）
            logger.info(f'HTTP Response: POST {refresh_url} "{resp.status_code} {resp.reason}"')
            
            if resp.status_code == 200:
                # 解析响应获取新的令牌
                resp_data = resp.json()
                new_access_token = resp_data.get('access_token')
                new_refresh_token = resp_data.get('refresh_token')
                
                if not new_access_token:
                    logger.error("Supabase刷新令牌成功但未返回新的访问令牌")
                    return jsonify({
                        'success': False,
                        'message': '令牌刷新失败，请重新登录',
                        'use_client_refresh': True
                    }), 500
                
                # 成功刷新，返回新令牌
                return jsonify({
                    'success': True,
                    'access_token': new_access_token,
                    'refresh_token': new_refresh_token
                })
            else:
                # 刷新失败
                error_message = '刷新令牌失败'
                try:
                    resp_data = resp.json()
                    if 'error' in resp_data and 'message' in resp_data:
                        error_message = f"{resp_data.get('error')}: {resp_data.get('message')}"
                except Exception:
                    pass
                
                logger.error(f"刷新令牌失败: {error_message}")
                
                # 对前端指示应使用客户端刷新或重新登录
                return jsonify({
                    'success': False,
                    'message': error_message,
                    'use_client_refresh': True
                }), 401
                
        except requests.exceptions.RequestException as e:
            # 网络错误或超时
            logger.error(f"调用Supabase刷新令牌API时出错: {str(e)}")
            return jsonify({
                'success': False,
                'message': '刷新令牌请求失败，网络错误',
                'use_client_refresh': True,
                'details': str(e)
            }), 500
            
    except Exception as e:
        logger.error(f"刷新令牌时出错: {str(e)}")
        return jsonify({
            'success': False, 
            'message': '刷新令牌时发生错误',
            'use_client_refresh': True,
            'details': str(e)
        }), 500

# 检查用户是否存在端点
@auth_bp.route('/auth/check-user-exists', methods=['POST'])
def check_user_exists():
    try:
        data = request.json
        user_id = data.get('user_id')
        email = data.get('email')
        
        if not user_id and not email:
            return jsonify({'success': False, 'message': '缺少用户ID或邮箱'}), 400
        
        # 获取Supabase配置
        supabase_config = get_supabase_config()
        supabase_url = supabase_config.get('url')
        supabase_key = supabase_config.get('key')
        
        if not supabase_url or not supabase_key:
            logger.error("检查用户存在性失败：Supabase配置缺失")
            return jsonify({'success': False, 'message': '服务器配置错误'}), 500
        
        # 查询用户积分表，看是否已有该用户记录
        try:
            query_url = f"{supabase_url}/rest/v1/user_credits"
            headers = {
                'apikey': supabase_key,
                'Authorization': f'Bearer {supabase_key}',
                'Content-Type': 'application/json'
            }
            
            # 构建查询参数
            if user_id:
                params = {'select': 'user_id', 'user_id': f'eq.{user_id}'}
            else:
                params = {'select': 'email', 'email': f'eq.{email}'}
            
            logger.info(f"HTTP Request: GET {query_url} (check user exists)")
            
            resp = requests.get(query_url, headers=headers, params=params, timeout=5)
            
            logger.info(f'HTTP Response: GET {query_url} "{resp.status_code} {resp.reason}"')
            
            if resp.status_code == 200:
                users = resp.json()
                exists = len(users) > 0
                
                return jsonify({
                    'success': True,
                    'exists': exists,
                    'user_count': len(users)
                })
            else:
                logger.error(f"查询用户存在性失败: HTTP {resp.status_code}")
                return jsonify({
                    'success': False,
                    'message': '查询用户信息失败',
                    'exists': False  # 默认视为不存在，允许继续
                })
                
        except requests.exceptions.RequestException as e:
            logger.error(f"查询用户存在性请求失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': '查询请求失败',
                'exists': False  # 默认视为不存在，允许继续
            })
            
    except Exception as e:
        logger.error(f"检查用户存在性时出错: {str(e)}")
        return jsonify({
            'success': False, 
            'message': '检查用户存在性时发生错误',
            'exists': False  # 默认视为不存在，允许继续
        })

@auth_bp.route('/api/auth/preload-credits', methods=['POST'])
def preload_user_credits():
    """
    登录后预加载用户积分
    直接查询数据库，避免API调用开销
    """
    try:
        data = request.json
        user_id = data.get('user_id')
        
        if not user_id:
            return jsonify({
                "success": False,
                "error": "缺少用户ID"
            }), 400
        
        logger.info(f"[积分预加载] 开始预加载用户积分: {user_id}")
        
        # 直接查询数据库获取积分
        if not service_supabase_client:
            logger.error("[积分预加载] 服务角色客户端未初始化")
            return jsonify({
                "success": False,
                "error": "服务暂时不可用"
            }), 500
            
        credits_response = service_supabase_client.table('user_credits').select('credits', 'updated_at').eq('user_id', user_id).execute()
        
        if not credits_response.data:
            logger.info(f"[积分预加载] 用户 {user_id} 没有积分记录，等待数据库触发器自动处理")
            return jsonify({
                "success": True,
                "credits": 0,
                "message": "积分记录正在初始化中",
                "cached": False
            })
        
        # 用户有积分记录
        credits = credits_response.data[0]['credits']
        updated_at = credits_response.data[0]['updated_at']
        
        # 立即缓存到后端
        cache_user_credits(user_id, credits)
        logger.info(f"[积分预加载] 用户积分查询并缓存成功: {credits}")
        
        return jsonify({
            "success": True,
            "credits": credits,
            "updated_at": updated_at,
            "cached": True
        })
        
    except Exception as e:
        logger.error(f"[积分预加载] 预加载用户积分失败: {str(e)}")
        logger.error(f"[积分预加载] 异常详情: {traceback.format_exc()}")
        return jsonify({
            "success": False,
            "error": "预加载积分失败"
        }), 500

def register_routes(app):
    """注册认证相关路由"""
    app.register_blueprint(auth_bp, url_prefix='/api')
    logger.info(f"为认证路由添加速率限制: 登录={auth_limits.get('login', {}).get('ip', '5 per minute')}, 密码重置={auth_limits.get('password_reset', {}).get('ip', '3 per 10 minutes')}")