-- ========================================
-- 补充缺失的用户积分触发器
-- 确保新用户自动获得30积分和积分批次记录
-- ========================================

-- ========================================
-- 1. 创建新用户自动赠送积分的函数（如果不存在）
-- ========================================
CREATE OR REPLACE FUNCTION public.handle_verified_user_with_batch()
RETURNS TRIGGER AS $$
DECLARE
    v_batch_id UUID;
BEGIN
    -- 只为已验证邮箱的用户创建积分记录
    IF NEW.email_confirmed_at IS NOT NULL THEN
        -- 检查是否已经有积分记录
        IF NOT EXISTS (SELECT 1 FROM public.user_credits WHERE user_id = NEW.id) THEN
            -- 创建积分批次
            SELECT public.add_credits_with_expiry(
                NEW.id, 
                30, 
                'signup_bonus', 
                30, 
                NULL, 
                '新用户注册赠送积分'
            ) INTO v_batch_id;
            
            RAISE NOTICE '为新用户 % 创建注册赠送积分批次: %', NEW.id, v_batch_id;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 2. 删除已存在的触发器（防止重复）
-- ========================================
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS on_auth_user_updated ON auth.users;

-- ========================================
-- 3. 创建触发器 - 邮箱验证时赠送积分
-- ========================================
CREATE TRIGGER on_auth_user_updated
    AFTER UPDATE ON auth.users
    FOR EACH ROW
    WHEN (OLD.email_confirmed_at IS NULL AND NEW.email_confirmed_at IS NOT NULL)
    EXECUTE FUNCTION public.handle_verified_user_with_batch();

-- ========================================
-- 4. 创建触发器 - 新用户注册且已验证时赠送积分
-- ========================================
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    WHEN (NEW.email_confirmed_at IS NOT NULL)
    EXECUTE FUNCTION public.handle_verified_user_with_batch();

-- ========================================
-- 5. 为现有已验证用户补充积分批次记录（如果没有）
-- ========================================
DO $$
DECLARE
    user_record RECORD;
    v_batch_id UUID;
BEGIN
    RAISE NOTICE '开始为现有用户补充积分批次记录...';
    
    -- 为有积分但没有积分批次的用户创建批次记录
    FOR user_record IN 
        SELECT uc.user_id, uc.credits
        FROM public.user_credits uc
        WHERE uc.credits > 0
          AND NOT EXISTS (
              SELECT 1 FROM public.credit_batches cb 
              WHERE cb.user_id = uc.user_id 
                AND cb.remaining_credits > 0
          )
    LOOP
        -- 为这些用户创建积分批次记录
        INSERT INTO public.credit_batches (
            user_id, credits_amount, remaining_credits, source_type,
            created_at, expires_at, description
        ) VALUES (
            user_record.user_id,
            user_record.credits,
            user_record.credits,
            'signup_bonus',
            NOW() - INTERVAL '1 day', -- 设置为昨天创建，保持逻辑一致性
            NOW() + INTERVAL '29 days', -- 30天有效期减去1天
            '新用户注册赠送积分（数据修复）'
        ) RETURNING id INTO v_batch_id;
        
        RAISE NOTICE '为用户 % 补充积分批次记录，积分: %，批次ID: %', 
                     user_record.user_id, user_record.credits, v_batch_id;
    END LOOP;
    
    RAISE NOTICE '积分批次记录补充完成';
END;
$$;

-- ========================================
-- 6. 验证触发器创建
-- ========================================
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement
FROM information_schema.triggers 
WHERE event_object_schema IN ('public', 'auth')
    AND event_object_table IN ('users', 'user_credits', 'payment_orders', 'user_profiles')
ORDER BY event_object_table, trigger_name;

-- 显示成功消息
SELECT 'User credit triggers with batch support created successfully!' as status; 