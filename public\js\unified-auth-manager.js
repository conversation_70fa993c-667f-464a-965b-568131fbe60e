/**
 * 统一认证管理器 - 解决登录后自动退出问题
 * 
 * 核心问题：
 * 1. 多个认证管理器同时运行（auth-state-manager.js、oauth-manager.js、token-refresh.js）
 * 2. 存在竞态条件，导致在令牌刷新成功后仍被误判为未登录
 * 3. 复杂的重试和延迟检查逻辑造成状态不一致
 * 
 * 解决方案：
 * 1. 单一认证状态管理入口
 * 2. 完全依赖Supabase的onAuthStateChange事件
 * 3. 移除主动检查和重试机制
 * 4. 基于事件驱动的状态更新
 */

class UnifiedAuthManager {
    constructor() {
        this.isInitialized = false;
        this.supabaseClient = null;
        this.authStateSubscription = null;
        
        // 认证状态
        this.authState = {
            isAuthenticated: false,
            user: null,
            session: null,
            lastUpdate: null
        };
        
        // 控制标志
        this.isUserInitiatedLogin = false; // 标识是否为用户主动登录
        
        // 事件监听器列表
        this.listeners = [];
        
        // 只在开发环境输出初始化日志
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('[Unified Auth] 统一认证管理器初始化');
        }
    }

    /**
     * 初始化统一认证管理器
     */
    async init() {
        if (this.isInitialized) {
            console.debug('[Unified Auth] 已初始化，跳过重复初始化');
            return;
        }

        try {
            // 快速等待Supabase客户端准备完成
            await this.waitForSupabaseClientFast();
            
            // 设置认证状态监听器
            this.setupAuthStateListener();
            
            this.isInitialized = true;
            console.debug('[Unified Auth] 初始化完成');
            
            // 异步启动定期清理检查，不阻塞初始化
            setTimeout(() => this.startPeriodicCleanup(), 1000);
            
        } catch (error) {
            console.error('[Unified Auth] 初始化失败:', error);
            throw error;
        }
    }

    /**
     * 快速等待Supabase客户端准备完成
     */
    async waitForSupabaseClientFast() {
        // 缩短等待时间，加快初始化
        let attempts = 0;
        const maxAttempts = 20; // 从50减少到20
        
        while (!window.supabaseClient && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 50)); // 从100ms减少到50ms
            attempts++;
        }
        
        if (!window.supabaseClient) {
            throw new Error('Supabase客户端未能在预期时间内初始化');
        }
        
        this.supabaseClient = window.supabaseClient;
        console.debug('[Unified Auth] Supabase客户端已快速准备完成');
    }

    /**
     * 启动定期清理检查
     */
    startPeriodicCleanup() {
        // 每30秒检查一次localStorage与内部状态的一致性
        setInterval(() => {
            if (this.isInitialized) {
                this.cleanupInvalidTokens();
            }
        }, 30000);
        
        console.debug('[Unified Auth] 已启动定期清理检查');
    }

    /**
     * 设置认证状态监听器 - 核心逻辑
     */
    setupAuthStateListener() {        // 监听Supabase认证状态变化
        this.authStateSubscription = this.supabaseClient.auth.onAuthStateChange(
            (event, session) => {
                console.debug('[Unified Auth] 认证状态变化:', event, session ? '有session' : '无session');
                console.debug('[Unified Auth] 是否为用户主动登录:', this.isUserInitiatedLogin);
                
                // 特殊处理各种认证事件
                if (event === 'SIGNED_IN' && session) {
                    if (this.isUserInitiatedLogin) {
                        console.debug('[Unified Auth] 用户主动登录成功，强制同步localStorage为后端session');
                        this.forceUpdateFromBackend(session);
                        // 重置标志
                        this.isUserInitiatedLogin = false;
                    } else {
                        console.debug('[Unified Auth] 自动恢复session成功，但阻止自动跳转');
                        // 对于自动恢复的session，我们仍需要验证其有效性
                        this.validateAndUpdateSession(session);
                    }
                } else if (event === 'TOKEN_REFRESHED' && session) {
                    console.debug('[Unified Auth] 令牌已刷新，更新localStorage');
                    this.forceUpdateFromBackend(session);
                } else if (event === 'SIGNED_OUT' || !session) {
                    console.debug('[Unified Auth] 用户登出或session失效，清理所有数据');
                    this.clearLocalAuthData();
                    this.updateAuthState(null);
                } else {
                    // 其他情况按正常流程处理
                    this.updateAuthState(session);
                }
                
                // 通知所有监听器，并传递是否为用户主动登录的信息
                this.notifyListeners(event, session, this.isUserInitiatedLogin);
                
                // 触发积分系统相关事件
                this.handleCreditsIntegration(event, session);
            }
        );
        
        console.debug('[Unified Auth] 认证状态监听器已设置');
    }

    /**
     * 更新认证状态
     */
    updateAuthState(session) {
        const isAuthenticated = !!(session && session.access_token);
        const previousState = this.authState.isAuthenticated;
        
        this.authState = {
            isAuthenticated,
            user: session ? session.user : null,
            session: session,
            lastUpdate: new Date().toISOString()
        };
          // 只在状态实际发生变化时输出详细日志
        if (previousState !== isAuthenticated || !this.authState.lastUpdate) {
            console.log('[Unified Auth] 认证状态已更新:', {
                isAuthenticated: this.authState.isAuthenticated,
                hasUser: !!this.authState.user,
                timestamp: this.authState.lastUpdate
            });
            
            // 只在状态变化时同步localStorage
            this.syncLocalStorageState(isAuthenticated, session);
        }
    }    /**
     * 强制从后端session更新状态 - 用于登录成功时
     * 这确保登录后localStorage完全以后端返回的数据为准
     */
    forceUpdateFromBackend(session) {
        console.debug('[Unified Auth] 强制从后端更新认证状态');
        
        // 立即清理所有localStorage中的认证数据
        this.clearLocalAuthData();
        
        // 等待一个微任务，确保清理完成
        setTimeout(() => {
            const isAuthenticated = !!(session && session.access_token);
            
            // 更新内部状态
            this.authState = {
                isAuthenticated,
                user: session ? session.user : null,
                session: session,
                lastUpdate: new Date().toISOString()
            };
            
            // 重新写入localStorage，只使用后端返回的数据
            if (isAuthenticated && session) {
                localStorage.setItem('authToken', session.access_token);
                localStorage.setItem('refreshToken', session.refresh_token);
                localStorage.setItem('userData', JSON.stringify(session.user));
                localStorage.setItem('isAuthenticated', 'true');
                localStorage.setItem('userId', session.user.id);
                
                console.log('[Unified Auth] 登录成功，已强制更新localStorage为后端数据');
                
                // 新增：谷歌登录时强制刷新积分
                if (window.CreditsManager && typeof window.CreditsManager.forceRefreshCreditsOnLogin === 'function') {
                    console.log('🔄 [统一认证] 开始谷歌登录后的积分同步流程...');
                    setTimeout(async () => {
                        try {
                            console.log('🔄 [统一认证] 调用积分强制刷新...');
                            await window.CreditsManager.forceRefreshCreditsOnLogin();
                            console.log('✅ [统一认证] 谷歌登录积分同步完成');
                        } catch (error) {
                            console.warn('⚠️ [统一认证] 谷歌登录后处理失败:', error);
                        }
                    }, 1000); // 增加延迟到1000ms确保所有组件完全初始化
                } else {
                    console.warn('⚠️ [统一认证] CreditsManager或forceRefreshCreditsOnLogin方法不可用');
                }
            }
        }, 10);
    }

    /**
     * 验证并更新session - 用于自动恢复的session
     * 这里我们需要验证session是否真的有效
     */
    async validateAndUpdateSession(session) {
        console.debug('[Unified Auth] 验证自动恢复的session有效性');
        
        try {
            // 简单验证：检查session结构是否完整
            if (!session || !session.access_token || !session.user || !session.user.id) {
                console.debug('[Unified Auth] Session结构不完整，清理数据');
                this.clearLocalAuthData();
                this.updateAuthState(null);
                return;
            }
            
            // 检查令牌是否即将过期
            const tokenPayload = JSON.parse(atob(session.access_token.split('.')[1]));
            const currentTime = Math.floor(Date.now() / 1000);
            
            if (tokenPayload.exp && tokenPayload.exp < (currentTime + 300)) {
                console.debug('[Unified Auth] 自动恢复的令牌即将过期，清理数据');
                this.clearLocalAuthData();
                this.updateAuthState(null);
                return;
            }
            
            // Session看起来有效，但不要自动跳转到main.html
            console.debug('[Unified Auth] 自动恢复的session有效，但不允许自动跳转');
            this.updateAuthState(session);
            
        } catch (error) {
            console.debug('[Unified Auth] 验证自动恢复session失败:', error.message);
            this.clearLocalAuthData();
            this.updateAuthState(null);
        }
    }

    /**
     * 标记用户主动发起登录 - 供登录按钮调用
     */
    markUserInitiatedLogin() {
        console.debug('[Unified Auth] 标记用户主动发起登录');
        this.isUserInitiatedLogin = true;
    }

    /**
     * 同步localStorage状态
     */
    syncLocalStorageState(isAuthenticated, session) {
        if (isAuthenticated && session) {
            // 在设置新的认证数据之前，先清理所有旧数据，确保不会有冲突
            this.clearLocalAuthData();
            
            // 设置新的认证数据
            localStorage.setItem('authToken', session.access_token);
            localStorage.setItem('refreshToken', session.refresh_token);
            localStorage.setItem('userData', JSON.stringify(session.user));
            
            // 兼容其他模块的认证状态检查
            localStorage.setItem('isAuthenticated', 'true');
            localStorage.setItem('userId', session.user.id);
            
            console.debug('[Unified Auth] 已更新localStorage认证数据');
        } else {
            // 清理所有相关的localStorage数据
            this.clearLocalAuthData();
        }
    }

    /**
     * 处理积分系统集成
     */    handleCreditsIntegration(event, session) {
        if (event === 'SIGNED_IN' && session) {
            // 防重复处理机制
            if (this._creditsIntegrationProcessed) {
                console.debug('[Unified Auth] 积分系统集成已处理，跳过重复调用');
                return;
            }
            this._creditsIntegrationProcessed = true;
            
            // 用户登录时，初始化积分系统
            console.debug('[Unified Auth] 用户登录，准备初始化积分系统');
            
            // 通知积分系统用户已登录
            if (window.CreditsManager) {
                // 异步并行获取积分，不阻塞登录
                setTimeout(async () => {
                    try {
                        // 优先使用强制刷新方法
                        if (typeof window.CreditsManager.forceRefreshCreditsOnLogin === 'function') {
                            console.log('[Unified Auth] 登录后强制刷新积分');
                            await window.CreditsManager.forceRefreshCreditsOnLogin();
                        } else if (typeof window.CreditsManager.handleUserLogin === 'function') {
                            window.CreditsManager.handleUserLogin(session.user);
                        } else {
                            window.CreditsManager.init();
                        }
                    } catch (error) {
                        console.error('[Unified Auth] 积分系统初始化失败:', error);
                    }
                }, 100);
            }
            
            // 3秒后重置标志，允许下次登录时重新处理
            setTimeout(() => {
                this._creditsIntegrationProcessed = false;
            }, 3000);
            
        } else if (event === 'SIGNED_OUT') {
            // 重置积分集成标志
            this._creditsIntegrationProcessed = false;
            
            // 用户登出时，清理积分缓存
            console.debug('[Unified Auth] 用户登出，清理积分数据');
            
            if (window.CreditsManager && typeof window.CreditsManager.handleUserLogout === 'function') {
                window.CreditsManager.handleUserLogout();
            }
        }
    }

    /**
     * 添加认证状态监听器
     */
    addListener(callback) {
        this.listeners.push(callback);
    }

    /**
     * 移除认证状态监听器
     */
    removeListener(callback) {
        const index = this.listeners.indexOf(callback);
        if (index > -1) {
            this.listeners.splice(index, 1);
        }
    }    /**
     * 通知所有监听器
     */
    notifyListeners(event, session, isUserInitiated = false) {
        this.listeners.forEach(callback => {
            try {
                callback(event, session, this.authState, isUserInitiated);
            } catch (error) {
                console.error('[Unified Auth] 监听器执行错误:', error);
            }
        });
    }

    /**
     * 获取当前认证状态
     */
    getAuthState() {
        return { ...this.authState };
    }

    /**
     * 检查是否已认证
     */
    isAuthenticated() {
        return this.authState.isAuthenticated;
    }

    /**
     * 获取当前用户
     */
    getCurrentUser() {
        return this.authState.user;
    }    /**
     * 获取当前session
     */
    getCurrentSession() {
        return this.authState.session;
    }

    /**
     * 获取当前有效的访问令牌
     * 其他模块应该使用此方法而不是直接读取localStorage
     */
    getAccessToken() {
        if (this.authState.session && this.authState.session.access_token) {
            return this.authState.session.access_token;
        }
        return null;
    }

    /**
     * 获取当前有效的刷新令牌
     */
    getRefreshToken() {
        if (this.authState.session && this.authState.session.refresh_token) {
            return this.authState.session.refresh_token;
        }
        return null;
    }

    /**
     * 检查并清理失效的localStorage数据
     * 这个方法会被定期调用，确保localStorage不会长期保留失效数据
     */
    cleanupInvalidTokens() {
        const authToken = localStorage.getItem('authToken');
        const isAuthenticated = localStorage.getItem('isAuthenticated');
        
        // 如果localStorage显示已认证，但我们的内部状态显示未认证，清理localStorage
        if (isAuthenticated === 'true' && !this.authState.isAuthenticated) {
            console.debug('[Unified Auth] 检测到localStorage与内部状态不一致，清理localStorage');
            this.clearLocalAuthData();
            return;
        }
        
        // 如果有令牌但没有有效session，清理localStorage
        if (authToken && authToken !== 'null' && !this.authState.session) {
            console.debug('[Unified Auth] 检测到localStorage有令牌但无有效session，清理localStorage');
            this.clearLocalAuthData();
            return;
        }
    }

    /**
     * 强制刷新认证状态（仅在必要时使用）
     */
    async refreshAuthState() {
        if (!this.supabaseClient) {
            console.warn('[Unified Auth] Supabase客户端不可用，无法刷新认证状态');
            return this.authState;
        }

        try {
            const { data: { session }, error } = await this.supabaseClient.auth.getSession();
            
            if (error) {
                console.error('[Unified Auth] 获取session失败:', error);
                this.updateAuthState(null);
            } else {
                this.updateAuthState(session);
            }
            
            return this.authState;
        } catch (error) {
            console.error('[Unified Auth] 刷新认证状态失败:', error);
            return this.authState;
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        if (this.authStateSubscription) {
            this.authStateSubscription.subscription.unsubscribe();
            this.authStateSubscription = null;
        }
          this.listeners = [];
        this.isInitialized = false;
        
        console.debug('[Unified Auth] 资源已清理');
    }    /**
     * 等待认证状态稳定
     * 用于页面初始化时确保认证状态已确定
     */
    async waitForAuthState(timeout = 5000) {        return new Promise(async (resolve) => {
            console.debug(`[Unified Auth] 开始等待认证状态，超时时间: ${timeout}ms`);
            
            // 检查是否为OAuth回调
            const isOAuthCallback = window.location.hash || 
                                  window.location.search.includes('code=') || 
                                  window.location.search.includes('access_token=');
            const oauthCompleted = sessionStorage.getItem('oauthCallbackCompleted') === 'true';
            
            console.debug('[Unified Auth] OAuth状态:', {
                isOAuthCallback,
                oauthCompleted,
                currentPath: window.location.pathname
            });
            
            // 检查localStorage中是否有认证数据 - 兼容多种检查方式
            const hasAuthToken = !!(localStorage.getItem('authToken') && localStorage.getItem('authToken') !== 'null');
            const hasUserData = !!(localStorage.getItem('userData') && localStorage.getItem('userData') !== 'null');
            const hasIsAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
            const hasUserId = !!(localStorage.getItem('userId') && localStorage.getItem('userId') !== 'null');
              const hasLocalAuth = (hasAuthToken && hasUserData) || (hasIsAuthenticated && hasUserId);
            
            console.debug('[Unified Auth] 本地认证数据检查:', {
                hasAuthToken,
                hasUserData, 
                hasIsAuthenticated,
                hasUserId,
                hasLocalAuth
            });
            
            // 如果是OAuth回调且已完成，优先使用localStorage数据
            if (isOAuthCallback && oauthCompleted && hasLocalAuth) {
                console.debug('[Unified Auth] OAuth回调已完成且有本地认证数据，立即使用');
                try {
                    const userData = JSON.parse(localStorage.getItem('userData'));
                    this.authState = {
                        isAuthenticated: true,
                        user: userData,
                        session: {
                            access_token: localStorage.getItem('authToken'),
                            user: userData
                        },
                        lastUpdate: new Date().toISOString()
                    };
                    resolve(this.authState);
                    return;
                } catch (e) {
                    console.debug('[Unified Auth] 解析localStorage数据失败，继续正常流程');
                }
            }
            
            // 如果已经有明确的认证状态且与本地数据一致，立即返回
            if (this.authState.lastUpdate && this.authState.isAuthenticated === hasLocalAuth) {
                console.debug('[Unified Auth] 使用已有认证状态');
                resolve(this.authState);
                return;
            }            // 为OAuth回调设置更长的超时时间
            const actualTimeout = isOAuthCallback ? Math.max(timeout, 6000) : Math.min(timeout, 3000);
            console.debug(`[Unified Auth] 实际等待时间: ${actualTimeout}ms`);
            
            // 设置超时
            const timeoutId = setTimeout(async () => {
                console.warn('[Unified Auth] 等待认证状态超时');
                
                // 超时时清理可能失效的本地数据（但OAuth回调时更宽松）
                if (!isOAuthCallback && hasLocalAuth && !this.authState.isAuthenticated) {
                    console.debug('[Unified Auth] 超时且无有效认证状态，清理本地数据');
                    this.clearLocalAuthData();
                }
                
                resolve(this.authState);
            }, actualTimeout);            // 如果是OAuth回调，监听认证数据保存事件
            if (isOAuthCallback) {
                const onAuthDataSaved = (event) => {
                    console.debug('[Unified Auth] 收到OAuth数据保存事件');
                    if (event.detail && event.detail.authToken && event.detail.userData) {
                        clearTimeout(timeoutId);
                        window.removeEventListener('auth-data-saved', onAuthDataSaved);
                        
                        this.authState = {
                            isAuthenticated: true,
                            user: event.detail.userData,
                            session: {
                                access_token: event.detail.authToken,
                                user: event.detail.userData
                            },
                            lastUpdate: event.detail.timestamp
                        };
                        
                        resolve(this.authState);
                    }
                };
                window.addEventListener('auth-data-saved', onAuthDataSaved);
                
                // 清理事件监听器
                setTimeout(() => {
                    window.removeEventListener('auth-data-saved', onAuthDataSaved);
                }, actualTimeout);
            }
            
            // 监听第一次状态更新
            const onStateUpdate = (event, session, authState) => {
                console.debug('[Unified Auth] 收到状态更新:', event, authState.isAuthenticated);
                
                // 接受任何明确的状态更新
                clearTimeout(timeoutId);
                this.removeListener(onStateUpdate);
                resolve(authState);
            };

            this.addListener(onStateUpdate);
        });
    }    /**
     * 尝试从localStorage手动恢复认证状态
     * 用作后备方案
     */    async tryRecoverFromLocalStorage() {
        console.debug('[Unified Auth] 尝试从localStorage恢复认证状态...');
        
        try {
            const authToken = localStorage.getItem('authToken');
            const refreshToken = localStorage.getItem('refreshToken');
            const userData = localStorage.getItem('userData');
            
            if (!authToken || !refreshToken || !userData || 
                authToken === 'null' || refreshToken === 'null' || userData === 'null') {
                console.debug('[Unified Auth] localStorage中无有效认证数据');
                this.clearLocalAuthData();
                return false;
            }
            
            // 尝试使用refreshToken恢复session
            const { data, error } = await this.supabaseClient.auth.setSession({
                access_token: authToken,
                refresh_token: refreshToken
            });
              if (error) {
                console.warn('[Unified Auth] 恢复session失败，令牌可能已失效:', error.message);
                
                // 清理失效的认证数据，让用户重新登录
                this.clearLocalAuthData();
                
                // 不要弹窗提示，让系统自然地引导用户重新登录
                console.debug('[Unified Auth] 已清理失效令牌，等待用户重新登录');
                
                return false;
            }
            
            console.debug('[Unified Auth] 成功从localStorage恢复session');
            
            // 使用强制更新确保数据一致性
            if (data.session) {
                this.forceUpdateFromBackend(data.session);
            }
            
            return true;
              } catch (error) {
            console.error('[Unified Auth] 恢复过程出错:', error);
            
            // 清理可能损坏的认证数据
            this.clearLocalAuthData();
            
            return false;
        }    }

    /**
     * 清理本地认证数据
     */
    clearLocalAuthData() {
        const keysToRemove = ['authToken', 'refreshToken', 'userData', 'isAuthenticated', 'userId'];
        keysToRemove.forEach(key => localStorage.removeItem(key));
        console.debug('[Unified Auth] 已清理本地认证数据');
    }

    /**
     * 等待认证管理器完成初始化并获取到首次AuthState
     * @param {number} timeout 最大等待时间（毫秒）
     */
    async waitForReady(timeout = 5000) {
        if (this.isInitialized && this.authState.lastUpdate) {
            return true;
        }
        return await this.waitForAuthState(timeout);
    }
}

// 创建全局实例
window.unifiedAuthManager = new UnifiedAuthManager();

// 在全局对象上暴露便捷方法，供其他模块使用
window.getAuthToken = () => {
    return window.unifiedAuthManager ? window.unifiedAuthManager.getAccessToken() : null;
};

window.getRefreshToken = () => {
    return window.unifiedAuthManager ? window.unifiedAuthManager.getRefreshToken() : null;
};

window.isUserAuthenticated = () => {
    return window.unifiedAuthManager ? window.unifiedAuthManager.isAuthenticated() : false;
};

window.getCurrentAuthUser = () => {
    return window.unifiedAuthManager ? window.unifiedAuthManager.getCurrentUser() : null;
};

// 供登录按钮调用，标记用户主动发起登录
window.markUserInitiatedLogin = () => {
    if (window.unifiedAuthManager) {
        window.unifiedAuthManager.markUserInitiatedLogin();
    }
};

// 自动初始化
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await window.unifiedAuthManager.init();
        console.debug('[Unified Auth] 自动初始化完成');
    } catch (error) {
        console.error('[Unified Auth] 自动初始化失败:', error);
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.unifiedAuthManager) {
        window.unifiedAuthManager.cleanup();
    }
});

// 只在开发环境输出脚本加载日志
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('[Unified Auth] 统一认证管理器脚本已加载');
}
