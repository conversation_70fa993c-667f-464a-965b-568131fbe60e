-- 完整的数据库初始化脚本
-- 用于在新的Supabase项目中创建所有必需的表和功能

-- ========================================
-- 1. 创建用户积分表
-- ========================================
CREATE TABLE IF NOT EXISTS public.user_credits (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    credits INTEGER NOT NULL DEFAULT 0,
    email VARCHAR(255) DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 启用行级安全
ALTER TABLE public.user_credits ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略 - 用户只能访问自己的积分记录
CREATE POLICY "Users can view own credits" ON public.user_credits
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own credits" ON public.user_credits
    FOR UPDATE USING (auth.uid() = user_id);

-- ========================================
-- 2. 创建积分操作记录表
-- ========================================
CREATE TABLE IF NOT EXISTS public.credit_operations (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    operation_type VARCHAR(50) NOT NULL,
    model VARCHAR(255),
    credits_amount INTEGER NOT NULL,
    operation_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    current_credits INTEGER,
    book_name VARCHAR(255),
    chapter_title VARCHAR(255)
);

-- 启用行级安全
ALTER TABLE public.credit_operations ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略 - 用户只能访问自己的操作记录
CREATE POLICY "Users can view own operations" ON public.credit_operations
    FOR SELECT USING (auth.uid() = user_id);

-- 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_credit_operations_user_id ON public.credit_operations(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_operations_time ON public.credit_operations(operation_time DESC);

-- ========================================
-- 3. 创建自动补充email的函数
-- ========================================
CREATE OR REPLACE FUNCTION update_user_credits_email()
RETURNS TRIGGER AS $$
BEGIN
    -- 如果email为空，尝试从auth.users表获取
    IF NEW.email IS NULL OR NEW.email = '' THEN
        -- 查询auth.users表获取真实email
        SELECT email INTO NEW.email 
        FROM auth.users 
        WHERE id = NEW.user_id;
        
        -- 如果仍然为空，使用临时邮箱格式
        IF NEW.email IS NULL OR NEW.email = '' THEN
            NEW.email := NEW.user_id || '@temp.oauth.user';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 4. 创建新用户自动赠送积分的函数
-- ========================================
CREATE OR REPLACE FUNCTION public.handle_verified_user()
RETURNS TRIGGER AS $$
BEGIN
    -- 只为已验证邮箱的用户创建积分记录
    -- NEW.email_confirmed_at 不为 NULL 表示邮箱已验证
    IF NEW.email_confirmed_at IS NOT NULL THEN
        -- 为已验证的用户创建积分记录，初始积分为30
        INSERT INTO public.user_credits (user_id, credits, created_at, updated_at)
        VALUES (NEW.id, 30, NOW(), NOW())
        ON CONFLICT (user_id)
        DO UPDATE SET credits = 30, updated_at = NOW();
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 5. 创建触发器
-- ========================================

-- 自动补充email的触发器
DROP TRIGGER IF EXISTS trigger_update_email ON public.user_credits;
CREATE TRIGGER trigger_update_email
    BEFORE INSERT OR UPDATE ON public.user_credits
    FOR EACH ROW
    EXECUTE FUNCTION update_user_credits_email();

-- 删除已存在的触发器（如果有）
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS on_auth_user_updated ON auth.users;

-- 创建触发器，在auth.users表中更新记录时触发（邮箱验证）
CREATE TRIGGER on_auth_user_updated
    AFTER UPDATE ON auth.users
    FOR EACH ROW
    WHEN (OLD.email_confirmed_at IS NULL AND NEW.email_confirmed_at IS NOT NULL)
    EXECUTE FUNCTION public.handle_verified_user();

-- 创建触发器，在auth.users表中插入新记录时触发（已验证的用户）
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    WHEN (NEW.email_confirmed_at IS NOT NULL)
    EXECUTE FUNCTION public.handle_verified_user();

-- ========================================
-- 6. 确保唯一约束
-- ========================================
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint
        WHERE conname = 'user_credits_user_id_key' AND conrelid = 'public.user_credits'::regclass
    ) THEN
        ALTER TABLE public.user_credits ADD CONSTRAINT user_credits_user_id_key UNIQUE (user_id);
    END IF;
END $$;

-- ========================================
-- 7. 为现有已验证用户添加积分记录
-- ========================================
INSERT INTO public.user_credits (user_id, credits, created_at, updated_at)
SELECT id, 30, NOW(), NOW()
FROM auth.users
WHERE
    email_confirmed_at IS NOT NULL AND
    NOT EXISTS (
        SELECT 1 FROM public.user_credits WHERE user_id = auth.users.id
    )
ON CONFLICT (user_id) DO NOTHING;

-- ========================================
-- 8. 验证设置
-- ========================================
-- 检查表是否创建成功
SELECT 
    'user_credits' as table_name,
    COUNT(*) as record_count
FROM public.user_credits
UNION ALL
SELECT 
    'credit_operations' as table_name,
    COUNT(*) as record_count
FROM public.credit_operations;

-- 检查触发器是否创建成功
SELECT 
    trigger_name, 
    event_manipulation, 
    event_object_table
FROM information_schema.triggers
WHERE event_object_table IN ('user_credits', 'users') 
    AND trigger_schema IN ('public', 'auth');

-- 脚本执行完成
SELECT 'Database setup completed successfully!' as status; 