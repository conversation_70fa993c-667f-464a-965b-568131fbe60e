"""
模型配置中心 - 集中管理所有模型信息和API调用
"""
import os
import logging
import importlib
from flask import Response

# 获取已配置的logger
logger = logging.getLogger(__name__)


# 积分兑换率 - 人民币到积分的转换率
CREDIT_COST_RATE = 0.0015

# 模型详细信息配置
MODEL_CONFIG = {
    # 智谱模型
    "zhipu_flash": {
        "id": "zhipu_glm_4_flash",
        "name": "GLM-4-Flash",  # 简化名称，不包含提供商前缀
        "internal_id": "zhipu_glm_4_flash",
        "api_model_name": "glm-4-flash",  # 添加这一行，指定API模型名称
        "env_var": "ZHIPU_API_KEY",  # 需要的环境变量
        "module": "zhipu_api",  # 模块名
        "function": "create_streaming_response",  # 调用函数名
        "max_input_tokens": 128000,  # 最大输入token限制
        "max_output_tokens": 8000,   # 最大输出token限制
        "pricing": {
            "input": 0.0 / 1_000_000,  # ￥0.1 per 1M tokens
            "output": 0.0 / 1_000_000  # ￥0.1 per 1M tokens
        },
        "features": ["summary", "mindmap", "knowledgegraph"],  # 支持的功能
        "is_default": True,  # 是否为默认模型
        "provider": "zhipu",  # 添加提供商标识
        "i18n_prefix": "main.models.zhipu_glm_4_flash"  # 国际化前缀
    },

    # 智谱模型
    "zhipu_glm_4_flashX": {
        "id": "zhipu_glm_4_flashX",
        "name": "GLM-4-FlashX",  # 简化名称，不包含提供商前缀
        "internal_id": "zhipu_glm_4_flashX",
        "api_model_name": "glm-4-flashx",  # 添加这一行，指定API模型名称
        "env_var": "ZHIPU_API_KEY",  # 需要的环境变量
        "module": "zhipu_api",  # 模块名
        "function": "create_streaming_response",  # 调用函数名
        "max_input_tokens": 128000,  # 最大输入token限制
        "max_output_tokens": 8000,   # 最大输出token限制
        "pricing": {
            "input": 0.1 / 1_000_000,  # ￥0.1 per 1M tokens
            "output": 0.1 / 1_000_000  # ￥0.1 per 1M tokens
        },
        "features": ["summary", "mindmap", "knowledgegraph"],  # 支持的功能
        "is_default": False,  # 是否为默认模型
        "provider": "zhipu",  # 添加提供商标识
        "i18n_prefix": "main.models.zhipu_glm_4_flashX"  # 国际化前缀
    },

    # 智谱模型
    "zhipu_glm_4_air": {
        "id": "zhipu_glm_4_air",
        "name": "GLM-4-Air",  # 简化名称，不包含提供商前缀
        "internal_id": "zhipu_glm_4_air",
        "api_model_name": "glm-4-air",  # 添加这一行，指定API模型名称
        "env_var": "ZHIPU_API_KEY",  # 需要的环境变量
        "module": "zhipu_api",  # 模块名
        "function": "create_streaming_response",  # 调用函数名
        "max_input_tokens": 128000,  # 最大输入token限制
        "max_output_tokens": 8000,   # 最大输出token限制
        "pricing": {
            "input": 0.5 / 1_000_000,  # ￥0.1 per 1M tokens
            "output": 0.5 / 1_000_000  # ￥0.1 per 1M tokens
        },
        "features": ["summary", "mindmap", "knowledgegraph"],  # 支持的功能
        "is_default": False,  # 是否为默认模型
        "provider": "zhipu",  # 添加提供商标识
        "i18n_prefix": "main.models.zhipu_glm_4_air"  # 国际化前缀
    },

    # 千问模型
    "qwen_turbo": {
        "id": "qwen_qwen_turbo",
        "name": "qwen-turbo",
        "internal_id": "qwen_qwen_turbo",
        "api_model_name": "qwen-turbo",  # 添加这一行，指定API模型名称
        "env_var": "DASHSCOPE_API_KEY",  # 需要的环境变量
        "module": "qwen_api",  # 模块名
        "function": "create_streaming_response",  # 调用函数名
        "max_input_tokens": 100000,  # 最大输入token限制
        "max_output_tokens": 8000,   # 最大输出token限制
        "pricing": {
            "input": 0.3 / 1_000_000,  # ￥0.3 per 1M tokens
            "output": 0.6 / 1_000_000  # ￥0.6 per 1M tokens
        },
        "features": ["summary", "mindmap", "knowledgegraph"],  # 支持的功能
        "is_default": False,  # 是否为默认模型
        "provider": "qwen",  # 添加提供商标识
        "i18n_prefix": "main.models.qwen_turbo"  # 国际化前缀
    },    # 千问模型
    "qwen_plus": {
        "id": "qwen_qwen_plus",
        "name": "qwen-plus",
        "internal_id": "qwen_qwen_plus",
        "api_model_name": "qwen-plus",  # 添加这一行，指定API模型名称
        "env_var": "DASHSCOPE_API_KEY",  # 需要的环境变量
        "module": "qwen_api",  # 模块名
        "function": "create_streaming_response",  # 调用函数名
        "max_input_tokens": 100000,  # 最大输入token限制
        "max_output_tokens": 8000,   # 最大输出token限制
        "pricing": {
            "input": 0.8 / 1_000_000,  # ￥0.8 per 1M tokens
            "output": 2 / 1_000_000  # ￥2 per 1M tokens
        },
        "features": ["summary", "mindmap", "knowledgegraph"],  # 支持的功能
        "is_default": False,  # 是否为默认模型
        "provider": "qwen",  # 添加提供商标识
        "i18n_prefix": "main.models.qwen_plus"  # 国际化前缀
    },

    # 千问多模态模型
    "qwen_omni_turbo": {
        "id": "qwen_omni_turbo",
        "name": "qwen-omni-turbo",
        "internal_id": "qwen_omni_turbo",
        "api_model_name": "qwen-omni-turbo",
        "env_var": "DASHSCOPE_API_KEY",
        "module": "qwen_api",
        "function": "create_streaming_response",
        "max_input_tokens": 100000,
        "max_output_tokens": 8000,
        "pricing": {
            # 多模态定价 - 基于千问官方定价（人民币转换）
            "input_text": 0.0004 / 1000,  # ￥0.0004 per 1K tokens
            "input_audio": 0.025 / 1000,   # ￥0.025 per 1K tokens
            "input_image": 0.0015 / 1000,  # ￥0.0015 per 1K tokens
            "output_text": 0.0016 / 1000,  # ￥0.0016 per 1K tokens (仅文本输入时)
            "output_text_multimodal": 0.0045 / 1000,  # ￥0.0045 per 1K tokens (包含音频/图片输入时)
            "output_audio": 0.05 / 1000,   # ￥0.05 per 1K tokens
        },
        "features": ["audio_analysis", "smart_qa"],
        "is_default": False,
        "provider": "qwen",
        "multimodal": True,  # 标记为多模态模型
        "i18n_prefix": "main.models.qwen_omni_turbo"
    },
    
    # DeepSeek模型
    "deepseek_V3": {
        "id": "deepseek_chat",
        "name": "deepseek-chat",
        "internal_id": "deepseek_chat",
        "env_var": "DEEPSEEK_API_KEY",
        "module": "deepseek_api",
        "api_model_name": "deepseek-chat",  # 添加这一行，指定API模型名称
        "function": "create_streaming_response",
        "max_input_tokens": 64000,  # 最大输入token限制
        "max_output_tokens": 8000,    # 最大输出token限制
        "pricing": {
            "input": 2.00 / 1_000_000,  # ￥2 per 1M tokens
            "output": 8.00 / 1_000_000  # ￥8 per 1M tokens
        },
        "features": ["summary", "mindmap", "knowledgegraph"],
        "is_default": False,
        "provider": "deepseek",  # 添加提供商标识
        "i18n_prefix": "main.models.deepseek_V3"  # 国际化前缀
    },

    # OpenRouter 模型 - Gemini 2.0 Flash
    "gemini_flash": {
        "id": "openrouter_google_gemini_2_0_flash",  # 前端模型ID
        "name": "Gemini 2.0 Flash",  # 简化名称，不包含 OpenRouter 前缀
        "internal_id": "google_gemini_2_0_flash",
        "api_model_name": "google/gemini-2.0-flash-001",  # 直接使用 frontend_model_id
        "env_var": "OPENROUTER_API_KEY",
        "module": "openrouter_api",
        "function": "create_streaming_response",
        "max_input_tokens": 1000000,  # 最大输入token限制
        "max_output_tokens": 8192,    # 最大输出token限制
        "pricing": {
            "input": 0.727 / 1_000_000,  # $0.1 per 1M tokens 
            "output": 2.909 / 1_000_000  # $0.4 per 1M tokens
        },
        "features": ["summary", "mindmap", "knowledgegraph"],
        "is_default": False,
        "provider": "openrouter",  # 添加提供商标识
        "i18n_prefix": "main.models.openrouter_google_gemini_2_0_flash"  # 国际化前缀
    },

    # Gemini 2.0 Flash - 别名配置，用于向后兼容
    "gemini_2_flash": {
        "id": "gemini_2_flash",
        "name": "Gemini 2.0 Flash",
        "internal_id": "google_gemini_2_0_flash",
        "api_model_name": "google/gemini-2.0-flash-001",
        "env_var": "OPENROUTER_API_KEY",
        "module": "openrouter_api",
        "function": "create_streaming_response",
        "max_input_tokens": 1000000,
        "max_output_tokens": 8192,
        "pricing": {
            "input": 0.727 / 1_000_000,  # $0.727 per 1M input tokens
            "output": 2.909 / 1_000_000  # $2.909 per 1M output tokens
        },
        "features": ["generate_timestamps", "summary", "mindmap", "knowledgegraph"],
        "is_default": False,
        "provider": "openrouter",
        "i18n_prefix": "main.models.gemini_2_flash"
    },

    # OpenRouter 模型 - Gemini 2.0 Flash Lite
    "openrouter_google_gemini_2_0_flash_lite": {
        "id": "openrouter_google_gemini_2_0_flash_lite",  # 前端模型ID
        "name": "Gemini 2.0 Flash Lite",  # 简化名称，不包含 OpenRouter 前缀
        "internal_id": "google_gemini_2_0_flash_lite",
        "api_model_name": "google/gemini-2.0-flash-lite-001",  # 直接使用 frontend_model_id
        "env_var": "OPENROUTER_API_KEY",
        "module": "openrouter_api",
        "function": "create_streaming_response",
        "max_input_tokens": 1000000,  # 最大输入token限制
        "max_output_tokens": 8192,    # 最大输出token限制
        "pricing": {
            "input": 0.545 / 1_000_000,  # $0.075 per 1M tokens 
            "output": 2.182 / 1_000_000  # $0.3 per 1M tokens
        },
        "features": ["summary", "mindmap", "knowledgegraph"],
        "is_default": False,
        "provider": "openrouter",  # 添加提供商标识
        "i18n_prefix": "main.models.openrouter_google_gemini_2_0_flash_lite"  # 国际化前缀
    },
    

    # OpenRouter 模型 - Gemini 2.5 Flash
    "openrouter_google_gemini_2_5_flash_preview": {
        "id": "openrouter_google_gemini_2_5_flash_preview",  # 前端模型ID
        "name": "Gemini 2.5 Flash",  # 简化名称，不包含 OpenRouter 前缀
        "internal_id": "google_gemini_2_5_flash_preview",
        "api_model_name": "google/gemini-2.5-flash-preview",  # 直接使用 frontend_model_id
        "env_var": "OPENROUTER_API_KEY",
        "module": "openrouter_api",
        "function": "create_streaming_response",
        "pricing": {
            "input": 1.09 / 1_000_000,  # $0.15 per 1M tokens 
            "output": 4.362 / 1_000_000  # $0.6 per 1M tokens
        },
        "features": ["summary", "mindmap", "knowledgegraph"],
        "is_default": False,
        "provider": "openrouter",  # 添加提供商标识
        "i18n_prefix": "main.models.openrouter_google_gemini_2_5_flash_preview"  # 国际化前缀
    }
}

# 功能所需的token估算
FEATURE_TOKEN_ESTIMATES = {
    "summary": {
        "input": 5000,  # 摘要生成平均输入token
        "output": 2000  # 摘要生成平均输出token
    },
    "mindmap": {
        "input": 5000,  # 脑图生成平均输入token
        "output": 1000  # 脑图生成平均输出token
    },
    "knowledgegraph": {
        "input": 5000,  # 知识图谱生成平均输入token
        "output": 1500  # 知识图谱生成平均输出token
    },    # 多模态功能估算
    "audio_analysis": {
        "prompt_text": 140,  # 提示词文本token（基于实际数据：139）
        "input_audio_per_second": 27,  # 每秒音频输入token（基于实际数据：160/6≈27）
        "output_text": 170,   # 输出文本token（基于实际数据：167）
        "output_audio_per_second": 233  # 每秒音频输出token（基于实际数据：1398/6≈233）
    },
    "smart_qa": {
        "prompt_text": 125,  # 提示词文本token（基于实际数据）
        "input_audio_per_second": 25,  # 每秒音频输入token（基于实际数据：152/6≈25）
        "output_text": 150,   # 输出文本token（基于实际数据）
        "output_audio_per_second": 190  # 每秒音频输出token（基于实际数据：1140/6≈190）
    }
}

def get_default_model(language=None):
    """获取默认模型ID
    
    参数:
        language (str): 用户界面语言，如'zh'或'en'
        
    返回:
        str: 默认模型ID
    """
    # 如果指定了语言，根据语言返回不同的默认模型
    if language:
        if language.lower().startswith('zh'):
            # 中文用户默认使用智谱GLM-4-Flash
            return "zhipu_glm_4_flash"
        else:
            # 非中文用户默认使用Gemini-2.0-Flash
            return "google_gemini_2_0_flash"
    
    # 如果没有指定语言，使用原来的逻辑
    for model_id, config in MODEL_CONFIG.items():
        if config.get("is_default", False):
            return model_id
    # 如果没有标记为默认的，返回第一个
    return next(iter(MODEL_CONFIG.keys()))

def get_model_configs():
    """获取所有模型配置信息"""
    return MODEL_CONFIG

def get_model_by_id(model_id):
    """根据ID获取模型配置"""
    return MODEL_CONFIG.get(model_id, MODEL_CONFIG[get_default_model()])

def get_model_internal_id(model_id):
    """获取模型的内部ID"""
    model_config = get_model_by_id(model_id)
    return model_config.get("internal_id", model_id)

def calculate_cost(model_id, feature_type):
    """
    计算特定功能使用特定模型的估算费用
    
    参数:
        model_id (str): 模型ID
        feature_type (str): 功能类型，如'summary'或'mindmap'
        
    返回:
        float: 估算的美元费用
    """
    model_config = get_model_by_id(model_id)
    pricing = model_config.get("pricing", {})
    
    if feature_type not in FEATURE_TOKEN_ESTIMATES:
        # 默认使用摘要功能的token估算
        feature_type = "summary"
    
    token_estimate = FEATURE_TOKEN_ESTIMATES[feature_type]
    
    input_cost = token_estimate["input"] * pricing.get("input", 0)
    output_cost = token_estimate["output"] * pricing.get("output", 0)
    
    return input_cost + output_cost

def calculate_multimodal_cost(model_id, feature_type, audio_duration=0, text_content=""):
    """
    计算多模态模型的估算费用
    
    参数:
        model_id (str): 模型ID
        feature_type (str): 功能类型，如'audio_analysis'或'smart_qa'
        audio_duration (float): 音频时长（秒）
        text_content (str): 文本内容
        
    返回:
        dict: 包含详细成本信息的字典
    """
    model_config = get_model_by_id(model_id)
    
    # 检查是否为多模态模型
    if not model_config.get("multimodal", False):
        # 非多模态模型，使用传统计算方式
        cost = calculate_cost(model_id, feature_type)
        return {
            "total_cost": cost,
            "input_cost": cost * 0.5,  # 简单估算
            "output_cost": cost * 0.5,
            "breakdown": {"traditional_model": cost}
        }
    
    pricing = model_config.get("pricing", {})
    
    if feature_type not in FEATURE_TOKEN_ESTIMATES:
        feature_type = "audio_analysis"  # 默认使用音频分析
    
    estimates = FEATURE_TOKEN_ESTIMATES[feature_type]
    
    # 计算输入成本
    input_costs = {}
    
    # 提示词文本成本
    prompt_tokens = estimates.get("prompt_text", 150)
    input_costs["prompt_text"] = prompt_tokens * pricing.get("input_text", 0)
    
    # 用户文本成本（如果有）
    if text_content:
        # 简单估算：每4个字符1个token
        text_tokens = len(text_content) // 4
        input_costs["user_text"] = text_tokens * pricing.get("input_text", 0)
    
    # 音频输入成本
    if audio_duration > 0:
        audio_input_tokens = audio_duration * estimates.get("input_audio_per_second", 26)
        input_costs["input_audio"] = audio_input_tokens * pricing.get("input_audio", 0)
    
    # 计算输出成本
    output_costs = {}
    
    # 文本输出成本（多模态输入时使用更高费率）
    text_output_tokens = estimates.get("output_text", 170)
    has_multimodal_input = audio_duration > 0
    text_rate = pricing.get("output_text_multimodal" if has_multimodal_input else "output_text", 0)
    output_costs["output_text"] = text_output_tokens * text_rate    # 音频输出成本
    # 注意：当前所有功能(smart_qa, audio_analysis等)都只产生文本输出，不产生音频输出
    # 如果将来有功能需要音频输出，可以在这里添加判断
    output_costs["output_audio"] = 0
    
    # 汇总成本
    total_input_cost = sum(input_costs.values())
    total_output_cost = sum(output_costs.values())
    total_cost = total_input_cost + total_output_cost
    
    return {
        "total_cost": total_cost,
        "input_cost": total_input_cost,
        "output_cost": total_output_cost,
        "breakdown": {
            "input": input_costs,
            "output": output_costs
        },        "estimated_tokens": {
            "input_audio": audio_duration * estimates.get("input_audio_per_second", 26) if audio_duration > 0 else 0,
            "input_text": prompt_tokens + (len(text_content) // 4 if text_content else 0),
            "output_text": text_output_tokens,
            "output_audio": 0  # 当前所有功能都不产生音频输出
        }
    }

def get_available_models():
    """获取可用的模型列表，适用于前端展示"""
    available_models = []
    
    for model_id, config in MODEL_CONFIG.items():
        # 检查是否可用 - 环境变量存在且函数已导入
        is_available = False
        env_var = config.get("env_var", "")
        
        # 首先检查环境变量是否存在
        if env_var and os.environ.get(env_var):
            try:
                # 稍后会尝试导入模块，这里只是检查环境变量
                is_available = True
            except:
                pass
        
        # 构建前端模型对象 - 只检查环境变量，不检查模块导入
        model_info = {
            "id": model_id,
            "name": config.get("name", model_id),
            "available": is_available
        }
        
        if not is_available:
            model_info["name"] += " (未配置)"
        
        available_models.append(model_info)
    
    return available_models

def check_module_imports():
    """检查模块导入情况，用于调试"""
    results = {}
    
    for model_id, config in MODEL_CONFIG.items():
        module_name = config.get("module", "")
        function_name = config.get("function", "")
        
        if not module_name:
            results[model_id] = {"status": "error", "message": "模块名为空"}
            continue
            
        try:
            # 尝试动态导入模块
            module = importlib.import_module(module_name)
            
            if hasattr(module, function_name):
                results[model_id] = {"status": "success", "message": f"成功导入 {module_name}.{function_name}"}
            else:
                results[model_id] = {"status": "error", "message": f"模块 {module_name} 中找不到函数 {function_name}"}
        except ImportError as e:
            results[model_id] = {"status": "error", "message": f"导入模块失败: {str(e)}"}
        except Exception as e:
            results[model_id] = {"status": "error", "message": f"其他错误: {str(e)}"}
    
    return results
