/* 自定义提示词模态框样式 */
.custom-prompt-modal {
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.custom-prompt-modal .modal-header {
    padding: 20px 24px 16px;
    border-bottom: 2px solid #e0e0e0;
    flex-shrink: 0;
}

.custom-prompt-modal .modal-header h3 {
    margin: 0;
    color: var(--primary-color, #2196F3);
    font-size: 1.25rem;
    font-weight: 600;
}

.custom-prompt-modal .modal-body {
    padding: 20px 24px;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.custom-prompt-modal .modal-footer {
    padding: 16px 24px 20px;
    border-top: 1px solid #e0e0e0;
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 标签页导航 */
.prompt-tabs {
    display: flex;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 24px;
}

.prompt-tab-button {
    padding: 12px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    flex: 1;
    text-align: center;
}

.prompt-tab-button:hover {
    color: var(--primary-color, #2196F3);
    background-color: rgba(33, 150, 243, 0.05);
}

.prompt-tab-button.active {
    color: var(--primary-color, #2196F3);
    border-bottom-color: var(--primary-color, #2196F3);
    background-color: rgba(33, 150, 243, 0.08);
    font-weight: 600;
}

/* 标签页内容 */
.prompt-tab-content {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 输入组 */
.prompt-input-group {
    margin-bottom: 20px;
}

.prompt-input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

/* 文本框样式 */
.prompt-textarea {
    width: 100%;
    min-height: 200px;
    max-height: 400px;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    box-sizing: border-box;
}

.prompt-textarea:focus {
    outline: none;
    border-color: var(--primary-color, #2196F3);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.prompt-textarea::placeholder {
    color: #999;
    font-size: 12px;
    line-height: 1.4;
}

/* 帮助文本 */
.prompt-help {
    margin-top: 8px;
}

.prompt-help small {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.btn-primary {
    background-color: var(--primary-color, #2196F3);
    color: white;
}

.btn-primary:hover {
    background-color: #1976D2;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.btn-secondary {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background-color: #e8e8e8;
    color: #333;
    border-color: #ccc;
}

/* 消息提示 */
.prompt-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    animation: slideInRight 0.3s ease;
}

.prompt-message-success {
    background-color: #4CAF50;
    color: white;
}

.prompt-message-error {
    background-color: #f44336;
    color: white;
}

.prompt-message-warning {
    background-color: #FF9800;
    color: white;
}

.prompt-message-info {
    background-color: #2196F3;
    color: white;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 关闭按钮 */
.close-button {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #999;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-button:hover {
    color: #333;
    background-color: rgba(0, 0, 0, 0.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .custom-prompt-modal {
        width: 95%;
        max-width: none;
        height: 90vh;
        max-height: 90vh;
    }
    
    .custom-prompt-modal .modal-header,
    .custom-prompt-modal .modal-body,
    .custom-prompt-modal .modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
    
    .prompt-tabs {
        flex-direction: column;
        border-bottom: none;
        border-right: 2px solid #e0e0e0;
        margin-bottom: 16px;
    }
    
    .prompt-tab-button {
        text-align: left;
        border-bottom: none;
        border-right: 3px solid transparent;
        padding: 10px 16px;
    }
    
    .prompt-tab-button.active {
        border-bottom-color: transparent;
        border-right-color: var(--primary-color, #2196F3);
    }
    
    .prompt-textarea {
        min-height: 150px;
        font-size: 14px;
    }
    
    .modal-footer {
        flex-direction: column-reverse;
        gap: 8px;
    }
    
    .btn {
        width: 100%;
        padding: 12px 20px;
    }
}

@media (max-width: 480px) {
    .custom-prompt-modal {
        width: 100%;
        height: 100vh;
        max-height: 100vh;
        border-radius: 0;
    }
    
    .prompt-tab-button {
        font-size: 13px;
        padding: 8px 12px;
    }
    
    .prompt-textarea {
        min-height: 120px;
        font-size: 13px;
    }
    
    .prompt-message {
        top: 10px;
        right: 10px;
        left: 10px;
        text-align: center;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .custom-prompt-modal .modal-content {
        background-color: #2d2d2d;
        color: #e0e0e0;
    }
    
    .custom-prompt-modal .modal-header {
        border-bottom-color: #404040;
    }
    
    .custom-prompt-modal .modal-footer {
        border-top-color: #404040;
    }
    
    .prompt-tabs {
        border-bottom-color: #404040;
    }
    
    .prompt-tab-button {
        color: #b0b0b0;
    }
    
    .prompt-tab-button:hover {
        color: #64b5f6;
        background-color: rgba(100, 181, 246, 0.1);
    }
    
    .prompt-tab-button.active {
        color: #64b5f6;
        border-bottom-color: #64b5f6;
        background-color: rgba(100, 181, 246, 0.15);
    }
    
    .prompt-input-group label {
        color: #e0e0e0;
    }
    
    .prompt-textarea {
        background-color: #3d3d3d;
        border-color: #555;
        color: #e0e0e0;
    }
    
    .prompt-textarea:focus {
        border-color: #64b5f6;
        box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.2);
    }
    
    .prompt-textarea::placeholder {
        color: #888;
    }
    
    .prompt-help small {
        color: #b0b0b0;
    }
    
    .btn-secondary {
        background-color: #404040;
        color: #e0e0e0;
        border-color: #555;
    }
    
    .btn-secondary:hover {
        background-color: #4d4d4d;
        border-color: #666;
    }
    
    .close-button {
        color: #b0b0b0;
    }
    
    .close-button:hover {
        color: #e0e0e0;
        background-color: rgba(255, 255, 255, 0.1);
    }
}

/* 滚动条样式 */
.prompt-textarea::-webkit-scrollbar {
    width: 8px;
}

.prompt-textarea::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.prompt-textarea::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.prompt-textarea::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 模态框动画 */
.modal-overlay .custom-prompt-modal {
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 确保模态框在最顶层 */
.modal-overlay {
    z-index: 9999;
}

.custom-prompt-modal {
    z-index: 10000;
} 