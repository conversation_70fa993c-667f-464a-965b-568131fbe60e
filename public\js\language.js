document.addEventListener("DOMContentLoaded", () => {
  const userLang = navigator.language || navigator.userLanguage; 
  let language = 'en'; // 默认语言

  if (userLang.startsWith('zh')) {
    language = 'zh';
  } else if (userLang.startsWith('en')) {
    language = 'en';
  }

  changeLanguage(language);
});

function changeLanguage(language) {
  fetch(`lang/${language}.json`)
    .then(response => response.json())
    .then(data => {
      document.title = data.title;
      document.getElementById('heading').innerText = data.heading;
      document.getElementById('description').innerText = data.description;
      document.getElementById('feature-summary').innerText = data.features.summary;
      document.getElementById('feature-formats').innerText = data.features.formats;
      document.getElementById('feature-storage').innerText = data.features.storage;
      document.getElementById('auth-welcome').innerText = data.auth.welcome;
      document.getElementById('auth-login-or-signup').innerText = data.auth.login_or_signup;
      document.getElementById('loginTab').innerText = data.auth.login;
      document.getElementById('signupTab').innerText = data.auth.signup;
      document.getElementById('loginEmail').placeholder = data.auth.email;
      document.getElementById('loginPassword').placeholder = data.auth.password;
      document.getElementById('loginButton').innerText = data.auth.login;
      document.getElementById('signupEmail').placeholder = data.auth.email;
      document.getElementById('signupPassword').placeholder = data.auth.password;
      document.getElementById('signupConfirmPassword').placeholder = data.auth.confirm_password;
      document.getElementById('verificationCode').placeholder = data.auth.verification_code;
      document.getElementById('requestVerification').innerText = data.auth.request_verification;
      document.getElementById('signupButton').innerText = data.auth.signup;
      document.getElementById('loginError').innerText = data.auth.login_error;
      document.getElementById('signupError').innerText = data.auth.signup_error;
    })
    .catch(error => console.error('Error loading language file:', error));
}

function showLogin() {
  document.getElementById('loginForm').classList.add('active');
  document.getElementById('signupForm').classList.remove('active');
  document.getElementById('loginTab').classList.add('active');
  document.getElementById('signupTab').classList.remove('active');
}

function showSignup() {
  document.getElementById('loginForm').classList.remove('active');
  document.getElementById('signupForm').classList.add('active');
  document.getElementById('loginTab').classList.remove('active');
  document.getElementById('signupTab').classList.add('active');
}