// 快速书签修复验证脚本
console.log('✅ 书签修复验证脚本已加载');

window.quickBookmarkFixTest = function() {
    console.log('✅ 开始快速书签修复验证');
    
    const progressContainer = document.querySelector('.progress-container');
    const progressBar = document.getElementById('uploadedProgressBar');
    
    console.log('✅ 进度条容器:', progressContainer);
    console.log('✅ 进度条元素:', progressBar);
    
    if (progressContainer) {
        const containerStyle = window.getComputedStyle(progressContainer);
        console.log('✅ 进度条容器显示状态:');
        console.log('  - display:', containerStyle.display);
        console.log('  - visibility:', containerStyle.visibility);
        console.log('  - overflow:', containerStyle.overflow);
        
        if (containerStyle.display === 'none') {
            console.error('❌ 进度条容器仍然被隐藏！');
            return false;
        } else {
            console.log('✅ 进度条容器现在可见了！');
        }
    }
    
    if (progressBar) {
        // 添加一个简单的测试书签标记
        const testMarker = document.createElement('div');
        testMarker.className = 'bookmark-marker quick-test-marker';
        testMarker.style.left = '50%';
        testMarker.title = '快速测试书签';
        testMarker.style.background = '#00ff00'; // 绿色以便区分
        
        progressBar.appendChild(testMarker);
        console.log('✅ 快速测试书签标记已添加');
        
        // 检查是否可见
        const markerRect = testMarker.getBoundingClientRect();
        console.log('✅ 书签标记位置:', {
            top: markerRect.top,
            left: markerRect.left,
            width: markerRect.width,
            height: markerRect.height,
            visible: markerRect.width > 0 && markerRect.height > 0
        });
        
        if (markerRect.width > 0 && markerRect.height > 0) {
            console.log('🎉 书签标记现在应该可见了！');
            
            // 3秒后移除测试标记
            setTimeout(() => {
                testMarker.remove();
                console.log('✅ 快速测试标记已移除');
            }, 3000);
            
            return true;
        } else {
            console.error('❌ 书签标记仍然不可见');
            testMarker.remove();
            return false;
        }
    }
    
    return false;
};

// 检查当前书签状态
window.checkCurrentBookmarks = function() {
    if (window.bookmarkManager) {
        console.log('📊 当前书签状态:');
        console.log('  - 书签数量:', window.bookmarkManager.bookmarks.length);
        console.log('  - 书签详情:', window.bookmarkManager.bookmarks);
        
        if (window.bookmarkManager.bookmarks.length > 0) {
            console.log('🔄 强制刷新现有书签标记显示');
            window.bookmarkManager.updateBookmarkMarkers('uploaded');
        }
    } else {
        console.log('📊 书签管理器未初始化');
    }
};

console.log('✅ 快速验证函数已准备：');
console.log('✅ - quickBookmarkFixTest() : 验证修复效果');
console.log('✅ - checkCurrentBookmarks() : 检查当前书签状态'); 