{"nav": {"features": "Features", "testimonials": "Reviews", "pricing": "Pricing", "faq": "FAQ", "getStarted": "Get Started"}, "landing": {"title": "AudioPilot - AI Audio Learning Assistant", "heading": "AI Audio Learning Assistant", "description": "Subscribe to podcasts and upload audio, record while listening, smart Q&A, ask anytime to improve language listening skills.", "hero": {"tagline": "AI Language Learning Assistant", "subtitle": "Master Audio Content Fast with AI-Powered Language Learning", "cta": "Start Learning Now"}, "features": {"title": "Powerful Features, Simple Operation", "upload": {"title": "Multi-Media Input", "desc": "Support podcast subscription, audio upload and online recording with multiple content sources"}, "ai": {"title": "Ask Anytime Assistant", "desc": "When you don't understand, ask anytime for AI transcription, translation and grammar analysis"}, "qa": {"title": "Smart Q&A", "desc": "Interactive Q&A based on audio content for deeper understanding of learning materials"}, "paygo": {"title": "Credit-Based Payment", "desc": "Purchase credit packages to use features, credits valid for 30 days"}}, "demo": {"title": "Experience Demo", "subtitle": "Upload audio or subscribe to podcasts, get AI smart analysis and learning guidance to efficiently improve language listening skills", "screenshot_alt": "Language Learning Assistant Interface Screenshot", "upload_button": "Try Now", "register_button": "Free Registration"}, "testimonials": {"title": "User Reviews", "user1": {"name": "Student <PERSON>", "title": "English Major Student", "quote": "\"AudioPilot helps me quickly analyze listening materials in English learning. Every audio gets detailed pronunciation and grammar guidance.\""}, "user2": {"name": "Teacher Li", "title": "English Teacher", "quote": "\"As an English teacher, I use AudioPilot to analyze students' speaking recordings. It saves me a lot of time and helps quickly identify learning issues.\""}}, "pricing": {"title": "Choose Your Plan", "monthly": "Monthly", "annual": "Annual", "annual_save": "Save 20%", "starter": {"name": "Starter", "price": "19", "credits": "1000 credits/month", "features": ["Basic audio analysis", "Pronunciation guidance", "Email support"]}, "pro": {"name": "Professional", "price": "39", "credits": "3000 credits/month", "features": ["Advanced speech analysis", "Unlimited smart Q&A", "Grammar correction", "Priority support"]}, "team": {"name": "Team", "price": "99", "credits": "10000 credits/month", "features": ["Team learning management", "Learning reports", "Dedicated support", "Custom features"]}}, "faq": {"title": "FAQ", "subtitle": "Find answers to common questions about using AudioPilot.", "q1": {"question": "What is AudioPilot?", "answer": "AudioPilot is an AI-powered language learning assistant that supports podcast subscription and audio upload. You can record while listening, and when you don't understand content, use the 'Ask Anytime' feature to get AI-provided transcription, translation and grammar analysis for quick comprehension."}, "q2": {"question": "How do I use the 'Ask Anytime' feature?", "answer": "When you encounter something you don't understand while playing audio, click the 'Ask Anytime' button. AI will automatically analyze the audio content at the current playback position and provide accurate transcription text, Chinese translation, and grammar structure analysis to help you quickly understand the content."}, "q3": {"question": "What audio formats and podcast sources are supported?", "answer": "We support MP3, WAV, M4A and other mainstream audio formats with a maximum file size of 100MB. We also support podcast RSS subscription for directly subscribing to and listening to various podcast shows."}, "q4": {"question": "Can I record while playing audio?", "answer": "Yes, this is one of our featured functions. You can record while playing audio or podcasts, such as for shadowing practice or taking notes, then perform AI analysis and Q&A on your recordings."}, "q5": {"question": "How does the credit system work?", "answer": "AudioPilot uses a credit-based payment system where each AI operation (transcription analysis, translation, smart Q&A, etc.) consumes corresponding credits. Credits are valid for 30 days after purchase, with no monthly subscription model. New users receive free trial credits upon registration."}, "q6": {"question": "How accurate is the AI analysis?", "answer": "We use advanced AI speech recognition and natural language processing technology with over 95% accuracy in audio transcription. Translation and grammar analysis quality is continuously optimized to meet daily language learning needs."}, "q7": {"question": "What languages are supported?", "answer": "Currently we mainly support English audio transcription, translation and analysis, with Chinese interface support. We will gradually expand to support learning features for more languages in the future."}, "q8": {"question": "How do I manage my podcast subscriptions?", "answer": "In the podcast tab, you can search and subscribe to podcasts of interest, view episode lists, and bookmark favorite shows. All subscription and bookmark information is saved in your account."}}, "footer": {"company": "AudioPilot", "copyright": "&copy; 2025 AudioPilot. All rights reserved."}}, "ui": {"languageSelector": {"title": "Select Language", "chinese": "Chinese", "english": "English", "useBrowser": "Use Browser Language"}, "menu": {"credits": "Credits", "credit_expiry": "Credit Validity", "recharge": "Recharge", "settings": "Settings", "logout": "Logout"}}, "credits": {"insufficient_title": "Insufficient Credits", "insufficient_message": "You don't have enough credits to complete this operation", "current_credits": "Current Credits", "needed_credits": "Credits Needed", "recharge_now": "Recharge Now", "close": "Close"}, "timestamp": {"title": "Time Stamps", "no_file": "Please upload a timestamp file first"}, "chapter": {"content_title": "Chapter Summary", "select_timestamp": "Please select a timestamp to view chapter summary"}, "log": {"page_title": "AI eBook Reading Assistant", "description": "Explore and read, upload your EPUB eBooks, get AI-generated summaries with diagram, mind maps and knowledge graphs, and acquire knowledge more efficiently.", "features_section": {"feature1": {"title": "Summaries with diagrams", "desc": "AI-powered text and diagram summaries, quickly understand key points"}, "feature2": {"title": "Mind Maps and knowledge graphs ", "desc": "Quickly visualize content structure and key concepts"}, "feature3": {"title": "Pay As You Go", "desc": "No monthly fees, pay only for what you use"}}, "alerts": {"not_enough_credits": "Not enough credits. Please recharge."}, "auth": {"title": "Welcome", "subtitle": "Enter your new password below", "login_tab": "<PERSON><PERSON>", "signup_tab": "Sign up", "email_placeholder": "Email", "password_placeholder": "New Password", "confirm_password": "Confirm New Password", "verification_code": "Verification Code", "request_code": "Request Code", "forgot_password": "Forgot password?", "reset_title": "Reset Password", "reset_email_placeholder": "Enter your email", "reset_button": "Reset Password", "password_mismatch": "Passwords do not match, please re-enter", "invalid_reset_link": "Invalid reset link, please request a new one.", "reset_failed": "Unable to reset password, please return to login and request a new reset link.", "reset_failed_prefix": "Password reset failed: ", "reset_success": "Password reset successful! Redirecting to main page in 3 seconds...", "same_password_error": "New password is the same as your old password. Please use a different password.", "terms_agreement": "By continuing, you agree to our"}}, "pricing": {"title": "Choose the Plan That's Right for You", "getStarted": "Get Started", "basic": {"name": "Basic", "credits": "3,000 credits", "analysis": "AI Audio Analysis", "qa": "Smart Q&A", "summary": "AI text & image summaries", "mindmap": "AI mind maps", "perOperation": "2-20 credits per AI operation"}, "standard": {"name": "Standard", "credits": "6,500 credits", "analysis": "AI Audio Analysis", "qa": "Smart Q&A", "summary": "AI text & image summaries", "mindmap": "AI mind maps", "perOperation": "2-20 credits per AI operation"}, "premium": {"name": "Premium", "credits": "10,000 credits", "analysis": "AI Audio Analysis", "qa": "Smart Q&A", "summary": "AI text & image summaries", "mindmap": "AI mind maps", "perOperation": "2-20 credits per AI operation"}}, "footer": {"tagline": "AI English Audio Pilot", "product": "Product", "features": "Features", "pricing": "Pricing", "tutorials": "Tutorials", "changelog": "Changelog", "support": "Support", "helpCenter": "Help Center", "faq": "FAQ", "contactUs": "Contact Us", "feedback": "<PERSON><PERSON><PERSON>", "company": "Company", "aboutUs": "About Us", "joinUs": "Join Us", "blog": "Blog", "media": "Media", "legal": "Legal", "terms": "Terms of Service", "privacy": "Privacy Policy", "copyright": " 2025 BookSum. All rights reserved."}, "audio": {"audio_input": "Media Input", "upload_audio": "Choose Media File", "start_recording": "Start Recording", "stop_recording": "Stop Recording", "no_file": "No File", "no_recording": "No Recording", "upload_success": "Upload Success", "upload_failed": "Upload Failed", "recording_start": "Recording Started", "recording_stop": "Recording Stopped", "ask_current_position": "Ask Anytime", "load_bookmark": "Load Bookmark", "select_timestamp": "Upload Timestamp File", "analysis_failed_prefix": "Analysis failed", "enter_question": "Enter your question...", "smart_qa": "Smart Q&A", "send": "Send", "uploaded_playback": "Playback", "current_audio_playback": "Play Current Audio Segment", "speed": "Speed", "processing": "Processing...", "menu": "<PERSON><PERSON>"}, "subtitle": {"drop_srt": "Drop SRT subtitle file here", "or_click": "or click to select file", "clear": "Clear", "upload_success": "Subtitle uploaded successfully", "upload_failed": "Subtitle upload failed", "parse_failed": "Subtitle parsing failed, please check file format", "invalid_format": "Please select SRT format subtitle file", "sync_with_audio": "Sync with audio", "no_subtitles": "No subtitles", "chat": {"title": "Subtitle Q&A", "placeholder": "Ask follow-up questions...", "send": "Send", "close": "Close", "play": "Play", "pause": "Pause", "thinking": "AI is thinking", "default_question": "Please analyze the meaning, grammar structure and idioms of this sentence.", "error_api_failed": "AI analysis failed, please try again"}, "search_placeholder": "Search subtitles"}, "sidebar": {"timestamp": "Timestamps", "podcast": "Podcast"}, "podcast": {"search_placeholder": "Search for podcasts...", "searching": "Searching...", "search_results": "Search Results", "favorites": "Favorite Podcasts", "no_favorites": "No favorite podcasts yet", "episodes": "Episodes", "select_podcast": "Select a podcast to view episodes", "no_episodes": "No episodes available", "download": "Download", "play": "Play", "pause": "Pause", "loading": "Loading...", "load_more": "Load More", "load_more_count": "Load More ({count} remaining)", "loading_episodes": "Loading episodes...", "download_failed": "Download failed", "play_failed": "Play failed, please check the link or network", "download_complete": "Download complete", "no_audio": "No audio available", "invalid_audio_link": "Invalid audio link", "unknown_date": "Unknown date"}, "title": "BookSum - AI-powered eBook Reading Assistant", "heading": "AI Audio Copilot", "description": "Upload your EPUB eBooks, get summaries with diagrams, mind maps and knowledge praphs, and acquire knowledge more efficiently.", "header": {"tagline": "AI-powered eBook Reading Assistant"}, "features": {"title": "Powerful Features, Simple Operation", "upload": {"title": "Multi-Media Input", "desc": "Support podcast subscription, audio upload and online recording with multiple content sources"}, "summary": {"title": "Summaries with diagrams", "desc": "AI-powered text and diagram summaries, quickly understand key points"}, "mindmap": {"title": "Mind Maps and Knowledge Graphs", "desc": "Quickly visualize content structure and key concepts"}, "credits": {"title": "Pay As You Go", "desc": "No monthly fees, pay only for what you use"}}, "auth": {"welcome": "Welcome", "login_or_signup": "Login or Sign up to start your reading journey", "login": "<PERSON><PERSON>", "loggingIn": "Logging in...", "google_login_in_progress": "Google login in progress...", "signup": "Sign up", "email": "Email", "password": "Password", "confirm_password": "Confirm Password", "verification_code": "Verification Code", "request_verification": "Request Code", "submit": "Submit", "login_error": "<PERSON><PERSON>", "signup_error": "Signup Error", "signup_disabled": "Registration is currently disabled. Please contact administrator to enable user registration.", "email_provider_disabled": "Email registration is disabled. Please enable Email Provider in Supabase Console.", "forgot_password": "Forgot password?", "reset_password": "Reset Password", "send_reset_link": "Send Reset Link", "or_continue_with": "Or continue with", "login_with_google": "Login with Google", "signup_with_google": "Sign up with Google", "loading_captcha": "Loading verification...", "captcha_error": "Verification failed, please refresh the page", "captcha_load_failed": "Capt<PERSON> failed to load, please refresh the page and try again", "captcha_disabled": "<PERSON><PERSON> is disabled", "captcha_timeout": "Verification timeout, please refresh the page", "terms_agreement": "By continuing, you agree to our", "and": "and", "invalid_email": "Please enter a valid email address", "password_required": "Please enter your password", "password_mismatch": "Passwords do not match, please re-enter", "verification_code_sent": "Verification code has been sent to your email", "resend": "Resend", "verification_code_required": "Please get and enter the verification code", "verification_required": "Please click the \"Request Code\" button first", "verifying": "Verifying...", "registration_success": "Email verification successful! Initial credits have been added to your account", "verification_failed": "Verification failed: ", "resend_countdown": " seconds until resend", "processing": "Processing...", "captcha_required": "Please complete the security verification first", "reset_link_sent": "Password reset link has been sent to your email", "reset_link_sending": "Sending...", "reset_link_failed": "Failed to send reset link: ", "please_enter_email": "Enter your email", "email_format_invalid": "Invalid email format", "disposable_email_not_allowed": "Disposable email addresses are not allowed, please use a regular email", "server_error": "Server error", "email_validation_service_unavailable": "Email validation service is temporarily unavailable, please try again later", "registration_failed": "Registration failed, please try again later", "email_already_registered": "This email is already registered, please log in directly", "registration_success_but_credits_failed": "Email verification successful! But failed to initialize credits, please contact customer service", "verification_success_but_no_user": "Verification successful but failed to retrieve user information", "google_login_failed": "Google login failed: ", "google_signup_failed": "Google signup failed: ", "email_not_verified": "Please verify your email before logging in", "redirecting_to": "Redirecting to", "language": "Language", "login_failed": "<PERSON><PERSON> failed, please try again", "invalid_credentials": "Incorrect email or password, please try again", "connection_error": "Connection error, please refresh the page and try again", "refresh": "Refresh Page"}, "hero": {"title": "Master Book Insights Fast with AI", "subtitle": "Upload eBooks, get summaries with diagrams, mindmaps and knowledge praphs,  save 80% reading time", "tryFree": "Try for Free"}, "testimonials": {"title": "User Reviews", "user1": {"quote": "\"Book<PERSON><PERSON> lets me keep up with the latest knowledge despite my busy work schedule. I can master the core content of each book in just 20 minutes.\"", "name": "Mr. <PERSON>", "role": "Business Manager"}, "user2": {"quote": "\"As a graduate student, I need to read many books. <PERSON><PERSON><PERSON> has saved me hundreds of hours by helping me quickly find valuable information.\"", "name": "Ms. <PERSON>", "role": "PhD Student"}, "user3": {"quote": "\"The quality of AI-generated summaries is amazing, capturing the key points of each chapter more comprehensively and accurately than my own notes.\"", "name": "Professor <PERSON>", "role": "University Teacher"}}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Find answers to common questions about using BookSum.", "q1": {"question": "What is BookSum?", "answer": "BookSum is an AI-powered eBook reading assistant that helps you quickly understand and summarize eBook content. Main features include generating chapter summaries, creating mind maps, building knowledge graphs, and intelligent Q&A."}, "q2": {"question": "How do I get started with BookSum?", "answer": "It's very simple: After registering an account, upload EPUB format eBook files or directly paste text content, select chapters to analyze, click \"Generate Summary\", \"Generate Mind Map\" or \"Generate Knowledge Graph\" buttons, and wait for AI processing to complete."}, "q3": {"question": "What file formats are supported?", "answer": "Currently we mainly support EPUB format eBooks (recommended) and direct text content pasting for analysis. File size limit is maximum 50MB."}, "q4": {"question": "What AI models are available?", "answer": "We support multiple advanced AI models including Zhipu GLM series, DeepSeek series, Gemini series, Qwen series, Claude series, OpenRouter models, etc., meeting different needs and budgets."}, "q5": {"question": "What is the credit system?", "answer": "BookSum uses a credit-based payment model where each AI operation (generating summaries, mind maps, knowledge graphs) consumes credits. Different AI models consume different amounts of credits, and new users receive free trial credits upon registration."}, "q6": {"question": "What are the credit consumption standards?", "answer": "Depending on the AI model and function used: summary generation, mind map generation, and knowledge graph generation typically consume 2-20 credits per operation, with specific consumption depending on content length and selected AI model."}, "q7": {"question": "Do you offer a free trial?", "answer": "Yes, we provide free trial credits for new users to fully experience our AI features. You can start using it without any cost."}, "q8": {"question": "What export formats are supported?", "answer": "Multiple export formats are supported: PNG images (suitable for sharing and printing), HTML files (complete formatting, can be opened in browsers), Markdown files (plain text format, easy to edit)."}, "q9": {"question": "How long does it take to generate summaries?", "answer": "Usually 1-3 minutes, depending on chapter content length, selected AI model, and server load. We continuously optimize processing speed."}, "q10": {"question": "How is data privacy protected?", "answer": "We strictly protect your data privacy: uploaded documents are only used for AI analysis, we don't save your document content, strictly comply with data protection regulations, and don't share personal information with third parties."}}, "cta": {"title": "Start Your Efficient Reading Journey", "freeCredits": "Register today and get free credits", "tryFree": "Try for Free"}, "main": {"upload_epub": "Upload eBook", "credits": "Credits:", "recharge": "Recharge", "logout": "Logout", "menu": "<PERSON><PERSON>", "ai_output_language": "Language", "chapters": "Chapter List", "chapter_content": "Chapter Content", "ai_summary": "Summary", "ai_mindmap": "MindMap", "ai_knowledgegraph": "Knowledge Graph", "generate_summary": "Generate Summary", "batch_generate_summary": "Batch Generate Summary", "generate_mindmap": "Generate Mind Map", "generate_knowledgegraph": "Generate Knowledge Graph", "no_chapter_selected": "No chapter selected", "select_chapter_prompt": "Please select a chapter from the list", "summary_cost": "Generating summary will cost approximately", "mindmap_cost": "Generating mind map will cost approximately", "knowledgegraph_cost": "Generating knowledge graph will cost approximately", "credits_unit": "credits", "not_enough_credits": "Not enough credits. Please recharge.", "confirm_generation": "Confirm generation?", "yes": "Yes", "no": "No", "close": "Close", "export": "Export", "generating": "Processing...", "batch_processing": "Batch Processing", "select_chapters": "Please select chapters for summary generation", "no_chapters_selected": "No chapters selected", "batch_summary_title": "<PERSON><PERSON>", "download_batch_summary": "Download Batch Summary", "processing_chapter": "Processing chapter", "chapter_completed": "Chapter summary completed", "all_completed": "All chapter summaries completed", "batch_completed_message": "Batch summary task completed, click to download and save", "copy": "Copy", "copied": "Copied!", "copy_failed": "Co<PERSON> failed", "no_data": "No data to display", "paste_text": "Paste Text", "text_chat_button": "Q&A", "text_chat_title": "Text Q&A", "paste_text_title": "Paste Text Content", "paste_title": "Title:", "paste_content": "Content:", "paste_title_placeholder": "Enter a title", "paste_content_placeholder": "Please paste text to analyze", "confirm": "Confirm", "cancel": "Cancel", "alert": {"no_content": "Please select a chapter or paste text content first", "select_epub": "Please select an EPUB file first", "select_chapter": "Please select a chapter first", "generate_mindmap_first": "Please generate mind map content first", "generate_summary_first": "Please generate summary content first", "mindmap_empty": "Mind map content is empty", "model_not_configured": "is not configured. Please ensure necessary environment variables are set."}, "download_as": "Download as", "notification": {"mindmap_exported_png": "Mind map successfully exported as PNG image", "summary_exported_png": "Summary successfully exported as PNG image", "knowledgegraph_exported_png": "Knowledge graph successfully exported as PNG image", "text_pasted": "You can now generate summary, mindmap or knowledge graph"}, "models": {"openrouter_google_gemini_2_5_flash_preview": "Gemini 2.5 Flash", "openrouter_google_gemini_2_0_flash": "Gemini 2.0 Flash", "openrouter_google_gemini_2_0_flash_lite": "Gemini 2.0 Flash Lite", "openrouter_anthropic_claude_3_opus": "Anthropic Claude 3 Opus", "openrouter_anthropic_claude_3_sonnet": "Anthropic Claude 3 Sonnet", "openrouter_anthropic_claude_3_haiku": "Anthropic Claude 3 Haiku", "zhipu_glm_4": "Zhipu GLM-4", "zhipu_flash": "Zhipu GLM-4-Flash", "zhipu_glm_4_flashX": "Zhipu GLM-4-FlashX", "zhipu_glm_4_air": "Zhipu GLM-4-Air", "deepseek_deepseek_chat": "DeepSeek Chat", "deepseek_V3": "DeepSeek Chat", "gemini_pro": "Google Gemini 1.5 Pro", "gemini_flash": "Gemini 2.0 Flash", "gemini_2_flash": "Gemini 2.0 Flash", "gemini_2_0_flash": "Gemini 2.0 Flash", "gemini_2_5_flash": "Gemini 2.5 Flash", "gemini_lite": "Gemini 2.0 Flash Lite", "qwen_turbo": "<PERSON><PERSON>", "qwen_plus": "<PERSON><PERSON>", "qwen_qwen3_14b": "Qwen3-14b", "qwen_qwen3_8b": "Qwen3-8b", "grok_2": "Grok-2", "grok_beta": "Grok Beta", "claude_3_5_haiku": "Claude 3.5 Haiku", "openrouter_Qwen3_4B": "Qwen3 4B", "openrouter_Qwen3_14B": "Qwen3 14B", "openrouter_Qwen3_8B": "Qwen3 8B", "qwen_max": "<PERSON><PERSON>", "claude_3_haiku": "Claude 3 Haiku", "claude_3_5_sonnet": "Claude 3.5 Sonnet"}, "error": {"upload_parse_failed": "Error uploading or parsing EPUB! Please try again", "mindmap_parse_failed": "Failed to parse mind map data", "model_unavailable": "Current model is unavailable, please switch to another model and retry.", "request_timeout": "Request timed out, please check your network and try again later", "connection_interrupted_partial": "Connection interrupted, attempting to process received content", "connection_interrupted_partial_display": "Connection interrupted, displaying partial content", "connection_interrupted_unrecoverable": "Connection interrupted, and unable to process received content", "mindmap_generation_failed": "Failed to generate mind map, please try again later", "summary_generation_failed": "Failed to generate summary, please try again later", "export_png_failed": "Error exporting PNG, please check console for details", "export_md_failed": "Error exporting Markdown, please check console for details", "generate_image_failed": "Error generating image", "check_console": "Please check console for details", "empty_content_message": "<div style=\"padding: 15px; color: #666; border-radius: 4px;\"><p style=\"font-size: 15px;\">The current model failed to generate valid content. Please try the following solutions:</p><ul style=\"font-size: 14px;\"><li>Try again with a different AI model</li><li>Check if the chapter content is valid</li><li>Try again later</li></ul></div>", "token_limit_exceeded": "Content length exceeds model limits. Please shorten content or use a model that supports longer context", "input_token_exceeded": "Input content exceeds model capacity (estimated {estimated} tokens, maximum allowed {max} tokens). Please reduce content or choose a model that supports longer context.", "output_token_exceeded": "Estimated output exceeds model capacity (estimated output {estimated} tokens, maximum allowed {max} tokens). Please reduce content or choose a model that supports longer outputs."}, "loading": {"generating_image": "Generating image, please wait...", "converting_image": "Converting to image...", "saving_image": "Saving image...", "processing_image": "Processing image...", "rendering_graph": "Rendering graph...", "adjusting_graph": "Adjusting graph...", "capturing_graph": "Capturing graph..."}, "node_chat": {"title": "Node Explanation & Q&A", "default_question": "Please explain the meaning and role of this node in the text", "placeholder": "Please enter your question...", "ask_button": "Ask", "close": "Close", "loading": "Thinking...", "error_no_content": "No context content available", "error_api_failed": "API call failed, please retry", "error_credits_insufficient": "Insufficient credits for Q&A", "credits_cost": "This Q&A will cost approximately", "send": "Send"}, "graph_node_chat": {"title": "Knowledge Graph Q&A", "default_question": "Please explain the meaning of this node in the text and its role in the knowledge graph", "placeholder": "Enter your question...", "close": "Close", "loading": "Loading...", "error_no_content": "No available context content", "error_api_failed": "API call failed, please try again", "error_credits_insufficient": "Insufficient credits to continue Q&A", "send": "Send"}, "text_chat": {"title": "Text Q&A", "placeholder": "Enter your question...", "send": "Send", "loading": "Loading...", "explain_selected": "Please explain the meaning and role of the selected text \"{text}\" in the current context", "error_api_failed": "API call failed, please try again", "error_credits_insufficient": "Insufficient credits to continue Q&A", "close": "Close"}}, "credit_history": {"title": "Credit Usage History", "loading": "Loading...", "no_records": "No credit usage records", "login_required": "Please login to view credit history", "auth_error": "Authentication expired, please refresh page or login again", "load_failed": "Loading failed", "operation_time": "Operation Time", "operation_type": "Operation Type", "book_name": "Audio", "credits_change": "Credits Change", "current_credits": "Current Credits", "current_credits_label": "Current Credits:", "prev_page": "Previous", "next_page": "Next", "page_info": "Page {current} of {total}", "operations": {"summary": "AI Summary", "mindmap": "AI Mind Map", "epub_extract": "EPUB Parsing", "recharge": "Credit Recharge", "knowledgegraph": "AI Knowledge Graph", "node_chat": "Mindmap Q&A", "graph_node_chat": "Graph Q&A", "text_chat": "Text Q&A", "batch_summary": "<PERSON><PERSON>"}}, "payment": {"title": "Credit Packages", "subtitle": "Choose the credit package that's right for you", "package_basic": "Basic Package", "package_standard": "Standard Package", "package_premium": "Premium Package", "package_basic_desc": "Suitable for light use", "package_standard_desc": "Most popular choice", "package_premium_desc": "Ideal for heavy use", "credits": "credits", "select": "Select", "payment_title": "Payment", "payment_subtitle": "Choose the credit package that's right for you", "payment_wechat": "WeChat Pay", "payment_alipay": "Alipay", "payment_check": "Checking payment status...", "payment_success": "Payment successful!", "payment_failed": "Payment failed. Please try again.", "close": "Close", "back": "Back to packages", "popular_tag": "Most Popular", "payment_subtitle_qr": "Scan the QR code to contact me if you use WeChat", "payment_subtitle_footer": "Wait for redirection after selecting a plan. Please retry if it fails.", "creating_order": "Creating order...", "cannot_show_options": "Cannot display recharge options, please refresh the page or contact customer service", "login_required": "Please login before recharging", "order_failed": "Order creation failed: {0}", "order_error": "Error creating order", "cannot_redirect": "Cannot redirect to payment page", "validation_failed": "Failed to verify payment result, please refresh the page or contact customer service", "recharge_success": "Recharge successful! Added {0} credits", "payment_processing": "Payment is being processed, please refresh the page later to see the result", "payment_incomplete": "Payment not completed, please try again or contact customer service", "payment_error": "Error processing payment result", "payment_cancelled": "Payment cancelled", "order_creating": "Creating order, please wait...", "redirecting": "Redirecting to payment page...", "order_retry": "Failed to create order, please try again", "retry": "Retry", "verifying_payment": "Verifying payment result..."}, "privacy": {"title": "Privacy Policy", "backbutton": "Back to Home", "general": {"heading": "1. Overview", "content": "This Privacy Policy describes how AudioPilot (\"we,\" \"our,\" or \"the Service\") collects, uses, and discloses your information when you use our audio learning assistant services. By using our Service, you agree to the collection and use of information in accordance with the terms outlined in this Privacy Policy."}, "collection": {"heading": "2. Information Collection", "intro": "The information we collect includes but is not limited to:", "account_title": "Account Information:", "account_desc": "When you register for an account, we collect your personally identifiable information (such as email address or name).", "usage_title": "Usage Data:", "usage_desc": "We record how you use our service, including pages accessed, features clicked, podcast subscriptions, and other interactions with our service.", "device_title": "Device Information:", "device_desc": "We may collect information about the device you use, including device type, operating system, and browser type.", "audio_title": "Audio Data:", "audio_desc": "We temporarily process your uploaded audio files and recordings to provide transcription, translation, and analysis services."}, "usage": {"heading": "3. Information Usage", "intro": "We use the collected information to:", "purpose1": "Provide, maintain, and improve our audio learning services, including podcast subscriptions, audio analysis, and smart Q&A", "purpose2": "Create and maintain your account", "purpose3": "Process your audio content to provide transcription, translation, and grammar analysis services", "purpose4": "Notify you about changes to our service", "purpose5": "Provide customer support", "purpose6": "Monitor and analyze usage patterns and trends", "purpose7": "Manage the credit system and payment services"}, "sharing": {"heading": "4. Information Sharing", "intro": "We do not sell, rent, or otherwise share your personal information with third parties, except in the following circumstances:", "providers_title": "Service Providers:", "providers_desc": "We may share information with third-party service providers that help us provide our services, such as cloud storage providers, AI service providers, and payment processors.", "legal_title": "Compliance and Protection:", "legal_desc": "We may disclose your information if required to comply with legal obligations, respond to legal processes, or protect our rights, property, or safety.", "business_title": "Business Transfers:", "business_desc": "If we are involved in a merger, acquisition, or asset sale, your information may be transferred as part of that transaction."}, "security": {"heading": "5. Data Security", "content": "We take reasonable measures to protect your personal information and audio data from unauthorized access, use, or disclosure. However, please note that no method of transmission over the Internet or electronic storage is 100% secure. We cannot guarantee the absolute security of your information."}, "retention": {"heading": "6. Data Retention", "content1": "We will retain your personal information as long as your account is active or as needed to provide services. After you request deletion of your account, we will delete or anonymize your personal information within a reasonable time, unless we are required by law to retain this information.", "content2": "We do not permanently retain your uploaded audio files, recordings, and the transcription, translation, and analysis results generated from audio content. Related data will be deleted according to our data retention policy after audio processing is completed.", "content3": "Podcast subscriptions and bookmark information will be saved in your account until you choose to unsubscribe or delete bookmarks."}, "rights": {"heading": "7. Your Rights", "intro": "Depending on applicable laws in your region, you may have the following rights:", "access": "Access your personal information", "correct": "Correct inaccurate personal information", "delete": "Delete your personal information", "contact": "To exercise your rights, please contact us using the contact information provided below."}, "cookies": {"heading": "8. Cookies and Similar Technologies", "content": "We use cookies and similar technologies to track and remember your preferences and understand service usage. You can control the acceptance or rejection of cookies through your browser settings, but doing so may affect certain service features."}, "thirdparty": {"heading": "9. Third-Party Services", "content": "Our service may contain links to third-party websites or services, including podcast sources and RSS subscriptions. We are not responsible for the privacy policies or content of these websites. We recommend that you review the privacy policies of any third-party websites you visit."}, "changes": {"heading": "10. Privacy Policy Changes", "content": "We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page. We recommend that you review this Privacy Policy periodically for any changes. Changes to this policy are effective when they are posted on this page."}, "contact": {"heading": "11. Contact Us", "intro": "If you have any questions about this Privacy Policy, please contact us:", "email": "Email: <EMAIL>"}}, "terms": {"title": "Terms of Service", "backbutton": "Back to Home", "acceptance": {"heading": "1. Acceptance of Terms", "content": "By accessing or using our services, you agree to be bound by these terms and our Privacy Policy. If you do not agree to these terms, you must not use our services."}, "description": {"heading": "2. Service Description", "content": "AudioPilot is an AI-powered audio learning assistant platform. The service supports users in subscribing to podcasts, uploading audio files, online recording, and provides audio transcription, translation, grammar analysis, and smart Q&A features. We use a credit-based payment model where credits are valid for 30 days. We reserve the right to modify, suspend, or terminate the service at any time without notice."}, "account": {"heading": "3. User Accounts", "accurate": "You are required to provide accurate, complete, and up-to-date registration information.", "security": "You are responsible for maintaining the confidentiality of your personal account and password.", "responsibility": "You agree to be responsible for all activities that occur under your account."}, "usage": {"heading": "4. Service Usage", "intro": "You agree to use the service only for lawful purposes and in accordance with the following provisions:", "legal": "Do not upload or transmit any illegal, harmful, defamatory, or third-party intellectual property infringing audio content, or you will be fully responsible for the consequences.", "unauthorized": "Do not attempt to gain unauthorized access to our systems, networks, or underlying code.", "automated": "Do not use automated scripts, bots, or other means to collect data from the service without authorization.", "security": "Do not attempt to circumvent any security or access controls implemented to protect the integrity of the service.", "credits": "Do not obtain service access through any illegal means, such as illegally manipulating user credits; otherwise, we have the right to terminate your service at any time.", "audio": "Only upload audio content that you own the rights to use, or content for which you have obtained appropriate permissions.", "educational": "Use the service for lawful purposes such as language learning and educational research."}, "ip": {"heading": "5. Intellectual Property", "content": "All content, features, and functionality in the service, including but not limited to text, graphics, logos, and software, are the exclusive property of us or our licensors and are protected by intellectual property laws. You may not copy, distribute, modify, or create any derivative works without explicit written permission."}, "data": {"heading": "6. Data Processing and Privacy", "content": "By using the service, you agree to our collection, storage, and processing of data in accordance with our Privacy Policy. You retain ownership of your uploaded audio content, but you grant us a license to temporarily use, store, and process the audio content to provide transcription, translation, and analysis services."}, "credits": {"heading": "7. Credits and Payment", "content": "Our service uses a credit-based payment model. Credits are valid for 30 days after purchase, and expired credits will automatically become invalid. Each AI operation (transcription, translation, smart Q&A, etc.) will consume corresponding credits. Credit consumption standards may be adjusted based on service costs, and we will notify users in advance."}, "disclaimer": {"heading": "8. <PERSON><PERSON><PERSON>", "content": "The service is provided 'as is' and 'as available' without any warranties of any kind, express or implied, including but not limited to warranties of merchantability, fitness for a particular purpose, or non-infringement. We do not guarantee the absolute accuracy of AI transcription and translation results, nor do we guarantee that the service will be uninterrupted, timely, secure, or error-free."}, "limitation": {"heading": "9. Limitation of Liability", "content": "To the maximum extent permitted by law, we shall not be liable for any direct, indirect, incidental, special, or punitive damages arising out of the use of the service."}, "indemnity": {"heading": "10. Indemnification", "content": "You agree to defend, indemnify, and hold harmless us and our affiliates, licensors, and service providers from any claims, liabilities, damages, expenses, or costs arising from your violation of these terms or use of the service."}, "law": {"heading": "11. Governing Law and Jurisdiction", "content": "These terms are governed by and construed in accordance with the laws of China. Any legal action or proceeding arising out of these terms or the service shall be brought exclusively in the courts of China."}, "changes": {"heading": "12. Changes to Terms", "content": "We reserve the right to modify these terms at any time. Modified terms will be effective immediately upon posting on the website. Your continued use of the service constitutes acceptance of the modified terms."}, "contact": {"heading": "13. Contact Us", "intro": "If you have any questions about these Terms of Service, please contact us at:", "email": "Email: <EMAIL>"}}, "customPrompts": "Custom Prompts", "customPromptsTitle": "Custom Prompts Settings", "summaryPrompt": "Summary Prompt", "mindmapPrompt": "Mind Map Prompt", "knowledgegraphPrompt": "Knowledge Graph Prompt", "savePrompts": "Save", "resetToDefault": "Reset to De<PERSON>ult", "summaryPromptHelp": "Custom prompt for generating content summaries. Leave empty to use system default prompt.", "mindmapPromptHelp": "Custom prompt for generating mind maps. Leave empty to use system default prompt.", "knowledgegraphPromptHelp": "Custom prompt for generating knowledge graphs. Leave empty to use system default prompt.", "promptPlaceholder": "Enter custom prompt...\\n\\nLeave empty to use system default prompt", "promptsSaved": "Custom prompts saved", "promptsReset": "Prompts reset to default", "promptsSaveError": "Save failed, please try again", "settings": {"title": "Settings", "snippet_duration": "Audio Snippet Duration", "seconds": "seconds", "snippet_description": "Set the duration of audio snippets generated by the 'Ask anytime' feature (3-10 seconds)"}, "credit_batch": {"loading": "Loading", "no_batches": "No credit batches available", "source": "Source", "original_credits": "Original Credits", "remaining_credits": "Remaining Credits", "created_time": "Created Time", "expires_time": "Expires Time", "days_remaining": "Days Remaining", "footer_info": "Credits are used on a first-in-first-out basis, please pay attention to expiration times", "source_purchase": "Credit Purchase", "source_gift": "System Gift", "source_admin": "Admin Add", "source_recharge": "Credit Recharge", "source_newuser": "New User Reward", "days": "days", "load_failed": "Failed to load credit expiry information, please try again later", "login_required": "Please login first to view credit expiry"}}