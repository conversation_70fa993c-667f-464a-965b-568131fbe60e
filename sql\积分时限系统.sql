-- ========================================
-- 积分时限管理系统完整SQL脚本
-- 创建日期: 2025-06-20
-- 说明: 为现有积分系统增加时间限制功能，所有积分都有30天有效期
-- ========================================

-- 1. 创建积分批次表
CREATE TABLE IF NOT EXISTS public.credit_batches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    credits_amount INTEGER NOT NULL,
    remaining_credits INTEGER NOT NULL,
    source_type VARCHAR(50) NOT NULL DEFAULT 'purchase', -- 'purchase', 'signup_bonus', 'admin_add'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_expired BOOLEAN DEFAULT FALSE,
    order_id VARCHAR(255),
    description TEXT
);

-- 启用行级安全
ALTER TABLE public.credit_batches ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Users can view own batches" ON public.credit_batches
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage batches" ON public.credit_batches
    FOR ALL USING (auth.role() = 'service_role');

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_credit_batches_user_id ON public.credit_batches(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_batches_expires_at ON public.credit_batches(expires_at);
CREATE INDEX IF NOT EXISTS idx_credit_batches_user_expires ON public.credit_batches(user_id, expires_at) WHERE NOT is_expired;

-- 2. 修改credit_operations表，添加batch_id字段
ALTER TABLE public.credit_operations 
ADD COLUMN IF NOT EXISTS batch_id UUID REFERENCES public.credit_batches(id);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_credit_operations_batch_id ON public.credit_operations(batch_id);

-- 3. 创建积分管理函数

-- 添加带有效期的积分
CREATE OR REPLACE FUNCTION public.add_credits_with_expiry(
    p_user_id UUID,
    p_credits INTEGER,
    p_source_type VARCHAR(50) DEFAULT 'purchase',
    p_validity_days INTEGER DEFAULT 30,
    p_order_id VARCHAR(255) DEFAULT NULL,
    p_description TEXT DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_batch_id UUID;
    v_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
    -- 计算到期时间
    v_expires_at := NOW() + INTERVAL '1 day' * p_validity_days;
    
    -- 创建积分批次
    INSERT INTO public.credit_batches (
        user_id, credits_amount, remaining_credits, source_type, 
        expires_at, order_id, description
    ) VALUES (
        p_user_id, p_credits, p_credits, p_source_type,
        v_expires_at, p_order_id, p_description
    ) RETURNING id INTO v_batch_id;
    
    -- 更新用户总积分
    INSERT INTO public.user_credits (user_id, credits, created_at, updated_at)
    VALUES (p_user_id, p_credits, NOW(), NOW())
    ON CONFLICT (user_id) 
    DO UPDATE SET 
        credits = user_credits.credits + p_credits,
        updated_at = NOW();
    
    RETURN v_batch_id;
END;
$$;

-- 按先进先出原则扣减积分
CREATE OR REPLACE FUNCTION public.deduct_credits_fifo(
    p_user_id UUID,
    p_credits_to_deduct INTEGER
) RETURNS TABLE(
    success BOOLEAN,
    remaining_credits INTEGER,
    affected_batches JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_current_credits INTEGER;
    v_remaining_to_deduct INTEGER;
    v_batch_record RECORD;
    v_deduct_from_batch INTEGER;
    v_affected_batches JSONB DEFAULT '[]'::JSONB;
    v_batch_info JSONB;
BEGIN
    -- 检查用户当前积分
    SELECT COALESCE(credits, 0) INTO v_current_credits
    FROM public.user_credits 
    WHERE user_id = p_user_id;
    
    -- 检查积分是否足够
    IF v_current_credits < p_credits_to_deduct THEN
        RETURN QUERY SELECT FALSE, v_current_credits, '[]'::JSONB;
        RETURN;
    END IF;
    
    -- 初始化待扣减积分
    v_remaining_to_deduct := p_credits_to_deduct;
    
    -- 按创建时间先进先出扣减
    FOR v_batch_record IN 
        SELECT id, remaining_credits, created_at
        FROM public.credit_batches
        WHERE user_id = p_user_id 
        AND remaining_credits > 0 
        AND NOT is_expired
        AND expires_at > NOW()
        ORDER BY created_at ASC
    LOOP
        EXIT WHEN v_remaining_to_deduct <= 0;
        
        -- 计算从当前批次扣减的积分
        v_deduct_from_batch := LEAST(v_batch_record.remaining_credits, v_remaining_to_deduct);
        
        -- 更新批次剩余积分
        UPDATE public.credit_batches
        SET remaining_credits = public.credit_batches.remaining_credits - v_deduct_from_batch
        WHERE id = v_batch_record.id;
        
        -- 记录批次信息
        v_batch_info := jsonb_build_object(
            'batch_id', v_batch_record.id,
            'deducted', v_deduct_from_batch,
            'remaining', v_batch_record.remaining_credits - v_deduct_from_batch
        );
        v_affected_batches := v_affected_batches || v_batch_info;
        
        -- 减少待扣减积分
        v_remaining_to_deduct := v_remaining_to_deduct - v_deduct_from_batch;
    END LOOP;
    
    -- 更新用户总积分
    UPDATE public.user_credits
    SET credits = credits - p_credits_to_deduct,
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    -- 获取更新后的积分
    SELECT credits INTO v_current_credits
    FROM public.user_credits 
    WHERE user_id = p_user_id;
    
    RETURN QUERY SELECT TRUE, v_current_credits, v_affected_batches;
END;
$$;

-- 清理过期积分
CREATE OR REPLACE FUNCTION public.expire_credits()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_expired_count INTEGER := 0;
    v_user_record RECORD;
    v_expired_credits INTEGER;
BEGIN
    -- 标记过期的批次
    UPDATE public.credit_batches
    SET is_expired = TRUE
    WHERE expires_at <= NOW() AND NOT is_expired;
    
    GET DIAGNOSTICS v_expired_count = ROW_COUNT;
    
    -- 更新每个用户的总积分
    FOR v_user_record IN
        SELECT DISTINCT user_id
        FROM public.credit_batches
        WHERE is_expired = TRUE AND remaining_credits > 0
    LOOP
        -- 计算用户的过期积分
        SELECT COALESCE(SUM(remaining_credits), 0) INTO v_expired_credits
        FROM public.credit_batches
        WHERE user_id = v_user_record.user_id AND is_expired = TRUE AND remaining_credits > 0;
        
        -- 从用户总积分中扣除过期积分
        IF v_expired_credits > 0 THEN
            UPDATE public.user_credits
            SET credits = GREATEST(0, credits - v_expired_credits),
                updated_at = NOW()
            WHERE user_id = v_user_record.user_id;
            
            -- 将过期批次的剩余积分清零
            UPDATE public.credit_batches
            SET remaining_credits = 0
            WHERE user_id = v_user_record.user_id AND is_expired = TRUE AND remaining_credits > 0;
            
            -- 记录过期操作
            INSERT INTO public.credit_operations (
                user_id, operation_type, model, credits_amount, 
                operation_time, current_credits
            ) VALUES (
                v_user_record.user_id, 'expiry', NULL, -v_expired_credits,
                NOW(), (SELECT credits FROM public.user_credits WHERE user_id = v_user_record.user_id)
            );
        END IF;
    END LOOP;
    
    RETURN v_expired_count;
END;
$$;

-- 获取用户积分详情
CREATE OR REPLACE FUNCTION public.get_user_credits_detail(p_user_id UUID)
RETURNS TABLE(
    total_credits INTEGER,
    earliest_expiry TIMESTAMP WITH TIME ZONE,
    batch_count INTEGER,
    expiring_soon_credits INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_total_credits INTEGER := 0;
    v_earliest_expiry TIMESTAMP WITH TIME ZONE;
    v_batch_count INTEGER := 0;
    v_expiring_soon_credits INTEGER := 0;
BEGIN
    -- 获取总积分
    SELECT COALESCE(credits, 0) INTO v_total_credits
    FROM public.user_credits 
    WHERE user_id = p_user_id;
    
    -- 获取有效批次信息
    SELECT 
        COALESCE(MIN(expires_at), NULL),
        COALESCE(COUNT(*), 0),
        COALESCE(SUM(CASE WHEN expires_at <= NOW() + INTERVAL '7 days' THEN remaining_credits ELSE 0 END), 0)
    INTO v_earliest_expiry, v_batch_count, v_expiring_soon_credits
    FROM public.credit_batches
    WHERE user_id = p_user_id 
    AND remaining_credits > 0 
    AND NOT is_expired
    AND expires_at > NOW();
    
    RETURN QUERY SELECT v_total_credits, v_earliest_expiry, v_batch_count, v_expiring_soon_credits;
END;
$$;

-- 4. 数据迁移：将现有积分转换为批次
DO $$
DECLARE
    v_user_record RECORD;
    v_batch_id UUID;
BEGIN
    -- 为所有现有用户创建积分批次
    FOR v_user_record IN
        SELECT user_id, credits, created_at
        FROM public.user_credits
        WHERE credits > 0
    LOOP
        -- 为现有积分创建30天有效期的批次
        INSERT INTO public.credit_batches (
            user_id, credits_amount, remaining_credits, source_type,
            created_at, expires_at, description
        ) VALUES (
            v_user_record.user_id, v_user_record.credits, v_user_record.credits, 'migration',
            v_user_record.created_at, v_user_record.created_at + INTERVAL '30 days',
            '现有积分迁移，30天有效期'
        ) RETURNING id INTO v_batch_id;
        
        -- 更新现有的积分操作记录，关联到新批次
        UPDATE public.credit_operations
        SET batch_id = v_batch_id
        WHERE user_id = v_user_record.user_id AND batch_id IS NULL
        AND operation_type = 'recharge'
        AND operation_time >= v_user_record.created_at;
    END LOOP;
    
    RAISE NOTICE '积分迁移完成，现有积分已转换为30天有效期的批次';
END;
$$;

-- 5. 设置定时任务清理过期积分（需要pg_cron扩展）
-- 注意：这需要在Supabase控制台中手动启用pg_cron扩展
-- 然后执行以下命令：
-- SELECT cron.schedule('expire-credits', '0 0 * * *', 'SELECT public.expire_credits();');

-- 6. 创建视图方便查询
CREATE OR REPLACE VIEW public.user_credits_summary AS
SELECT 
    u.user_id,
    u.credits as total_credits,
    d.earliest_expiry,
    d.batch_count,
    d.expiring_soon_credits,
    CASE 
        WHEN d.earliest_expiry <= NOW() + INTERVAL '7 days' THEN 'warning'
        WHEN d.earliest_expiry <= NOW() + INTERVAL '1 day' THEN 'danger'
        ELSE 'normal'
    END as expiry_status
FROM public.user_credits u
LEFT JOIN LATERAL public.get_user_credits_detail(u.user_id) d ON true;

-- 7. 权限设置
GRANT EXECUTE ON FUNCTION public.add_credits_with_expiry TO service_role;
GRANT EXECUTE ON FUNCTION public.deduct_credits_fifo TO service_role;
GRANT EXECUTE ON FUNCTION public.expire_credits TO service_role;
GRANT EXECUTE ON FUNCTION public.get_user_credits_detail TO service_role, anon, authenticated;

GRANT SELECT ON public.credit_batches TO anon, authenticated;
GRANT SELECT ON public.user_credits_summary TO anon, authenticated;

-- 8. 验证安装
DO $$
BEGIN
    RAISE NOTICE '========================================';
    RAISE NOTICE '积分时限系统安装完成！';
    RAISE NOTICE '========================================';
    RAISE NOTICE '新增表: credit_batches';
    RAISE NOTICE '新增函数: add_credits_with_expiry, deduct_credits_fifo, expire_credits, get_user_credits_detail';
    RAISE NOTICE '新增视图: user_credits_summary';
    RAISE NOTICE '数据迁移: 现有积分已转换为30天有效期批次';
    RAISE NOTICE '========================================';
    RAISE NOTICE '请手动设置定时任务：';
    RAISE NOTICE '1. 在Supabase控制台启用pg_cron扩展';
    RAISE NOTICE '2. 执行: SELECT cron.schedule(''expire-credits'', ''0 0 * * *'', ''SELECT public.expire_credits();'');';
    RAISE NOTICE '========================================';
END;
$$; 