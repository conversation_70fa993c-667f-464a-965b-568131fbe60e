/* 专门针对 Firefox 浏览器的样式调整 */
@-moz-document url-prefix() {
    /* 基本布局 */
    html, body {
        height: 100%;
        width: 100%;
        overflow: hidden;
    }
    
    body .container {
        display: flex !important;
        width: calc(100% - 40px) !important; /* 增加减去的宽度 */
        height: calc(100vh - 110px) !important;
        margin: 10px 15px !important; /* 左右边距各增加 */
        padding: 0 !important;
        padding-right: 20px !important; /* 与aiContent的右边距匹配 */
        overflow: hidden !important;
        gap: 15px !important;
        box-sizing: border-box !important;
        max-width: 100% !important;
        justify-content: flex-start !important; /* 改为左对齐，避免space-between挤压空间 */
    }
    
    /* 列宽度控制 */
    #chapters {
        width: 20% !important;
        min-width: 20% !important;
        flex: 0 0 20% !important;
        max-width: 20% !important;
        box-sizing: border-box !important;
        margin: 0 !important;
    }
    
    #content {
        width: 45% !important;
        min-width: 45% !important;
        flex: 0 0 45% !important;
        max-width: 45% !important;
        box-sizing: border-box !important;
        margin: 0 !important;
    }
    
    body .container #aiContent,
    html body .container #aiContent,
    #aiContent {
        width: 33% !important;
        min-width: 33% !important;
        flex: 0 0 33% !important;
        max-width: 33% !important;
        margin-right: 20px !important; /* 增加右边距 */
        padding-right: 20px !important; /* 增加右内边距 */
        box-sizing: border-box !important;
        flex-basis: 33% !important;
        flex-grow: 0 !important;
        flex-shrink: 0 !important;
        transition: none !important;
        animation: none !important;
        position: relative !important;
        right: auto !important;
        left: auto !important;
        flex-basis: auto !important; /* 移除冲突的flex-basis */
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
        padding-bottom: 5px !important; /* 减少底部内边距 */
    }
    
    /* 禁用可能导致问题的属性 */
    .column {
        contain: none !important;
        will-change: auto !important;
        float: none !important;
        position: relative !important;
        overflow: auto !important; /* 防止内容溢出 */
    }
    
    /* 按钮修复 - 减少底部间距并调整布局 */
    #generateSummaryBtn, #generateMindmapBtn {
        width: 80% !important;
        margin: 0 auto !important; /* 移除上边距 */
        padding: 10px 15px !important; 
        text-align: center !important;
        display: block !important;
        position: relative !important; /* 改为相对定位而不是绝对定位 */
        bottom: auto !important; /* 移除底部定位 */
        left: auto !important; /* 移除左侧定位 */
        transform: none !important; /* 移除变换 */
    }

    /* 创建一个按钮容器 */
    .button-container {
        width: 100% !important;
        margin-top: 5px !important; /* 减少顶部边距 */
        margin-bottom: 0 !important; /* 移除底部边距 */
        padding: 0 !important; /* 移除内边距 */
        text-align: center !important;
        position: sticky !important; /* 使用sticky定位 */
        bottom: 0 !important; /* 粘附在容器底部 */
        flex-shrink: 0 !important; /* 防止容器被压缩 */
        height: auto !important; /* 自动高度 */
        background-color: transparent !important; /* 确保背景透明 */
    }

    /* AI 内容区域 - 调整高度与间距 */
    #aiSummary, #aiMindmap {
        overflow-y: auto !important;
        overflow-x: hidden !important;
        scrollbar-width: thin !important;
        scrollbar-color: #888 #f1f1f1 !important;
        padding: 15px 20px !important;
        width: 100% !important;
        box-sizing: border-box !important;
        height: calc(100% - 45px) !important; /* 减少为按钮留出的空间 */
        margin-bottom: 0 !important; /* 移除底部外边距 */
        flex: 1 1 auto !important; /* 允许伸缩但保持高度 */
    }

    /* AI 内容区域容器 */
    .ai-tab-content {
        display: none;
        flex-direction: column !important;
        height: 100% !important; /* 修改为100%高度 */
        width: 100% !important;
        overflow: hidden !important;
        position: relative !important;
        padding-bottom: 0 !important;
    }

    .ai-tab-content.active {
        display: flex !important;
    }

    /* AI内容主区域 */
    #aiContent {
        display: flex !important;
        flex-direction: column !important;
        overflow: hidden !important;
    }
    
    /* 修复标签页布局 */
    .ai-tabs {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important; /* 确保所有子元素垂直居中 */
        width: 100% !important;
        border-bottom: 1px solid #e0e0e0 !important;
        box-sizing: border-box !important;
        flex-wrap: wrap !important;
        margin-bottom: 10px !important;
        padding: 5px 0 !important; /* 添加上下内边距 */
    }
    
    .tab-buttons {
        display: flex !important;
        align-items: center !important;
    }
    
    /* 语言选择器修复 */
    .language-selector {
        position: absolute !important;
        right: 130px !important; /* 从110px增加到170px，向左移动更多 */
        width: 75px !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        margin-right: auto !important;
        z-index: 100 !important;
    }
    
    /* 模型选择器修复 */
    .model-selector {
        flex-shrink: 0 !important;
        width: 170px !important;
        box-sizing: border-box !important;
        margin-left: auto !important;
        margin-right: 5px !important;
        align-self: center !important; /* 由 flex-end 改为 center */
        display: flex !important; /* 添加 flex 显示 */
        align-items: center !important; /* 内部元素垂直居中 */
    }
    
    /* 脑图容器修复 */
    .mindmap-container {
        width: 100% !important;
        height: 100% !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
    }
    
    /* 确保滚动条显示正常 */
    #chapters::-moz-scrollbar,
    #content::-moz-scrollbar,
    #aiSummary::-moz-scrollbar,
    #aiMindmap::-moz-scrollbar {
        width: 6px !important;  /* 减小宽度 */
        background-color: #f1f1f1 !important;
    }
    
    #chapters::-moz-scrollbar-thumb,
    #content::-moz-scrollbar-thumb,
    #aiSummary::-moz-scrollbar-thumb,
    #aiMindmap::-moz-scrollbar-thumb {
        background: #bbb !important;
        border-radius: 3px !important;
    }
    
    /* 修改 Firefox 滚动条 - 更新 */
    .column,
    #chapters,
    #content,
    #aiContent,
    #aiSummary, 
    #aiMindmap,
    .mindmap-container,
    .model-dropdown,
    #chapterList,
    #chapterContent {
        scrollbar-width: thin !important; /* 使用细滚动条 */
        scrollbar-color: #bbb #f1f1f1 !important; /* 滚动条颜色和轨道颜色 */
    }
    
    /* 调整模型选择器滚动条 */
    .model-dropdown .model-list {
        scrollbar-width: thin !important;
        scrollbar-color: #bbb #f1f1f1 !important;
        max-height: 300px;
        overflow-y: auto !important;
    }
    
    /* 修复头部样式 */
    header {
        box-sizing: border-box !important;
        width: 100% !important;
        height: 100px !important;
        display: grid !important;
        grid-template-columns: 1fr auto 1fr !important;
        align-items: center !important;
        position: relative !important;
    }
    
    /* 修复按钮容器样式 */
    .buttons-container {
        display: flex !important;
        gap: 15px !important;
        align-items: center !important;
    }
    
    /* 修复上传按钮样式 */
    .upload-label {
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    #aiKnowledgeGraph {
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
    }

}