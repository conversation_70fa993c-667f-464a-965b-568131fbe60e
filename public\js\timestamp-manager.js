// 时间戳管理器
class TimestampManager {
    constructor() {
        this.timestamps = [];
        this.currentTimestamp = null;
        this.isUpdatingFromProgress = false; // 防止循环更新
        this.audioPlayer = null; // 当前音频播放器引用
        
        this.initializeElements();
        this.attachEventListeners();
        this.initializeSidebar();
        
        // 确保国际化翻译生效
        this.translateElements();
        
        console.log('时间戳管理器初始化完成');
    }
    
    initializeElements() {
        // 获取DOM元素
        this.timestampBtn = document.getElementById('timestampBtn'); // 保持兼容性，可能有旧代码使用
        this.generateTimestampBtn = document.getElementById('generateTimestampBtn');
        this.uploadTimestampBtn = document.getElementById('uploadTimestampBtn');
        this.timestampFileInput = document.getElementById('timestampFileInput');
        this.timestampList = document.getElementById('timestampList');
        this.chapterText = document.getElementById('chapterText');
        this.sidebar = document.getElementById('sidebar');
        
        // 音频播放器元素
        this.uploadedPlayer = document.getElementById('uploadedPlayer');
        
        // 用于流式生成的状态
        this.isGenerating = false;
        this.generationAbortController = null;
    }
    
    attachEventListeners() {
        // 生成时间戳按钮点击事件
        if (this.generateTimestampBtn && !this.generateTimestampBtn.hasAttribute('data-generate-event-bound')) {
            this.generateTimestampBtn.addEventListener('click', () => {
                this.handleGenerateTimestamp();
            });
            this.generateTimestampBtn.setAttribute('data-generate-event-bound', 'true');
        }
        
        // 上传时间戳按钮点击事件
        if (this.uploadTimestampBtn && !this.uploadTimestampBtn.hasAttribute('data-upload-event-bound')) {
            this.uploadTimestampBtn.addEventListener('click', () => {
                this.timestampFileInput.click();
            });
            this.uploadTimestampBtn.setAttribute('data-upload-event-bound', 'true');
        }
        
        // 保持兼容性：旧时间戳按钮点击事件
        if (this.timestampBtn && !this.timestampBtn.hasAttribute('data-timestamp-event-bound')) {
            this.timestampBtn.addEventListener('click', () => {
                this.timestampFileInput.click();
            });
            this.timestampBtn.setAttribute('data-timestamp-event-bound', 'true');
        }
        
        // 文件选择事件 - 避免重复绑定
        if (this.timestampFileInput && !this.timestampFileInput.hasAttribute('data-timestamp-file-bound')) {
            this.timestampFileInput.addEventListener('change', (e) => {
                this.handleTimestampFileUpload(e);
            });
            this.timestampFileInput.setAttribute('data-timestamp-file-bound', 'true');
        }
        
        // 监听音频播放事件，实现实时同步 - 避免重复绑定
        if (this.uploadedPlayer && !this.uploadedPlayer.hasAttribute('data-timestamp-sync-bound')) {
            this.uploadedPlayer.addEventListener('timeupdate', () => {
                if (!this.isUpdatingFromProgress) {
                    this.syncCurrentTimestamp();
                }
            });
            this.uploadedPlayer.setAttribute('data-timestamp-sync-bound', 'true');
        }
        
        // 监听音频文件上传事件，启用时间戳按钮 - 避免重复绑定
        if (!window.timestampAudioListenerBound) {
            document.addEventListener('audioFileUploaded', () => {
                this.enableTimestampButtons();
            });
            window.timestampAudioListenerBound = true;
        }
        
        // 监听字幕文件上传成功事件
        document.addEventListener('subtitleFileUploaded', (event) => {
            console.log('TimestampManager 监听到 subtitleFileUploaded 事件', event.detail);
            if (event.detail && event.detail.fileId) {
                // 更新生成按钮状态（isGenerating=false -> 根据 hasSubtitleFile 判断 enable）
                this.updateGenerateButtonState(false);
            }
        });

        // 监听音频文件选择事件
        document.addEventListener('audioFileSelected', (event) => {
            // ... existing code ...
            
            // 更新生成按钮状态
            this.updateGenerateButtonState();
        });
    }
    
    initializeSidebar() {
        // 默认显示侧边栏
        this.showSidebar();
    }
    
    translateElements() {
        if (window.UILanguage) {
            window.translatedPageDone = false; // 强制允许translatePage再次执行
            window.UILanguage.translatePage();
            setTimeout(() => {
                window.translatedPageDone = false;
                window.UILanguage.translatePage();
            }, 100);
        }
    }
    
    enableTimestampButtons() {
        // 启用上传按钮（音频文件上传后）
        if (this.uploadTimestampBtn) {
            this.uploadTimestampBtn.disabled = false;
        }
        if (this.timestampBtn) { // 保持兼容性
            this.timestampBtn.disabled = false;
        }
        console.log('Timestamp upload button enabled');
    }
    
    enableGenerateButton(enabled) {
        if (this.generateTimestampBtn) {
            this.generateTimestampBtn.disabled = !enabled;
            this.generateTimestampBtn.classList.toggle('disabled', !enabled);
        }
    }
    
    async handleTimestampFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        console.log('Starting timestamp file upload:', file.name);
        
        try {
            // 显示加载状态
            this.showLoadingState();
            
            // 读取文件内容
            const content = await this.readFileContent(file);
            
            // 解析时间戳内容
            const result = this.parseTimestampContent(content);
            
            if (result.success) {
                this.timestamps = result.timestamps;
                this.renderTimestamps();
                this.showSidebar();
                this.updateTimestampButtonState(true);
                
                console.log(`Timestamp file parsed successfully, ${this.timestamps.length} time points found`);
                this.showToast('Timestamp file uploaded successfully!', 'success');
            } else {
                console.error('Timestamp file parsing failed:', result.error);
                this.showError(result.error);
                this.showToast('Timestamp file format error', 'error');
            }
        } catch (error) {
            console.error('Timestamp file upload failed:', error);
            this.showError('File reading failed: ' + error.message);
            this.showToast('File upload failed', 'error');
        }
    }
    
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('File reading failed'));
            reader.readAsText(file, 'UTF-8');
        });
    }
    
    parseTimestampContent(content) {
        try {
            const lines = content.split('\n').filter(line => line.trim());
            const timestamps = [];
            let currentChapter = null;
            
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].trim();
                
                // 跳过空行和注释（但不跳过以 # 开头但包含时间戳的行）
                if (!line || (line.startsWith('#') && !line.match(/^#\s*\d{1,2}:\d{2}/)) || line.startsWith('//')) {
                    continue;
                }
                
                // 检查是否是时间戳行（格式：HH:MM:SS 或 MM:SS，可能以 # 开头）
                const timeMatch = line.match(/^#?\s*(\d{1,2}):(\d{2})(?::(\d{2}))?\s*-?\s*(.+)$/);
                if (timeMatch) {
                    const hours = timeMatch[3] ? parseInt(timeMatch[1]) : 0;
                    const minutes = timeMatch[3] ? parseInt(timeMatch[2]) : parseInt(timeMatch[1]);
                    const seconds = timeMatch[3] ? parseInt(timeMatch[3]) : parseInt(timeMatch[2]);
                    
                    const totalSeconds = hours * 3600 + minutes * 60 + seconds;
                    // 如果标题前有 # 号，去掉它
                    const title = timeMatch[4].replace(/^#\s*/, '').trim();
                    
                    currentChapter = {
                        time: totalSeconds,
                        timeDisplay: this.formatTime(totalSeconds),
                        title: title,
                        content: []
                    };
                    
                    timestamps.push(currentChapter);
                } else if (currentChapter && line) {
                    // 这是章节内容行
                    currentChapter.content.push(line);
                }
            }
            
            if (timestamps.length === 0) {
                return {
                    success: false,
                    error: 'No valid timestamp format found. Please ensure format is: MM:SS Title or HH:MM:SS Title'
                };
            }
            
            // 按时间排序
            timestamps.sort((a, b) => a.time - b.time);
            
            return {
                success: true,
                timestamps: timestamps
            };
        } catch (error) {
            return {
                success: false,
                error: 'File parsing error: ' + error.message
            };
        }
    }
    
    renderTimestamps() {
        if (!this.timestampList) return;
        
        if (this.timestamps.length === 0) {
            // 区分是生成失败还是没有时间戳文件
            if (this.isGenerating) {
                // 生成过程中，保持加载状态
                return;
            } else {
                // 显示没有时间戳的提示
                this.timestampList.innerHTML = '<div class="no-timestamps" data-i18n="timestamp.no_file">Please upload a timestamp file first</div>';
                this.translateElements();
                return;
            }
        }
        
        const html = this.timestamps.map((timestamp, index) => `
            <div class="timestamp-item" data-index="${index}" data-time="${timestamp.time}">
                <span class="time">${timestamp.timeDisplay}</span>
                <span class="title">${timestamp.title}</span>
            </div>
        `).join('');
        
        this.timestampList.innerHTML = html;
        
        // 添加点击事件监听
        this.timestampList.addEventListener('click', (e) => {
            const item = e.target.closest('.timestamp-item');
            if (item) {
                const index = parseInt(item.dataset.index);
                const time = parseFloat(item.dataset.time);
                this.jumpToTimestamp(index, time);
            }
        });
    }
    
    jumpToTimestamp(index, time) {
        if (!this.uploadedPlayer) {
            this.showToast('Please upload an audio file first', 'warning');
            return;
        }
        
        console.log(`Jump to timestamp ${index}: ${this.formatTime(time)}`);
        
        try {
            // 设置标志防止循环更新
            this.isUpdatingFromProgress = true;
            
            // 跳转音频播放位置
            this.uploadedPlayer.currentTime = time;
            
            // 开始播放
            if (this.uploadedPlayer.paused) {
                this.uploadedPlayer.play().catch(e => {
                    console.log('Auto-play blocked by browser, user needs to play manually');
                });
            }
            
            // 更新当前时间戳
            this.setCurrentTimestamp(index);
            
            // 显示对应章节内容
            this.showChapterContent(index);
            
            // 延迟重置标志
            setTimeout(() => {
                this.isUpdatingFromProgress = false;
            }, 1000);
            
        } catch (error) {
            console.error('Jump to timestamp failed:', error);
            this.showToast('Jump failed', 'error');
            this.isUpdatingFromProgress = false;
        }
    }
    
    syncCurrentTimestamp() {
        if (!this.uploadedPlayer || this.timestamps.length === 0) return;
        
        const currentTime = this.uploadedPlayer.currentTime;
        
        // 找到当前时间对应的时间戳
        let currentIndex = -1;
        for (let i = this.timestamps.length - 1; i >= 0; i--) {
            if (currentTime >= this.timestamps[i].time) {
                currentIndex = i;
                break;
            }
        }
        
        // 如果当前时间戳改变了，更新显示
        if (currentIndex !== this.currentTimestamp && currentIndex >= 0) {
            this.setCurrentTimestamp(currentIndex);
            this.showChapterContent(currentIndex);
        }
    }
    
    setCurrentTimestamp(index) {
        this.currentTimestamp = index;
        
        // 更新时间戳列表的活动状态
        const items = this.timestampList.querySelectorAll('.timestamp-item');
        items.forEach((item, i) => {
            item.classList.toggle('active', i === index);
        });
    }
    
    showChapterContent(index) {
        if (!this.chapterText || index < 0 || index >= this.timestamps.length) return;
        
        const timestamp = this.timestamps[index];
        
        let html = `<h3>${timestamp.title}</h3>`;
        
        if (timestamp.content && timestamp.content.length > 0) {
            html += timestamp.content.map(line => {
                // 简单的markdown解析
                if (line.startsWith('# ')) {
                    return `<h1>${line.substring(2)}</h1>`;
                } else if (line.startsWith('## ')) {
                    return `<h2>${line.substring(3)}</h2>`;
                } else if (line.startsWith('### ')) {
                    return `<h3>${line.substring(4)}</h3>`;
                } else if (line.startsWith('- ') || line.startsWith('* ')) {
                    return `<li>${line.substring(2)}</li>`;
                } else {
                    return `<p>${line}</p>`;
                }
            }).join('');
            
            // 包装列表项
            html = html.replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>');
        }
        
        this.chapterText.innerHTML = html;
    }
    
    showSidebar() {
        if (this.sidebar) {
            this.sidebar.classList.remove('hidden');
        }
    }
    
    hideSidebar() {
        if (this.sidebar) {
            this.sidebar.classList.add('hidden');
        }
    }
    
    toggleSidebar(show = null) {
        if (show === null) {
            show = this.sidebar.classList.contains('hidden');
        }
        
        if (show) {
            this.showSidebar();
        } else {
            this.hideSidebar();
        }
    }
    
    updateTimestampButtonState(hasFile) {
        if (!this.timestampBtn) return;
        
        // 保持按钮样式和文字不变，不显示状态变化
        // 仅内部记录状态即可
        this.hasTimestampFile = hasFile;
    }
    
    showLoadingState() {
        if (this.timestampList) {
            this.timestampList.innerHTML = '<div class="timestamp-loading"><i class="fas fa-spinner"></i><span data-i18n="timestamp.loading">Loading...</span></div>';
        }
    }
    
    showError(message) {
        if (this.timestampList) {
            this.timestampList.innerHTML = `<div class="timestamp-error">${message}</div>`;
        }
        
        if (this.chapterText) {
            this.chapterText.innerHTML = `<div class="chapter-error">${message}</div>`;
        }
    }
    
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
    }
    
    showToast(message, type = 'info') {
        // 如果存在全局的toast函数，使用它
        if (window.audioApp && window.audioApp.showToast) {
            window.audioApp.showToast(message, type);
        } else {
            console.log(`Toast (${type}): ${message}`);
        }
    }
    
    // 公开API方法
    getCurrentTimestamp() {
        return this.currentTimestamp >= 0 ? this.timestamps[this.currentTimestamp] : null;
    }
    
    getAllTimestamps() {
        return this.timestamps;
    }
    
    hasTimestamps() {
        return this.timestamps.length > 0;
    }
    
    // 生成时间戳处理
    async handleGenerateTimestamp() {
        if (this.isGenerating) {
            console.log('Already generating timestamps');
            return;
        }
        
        // 🚀 立即显示生成状态，提供即时反馈
        this.isGenerating = true;
        this.generationAbortController = new AbortController();
        this.updateGenerateButtonState(true);
        this.showGeneratingState();
        
        try {
            // 检查是否有字幕文件
            if (!this.hasSubtitleFile()) {
                this.showToast(window.UILanguage ? window.UILanguage.getText('audio.upload_srt_first') : 'Please upload SRT subtitle file first', 'warning');
                return;
            }
            
            // 双阶段积分校验：前端缓存 -> 后端缓存
            const creditsManager = window.CreditsManager;
            if (!creditsManager) {
                console.warn('积分管理器未初始化，跳过积分检查');
            } else {
                const required = 1;
                let cached = creditsManager.getCachedCredits(); if (cached === null) cached = 0;
                if (cached < required) { 
                    creditsManager.showInsufficientCreditsAlert(cached); 
                    return; 
                }
                try { 
                    const fast = await creditsManager.fetchCreditsFromBackendCache(); 
                    if (fast < required) { 
                        creditsManager.showInsufficientCreditsAlert(fast); 
                        return; 
                    } 
                } catch (err) { 
                    creditsManager.showInsufficientCreditsAlert(cached); 
                    return; 
                }
            }
            // 全局统一检查
            if (window.checkCredits) {
                const hasCredits = await window.checkCredits('generate_timestamps');
                if (!hasCredits) {
                    return;
                }
            }
            
            // 发起生成请求
            await this.generateTimestampsFromSubtitle();
            
        } catch (error) {
            console.error('Generate timestamps failed:', error);
            this.showGenerationError(error.message || 'Unknown error occurred');
        } finally {
            this.isGenerating = false;
            this.generationAbortController = null;
            this.updateGenerateButtonState(false);
        }
    }
    
    hasSubtitleFile() {
        // 优先检查字幕管理器中是否已有上传成功的文件ID
        if (window.subtitleManager && window.subtitleManager.subtitleFileId) {
            return true;
        }
        
        // 兼容旧的检查逻辑
        if (window.audioApp && window.audioApp.subtitleFileId) {
            return true;
        }
        
        return false;
    }
    
    async generateTimestampsFromSubtitle() {
        // 获取字幕文件ID
        let subtitleFileId;
        // 优先使用新的 SubtitleManager 管理的文件ID
        if (window.subtitleManager && window.subtitleManager.subtitleFileId) {
            subtitleFileId = window.subtitleManager.subtitleFileId;
        } else if (window.audioApp && window.audioApp.subtitleFileId) {
            subtitleFileId = window.audioApp.subtitleFileId;
        } else {
            throw new Error('No subtitle file found');
        }

        // 显示连接状态
        this.showConnectingState();
        // 启动网络超时计时器
        const timeoutId = setTimeout(() => {
            this.generationAbortController.abort();
            this.showNetworkError();
            this.showToast(window.UILanguage ? window.UILanguage.getText('audio.network_error') : 'Network timeout, please try again later.', 'error');
        }, 30000);

        try {
            // 发起生成请求
            const response = await fetch('/api/generate-timestamps', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify({ subtitle_file_id: subtitleFileId }),
                signal: this.generationAbortController.signal
            });

            // 网络已建立
            clearTimeout(timeoutId);
            this.showConnectionEstablishedState();
            this.showGeneratingState();

            if (!response.ok) {
                throw new Error(`Server error: ${response.status}`);
            }

            // 处理流式响应
            await this.handleStreamingResponse(response);
        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('Generation cancelled');
            } else {
                throw error;
            }
        } finally {
            clearTimeout(timeoutId);
        }
    }
    
    async handleStreamingResponse(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        this.timestamps = [];
        let buffer = '';
        
        try {
            while (true) {
                const { done, value } = await reader.read();
                
                if (done) break;
                
                buffer += decoder.decode(value, { stream: true });
                
                // 按行分割处理
                const lines = buffer.split('\n');
                buffer = lines.pop() || ''; // 保留最后一个可能不完整的行
                
                for (const line of lines) {
                    if (line.trim()) {
                        await this.processStreamingLine(line.trim());
                    }
                }
            }
            
            // 处理剩余的buffer
            if (buffer.trim()) {
                await this.processStreamingLine(buffer.trim());
            }
            
            // 完成时更新显示 - 只有在成功生成时间戳时才调用
            if (this.timestamps.length > 0) {
                this.onGenerationComplete();
            } else {
                // 没有生成任何时间戳，显示失败信息
                this.showGenerationError('No timestamps were generated');
            }
            
        } catch (error) {
            console.error('Streaming error:', error);
            throw error;
        } finally {
            reader.releaseLock();
        }
    }
    
    async processStreamingLine(line) {
        try {
            // 处理Server-Sent Events格式
            let jsonStr = line;
            if (line.startsWith('data: ')) {
                jsonStr = line.substring(6); // 移除"data: "前缀
            }
            
            // 跳过空行
            if (!jsonStr.trim()) {
                return;
            }
            
            // 尝试解析JSON数据
            const data = JSON.parse(jsonStr);
            
            if (data.type === 'timestamp') {
                // 收到新的时间戳数据
                const timestampData = {
                    time: data.time,
                    timeDisplay: this.formatTime(data.time),
                    title: data.title,
                    content: Array.isArray(data.content) ? data.content : (data.content ? data.content.split('\n') : [])
                };
                
                this.timestamps.push(timestampData);
                
                // 实时更新显示
                this.renderStreamingTimestamp(timestampData);
                
            } else if (data.type === 'timestamp_update') {
                // 收到时间戳内容更新数据
                const existingIndex = this.timestamps.findIndex(t => t.time === data.time);
                if (existingIndex !== -1) {
                    // 更新现有时间戳的内容
                    this.timestamps[existingIndex].content = Array.isArray(data.content) ? data.content : (data.content ? data.content.split('\n') : []);
                    console.log(`📝 更新时间戳内容: ${data.time}s - ${data.title} (内容行数: ${this.timestamps[existingIndex].content.length})`);
                } else {
                    // 如果找不到对应的时间戳，作为新时间戳添加
                    const timestampData = {
                        time: data.time,
                        timeDisplay: this.formatTime(data.time),
                        title: data.title,
                        content: Array.isArray(data.content) ? data.content : (data.content ? data.content.split('\n') : [])
                    };
                    this.timestamps.push(timestampData);
                    this.renderStreamingTimestamp(timestampData);
                }
                
            } else if (data.type === 'progress') {
                // 更新进度
                this.updateGenerationProgress(data.progress, data.message);
                
            } else if (data.type === 'complete') {
                // 生成完成
                console.log('Generation completed');
                
                // 显示积分使用信息（如果有）
                if (data.credits_info) {
                    const creditsInfo = data.credits_info;
                    console.log(`积分扣减: ${creditsInfo.credits_deducted}, 剩余积分: ${creditsInfo.remaining_credits}, 使用tokens: ${creditsInfo.tokens_used}`);
                    
                    // 可选：在UI上显示积分信息
                    if (creditsInfo.credits_deducted > 0) {
                        this.showToast(`生成完成，消耗 ${creditsInfo.credits_deducted} 积分 (${creditsInfo.tokens_used} tokens)`, 'info');
                    }
                    
                    // 触发积分更新事件，通知其他组件更新积分显示
                    if (creditsInfo.remaining_credits !== undefined) {
                        document.dispatchEvent(new CustomEvent('creditsUpdated', { 
                            detail: { remaining: creditsInfo.remaining_credits } 
                        }));
                    }
                }
                
            } else if (data.type === 'error') {
                // 服务器返回错误
                throw new Error(data.error || data.message || 'Generation failed');
            }
            
        } catch (error) {
            // 如果不是JSON格式，可能是普通文本状态信息
            console.log('Streaming info:', line);
        }
    }
    
    renderStreamingTimestamp(timestampData) {
        if (!this.timestampList) return;
        
        // 如果是第一个时间戳，清空加载状态
        if (this.timestamps.length === 1) {
            this.timestampList.innerHTML = '';
        }
        
        // 创建时间戳元素
        const index = this.timestamps.length - 1;
        const timestampElement = document.createElement('div');
        timestampElement.className = 'timestamp-item streaming';
        timestampElement.setAttribute('data-index', index);
        timestampElement.setAttribute('data-time', timestampData.time);
        timestampElement.innerHTML = `
            <span class="time">${timestampData.timeDisplay}</span>
            <span class="title">${timestampData.title}</span>
        `;
        
        // 添加点击事件
        timestampElement.addEventListener('click', (e) => {
            const index = parseInt(e.currentTarget.getAttribute('data-index'));
            const time = parseInt(e.currentTarget.getAttribute('data-time'));
            this.jumpToTimestamp(index, time);
        });
        
        // 添加到列表
        this.timestampList.appendChild(timestampElement);
        
        // 滚动到新添加的元素
        timestampElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        
        // 移除streaming类以触发动画完成
        setTimeout(() => {
            timestampElement.classList.remove('streaming');
        }, 500);
    }
    
    updateGenerationProgress(progress, message) {
        // 更新生成进度显示
        const progressElement = this.timestampList.querySelector('.timestamp-generating');
        if (progressElement) {
            const textElement = progressElement.querySelector('.generating-text');
            if (textElement) {
                textElement.textContent = window.UILanguage ? window.UILanguage.getText('audio.generating_timestamp', 'Generating timestamps...') : 'Generating timestamps...';
                // 应用翻译
                this.translateElements();
            }
        }
    }
    
    onGenerationComplete() {
        console.log(`Generated ${this.timestamps.length} timestamps`);
        
        // 确保时间戳按时间排序
        this.timestamps.sort((a, b) => a.time - b.time);
        
        // 重新渲染以确保索引正确
        this.renderTimestamps();
        
        // 显示侧边栏
        this.showSidebar();
        
        // 更新按钮状态
        this.updateTimestampButtonState(true);
        
        // 显示成功提示
        this.showToast(`Successfully generated ${this.timestamps.length} timestamps`, 'success');
    }
    
    showGeneratingState() {
        if (this.timestampList) {
            // 使用国际化获取默认提示文本，保证在语言数据未加载时也显示正确
            const defaultText = window.UILanguage ? window.UILanguage.getText('audio.generating_timestamp', 'Generating timestamps...') : 'Generating timestamps...';
            this.timestampList.innerHTML = `
                <div class="timestamp-generating">
                    <div class="generating-spinner"></div>
                    <div class="generating-text" data-i18n="audio.generating_timestamp">${defaultText}</div>
                </div>
            `;
            this.translateElements();
        }
    }
    
    showGenerationError(message) {
        if (this.timestampList) {
            this.timestampList.innerHTML = `
                <div class="timestamp-error">
                    <div class="error-icon">⚠️</div>
                    <div class="error-text">Generation failed: ${message}</div>
                    <div class="error-hint">Please try again or check your subtitle file</div>
                </div>
            `;
        }
        
        // 显示错误提示
        this.showToast('Generation failed: ' + message, 'error');
    }
    
    updateGenerateButtonState(isGenerating) {
        if (!this.generateTimestampBtn) return;
        
        if (isGenerating) {
            this.generateTimestampBtn.disabled = true;
            this.generateTimestampBtn.classList.add('generating');
            const textSpan = this.generateTimestampBtn.querySelector('span');
            if (textSpan) {
                textSpan.setAttribute('data-i18n', 'audio.generating_timestamp');
                // 文本内容留给 translatePage 根据 data-i18n 填充
            }
        } else {
            this.generateTimestampBtn.disabled = !this.hasSubtitleFile();
            this.generateTimestampBtn.classList.remove('generating');
            const textSpan = this.generateTimestampBtn.querySelector('span');
            if (textSpan) {
                textSpan.setAttribute('data-i18n', 'audio.generate_timestamp');
                // 文本内容留给 translatePage 根据 data-i18n 填充
            }
        }
        
        this.translateElements();
    }

    reset() {
        this.timestamps = [];
        this.currentTimestamp = null;
        this.renderTimestamps();
        this.updateTimestampButtonState(false);
        
        if (this.chapterText) {
            this.chapterText.innerHTML = '<div class="no-chapter" data-i18n="chapter.select_timestamp">Please select a timestamp to view the corresponding chapter content</div>';
            this.translateElements();
        }
    }

    showConnectingState() {
        if (this.timestampList) {
            // 使用国际化获取连接提示文本
            const defaultText = window.UILanguage ? window.UILanguage.getText('audio.connecting', 'Connecting...') : 'Connecting...';
            this.timestampList.innerHTML = `
                <div class="timestamp-generating">
                    <div class="generating-spinner"></div>
                    <div class="generating-text" data-i18n="audio.connecting">${defaultText}</div>
                </div>
            `;
            this.translateElements();
        }
    }

    showConnectionEstablishedState() {
        const progressElement = this.timestampList.querySelector('.timestamp-generating');
        if (progressElement) {
            const textElement = progressElement.querySelector('.generating-text');
            if (textElement) {
                textElement.setAttribute('data-i18n', 'audio.connection_established');
                textElement.textContent = window.UILanguage ? window.UILanguage.getText('audio.connection_established', 'Connection established') : 'Connection established';
                // 应用翻译
                this.translateElements();
            }
        }
    }

    showNetworkError() {
        if (this.timestampList) {
            this.timestampList.innerHTML = `
                <div class="timestamp-error">
                    <div class="error-icon">⚠️</div>
                    <div class="error-text" data-i18n="audio.network_error">Network timeout, please try again later.</div>
                </div>
            `;
            this.translateElements();
        }
        this.updateGenerateButtonState(false);
    }
}

// 创建全局时间戳管理器实例
let timestampManager = null;

// 等待DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    timestampManager = new TimestampManager();
    
    // 将实例添加到全局window对象供其他模块使用
    window.timestampManager = timestampManager;
    
    console.log('Timestamp manager created and loaded to global object');
});

// 导出类定义供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TimestampManager;
}