"""
集中配置的速率限制模块 - 2000用户规模版本

此配置适用于月活跃用户约2000人的应用规模。
基于用户分析：80%免费用户(1600人，20次操作/月)，20%付费用户(400人，600次操作/月)
预计月AI操作总量：272,000次
"""
from functools import wraps
from flask import request, current_app
import logging

logger = logging.getLogger(__name__)

# 速率限制配置 - 2000用户规模
# 月AI操作：272,000次，日均：9,067次，时均：378次，分均：6.3次
RATE_LIMITS = {
    # 认证相关 - 大幅放宽以支持大量用户
    "auth": {
        # 注册/邮箱验证 - 支持大量新用户注册
        "register": "25 per minute, 150 per hour, 600 per day",
        "email_verification": "25 per minute, 150 per hour, 600 per day",
        # 登录 - 支持大量用户登录
        "login": "45 per minute, 320 per hour, 1200 per day",
        # 密码重置
        "password_reset": "12 per minute, 60 per hour, 120 per day",
    },
    
    # 内容处理相关 - 核心AI功能，大幅提升限制以支持高并发
    "content": {
        # 上传epub - 大量付费用户需要频繁上传
        "upload_epub": "30 per minute, 200 per hour, 800 per day",
        # AI摘要 - 主要操作，需要支持高频高并发使用
        "summarize": "60 per minute, 450 per hour, 2500 per day",
        # 脑图生成 - 主要操作
        "mindmap": "60 per minute, 450 per hour, 2500 per day",
        # 知识图谱生成 - 主要操作
        "knowledgegraph": "60 per minute, 450 per hour, 2500 per day",
    },
    
    # 积分相关 - 支持超高频的积分操作
    "credits": {
        # 积分查询 - 用户会非常频繁查看
        "get_credits": "80 per minute, 600 per hour, 3200 per day",
        # 积分扣除 - 每次AI操作都需要，是最高频操作
        "deduct_credits": "75 per minute, 550 per hour, 2800 per day",
        # 积分添加 - 支付成功后添加
        "add_credits": "15 per minute, 60 per hour, 150 per day",
        # 积分历史
        "credits_history": "40 per minute, 250 per hour, 800 per day",
    },
    
    # 支付相关 - 支持大量付费用户
    "payment": {
        # 支付套餐查询
        "payment_packages": "30 per minute, 150 per hour, 600 per day",
        # 支付订单创建 - 支持大量付费用户购买
        "create_order": "15 per minute, 70 per hour, 150 per day",
        # 支付状态检查
        "check_status": "30 per minute, 120 per hour, 300 per day",
        # 支付成功回调
        "payment_success": "30 per minute, 120 per hour, 300 per day",
        # 支付取消回调
        "payment_cancel": "30 per minute, 120 per hour, 300 per day",
        # 支付回调（webhook）
        "payment_webhook": "40 per minute, 300 per hour",
        # 重定向到应用
        "redirect_to_app": "30 per minute, 120 per hour, 300 per day",
    },
    
    # 其他API
    "other": {
        # 默认限制 - 超高基础限制以应对大量用户
        "default": "120 per minute, 1200 per hour, 6000 per day",
        # 模型配置获取
        "model_config": "60 per minute, 300 per hour, 1200 per day",
        # CSRF令牌
        "csrf_token": "60 per minute, 250 per hour, 800 per day",
    }
}

def get_limit(category, operation):
    """
    获取指定类别和操作的速率限制
    
    Args:
        category: 限制类别，如 'auth', 'content', 'credits', 'payment', 'other'
        operation: 操作名称，如 'login', 'summarize', 'get_credits' 等
        
    Returns:
        str: 速率限制字符串，如 "5 per minute, 40 per hour, 200 per day"
    """
    category_limits = RATE_LIMITS.get(category, {})
    return category_limits.get(operation, RATE_LIMITS["other"]["default"])

def create_rate_limiter(category, operation):
    """
    创建适合特定操作类型的速率限制装饰器
    
    根据操作类型使用不同的标识符策略：
    - 认证相关操作（auth类别）：始终使用IP地址作为标识符
    - 其他操作（内容、积分、支付等）：优先使用用户ID，无法获取时回退到IP地址
    
    Args:
        category: 限制类别，如 'auth', 'content', 'credits', 'payment', 'other'
        operation: 操作名称，如 'login', 'summarize', 'get_credits' 等
        
    Returns:
        function: 装饰器函数
    """
    # 获取限制值
    limit_value = get_limit(category, operation)
    
    # 返回一个简单的装饰器，它只保存操作类型和限制值
    def decorator(f):
        # 在函数上保存操作类型和限制值，供后续使用
        f._rate_limit_category = category
        f._rate_limit_operation = operation
        f._rate_limit_value = limit_value
        return f
    
    return decorator

def get_auth_limits():
    """
    返回认证相关操作的速率限制配置（兼容旧版API）
    
    Returns:
        dict: 包含各种认证操作的速率限制
    """
    return {
        # 登录尝试限制 - 防止暴力破解，但要支持大量正常用户
        'login': {
            'ip': RATE_LIMITS["auth"]["login"],  # IP级别限制
            'email': "60 per hour, 120 per day"  # 每个邮箱的限制
        },
        # 注册限制 - 防止批量注册，但支持大量正常注册
        'register': {
            'ip': RATE_LIMITS["auth"]["register"],
            'email_domain': "120 per hour, 300 per day"  # 每个邮箱域名的限制
        },
        # 密码重置限制 - 防止滥用
        'password_reset': {
            'ip': RATE_LIMITS["auth"]["password_reset"],
            'email': "12 per hour, 30 per day"
        },
        # 邮箱验证限制
        'email_verification': {
            'ip': RATE_LIMITS["auth"]["email_verification"],
            'email': "30 per hour, 60 per day"
        }
    } 