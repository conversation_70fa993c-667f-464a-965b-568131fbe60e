我的项目在登录注册重置密码等处需要集成cloudflare的验证码

已经在cloudflare的turnstile进行了下面的配置

Site Key

0x4AAAAAABkTvsQTpOrFmwv2

Secret key

0x4AAAAAABkTvvrBrXQF8YhPDjqV3DTT0_M

host name

127.0.0.1
locallhost

并且已经在supabase数据库的attack protection把上面的secret key配置到Captcha secret中。

现在请你帮我修改代码，将cloudflare加载验证配置到项目的登录/注册/重置密码等环节中，原先项目设置了开发环保不使用cloudflare验证码，现在改为无论开发环节还是生产环节，一律使用cloudflare验证码。


请采用行业标准做法进行修改实施。
-------------------------------------------------------------------

我将帮您集成Cloudflare Turnstile验证码到项目的登录、注册、重置密码等环节中。首先让我了解一下项目的现有结构和认证流程。
Search files...
Search files...
让我查看现有的环境配置文件，以了解Cloudflare验证码的配置方式：
Read file: - 副本.env


我发现现有环境配置中没有Cloudflare的配置，但在代码中可以看到项目已经部分集成了Cloudflare Turnstile。现在让我分析问题的根本原因：

1. 代码中已有Cloudflare Turnstile的集成，但有环境判断逻辑，开发环境下会跳过验证码
2. 您提供的Site Key和Secret Key需要在环境配置中正确设置
3. 需要修改代码，移除开发环境跳过验证码的逻辑

让我开始实施修改：
Ran tool
Ran tool
首先，在环境配置文件中添加Cloudflare Turnstile配置：


# Creem API配置
# 以下密钥应在系统环境变量中设置，不要在此处设置实际值
# 开发环境使用测试密钥，生产环境使用正式密钥
# CREEM_API_KEY="creem_test_xxx" # 从系统环境变量获取
CREEM_API_URL="https://test-api.creem.io/v1" # 开发环境URL
# CREEM_WEBHOOK_SECRET="whsec_xxx" # 从系统环境变量获取
CREEM_PUBLIC_URL="https://checkout.creem.io"

->

# Cloudflare Turnstile 验证码配置
# Site Key (可在前端使用，不敏感)
CLOUDFLARE_SITE_KEY="0x4AAAAAABkTvsQTpOrFmwv2"
# Secret Key (敏感信息，需要在后端使用) - 已在Supabase中配置
CLOUDFLARE_SECRET_KEY="0x4AAAAAABkTvvrBrXQF8YhPDjqV3DTT0_M"

# Creem API配置
# 以下密钥应在系统环境变量中设置，不要在此处设置实际值
# 开发环境使用测试密钥，生产环境使用正式密钥
# CREEM_API_KEY="creem_test_xxx" # 从系统环境变量获取
CREEM_API_URL="https://test-api.creem.io/v1" # 开发环境URL
# CREEM_WEBHOOK_SECRET="whsec_xxx" # 从系统环境变量获取
CREEM_PUBLIC_URL="https://checkout.creem.io"

Ran tool
现在修改app.py中的环境配置生成逻辑，确保正确传递Cloudflare配置：

        # Cloudflare Turnstile配置
        dev_site_key = os.getenv('CLOUDFLARE_DEV_SITE_KEY', '0x4AAAAAABBtS_UBBNZfC7Jw')
        prod_site_key = os.getenv('CLOUDFLARE_PROD_SITE_KEY', '0x4AAAAAABdeHiCxxkcdDFWJ')

->

        # Cloudflare Turnstile配置
        cloudflare_site_key = os.getenv('CLOUDFLARE_SITE_KEY', '0x4AAAAAABkTvsQTpOrFmwv2')
        cloudflare_secret_key = os.getenv('CLOUDFLARE_SECRET_KEY', '0x4AAAAAABkTvvrBrXQF8YhPDjqV3DTT0_M')


  // Cloudflare Turnstile配置
  CLOUDFLARE: {{
    // 本地开发环境站点密钥
    DEV_SITE_KEY: "{dev_site_key}",
    // 生产环境站点密钥
    PROD_SITE_KEY: "{prod_site_key}",
    // 当前使用的站点密钥 - 将根据环境自动选择
    SITE_KEY: ""
  }},

->

  // Cloudflare Turnstile配置
  CLOUDFLARE: {{
    // 统一站点密钥 - 开发环境和生产环境使用同一密钥
    SITE_KEY: "{cloudflare_site_key}"
  }},


// 为了兼容auth.js，同时设置_env_格式
window._env_ = {{
  SUPABASE_URL: "{supabase_url}",
  SUPABASE_ANON_KEY: "{supabase_anon_key}",
  CLOUDFLARE_SITE_KEY: "",
  APP_URL: "{app_url}",
  STRIPE_PUBLISHABLE_KEY: "{stripe_key}",
  CREEM_API_URL: "{creem_api_url}"
}};

// 根据当前环境设置正确的Turnstile站点密钥
(function() {{
  const isProd = !['localhost', '127.0.0.1'].includes(window.location.hostname);
  const siteKey = isProd ? 
    window.ENV.CLOUDFLARE.PROD_SITE_KEY : 
    window.ENV.CLOUDFLARE.DEV_SITE_KEY;
  
  window.ENV.CLOUDFLARE.SITE_KEY = siteKey;
  window._env_.CLOUDFLARE_SITE_KEY = siteKey;
  
  console.log(`使用Turnstile ${{isProd ? '生产' : '开发'}}环境密钥: ${{siteKey}}`);
}})();

->

// 为了兼容auth.js，同时设置_env_格式
window._env_ = {{
  SUPABASE_URL: "{supabase_url}",
  SUPABASE_ANON_KEY: "{supabase_anon_key}",
  CLOUDFLARE_SITE_KEY: "{cloudflare_site_key}",
  APP_URL: "{app_url}",
  STRIPE_PUBLISHABLE_KEY: "{stripe_key}",
  CREEM_API_URL: "{creem_api_url}"
}};

// 日志记录当前使用的Turnstile站点密钥
console.log(`使用Turnstile站点密钥: {cloudflare_site_key}`);

现在修改前端代码，移除开发环境跳过验证码的逻辑。先修改log.html中的Turnstile初始化逻辑：

    // 分离 Turnstile 初始化为独立函数
    function initializeTurnstile() {
      console.log('尝试初始化 Turnstile 验证码');
      
      // 检测是否为开发环境
      const isProd = !['localhost', '127.0.0.1'].includes(window.location.hostname);
      
      // 在开发环境下跳过 Turnstile 验证码
      if (!isProd) {
        console.log('[INFO] 开发环境下跳过 Turnstile 验证码');
        
        // 隐藏加载指示器
        document.querySelectorAll('.captcha-loader').forEach(loader => {
          loader.style.display = 'none';
        });
        
        // 直接启用按钮
        document.getElementById('loginButton').disabled = false;
        document.getElementById('requestVerification').disabled = false;
        document.getElementById('sendResetLink').disabled = false;
        
        // 显示开发环境提示
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = '<div class="captcha-dev-notice" style="color: #28a745; font-size: 12px; text-align: center;">开发环境 - 验证码已禁用</div>';
        });
        return;
      }

->

    // 分离 Turnstile 初始化为独立函数
    function initializeTurnstile() {
      console.log('尝试初始化 Turnstile 验证码');
      
      // 移除开发环境跳过逻辑，始终启用Cloudflare验证码
      console.log('[INFO] 开始初始化 Cloudflare Turnstile 验证码');


    // 获取Turnstile站点密钥的函数
    function getTurnstileSiteKey() {
      // 检测当前环境
      const isProd = !['localhost', '127.0.0.1'].includes(window.location.hostname);
      console.log(`当前环境: ${isProd ? '生产环境' : '开发环境'}`);
      console.log(`主机名: ${window.location.hostname}`);
      
      // 优先使用环境变量
      if (window._env_ && window._env_.CLOUDFLARE_SITE_KEY) {
        console.log('使用_env_中的Turnstile密钥');
        return window._env_.CLOUDFLARE_SITE_KEY;
      }
      
      // 其次使用ENV配置
      if (window.ENV && window.ENV.CLOUDFLARE) {
        // 如果有SITE_KEY直接使用
        if (window.ENV.CLOUDFLARE.SITE_KEY) {
          console.log('使用ENV.CLOUDFLARE.SITE_KEY中的Turnstile密钥');
          return window.ENV.CLOUDFLARE.SITE_KEY;
        }
        
        // 否则根据环境选择
        if (isProd && window.ENV.CLOUDFLARE.PROD_SITE_KEY) {
          console.log('使用ENV.CLOUDFLARE.PROD_SITE_KEY中的生产环境Turnstile密钥');
          return window.ENV.CLOUDFLARE.PROD_SITE_KEY;
        } else if (!isProd && window.ENV.CLOUDFLARE.DEV_SITE_KEY) {
          console.log('使用ENV.CLOUDFLARE.DEV_SITE_KEY中的开发环境Turnstile密钥');
          return window.ENV.CLOUDFLARE.DEV_SITE_KEY;
        }
      }
      
      // 最后使用默认值
      console.log(`使用默认${isProd ? '生产' : '开发'}环境Turnstile密钥`);
      
      // 根据环境使用不同密钥
      return isProd 
        ? "0x4AAAAAABdeHiCxxkcdDFWJ" // 生产环境密钥
        : "0x4AAAAAABBtS_UBBNZfC7Jw"; // 开发环境密钥
    }

->

    // 获取Turnstile站点密钥的函数
    function getTurnstileSiteKey() {
      console.log(`主机名: ${window.location.hostname}`);
      
      // 优先使用环境变量
      if (window._env_ && window._env_.CLOUDFLARE_SITE_KEY) {
        console.log('使用_env_中的Turnstile密钥');
        return window._env_.CLOUDFLARE_SITE_KEY;
      }
      
      // 其次使用ENV配置
      if (window.ENV && window.ENV.CLOUDFLARE && window.ENV.CLOUDFLARE.SITE_KEY) {
        console.log('使用ENV.CLOUDFLARE.SITE_KEY中的Turnstile密钥');
        return window.ENV.CLOUDFLARE.SITE_KEY;
      }
      
      // 最后使用您提供的密钥作为默认值
      console.log('使用配置的默认Turnstile密钥');
      return "0x4AAAAAABkTvsQTpOrFmwv2";
    }

现在修改auth.js中的相关逻辑：

    // 获取Cloudflare Turnstile站点密钥的函数
    function getTurnstileSiteKey() {
      // 检测当前环境
      const isProd = !['localhost', '127.0.0.1'].includes(window.location.hostname);
      
      // 优先使用环境变量
      if (window._env_ && window._env_.CLOUDFLARE_SITE_KEY) {
        return window._env_.CLOUDFLARE_SITE_KEY;
      }
      
      // 其次使用ENV配置
      if (window.ENV && window.ENV.CLOUDFLARE) {
        // 如果有SITE_KEY直接使用
        if (window.ENV.CLOUDFLARE.SITE_KEY) {
          return window.ENV.CLOUDFLARE.SITE_KEY;
        }
        
        // 否则根据环境选择
        if (isProd && window.ENV.CLOUDFLARE.PROD_SITE_KEY) {
          return window.ENV.CLOUDFLARE.PROD_SITE_KEY;
        } else if (!isProd && window.ENV.CLOUDFLARE.DEV_SITE_KEY) {
          return window.ENV.CLOUDFLARE.DEV_SITE_KEY;
        }
      }
      
      // 最后使用默认值
      return isProd 
        ? "0x4AAAAAABdeHiCxxkcdDFWJ" // 默认生产环境密钥，应替换为实际的生产环境密钥
        : "0x4AAAAAABBtS_UBBNZfC7Jw"; // 默认开发环境密钥
    }

->

    // 获取Cloudflare Turnstile站点密钥的函数
    function getTurnstileSiteKey() {
      // 优先使用环境变量
      if (window._env_ && window._env_.CLOUDFLARE_SITE_KEY) {
        return window._env_.CLOUDFLARE_SITE_KEY;
      }
      
      // 其次使用ENV配置
      if (window.ENV && window.ENV.CLOUDFLARE && window.ENV.CLOUDFLARE.SITE_KEY) {
        return window.ENV.CLOUDFLARE.SITE_KEY;
      }
      
      // 最后使用您提供的密钥作为默认值
      return "0x4AAAAAABkTvsQTpOrFmwv2";
    }

现在修改登录逻辑，确保始终检查验证码：
修改登录请求，添加验证码参数：

auth.js


        // 简化的登录请求
        const { data, error } = await window.supabaseClient.auth.signInWithPassword({
          email: email,
          password: password
        });

->

        // 登录请求包含验证码
        const { data, error } = await window.supabaseClient.auth.signInWithPassword({
          email: email,
          password: password,
          options: {
            captchaToken: loginCaptchaToken
          }
        });

确保注册流程也正确检查验证码：

        // 只在生产环境或有验证码令牌时添加captchaToken
        if (isProd && captchaToken) {
          signupOptions.options.captchaToken = captchaToken;
        }

->

        // 添加captchaToken到注册选项
        if (captchaToken) {
          signupOptions.options.captchaToken = captchaToken;
        }

现在检查并修改注册验证码检查逻辑：
Ran tool
让我检查注册获取验证码的逻辑：
Read file: public/js/auth.js
我发现注册逻辑中还有开发环境跳过验证码的代码，需要修改：

      // 验证码验证（开发环境跳过）
      const isProd = !['localhost', '127.0.0.1'].includes(window.location.hostname);
      if (isProd && !captchaToken) {
        signupError.textContent = UILanguage.getText('auth.captcha_error');
        signupError.style.display = 'block';
        logDebug('请求验证码失败: 未完成人机验证', 'error');
        // 重置按钮状态
        requestVerificationBtn.disabled = false;
        requestVerificationBtn.classList.remove('btn-loading');
        return;
      }

->

      // 验证码验证（始终检查）
      if (!captchaToken) {
        signupError.textContent = UILanguage.getText('auth.captcha_error');
        signupError.style.display = 'block';
        logDebug('请求验证码失败: 未完成人机验证', 'error');
        // 重置按钮状态
        requestVerificationBtn.disabled = false;
        requestVerificationBtn.classList.remove('btn-loading');
        return;
      }


Ran tool
现在修改重置密码逻辑，确保也检查验证码。先查看重置密码模态框的HTML：
Read file: public/log.html
现在我需要修改重置密码模态框中的按钮，默认应该是禁用状态：

        <button type="submit" id="sendResetLink" data-i18n="auth.send_reset_link">发送重置链接</button>

->

        <button type="submit" id="sendResetLink" data-i18n="auth.send_reset_link" disabled>发送重置链接</button>

现在我需要确保登录和注册按钮也默认禁用，直到验证码完成：

log.html

              <button type="submit" id="loginButton" data-i18n="auth.login">登录</button>

->

              <button type="submit" id="loginButton" data-i18n="auth.login" disabled>登录</button>

现在修改Turnstile初始化逻辑，确保在验证码加载失败时也能正确启用按钮：
log.html

      // 确保 turnstile 对象已加载
      if (typeof turnstile === 'undefined') {
        console.log('[ERROR] Turnstile API not available');
        
        // 直接启用按钮，不再依赖验证码
        document.getElementById('loginButton').disabled = false;
        document.getElementById('requestVerification').disabled = false;
        
        // 显示友好的错误消息
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = '<div class="captcha-error" style="color: #666;">' + (UILanguage.getText('auth.captcha_error') || '验证码加载失败，但您仍可以继续登录') + '</div>';
        });
        return;
      }

->

      // 确保 turnstile 对象已加载
      if (typeof turnstile === 'undefined') {
        console.log('[ERROR] Turnstile API not available');
        
        // 显示友好的错误消息，但保持按钮禁用，要求完成验证码
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = '<div class="captcha-error" style="color: #dc3545;">' + (UILanguage.getText('auth.captcha_load_failed') || '验证码加载失败，请刷新页面重试') + '</div>';
        });
        return;
      }

修改超时处理逻辑：

log.html

        // 自动启用登录按钮
        const loginButton = document.getElementById('loginButton');
        if (loginButton) loginButton.disabled = false;
        
        const requestVerification = document.getElementById('requestVerification');
        if (requestVerification) requestVerification.disabled = false;
        
        // 显示友好的错误消息
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = '<div class="captcha-error" style="color: #666;">验证码加载超时，您仍可以继续登录</div>';
        });

->

        // 显示友好的错误消息，但保持按钮禁用
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = '<div class="captcha-error" style="color: #dc3545;">验证码加载超时，请刷新页面重试</div>';
        });

修改错误回调处理：

log.html

        // 自动启用登录按钮
        const loginButton = document.getElementById('loginButton');
        if (loginButton) loginButton.disabled = false;
        
        const requestVerification = document.getElementById('requestVerification');
        if (requestVerification) requestVerification.disabled = false;
        
        // 显示友好的错误消息
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = '<div class="captcha-error" style="color: #666;">验证码加载超时，您仍可以继续登录</div>';
        });

->

        // 显示友好的错误消息，但保持按钮禁用
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = '<div class="captcha-error" style="color: #dc3545;">验证码加载超时，请刷新页面重试</div>';
        });


继续修改重置密码的错误处理：
Read file: public/log.html
修改重置密码验证码的错误处理：

log.html

          'error-callback': function(error) {
            console.log(`[ERROR] Reset password verification failed to load: ${error}`);
            document.querySelector('#resetPasswordModal .captcha-container').innerHTML = 
              '<div class="captcha-error">' + (UILanguage.getText('auth.captcha_error') || '验证失败，请刷新页面') + '</div>';
            document.getElementById('sendResetLink').disabled = false;
          },
          'timeout-callback': function() {
            console.log('[WARN] Reset password verification timed out');
            document.querySelector('#resetPasswordModal .captcha-container').innerHTML = 
              '<div class="captcha-error" style="color: #666;">验证超时，您仍可以继续重置密码</div>';
            document.getElementById('sendResetLink').disabled = false;
          }

->

          'error-callback': function(error) {
            console.log(`[ERROR] Reset password verification failed to load: ${error}`);
            document.querySelector('#resetPasswordModal .captcha-container').innerHTML = 
              '<div class="captcha-error" style="color: #dc3545;">' + (UILanguage.getText('auth.captcha_error') || '验证失败，请刷新页面') + '</div>';
          },
          'timeout-callback': function() {
            console.log('[WARN] Reset password verification timed out');
            document.querySelector('#resetPasswordModal .captcha-container').innerHTML = 
              '<div class="captcha-error" style="color: #dc3545;">验证超时，请刷新页面</div>';
          }

修改异常处理逻辑，不再启用按钮：

log.html

        // 启用替代登录
        document.getElementById('loginButton').disabled = false;
        document.getElementById('requestVerification').disabled = false;
        document.getElementById('sendResetLink').disabled = false;
        
        // 显示更有用的错误信息
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = `<div class="captcha-error" style="color: #666;">验证码加载失败: ${error.message || '未知错误'}，您仍可以继续登录</div>`;
        });

->

        // 显示错误信息，要求用户刷新页面
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = `<div class="captcha-error" style="color: #dc3545;">验证码加载失败: ${error.message || '未知错误'}，请刷新页面重试</div>`;
        });

现在确保重置密码回调函数能够启用按钮：

    // Cloudflare Turnstile 回调函数 - 重置密码表单
    window.onResetCaptchaVerified = function(token) {
      resetCaptchaToken = token;
      logDebug(`重置密码验证码验证成功，Token 长度: ${token.length}`, 'important');
    };

->

    // Cloudflare Turnstile 回调函数 - 重置密码表单
    window.onResetCaptchaVerified = function(token) {
      resetCaptchaToken = token;
      logDebug(`重置密码验证码验证成功，Token 长度: ${token.length}`, 'important');
      
      // 启用重置密码按钮
      const sendResetLinkBtn = document.getElementById('sendResetLink');
      if (sendResetLinkBtn && sendResetLinkBtn.disabled) {
        sendResetLinkBtn.disabled = false;
        logDebug('启用重置密码按钮');
      }
    };

Ran tool
现在检查后端是否需要添加Cloudflare验证码的服务端验证逻辑。由于Supabase已经在数据库层配置了Cloudflare Secret Key，理论上验证应该在Supabase层面完成，但让我确认一下：
Ran tool
让我检查auth.py中是否有验证码相关的验证逻辑：
Ran tool
我发现在api/auth.py中有一个验证码配置端点，让我查看详细内容：
Read file: api/auth.py
基于代码分析，我发现当前的验证码配置端点被设置为`enabled: False`。由于您要求在所有环境都启用Cloudflare验证码，而且Supabase已经在数据库层配置了Secret Key，后端实际上不需要额外的验证逻辑（Supabase会处理验证码验证）。但我需要更新这个配置端点，以反映验证码已启用：


auth.py

        # 这里可以从配置或数据库获取验证码配置
        captcha_config = {
            'enabled': False,  # 是否启用验证码
            'type': 'recaptcha',  # 验证码类型
            'siteKey': ''  # 验证码站点密钥
        }

->

        # 从环境变量获取Cloudflare Turnstile配置
        import os
        cloudflare_site_key = os.getenv('CLOUDFLARE_SITE_KEY', '0x4AAAAAABkTvsQTpOrFmwv2')
        
        captcha_config = {
            'enabled': True,  # 始终启用Cloudflare Turnstile验证码
            'type': 'turnstile',  # 使用Cloudflare Turnstile验证码
            'siteKey': cloudflare_site_key  # Cloudflare站点密钥
        }

现在还需要在语言文件中添加验证码加载失败的错误提示：

zh.json

    "captcha_required": "请先完成人机验证",
    "reset_link_sent": "密码重置链接已发送到您的邮箱，请查收",

->

    "captcha_required": "请先完成人机验证",
    "captcha_load_failed": "验证码加载失败，请刷新页面重试",
    "reset_link_sent": "密码重置链接已发送到您的邮箱，请查收",

en.json
    "loading_captcha": "Loading verification...",
    "captcha_error": "Verification failed, please refresh the page",

->

    "loading_captcha": "Loading verification...",
    "captcha_error": "Verification failed, please refresh the page",
    "captcha_load_failed": "Captcha failed to load, please refresh the page",

Ran tool
现在让我测试配置是否正确。让我检查一下有没有遗漏的地方，特别是重置密码页面的验证码集成：
Ran tool
让我查看重置密码页面：
Ran tool
Read file: public/reset-password.html
我发现重置密码页面`reset-password.html`并没有集成Cloudflare验证码，这个页面是用户通过邮件链接访问的密码重置页面，主要是设置新密码，与登录页面的重置密码模态框不同。这个页面通常不需要验证码，因为用户已经通过邮件验证了身份。

让我总结一下已完成的修改：

## 修改总结

我已按照您的要求，将Cloudflare Turnstile验证码集成到项目的登录、注册、重置密码等环节中，并移除了开发环境跳过验证码的逻辑。以下是具体修改：

### 1. 环境配置更新
- 在`- 副本.env`文件中添加了您提供的Cloudflare配置：
  - `CLOUDFLARE_SITE_KEY="0x4AAAAAABkTvsQTpOrFmwv2"`
  - `CLOUDFLARE_SECRET_KEY="0x4AAAAAABkTvvrBrXQF8YhPDjqV3DTT0_M"`

### 2. 后端配置修改 (`app.py`)
- 更新了环境配置生成逻辑，统一使用您提供的Site Key
- 移除了开发/生产环境分别使用不同密钥的逻辑
- 简化了前端配置，确保一致性

### 3. 前端验证码集成强化
**登录页面 (`public/log.html`)**：
- 移除了开发环境跳过验证码的逻辑
- 登录、注册、重置密码按钮默认设为禁用状态
- 只有完成Cloudflare验证码后才能启用按钮
- 优化了错误处理，验证码加载失败时要求用户刷新页面

**认证脚本 (`public/js/auth.js`)**：
- 更新了站点密钥获取逻辑，优先使用您提供的密钥
- 强化了登录和注册流程的验证码检查
- 登录请求中正确包含了验证码token
- 移除了所有开发环境绕过验证码的代码

### 4. 后端API配置 (`api/auth.py`)
- 更新了验证码配置API，将验证码设置为始终启用
- 配置类型更改为`turnstile`，反映实际使用的验证码类型

### 5. 多语言支持
- 在中英文语言文件中添加了验证码加载失败的错误提示

### 6. 验证码验证流程
- **登录**：必须完成验证码才能提交登录表单
- **注册**：必须完成验证码才能获取邮箱验证码
- **重置密码**：必须完成验证码才能发送重置链接

### 7. 安全增强
- 所有认证相关操作都需要通过Cloudflare Turnstile验证
- 验证码token正确传递给Supabase进行服务端验证
- 无论在开发环境还是生产环境，都强制要求完成验证码

现在您的项目已经完全集成了Cloudflare Turnstile验证码，在登录、注册、重置密码等所有环节都会强制要求用户完成人机验证，有效防止了恶意攻击和滥用。


