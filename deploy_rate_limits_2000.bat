@echo off
echo ============================================================
echo 部署2000用户规模的统一Rate Limit配置到Fly.io
echo ============================================================
echo.

echo 正在设置用户规模环境变量...
fly secrets set USER_SCALE=2000

echo.
echo 正在设置API速率限制环境变量...
echo.

fly secrets set ZHIPU_API_RATE_LIMIT=40
fly secrets set GEMINI_API_RATE_LIMIT=40  
fly secrets set DEEPSEEK_API_RATE_LIMIT=40
fly secrets set QWEN_API_RATE_LIMIT=40
fly secrets set GROK_API_RATE_LIMIT=40
fly secrets set CLAUDE_API_RATE_LIMIT=40
fly secrets set OPENROUTER_API_RATE_LIMIT=40

echo.
echo ============================================================
echo 统一Rate Limit配置部署完成！
echo ============================================================
echo.
echo 配置详情:
echo - 适用规模: 2,000用户
echo - 预期月AI操作: 272,000次
echo - 用户层限制: 已通过统一配置文件管理
echo - API层限制: 40次/秒 (含50%缓冲)
echo.
echo 请检查设置是否成功:
echo   fly secrets list
echo.
pause 