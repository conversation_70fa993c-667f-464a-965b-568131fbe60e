/**
 * 积分批次详情管理器
 * 负责显示用户积分批次信息和有效期详情
 */
(function() {
    'use strict';

    class CreditBatchManager {
        constructor() {
            this.modal = null;
            this.supabase = null;
            this.userId = null;
            this.batchInfo = null;
            this.isShowingModal = false;
            this.needsReinitialization = false;
            this.initializationFailed = false;
            
            // 创建模态框结构
            this.createModalStructure();
            
            // 初始化DOM元素
            this.initDomElements();
            
            // 初始化事件监听器
            this.initEventListeners();
        }

        /**
         * 初始化DOM元素
         */
        initDomElements() {
            this.modal = document.getElementById('creditBatchModal');
            this.closeBtn = document.getElementById('creditBatchClose');
            this.loadingElement = document.getElementById('creditBatchLoading');
            this.emptyElement = document.getElementById('creditBatchEmpty');
            this.contentElement = document.getElementById('creditBatchContent');
            this.tableBody = document.getElementById('creditBatchTableBody');
        }

        /**
         * 创建模态框结构
         */
        createModalStructure() {
            if (document.getElementById('creditBatchModal')) {
                return; // 已存在，不重复创建
            }

            // 创建内联样式，与积分使用记录保持完全一致的样式
            const styleElement = document.createElement('style');
            styleElement.id = 'credit-batch-inline-styles';
            styleElement.textContent = `
                /* 积分有效期模态框样式 - 与积分使用记录统一 */
                #creditBatchModal {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: none;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                }
                
                .credit-batch-container {
                    background-color: white;
                    border-radius: 12px;
                    width: 90%;
                    max-width: 900px;
                    max-height: 90vh;
                    display: flex;
                    flex-direction: column;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    overflow: hidden;
                }
                
                .credit-batch-header {
                    background: #1e88e5;
                    color: white;
                    padding: 16px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    position: relative;
                }
                
                .credit-batch-header h2 {
                    margin: 0;
                    font-size: 18px;
                    font-weight: 600;
                    text-align: center;
                    flex: 1;
                }
                
                .credit-batch-close {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 24px;
                    cursor: pointer;
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: absolute;
                    right: 16px;
                    top: 50%;
                    transform: translateY(-50%);
                }
                
                .credit-batch-close:hover {
                    background: rgba(255, 255, 255, 0.2);
                }
                
                .credit-batch-content {
                    flex: 1;
                    overflow-y: auto;
                    padding: 0;
                }
                
                /* 加载中样式 - 与积分使用记录完全一致 */
                .credit-batch-loading {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 40px;
                    color: #666;
                    flex: 1;
                    min-height: 200px;
                }
                
                .credit-batch-loading span {
                    font-size: 16px;
                    color: #4a5568;
                    font-weight: 500;
                    position: relative;
                }
                
                .credit-batch-loading span::after {
                    content: '';
                    animation: loading-dots 1.5s steps(4, end) infinite;
                    position: absolute;
                    left: 100%;
                    top: 0;
                    width: 20px;
                    text-align: left;
                }
                
                @keyframes loading-dots {
                    0% { content: ''; }
                    25% { content: '.'; }
                    50% { content: '..'; }
                    75% { content: '...'; }
                    100% { content: ''; }
                }
                
                /* 空状态样式 */
                .credit-batch-empty {
                    display: none;
                    padding: 40px;
                    text-align: center;
                    color: #666;
                    font-size: 16px;
                }
                
                /* 表格样式 - 与积分使用记录统一 */
                .batch-details-table {
                    width: 100%;
                    border-collapse: collapse;
                    background: white;
                    border-radius: 0;
                    margin: 0;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                }
                
                .batch-details-table th {
                    background: #f5f5f5 !important;
                    padding: 12px 16px !important;
                    text-align: left !important;
                    font-weight: 600 !important;
                    color: #333 !important;
                    border-bottom: 2px solid #e0e0e0 !important;
                    font-size: 14px !important;
                    white-space: nowrap;
                }
                
                .batch-details-table td {
                    padding: 12px 16px !important;
                    border-bottom: 1px solid #eee !important;
                    font-size: 15px !important;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                
                .batch-details-table .source-cell {
                    width: 15%;
                }
                
                .batch-details-table .credits-cell {
                    text-align: center !important;
                    font-weight: 500 !important;
                    width: 12%;
                }
                
                .batch-details-table .remaining-credits {
                    color: #1e88e5;
                    font-weight: 600;
                }
                
                .batch-details-table .expiring-soon {
                    color: #ff9800;
                    font-weight: 600;
                }
                
                .batch-details-table .date-cell {
                    width: 20%;
                    font-size: 14px;
                }
                
                .batch-details-table .days-cell {
                    text-align: center !important;
                    width: 10%;
                }
                
                .batch-details-table .days-urgent {
                    color: #f44336;
                    font-weight: 600;
                }
                
                .batch-details-table .days-warning {
                    color: #ff9800;
                    font-weight: 500;
                }
                
                .batch-details-table .days-normal {
                    color: #4caf50;
                }
                
                /* 表格行悬停效果 */
                .batch-details-table tbody tr:hover {
                    background-color: #f9f9f9 !important;
                }
                
                /* 底部信息栏样式 - 与积分使用记录一致 */
                .credit-batch-footer {
                    padding: 10px 16px;
                    border-top: 1px solid #eee;
                    background: #f9f9f9;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
                
                .credit-batch-footer-info {
                    font-weight: 500;
                    color: #1e88e5;
                    font-size: 14px;
                }
            `;
            
            // 移除可能存在的旧样式
            const existingStyle = document.getElementById('credit-batch-inline-styles');
            if (existingStyle) {
                existingStyle.remove();
            }
            document.head.appendChild(styleElement);

            const modalHtml = `
                <div id="creditBatchModal">
                    <div class="credit-batch-container">
                        <div class="credit-batch-header">
                            <h2 data-i18n="menu.credit_expiry">积分有效期</h2>
                            <button id="creditBatchClose" class="credit-batch-close">&times;</button>
                        </div>
                        <div class="credit-batch-content">
                            <div id="creditBatchLoading" class="credit-batch-loading">
                                <span data-i18n="credit_batch.loading">加载中</span>
                            </div>
                            <div id="creditBatchEmpty" class="credit-batch-empty" data-i18n="credit_batch.no_batches">
                                暂无积分批次信息
                            </div>
                            <div id="creditBatchContent" style="display: none;">
                                <div style="flex: 1; overflow-y: auto; padding: 0 15px;">
                                    <table class="batch-details-table">
                                        <thead>
                                            <tr>
                                                <th class="source-cell" data-i18n="credit_batch.source">来源</th>
                                                <th class="credits-cell" data-i18n="credit_batch.original_credits">原始积分</th>
                                                <th class="credits-cell" data-i18n="credit_batch.remaining_credits">剩余积分</th>
                                                <th class="date-cell" data-i18n="credit_batch.created_time">获得时间</th>
                                                <th class="date-cell" data-i18n="credit_batch.expires_time">到期时间</th>
                                                <th class="days-cell" data-i18n="credit_batch.days_remaining">剩余天数</th>
                                            </tr>
                                        </thead>
                                        <tbody id="creditBatchTableBody"></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="credit-batch-footer">
                            <div class="credit-batch-footer-info">
                                <span data-i18n="credit_batch.footer_info">Credits are used on a first-in-first-out basis, please pay attention to expiration times</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 将模态框添加到body
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = modalHtml;
            document.body.appendChild(tempDiv.firstElementChild);
        }

        /**
         * 初始化事件监听器
         */
        initEventListeners() {
            // 关闭按钮点击事件
            if (this.closeBtn) {
                this.closeBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.hideModal();
                });
            }
            
            // 点击模态框背景关闭
            if (this.modal) {
                this.modal.addEventListener('click', (e) => {
                    if (e.target === this.modal) {
                        this.hideModal();
                    }
                });
            }
            
            // ESC键关闭模态框
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.modal.style.display === 'flex') {
                    this.hideModal();
                }
            });
        }

        /**
         * 初始化积分批次管理器
         */
        async init() {
            try {
                // 获取Supabase客户端
                this.supabase = window.getSupabaseClient();
                if (!this.supabase) {
                    throw new Error('Supabase客户端未初始化');
                }
                
                try {
                    // 优先从localStorage获取用户信息
                    const isAuthenticated = localStorage.getItem('isAuthenticated');
                    const localUserId = localStorage.getItem('userId');
                    if (isAuthenticated === 'true' && localUserId) {
                        this.userId = localUserId;
                        this.needsReinitialization = false;
                        console.log('积分批次管理器初始化成功，用户ID:', this.userId);
                        return;
                    }
                    
                    throw new Error('用户未登录');
                    
                } catch (authError) {
                    console.warn('获取用户信息失败，积分批次功能将在用户登录后可用:', authError.message);
                    this.needsReinitialization = true;
                    
                    // 监听认证状态变化，在用户登录后重新初始化
                    document.addEventListener('auth-state-changed', (event) => {
                        if (event.detail && event.detail.loggedIn && this.needsReinitialization) {
                            console.log('检测到用户登录，重新初始化积分批次管理器');
                            this.needsReinitialization = false;
                            setTimeout(() => this.reinitialize(), 1000);
                        }
                    });
                }
            } catch (error) {
                console.error('初始化积分批次管理器失败:', error);
                this.initializationFailed = true;
            }
        }
        
        /**
         * 重新初始化积分批次管理器
         */
        async reinitialize() {
            try {
                this.supabase = window.getSupabaseClient();
                if (!this.supabase) {
                    throw new Error('Supabase客户端未初始化');
                }
                
                const isAuthenticated = localStorage.getItem('isAuthenticated');
                const localUserId = localStorage.getItem('userId');
                if (isAuthenticated === 'true' && localUserId) {
                    this.userId = localUserId;
                    this.initializationFailed = false;
                    console.log('积分批次管理器重新初始化成功');
                } else {
                    throw new Error('用户未登录');
                }
            } catch (error) {
                console.error('重新初始化积分批次管理器失败:', error);
                setTimeout(() => {
                    if (this.initializationFailed) {
                        this.reinitialize();
                    }
                }, 3000);
            }
        }

        /**
         * 显示模态框
         */
        async showModal() {
            if (this.isShowingModal) {
                console.log('积分批次模态框正在显示中，忽略重复调用');
                return;
            }
            
            this.isShowingModal = true;
            
            try {
                // 确保用户已认证
                await this.ensureUserIdAvailable();
                
                if (!this.userId) {
                    alert(window.UILanguage?.getText('credit_batch.login_required') || 'Please login first to view credit expiry');
                    this.isShowingModal = false;
                    return;
                }
                
                // 显示模态框
                if (this.modal) {
                    this.modal.style.display = 'flex';
                    document.body.style.overflow = 'hidden';
                }
                
                // 翻译文本
                this.translateModalText();
                
                // 加载批次数据
                await this.loadBatchData();
                
            } catch (error) {
                console.error('Failed to show credit batch modal:', error);
                alert(window.UILanguage?.getText('credit_batch.load_failed') || 'Failed to load credit expiry information, please try again later');
            } finally {
                this.isShowingModal = false;
            }
        }

        /**
         * 隐藏模态框
         */
        hideModal() {
            if (this.modal) {
                this.modal.style.display = 'none';
                document.body.style.overflow = '';
            }
            
            // 重置显示状态
            this.showLoading();
            if (this.contentElement) {
                this.contentElement.style.display = 'none';
            }
            if (this.emptyElement) {
                this.emptyElement.style.display = 'none';
            }
        }

        /**
         * 确保用户ID可用
         */
        async ensureUserIdAvailable() {
            if (this.userId) {
                return;
            }
            
            const isAuthenticated = localStorage.getItem('isAuthenticated');
            const localUserId = localStorage.getItem('userId');
            
            if (isAuthenticated === 'true' && localUserId) {
                // 验证用户ID的有效性
                if (await this.validateUserIdWithBackend(localUserId)) {
                    this.userId = localUserId;
                    console.log('用户ID验证成功:', localUserId);
                } else {
                    console.warn('用户ID验证失败，清理无效数据');
                    this.clearInvalidLocalData();
                    throw new Error('用户认证信息无效');
                }
            } else {
                throw new Error('用户未登录');
            }
        }

        /**
         * 验证用户ID
         */
        async validateUserIdWithBackend(userId) {
            try {
                const token = localStorage.getItem('authToken');
                if (!token) {
                    return false;
                }

                const response = await fetch('/api/credits/get', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                return response.ok;
            } catch (error) {
                console.error('验证用户ID失败:', error);
                return false;
            }
        }

        /**
         * 清理无效的本地数据
         */
        clearInvalidLocalData() {
            localStorage.removeItem('isAuthenticated');
            localStorage.removeItem('userId');
            localStorage.removeItem('authToken');
            localStorage.removeItem('cachedCredits');
        }

        /**
         * 翻译模态框文本
         */
        translateModalText() {
            if (window.UILanguage && typeof window.UILanguage.translatePage === 'function') {
                setTimeout(() => {
                    window.UILanguage.translatePage();
                }, 50);
            }
        }

        /**
         * 加载积分批次数据
         */
        async loadBatchData() {
            this.showLoading();
            
            try {
                const token = await this.getValidToken();
                if (!token) {
                    throw new Error('Failed to get auth token');
                }

                // 获取积分批次详情
                const response = await fetch('/api/credits/batches', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`Failed to get credit batch info: ${response.status}`);
                }

                const batchData = await response.json();
                console.log('Credit batch info retrieved successfully:', batchData);

                this.batchInfo = batchData;
                this.renderBatchDetails();

            } catch (error) {
                console.error('Failed to load credit batch data:', error);
                this.showEmpty(window.UILanguage?.getText('credit_batch.load_failed') || 'Failed to load credit expiry information');
            } finally {
                this.hideLoading();
            }
        }

        /**
         * 获取有效的认证令牌
         */
        async getValidToken() {
            let token = localStorage.getItem('authToken');
            
            if (!token) {
                console.warn('认证令牌不存在');
                return null;
            }
            
            return token;
        }

        /**
         * 获取本地化的来源描述
         */
        getLocalizedSource(sourceDescription) {
            // 来源描述映射
            const sourceMap = {
                '积分购买': window.UILanguage?.getText('credit_batch.source_purchase') || 'Credit Purchase',
                '系统赠送': window.UILanguage?.getText('credit_batch.source_gift') || 'System Gift',
                '管理员添加': window.UILanguage?.getText('credit_batch.source_admin') || 'Admin Add',
                '积分充值': window.UILanguage?.getText('credit_batch.source_recharge') || 'Credit Recharge',
                '新用户奖励': window.UILanguage?.getText('credit_batch.source_newuser') || 'New User Reward'
            };
            
            return sourceMap[sourceDescription] || sourceDescription;
        }

        /**
         * 渲染积分批次详情
         */
        renderBatchDetails() {
            if (!this.tableBody || !this.batchInfo) {
                this.showEmpty(window.UILanguage?.getText('credit_batch.no_batches') || 'No credit batch information');
                return;
            }

            const { batches } = this.batchInfo;
            
            if (!batches || batches.length === 0) {
                this.showEmpty(window.UILanguage?.getText('credit_batch.no_batches') || 'No credit batch information');
                return;
            }

            // 清空表格内容
            this.tableBody.innerHTML = '';

            // 渲染批次数据
            batches.forEach(batch => {
                const row = document.createElement('tr');
                
                // 时间格式化
                const createdDate = new Date(batch.created_at).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                }).replace(/\//g, '-');
                
                const expiresDate = new Date(batch.expires_at).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                }).replace(/\//g, '-');

                // 剩余天数样式
                let daysClass = 'days-normal';
                if (batch.days_until_expiry <= 3) {
                    daysClass = 'days-urgent';
                } else if (batch.days_until_expiry <= 7) {
                    daysClass = 'days-warning';
                }

                // 剩余积分样式
                let remainingClass = '';
                if (batch.is_expiring_soon) {
                    remainingClass = 'expiring-soon';
                } else if (batch.remaining_credits > 0) {
                    remainingClass = 'remaining-credits';
                }

                row.innerHTML = `
                    <td class="source-cell">${this.getLocalizedSource(batch.source_description)}</td>
                    <td class="credits-cell">${batch.credits_amount}</td>
                    <td class="credits-cell ${remainingClass}">${batch.remaining_credits}</td>
                    <td class="date-cell">${createdDate}</td>
                    <td class="date-cell">${expiresDate}</td>
                    <td class="days-cell ${daysClass}">${batch.days_until_expiry}${window.UILanguage?.getText('credit_batch.days') || 'days'}</td>
                `;

                this.tableBody.appendChild(row);
            });

            // 显示内容区域
            this.hideLoading();
            this.hideEmpty();
            if (this.contentElement) {
                this.contentElement.style.display = 'block';
            }
        }

        /**
         * 显示加载状态
         */
        showLoading() {
            if (this.loadingElement) {
                this.loadingElement.style.display = 'flex';
                this.loadingElement.style.visibility = 'visible';
            }
            if (this.emptyElement) {
                this.emptyElement.style.display = 'none';
            }
            if (this.contentElement) {
                this.contentElement.style.display = 'none';
            }
        }

        /**
         * 隐藏加载状态
         */
        hideLoading() {
            if (this.loadingElement) {
                this.loadingElement.style.display = 'none';
                this.loadingElement.style.visibility = 'hidden';
            }
        }

        /**
         * 显示空状态
         */
        showEmpty(message) {
            if (this.emptyElement) {
                this.emptyElement.textContent = message || (window.UILanguage?.getText('credit_batch.no_batches') || 'No credit batch information');
                this.emptyElement.style.display = 'block';
            }
            if (this.contentElement) {
                this.contentElement.style.display = 'none';
            }
            this.hideLoading();
        }

        /**
         * 隐藏空状态
         */
        hideEmpty() {
            if (this.emptyElement) {
                this.emptyElement.style.display = 'none';
            }
        }
    }

    // 创建全局实例
    window.CreditBatchManager = new CreditBatchManager();

    // 在DOM加载完成后初始化
    document.addEventListener('DOMContentLoaded', async () => {
        try {
            const supabase = window.getSupabaseClient();
            
            if (!supabase) {
                console.warn('Supabase客户端不可用，积分批次管理器将在客户端可用时初始化');
                document.addEventListener('auth-state-changed', (event) => {
                    if (event.detail && (event.detail.status === 'authenticated' || event.detail.loggedIn)) {
                        setTimeout(() => {
                            if (window.CreditBatchManager && (!window.CreditBatchManager.userId || window.CreditBatchManager.needsReinitialization)) {
                                window.CreditBatchManager.init().catch(error => {
                                    console.error('认证状态变化后初始化积分批次管理器失败:', error);
                                });
                            }
                        }, 500);
                    }
                });
            } else {
                const isAuthenticated = localStorage.getItem('isAuthenticated');
                const userId = localStorage.getItem('userId');
                if (isAuthenticated === 'true' && userId) {
                    console.log('检测到localStorage中的认证状态，准备初始化积分批次管理器');
                    if (window.CreditBatchManager) {
                        await window.CreditBatchManager.init();
                    }
                } else {
                    console.log('用户未登录，积分批次管理器将在用户登录后初始化');
                    document.addEventListener('auth-state-changed', (event) => {
                        if (event.detail && (event.detail.status === 'authenticated' || event.detail.loggedIn)) {
                            setTimeout(() => {
                                if (window.CreditBatchManager && (!window.CreditBatchManager.userId || window.CreditBatchManager.needsReinitialization)) {
                                    window.CreditBatchManager.init().catch(error => {
                                        console.error('认证状态变化后初始化积分批次管理器失败:', error);
                                    });
                                }
                            }, 500);
                        }
                    });
                }
            }
        } catch (error) {
            console.error('初始化积分批次管理器过程中出错:', error);
        }
    });
})(); 