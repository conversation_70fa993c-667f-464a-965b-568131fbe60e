:root {
  --primary-color: #2196F3; /* Updated to match main.html blue */
  --primary-hover: #1976D2; /* Updated to match main.html darker blue */
  --secondary-color: #f8f9fa;
  --text-primary: #333;
  --text-secondary: #6c757d;
  --border-radius: 8px; /* Match main.html rounded corners */
  --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Match main.html shadow style */
  --transition: all 0.3s ease;
}

/* 旋转动画定义 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Microsoft YaHei", "微软雅黑", sans-serif; /* Match main.html font */
}

body {
  background: linear-gradient(135deg, #f5f7fa, #c3cfe2); /* Match main.html gradient background */
  color: var(--text-primary);
  min-height: 100vh;
  display: flex;
  overflow-x: hidden;
}

/* 全屏两栏布局 */
.branding-section {
  width: 50%;
  height: 100vh;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); /* Updated to match main.html blue */
  color: white;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.branding-section h1 {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 1.2;
  text-align: center; /* 让标题居中显示 */
}

.branding-section p {
  font-size: 1.1rem;
  margin-bottom: 25px;
  opacity: 0.9;
  line-height: 1.6;
}

.features {
  margin-top: 30px;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.feature-icon {
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.feature-icon svg {
  width: 18px;
  height: 18px;
  fill: white;
}

.feature-text {
  font-size: 0.95rem;
}

.auth-section {
  width: 50%;
  height: 100vh;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.9); /* Add slight transparency to blend with gradient */
  position: relative; /* Add position relative for absolute positioning of children */
}

.auth-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  position: relative;
  top: auto;
  left: auto;
  transform: none;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  height: auto;
}

/* 固定欢迎标题的位置 */
.auth-header {
  position: relative;
  z-index: 5;
  background-color: transparent;
  height: 30px;
  margin-top: 10px; /* 增加顶部间距 */
  margin-bottom: 18px; /* 增加底部间距，使标题更居中 */
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 30px;
  max-height: 30px;
}

.auth-header h2 {
  font-size: 1.7rem; /* 略微增大字体 */
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  text-align: center;
}

.auth-header p {
  display: none;
}

.auth-tabs {
  width: 100%;
  position: relative;
  margin-top: 20px; /* 增加间距使标题居中 */
  height: auto;
  min-height: auto;
  overflow: visible;
}

.tabs {
  display: flex;
  margin-bottom: 8px; /* 减少间距 */
  border-bottom: 1px solid #eee;
  position: relative;
  z-index: 5;
}

.tab-button {
  padding: 8px 0;
  border: none;
  background-color: transparent;
  cursor: pointer;
  flex: 1;
  text-align: center;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 1rem;
  position: relative;
  transition: all 0.3s ease;
}

/* 优化验证码容器样式 */
.captcha-container {
  margin: 10px 0; /* 增加上下间距，确保与其他元素有均匀的间距 */
  height: 65px; /* 固定高度，与Turnstile iframe高度一致 */
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative; /* 添加相对定位以便内部元素可以绝对定位 */
  overflow: hidden; /* 防止内容溢出 */
  min-height: 65px; /* 确保最小高度固定 */
  max-height: 65px; /* 确保最大高度固定 */
}

/* 验证码占位符样式 */
.captcha-placeholder {
  width: 100%;
  height: 65px; /* 固定高度与容器一致 */
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 65px; /* 确保最小高度固定 */
  max-height: 65px; /* 确保最大高度固定 */
}

/* 针对Cloudflare Turnstile的特定样式 */
.captcha-placeholder iframe {
  display: block !important;
  margin: 0 auto !important;
  height: 65px !important; /* 固定iframe高度 */
  position: static !important; /* 防止绝对定位导致的布局问题 */
  transform: none !important; /* 禁用变换效果 */
  transition: none !important; /* 禁用过渡效果 */
}

/* 验证码加载提示样式 */
.captcha-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #666;
  font-size: 14px;
}

/* 移动端调整 */
@media (max-width: 768px) {
  .tab-content {
    min-height: 380px; /* 减少最小高度 */
  }
  
  .auth-header {
    position: relative;
    height: 28px; /* 移动端调整高度 */
    margin-top: 8px; /* 移动端调整顶部间距 */
    margin-bottom: 15px; /* 移动端调整底部间距 */
  }
  
  .auth-header h2 {
    position: absolute;
    width: 100%;
    text-align: center;
    font-size: 1.6rem; /* 移动端字体大小 */
  }
  
  .auth-tabs {
    margin-top: 15px; /* 移动端减少间距 */
  }
  
  .captcha-container {
    margin: 3px 0 3px; /* 移动端减少间距 */
    min-height: 52px; /* 移动端减少高度 */
  }
  
  .forgot-password {
    margin: 1px 0 2px; /* 移动端减少底部间距 */
  }
}

@media (max-width: 600px) {
  .tab-content {
    min-height: 350px; /* 进一步减少最小高度 */
  }
}

@media (max-width: 480px) {
  .tab-content {
    min-height: 320px; /* 最小屏幕使用最小高度 */
  }
  
  .auth-header {
    position: relative;
    height: 25px; /* 更小屏幕调整高度 */
    margin-top: 5px; /* 更小屏幕调整顶部间距 */
    margin-bottom: 12px; /* 更小屏幕调整底部间距 */
  }
  
  .auth-header h2 {
    position: absolute;
    width: 100%;
    text-align: center;
    font-size: 1.5rem; /* 更小屏幕字体大小 */
  }
  
  .auth-tabs {
    margin-top: 12px; /* 更小屏幕减少间距 */
  }
  
  .captcha-container {
    margin: 2px 0 2px; /* 更小屏幕减少间距 */
    min-height: 50px; /* 更小屏幕减少高度 */
  }
  
  .forgot-password {
    margin: 1px 0 1px; /* 更小屏幕进一步减少间距 */
  }
  
  form {
    gap: 2px; /* 更小屏幕减少表单元素间距 */
  }
}

@media (max-width: 900px) {
  .auth-section {
    padding-top: 30px;
    min-height: 60vh;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;
  }

  .auth-container {
    position: relative;
    top: auto;
    left: auto;
    transform: none;
    margin-top: 20px;
  }
}

.tab-content {
  position: relative;
  height: auto;
  min-height: 420px; /* 调整高度以确保内容完全显示 */
  margin-top: 0;
  padding-top: 0;
  overflow: visible; /* 允许内容溢出 */
}

/* 移动端样式覆盖 */
@media (max-width: 768px) {
  /* 表单元素间距 */
  form {
    gap: 4px;
  }
  
  /* 忘记密码间距优化 */
  .forgot-password {
    margin: 1px 0 2px; /* 移动端减少底部间距 */
  }
  
  /* 验证码容器优化 */
  .captcha-container {
    margin: 8px 0; /* 移动端上下间距，确保均匀 */
    height: 70px; /* 移动端固定高度，确保有足够空间 */
  }
  
  /* 移动端验证码加载提示样式 */
  .captcha-loader {
    font-size: 12px;
  }
  
  /* 按钮间距优化 */
  button[type="submit"] {
    margin-top: 2px; /* 移动端减少顶部间距 */
    margin-bottom: 2px; /* 移动端减少底部间距 */
  }
  
  /* 社交登录区域优化 */
  .social-login {
    margin: 5px 0;
  }
  
  /* 注册页验证码区域优化 */
  #verificationSection {
    margin-bottom: 4px;
  }
  
  #requestVerification {
    margin-bottom: 4px;
  }
  
  /* 减少条款与协议间距 */
  .terms-privacy {
    margin-top: 4px;
  }
  
  /* 减少input组之间的间距 */
  .input-group {
    margin-bottom: 4px;
  }
  
  /* 减少divider间距 */
  .divider {
    margin: 4px 0;
  }
}

/* 更小屏幕的更激进优化 */
@media (max-width: 480px) {
  form {
    gap: 3px;
  }
  
  .captcha-container {
    margin: 6px 0; /* 超小屏幕上下间距，确保均匀 */
    height: 65px; /* 超小屏幕固定高度，确保有足够空间 */
  }
  
  /* 超小屏幕验证码加载提示样式 */
  .captcha-loader {
    font-size: 11px;
  }
  
  button[type="submit"] {
    margin-top: 1px; /* 更小屏幕进一步减少顶部间距 */
    margin-bottom: 1px; /* 更小屏幕进一步减少底部间距 */
  }
  
  .input-group {
    margin-bottom: 3px;
  }
  
  /* 减少divider间距 */
  .divider {
    margin: 3px 0;
  }
  
  .social-login {
    margin: 4px 0;
  }
  
  .terms-privacy {
    margin-top: 1.5px; /* 减少一半间距 */
  }
}

.form-container {
  position: relative; /* 改为相对定位 */
  width: 100%;
  height: auto; /* 允许容器高度自适应 */
  transition: opacity 0.3s ease-in-out;
  opacity: 0;
  visibility: hidden;
  display: none;
  padding-bottom: 10px; /* 添加底部间距 */
}

.form-container.active {
  opacity: 1;
  visibility: visible;
  display: block;
}

.form-container:not(.active) {
  display: none;
}

form {
  display: flex;
  flex-direction: column;
  gap: 3px; /* 减少表单元素间距 */
}

/* 忘记密码链接调整 */
.forgot-password {
  margin: 1px 0 4px; /* 增加底部间距 */
  text-align: center; /* 居中对齐 */
  font-size: 0.85rem; /* 减小字体 */
}

/* 登录按钮与验证码的间距 */
button[type="submit"] {
  margin-top: 3px; /* 减少顶部间距 */
  margin-bottom: 1.5px; /* 减少底部间距一半 */
  position: relative;
  z-index: 5;
}

/* 优化验证码错误提示 */
.captcha-error {
  color: #666;
  text-align: center;
  padding: 3px;
  font-size: 0.8rem;
}

/* 错误消息区域样式 */
.error-message {
  margin: 0;
  padding: 0;
  min-height: 0;
  font-size: 0.8rem;
  color: #f44336;
  text-align: center;
}

/* 优化按钮样式 */
button {
  padding: 8px 12px; /* 减小内边距 */
  background-color: #2196F3;
  color: white;
  border: none;
  border-radius: 8px; /* 略微减小圆角 */
  cursor: pointer;
  font-size: 0.9rem; /* 减小字体 */
  font-weight: 500;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

button:hover {
  background-color: #1976D2;
  transform: translateY(-1px); /* 减小悬停效果 */
  box-shadow: 0 3px 6px rgba(33, 150, 243, 0.2);
}

/* 验证码区域按钮样式 */
#verificationSection button {
  margin-top: 0;
  height: 36px; /* 减小高度 */
  white-space: nowrap;
  padding: 0 8px; /* 减小内边距 */
  font-size: 0.8rem; /* 减小字体 */
}

.terms-privacy {
  text-align: center;
  font-size: 0.8rem;
  margin-top: 6px; /* 进一步减少间距 */
  color: var(--text-secondary);
}

.social-login {
  margin-top: 4px; /* 进一步减少间距 */
  margin-bottom: 8px;
  width: 100%;
  display: block; /* 确保社交登录区域显示 */
  clear: both; /* 防止浮动干扰 */
}

.divider {
  margin: 2px 0; /* 进一步减少间距 */
}

#verificationSection {
  margin-bottom: 3px; /* 进一步减少间距 */
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 5px; /* 进一步减少间距 */
  align-items: center;
}

#requestVerification {
  margin-bottom: 2px; /* 进一步减少间距 */
}

.input-group {
  position: relative;
  margin-bottom: 3px; /* 进一步减少间距 */
}

input {
  width: 100%;
  padding: 10px 12px; /* 减少内边距 */
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: var(--transition);
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-text-fill-color: var(--text-primary);
  -webkit-box-shadow: 0 0 0px 1000px white inset;
  transition: background-color 5000s ease-in-out 0s;
  font-size: 0.95rem !important;
}

input:focus {
  border-color: #2196F3; /* Updated to match main.html blue */
  outline: none;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.15); /* Updated to match main.html blue */
}

::placeholder {
  color: #999;
}

.error-message {
  color: #d32f2f;
  font-size: 0.85rem;
  margin-top: 6px;
  text-align: center;
}

#verificationStatus {
  grid-column: 1 / -1;
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 2px; /* 减少间距 */
}

.countdown {
  grid-column: 1 / -1;
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 3px;
}

/* 社交登录样式 */
.social-login {
  margin-top: 10px; /* 减少上边距 */
  text-align: center;
  width: 100%;
}

.divider {
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  font-size: 0.85rem;
  margin: 8px 0; /* 减少间距 */
}

.divider::before,
.divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: #e0e0e0;
}

.divider span {
  padding: 0 10px;
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  background-color: white;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: var(--transition);
  margin-bottom: 5px;
  z-index: 10; /* 确保按钮总是可点击 */
}

.social-btn:hover {
  background-color: #f5f5f5;
  transform: translateY(-2px); /* Match main.html hover effect */
  box-shadow: var(--box-shadow);
}

.google-btn {
  color: #757575;
  background-color: #ffffff;
  border-color: #dddddd;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.google-btn:hover {
  background-color: #f5f5f5;
  box-shadow: 0 2px 5px rgba(0,0,0,0.15);
}

.google-btn svg {
  margin-right: 10px;
  flex-shrink: 0; /* 防止图标被压缩 */
}

/* 密码重置链接 */
.forgot-password a {
  color: #2196F3;
  text-decoration: none;
  font-size: 0.8rem; /* 减小字体 */
  opacity: 0.85; /* 略微降低不透明度 */
}

.forgot-password a:hover {
  text-decoration: underline;
}

/* 密码重置模态框样式 */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #fff;
  margin: 10% auto;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  position: relative;
  max-height: 85vh;
  overflow-y: auto;
}

.close-modal {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
}

.modal h2 {
  margin-top: 0;
  margin-bottom: 12px;
  font-size: 1.3rem;
  color: #333;
}

.message {
  margin-top: 10px;
  padding: 8px;
  border-radius: 4px;
}

.message.success {
  background-color: #e7f7e7;
  color: #2e7d32;
}

.message.error {
  background-color: #feeeee;
  color: #c62828;
}

/* 调试区域样式 */
.debug-panel {
  font-size: 11px;
  line-height: 1.4;
  display: none;
}

/* 响应式设计 */
@media (max-width: 900px) {
  body {
    flex-direction: column;
    overflow-y: auto;
  }
  
  .branding-section,
  .auth-section {
    width: 100%;
    height: auto;
    min-height: auto;
    padding: 25px 20px;
  }
  
  .branding-section {
    padding-bottom: 30px;
    min-height: 40vh;
  }
  
  .auth-section {
    padding-top: 30px;
    min-height: 60vh;
    display: flex;
    justify-content: center;
    position: relative;
  }
  
  .auth-container {
    position: relative;
    top: 0;
    left: 0;
    transform: none;
    max-width: 400px;
    margin: 0 auto;
  }
  
  .branding-section h1 {
    font-size: 2.2rem;
    text-align: center; /* 确保移动端也居中显示 */
  }
  
  /* 确保表单内容在移动端可见 */
  .tab-content {
    min-height: auto;
    padding-top: 5px; /* 减少tab和表单之间的距离 */
  }
  
  .form-container {
    position: relative;
    padding-bottom: 10px; /* 减少底部间距 */
  }
  
  /* 优化社交登录按钮在移动端的显示 */
  .social-login {
    margin-top: 10px;
    margin-bottom: 15px;
  }
  
  .social-btn {
    padding: 12px 20px;
    font-size: 16px;
    margin-bottom: 10px;
  }
  
  /* 确保验证码在移动端正确显示 */
  .captcha-container {
    margin: 0px 0; /* 减少验证码区域上下间距 */
    min-height: 65px;
  }
  
  .cf-turnstile iframe {
    transform: none !important; /* 禁用缩放变换 */
    height: 65px !important;
    width: 100% !important;
    position: static !important;
    display: block !important;
    margin: 0 auto !important;
    transition: none !important;
  }
  
  /* 优化忘记密码链接间距 */
  .forgot-password {
    margin: 5px 0;
  }
  
  /* 减少按钮下方间距 */
  button[type="submit"] {
    margin-bottom: 5px;
  }
  
  .auth-header {
    margin-bottom: 15px !important;
    height: 35px;
  }
  
  .auth-header h2 {
    font-size: 1.6rem;
  }
  
  .auth-tabs {
    margin-top: 30px !important;
  }
}

@media (max-width: 600px) {
  .branding-section,
  .auth-section {
    padding: 20px 15px;
  }
  
  .branding-section {
    min-height: 35vh;
  }
  
  .auth-section {
    min-height: 65vh;
    padding-top: 15px;
    padding-bottom: 15px;
  }
  
  .branding-section h1 {
    font-size: 1.8rem;
    text-align: center; /* 确保600px以下屏幕也居中显示 */
  }
  
  #verificationSection {
    grid-template-columns: 1fr;
  }
  
  .auth-header {
    margin-bottom: 10px !important;
    height: 30px;
  }
  
  .auth-header h2 {
    font-size: 1.5rem;
  }
  
  .auth-container {
    padding: 0 10px;
    margin: 0 auto;
  }
  
  .tab-content {
    min-height: 300px; /* 减少最小高度，减少空白间距 */
  }
  
  /* 移动端表单优化 */
  input {
    padding: 12px 15px;
    font-size: 16px; /* 防止iOS缩放 */
  }
  
  button {
    padding: 12px 20px;
    font-size: 16px;
    margin-top: 5px; /* 减少按钮上方间距 */
    margin-bottom: 5px; /* 减少按钮下方间距 */
  }
  
  .social-btn {
    padding: 14px 20px;
    font-size: 16px;
    margin-bottom: 5px; /* 减少社交登录按钮下方间距 */
  }
  
  /* 验证码在小屏幕上的优化 */
  .captcha-container {
    margin: 0px 0; /* 减少验证码区域上下间距 */
    min-height: 65px;
  }
  
  .cf-turnstile iframe {
    transform: none !important; /* 禁用缩放变换 */
    height: 65px !important;
    width: 100% !important;
    position: static !important;
    display: block !important;
    margin: 0 auto !important;
    transition: none !important;
  }
  
  /* 优化忘记密码链接间距 */
  .forgot-password {
    margin: 3px 0;
  }
  
  /* 验证码容器优化 */
  .captcha-container {
    margin: 8px 0;
    min-height: 65px;
  }
  
  /* 优化获取验证码按钮与验证码容器间距 */
  #requestVerification {
    margin-bottom: 8px;
  }
  
  /* 优化注册按钮与下方文字间距 */
  #signupButton {
    margin-bottom: 8px;
  }
  
  /* 表单容器高度优化 */
  .form-container, .form-container.active {
    min-height: 300px; /* 更小屏幕进一步减小高度 */
  }
}

@media (max-width: 480px) {
  .branding-section {
    min-height: 30vh;
    padding: 15px 10px;
  }
  
  .auth-section {
    min-height: 70vh;
    padding: 12px 8px;
  }
  
  .branding-section h1 {
    font-size: 1.5rem;
    text-align: center; /* 确保480px以下屏幕也居中显示 */
  }
  
  .auth-container {
    padding: 0 5px;
    width: 100%;
  }
  
  .tab-content {
    min-height: 280px; /* 减少最小高度，使界面更紧凑 */
  }
  
  .auth-header {
    margin-bottom: 8px !important;
    height: 28px;
  }
  
  .auth-header h2 {
    font-size: 1.5rem; /* 更小屏幕字体大小 */
  }
  
  .auth-tabs {
    margin-top: 20px !important;
  }
  
  /* 极小屏幕的验证码优化 */
  .cf-turnstile iframe {
    transform: scale(0.75);
  }
  
  .social-btn {
    padding: 10px 15px; /* 减少内边距 */
    font-size: 15px;
    margin-bottom: 3px; /* 减少下方间距 */
  }
  
  /* 表单元素间距进一步优化 */
  form {
    gap: 5px; /* 减少表单元素间距 */
  }
  
  /* 验证码容器在最小屏幕上的优化 */
  .captcha-container {
    margin: 5px 0;
    min-height: 60px;
  }
  
  /* 进一步优化忘记密码链接间距 */
  .forgot-password {
    margin: 1px 0 1px; /* 更小屏幕进一步减少间距 */
  }
  
  /* 按钮优化 */
  button {
    padding: 10px 15px;
    margin-top: 1px; /* 更小屏幕进一步减少顶部间距 */
    margin-bottom: 1px; /* 更小屏幕进一步减少底部间距 */
  }
  
  /* 表单容器高度优化 */
  .form-container, .form-container.active {
    min-height: 280px; /* 最小屏幕使用最小高度 */
  }
}

/* 添加到 log.css 文件 */
.captcha-loader {
  padding: 10px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

/* 条款与隐私政策链接样式 */
.terms-privacy {
  text-align: center;
  font-size: 0.8rem; 
  margin-top: 10px;
  color: var(--text-secondary);
}

.policy-link {
  color: inherit;
  text-decoration: underline; /* 添加下划线使链接更醒目 */
  transition: color 0.3s ease;
  font-weight: 500; /* 增加字体粗细，使其更醒目 */
}

.policy-link:hover {
  color: #2196F3;
}

/* 定义鼠标悬停状态 - 蓝色背景配白色文字 */
.tab-button:hover {
  background-color: #2196F3;
  color: white;
  border-radius: 4px 4px 0 0;
}

.tab-button:focus {
  outline: none;
}

.tab-button.active {
  color: #2196F3; /* Updated to match main.html blue */
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #2196F3; /* Updated to match main.html blue */
}

/* 特别处理激活标签的悬停状态 */
.tab-button.active:hover {
  color: white;
}

/* 提交按钮样式 */
button[type="submit"] {
  margin-top: 4px; /* 增加顶部间距 */
  margin-bottom: 3px;
  position: relative;
  z-index: 5;
}

/* 确保验证码缩放以适应容器 */
.cf-turnstile iframe {
  max-width: 100%;
  transform: scale(0.95); /* 稍微缩小验证码 */
  transform-origin: center;
}

/* 按钮加载状态样式 */
.btn-loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
  opacity: 0.8;
  cursor: not-allowed;
}

.btn-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 18px;
  height: 18px;
  margin: -9px 0 0 -9px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #ffffff;
  animation: spin 1s linear infinite;
}

/* 为不同大小的按钮适配加载动画 */
.btn-loading.btn-small::after {
  width: 14px;
  height: 14px;
  margin: -7px 0 0 -7px;
  border-width: 1.5px;
}

.btn-loading.btn-large::after {
  width: 22px;
  height: 22px;
  margin: -11px 0 0 -11px;
  border-width: 2.5px;
}

/* 登录按钮特殊样式 */
button[type="submit"].btn-loading {
  background-color: #1976D2;
  transform: none;
  box-shadow: none;
}

button[type="submit"].btn-loading:hover {
  background-color: #1976D2;
  transform: none;
  box-shadow: none;
}

/* 社交登录按钮加载状态 */
.social-btn.btn-loading {
  color: transparent !important;
  background-color: #f5f5f5;
  border-color: #dddddd;
}

.social-btn.btn-loading::after {
  border: 2px solid rgba(0, 0, 0, 0.2);
  border-top-color: #666666;
}