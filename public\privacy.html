<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Policy - AudioPilot</title>
    <link rel="stylesheet" href="css/privacy.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            visibility: hidden; /* Hide content until language is loaded */
        }
        
        .container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .content {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 40px;
        }
        
        h1 {
            font-size: 1.8rem;
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        section {
            margin-bottom: 30px;
        }
        
        h2 {
            font-size: 1.2rem;
            color: #2196F3;
            margin-bottom: 15px;
        }
        
        p, ul {
            margin-bottom: 15px;
            line-height: 1.7;
        }
        
        ul {
            padding-left: 25px;
        }
        
        li {
            margin-bottom: 8px;
        }
        
        strong {
            font-weight: 600;
        }
        
        .contact-info {
            padding: 10px 15px;
            background-color: #f5f7fa;
            border-left: 4px solid #007bff;
            font-weight: 500;
        }
        
        .back-button {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .back-button:hover {
            background-color: #0056b3;
        }        
        
        .language-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            z-index: 1000;
            display: none;
            min-width: 120px;
            padding: 5px 0;
            margin-top: 2px;
            background-color: #fff;
            border: 1px solid rgba(0,0,0,.15);
            border-radius: 4px;
            box-shadow: 0 6px 12px rgba(0,0,0,.175);
        }
        
        .language-dropdown.show {
            display: block;
        }
        
        .language-dropdown .language-option {
            display: block;
            width: 100%;
            padding: 6px 15px;
            clear: both;
            font-weight: 400;
            text-align: left;
            white-space: nowrap;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 14px;
        }
        
        .language-dropdown .language-option:hover {
            background-color: #f5f5f5;
        }
        
        .language-dropdown .language-option.selected {
            font-weight: bold;
            background-color: #f0f7ff;
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        /* Language selector */
        .language-selector {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            z-index: 100;
        }
        
        .language-btn {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px 10px;
            margin-left: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .language-btn:hover {
            background: #f0f0f0;
        }
        
        .language-btn.active {
            background: #e7f3ff;
            border-color: #2196F3;
            color: #2196F3;
        }
    </style>
</head>
<body>
    <!-- Language selector -->
    <div class="language-selector">
        <button class="language-btn" data-lang="zh">中文</button>
        <button class="language-btn" data-lang="en">English</button>
    </div>
    
    <div class="container">
        <div class="content">
            <h1 data-i18n="privacy.title">隐私政策</h1>

            <section>
                <h2 data-i18n="privacy.general.heading">1. 总则</h2>
                <p data-i18n="privacy.general.content">本隐私政策描述了BookSum（"我们"、"我们的"或"服务"）在您使用我们的电子书摘要和思维导图生成服务时如何收集、使用和披露您的信息。使用我们的服务，即表示您同意根据本隐私政策中概述的条款收集和使用您的信息。</p>
            </section>

            <section>
                <h2 data-i18n="privacy.collection.heading">2. 信息收集</h2>
                <p data-i18n="privacy.collection.intro">我们收集的信息包括但不限于：</p>
                <ul>
                    <li><strong data-i18n="privacy.collection.account_title">账户信息：</strong><span data-i18n="privacy.collection.account_desc">当您注册账户时，我们会收集您的个人身份信息（如电子邮件地址或姓名）。</span></li>
                    <li><strong data-i18n="privacy.collection.usage_title">使用数据：</strong><span data-i18n="privacy.collection.usage_desc">我们记录您如何使用我们的服务，包括访问的页面、点击的功能以及与我们服务的其他互动。</span></li>
                    <li><strong data-i18n="privacy.collection.device_title">设备信息：</strong><span data-i18n="privacy.collection.device_desc">我们可能会收集有关您使用的设备的信息，包括设备类型、操作系统和浏览器类型。</span></li>
                </ul>
            </section>

            <section>
                <h2 data-i18n="privacy.usage.heading">3. 信息使用</h2>
                <p data-i18n="privacy.usage.intro">我们使用收集的信息来：</p>
                <ul>
                    <li data-i18n="privacy.usage.purpose1">提供、维护和改进我们的电子书摘要和思维导图生成服务</li>
                    <li data-i18n="privacy.usage.purpose2">创建和维护您的账户</li>
                    <li data-i18n="privacy.usage.purpose3">处理您的电子书内容以生成摘要和思维导图</li>
                    <li data-i18n="privacy.usage.purpose4">通知您有关我们服务的变更</li>
                    <li data-i18n="privacy.usage.purpose5">提供客户支持</li>
                    <li data-i18n="privacy.usage.purpose6">监控和分析使用模式和趋势</li>
                </ul>
            </section>

            <section>
                <h2 data-i18n="privacy.sharing.heading">4. 信息共享</h2>
                <p data-i18n="privacy.sharing.intro">我们不会出售、出租或以其他方式与第三方共享您的个人信息，以下情况除外：</p>
                <ul>
                    <li><strong data-i18n="privacy.sharing.providers_title">服务提供商：</strong><span data-i18n="privacy.sharing.providers_desc">我们可能会与帮助我们提供服务的第三方服务提供商共享信息，例如云存储提供商和支付处理商。</span></li>
                    <li><strong data-i18n="privacy.sharing.legal_title">合规和保护：</strong><span data-i18n="privacy.sharing.legal_desc">如果需要遵守法律义务、回应法律程序或保护我们的权利、财产或安全，我们可能会披露您的信息。</span></li>
                    <li><strong data-i18n="privacy.sharing.business_title">业务转让：</strong><span data-i18n="privacy.sharing.business_desc">如果我们参与合并、收购或资产出售，您的信息可能作为该交易的一部分被转让。</span></li>
                </ul>
            </section>

            <section>
                <h2 data-i18n="privacy.security.heading">5. 数据安全</h2>
                <p data-i18n="privacy.security.content">我们采取合理措施保护您的个人信息免遭未经授权的访问、使用或披露。但请注意，通过互联网传输的方法或电子存储的方法都不是100%安全的。我们不能保证您的信息的绝对安全。</p>
            </section>

            <section>
                <h2 data-i18n="privacy.retention.heading">6. 数据保留</h2>
                <p data-i18n="privacy.retention.content1">只要您的账户处于活动状态或需要提供服务，我们就会保留您的个人信息。在您请求删除您的账户后，我们将在合理的时间内删除或匿名化您的个人信息，除非法律要求我们保留这些信息。</p>
                <p data-i18n="privacy.retention.content2">我们不会保留您上传的电子书内容以及基于电子书内容生成的摘要和思维导图。</p>
            </section>

            <section>
                <h2 data-i18n="privacy.rights.heading">7. 您的权利</h2>
                <p data-i18n="privacy.rights.intro">根据您所在地区适用的法律，您可能拥有以下权利：</p>
                <ul>
                    <li data-i18n="privacy.rights.access">访问您的个人信息</li>
                    <li data-i18n="privacy.rights.correct">更正不准确的个人信息</li>
                    <li data-i18n="privacy.rights.delete">删除您的个人信息</li>
                </ul>
                <p data-i18n="privacy.rights.contact">要行使您的权利，请使用下面提供的联系信息联系我们。</p>
            </section>

            <section>
                <h2 data-i18n="privacy.cookies.heading">8. Cookie和类似技术</h2>
                <p data-i18n="privacy.cookies.content">我们使用Cookie和类似技术来跟踪和记住您的偏好，并理解服务使用情况。您可以通过浏览器设置控制接受或拒绝Cookie，但这样做可能会影响某些服务功能。</p>
            </section>

            <section>
                <h2 data-i18n="privacy.thirdparty.heading">9. 第三方服务</h2>
                <p data-i18n="privacy.thirdparty.content">我们的服务可能包含指向第三方网站或服务的链接。我们对这些网站的隐私政策或内容不负责任。我们建议您查看您访问的任何第三方网站的隐私政策。</p>
            </section>

            <section>
                <h2 data-i18n="privacy.changes.heading">10. 隐私政策变更</h2>
                <p data-i18n="privacy.changes.content">我们可能会不时更新此隐私政策。我们将通过在此页面上发布新的隐私政策来通知您任何更改。建议您定期查看此隐私政策以了解任何变更。对该政策的更改在发布到此页面时生效。</p>
            </section>

            <section>
                <h2 data-i18n="privacy.contact.heading">11. 联系我们</h2>
                <p data-i18n="privacy.contact.intro">如果您对本隐私政策有任何疑问，请联系我们：</p>                <p class="contact-info" data-i18n="privacy.contact.email">电子邮件：<EMAIL></p>
            </section>
            
            <a href="index.html" class="back-button" data-i18n="privacy.backbutton">返回首页</a>
        </div>
    </div>

    <!-- Load UI language script -->
    <script src="js/ui-language.js"></script>
    <script>
        // Wait for the language system to be ready
        document.addEventListener('DOMContentLoaded', function() {
            // Set up language buttons
            const langButtons = document.querySelectorAll('.language-btn');
            
            // Function to show the page content once language is loaded
            function showPageContent() {
                document.body.style.visibility = 'visible';
            }
            
            // Listen for language loaded event
            document.addEventListener('ui-language-loaded', showPageContent);
            
            // Set up language button click handlers
            langButtons.forEach(button => {
                const lang = button.getAttribute('data-lang');
                
                // Mark the active language button
                if (window.UILanguage && window.UILanguage.getCurrentLanguage() === lang) {
                    button.classList.add('active');
                }
                
                // Handle language button clicks
                button.addEventListener('click', function() {
                    const selectedLang = this.getAttribute('data-lang');
                    
                    if (window.UILanguage) {
                        // Set the language and update UI
                        window.UILanguage.setLanguage(selectedLang);
                        
                        // Update active button styling
                        langButtons.forEach(btn => btn.classList.remove('active'));
                        this.classList.add('active');
                    }
                });
            });
            
            // If UILanguage is already loaded, translate and show the page
            if (window.UILanguage) {
                setTimeout(() => {
                    showPageContent();
                }, 100);
            }
        });
    </script>
</body>
</html>