import os
import requests
import json
import logging
import uuid
import hmac
import hashlib
import time
from datetime import datetime
from flask import request, current_app, jsonify

logger = logging.getLogger(__name__)

class CreemAPI:
    def __init__(self, api_key=None, api_url=None, webhook_secret=None, app_url=None):
        is_production = os.environ.get('ENVIRONMENT') == 'production'
        
        self.api_key = api_key or os.environ.get('CREEM_API_KEY')
        
        # 根据环境选择正确的API端点
        default_api_url = 'https://api.creem.io/v1' if is_production else 'https://test-api.creem.io/v1'
        self.api_url = api_url or os.environ.get('CREEM_API_URL', default_api_url)
        
        self.app_url = app_url or os.environ.get('APP_URL')
        self.webhook_secret = webhook_secret or os.environ.get('CREEM_WEBHOOK_SECRET')
        
        if not self.api_key:
            logger.error("Creem API密钥未配置")
        
        logger.info(f"初始化Creem API客户端: {self.api_url}")
    
    def create_checkout_session(self, order_id, amount, currency, user_email, product_id=None, success_url=None, cancel_url=None):
        """创建Creem支付会话"""
        logger.info(f"创建支付会话: 订单={order_id}, 金额={amount}{currency}")
        
        # 环境检测逻辑
        is_production = os.environ.get('ENVIRONMENT') == 'production'
        logger.info(f"当前环境: {'生产' if is_production else '开发'}")
        
        # 确保成功和取消URL包含完整的域名
        # 使用配置的APP_URL或环境变量，而不是硬编码的localhost
        base_url = self.app_url or os.environ.get('APP_URL')
        
        # 强制开发环境使用本地URL
        if not is_production:
            # 开发环境强制使用本地URL
            # 对Creem API使用localhost格式，因为它只接受这种格式
            base_url = "http://localhost:8080"  # 使用localhost:8080而非127.0.0.1:8080
            logger.info(f"开发环境强制使用本地URL: {base_url}")
        else:
            # 如果没有配置APP_URL，使用合适的默认值
            if not base_url:
                base_url = "https://reader-app.fly.dev"  # 使用实际的生产域名
                logger.warning(f"生产环境未配置APP_URL，使用默认值: {base_url}")
            
            # 生产环境URL额外验证
            if 'localhost' in base_url or '127.0.0.1' in base_url:
                safer_url = "https://reader-app.fly.dev"  # 使用实际的生产域名
                logger.warning(f"⚠️ 生产环境检测到本地URL: {base_url}，已自动替换为: {safer_url}")
                base_url = safer_url
        
        logger.info(f"使用基础URL: {base_url}")
        
        if not success_url:
            # 使用特殊的重定向处理器，将支付成功信息传递给它
            success_url = f"{base_url}/api/payment/redirect-to-app?payment_success=true&order_id={order_id}"
        if not cancel_url:
            # 使用特殊的重定向处理器，将支付取消信息传递给它
            cancel_url = f"{base_url}/api/payment/redirect-to-app?payment_cancelled=true&order_id={order_id}"
        
        logger.info(f"成功URL: {success_url}")
        logger.info(f"取消URL: {cancel_url}")
        
        try:
            headers = {
                "x-api-key": self.api_key,
                "Content-Type": "application/json"
            }
            
            # 使用传入的产品ID，如果没有传入则使用默认值
            if not product_id:
                product_id = "prod_2oQ4GaxAW1IsndJysadjMo"  # 默认产品ID
            
            logger.info(f"使用产品ID: {product_id}")
            
            # 根据Creem文档构建请求参数
            payload = {
                "product_id": product_id
            }
            
            # 仅添加success_url，不添加cancel_url
            # API错误表明cancel_url不应该存在
            if success_url:
                payload["success_url"] = success_url
                
            # 添加元数据
            payload["metadata"] = {
                "order_id": order_id,
                "user_email": user_email,
                "amount": amount,
                "currency": currency
            }
            
            logger.info(f"API密钥: {self.api_key}")
            logger.info(f"支付请求数据: {json.dumps(payload)}")
            
            # 使用正确的API路径
            api_endpoint = f"{self.api_url}/checkouts"
            logger.info(f"发送请求到: {api_endpoint}")
            
            response = requests.post(
                api_endpoint,
                headers=headers,
                json=payload
            )
            
            logger.info(f"API响应状态码: {response.status_code}")
            
            # 尝试解析响应内容
            try:
                response_data = response.json()
                logger.info(f"API响应内容: {json.dumps(response_data)}")
            except Exception as e:
                logger.error(f"解析API响应失败: {str(e)}")
                logger.info(f"原始响应内容: {response.text}")
                response_data = {}
            
            if response.status_code == 200 or response.status_code == 201:
                # 根据Creem API文档，响应中应该包含checkout_url
                # 但有时可能在不同的字段中，尝试多种可能的字段名
                checkout_id = response_data.get('id')
                
                # 尝试多种可能的字段名来获取checkout_url
                checkout_url = None
                possible_url_fields = ['url', 'checkout_url', 'payment_url', 'redirect_url']
                
                for field in possible_url_fields:
                    if field in response_data and response_data[field]:
                        checkout_url = response_data[field]
                        logger.info(f"找到checkout_url在字段 '{field}': {checkout_url}")
                        break
                
                # 如果在顶层没有找到，尝试在嵌套字段中查找
                if not checkout_url and 'data' in response_data and isinstance(response_data['data'], dict):
                    data = response_data['data']
                    for field in possible_url_fields:
                        if field in data and data[field]:
                            checkout_url = data[field]
                            logger.info(f"在data字段中找到checkout_url在字段 '{field}': {checkout_url}")
                            break
                
                if not checkout_url:
                    logger.error("API响应中缺少checkout_url")
                    logger.error(f"完整响应: {json.dumps(response_data)}")
                    return {
                        "success": False,
                        "error": "支付系统暂时无法生成支付链接，请稍后再试",
                    }
                
                return {
                    "success": True,
                    "checkout_id": checkout_id,
                    "checkout_url": checkout_url
                }
            else:
                error_message = "支付系统暂时不可用，请稍后再试"
                logger.error(f"创建支付会话失败: {response_data.get('message', f'API错误: {response.status_code}')}")
                return {
                    "success": False,
                    "error": error_message
                }
        except Exception as e:
            # 记录详细的异常信息到日志
            logger.exception(f"创建支付会话时发生异常: {str(e)}")
            
            # 返回通用错误消息给客户端
            return {
                "success": False,
                "error": "支付服务暂时不可用，请稍后再试"
            }
    
    def verify_webhook_signature(self, payload, signature_header):
        """验证Webhook签名"""
        if not self.webhook_secret:
            logger.error("Webhook密钥未配置，无法验证签名")
            return False
        
        try:
            # 获取时间戳和签名
            parts = signature_header.split(',')
            timestamp = None
            signature = None
            
            for part in parts:
                if part.startswith('t='):
                    timestamp = part[2:]
                elif part.startswith('v1='):
                    signature = part[3:]
            
            if not timestamp or not signature:
                logger.error("签名格式无效")
                return False
            
            # 验证时间戳是否过期（5分钟内有效）
            if int(time.time()) - int(timestamp) > 300:
                logger.error("签名已过期")
                return False
            
            # 计算签名
            signed_payload = f"{timestamp}.{payload.decode('utf-8') if isinstance(payload, bytes) else payload}"
            computed_signature = hmac.new(
                self.webhook_secret.encode(),
                signed_payload.encode(),
                hashlib.sha256
            ).hexdigest()
            
            # 比较签名
            if computed_signature == signature:
                logger.info("Webhook签名验证成功")
                return True
            else:
                logger.error("Webhook签名验证失败")
                return False
        except Exception as e:
            logger.exception(f"验证Webhook签名时发生异常: {str(e)}")
            return False
            
    def verify_redirect_signature(self, params, signature):
        """验证重定向参数签名 - 完全按照Creem文档实现
        
        Args:
            params: 包含所有参数的字典(不含signature)
            signature: 签名字符串
        
        Returns:
            bool: 签名验证结果
        """
        if not self.api_key:
            logger.error("API密钥未配置，无法验证签名")
            return False
        
        try:
            # 记录所有接收到的参数，用于调试
            logger.info(f"收到的所有参数: {params}")
            
            # 从URL参数中提取需要验证的字段
            redirect_params = {}
            
            # 按照Creem文档中的字段列表
            valid_keys = ['checkout_id', 'order_id', 'customer_id', 'subscription_id', 'product_id', 'request_id']
            
            # 处理可能的多值参数
            for key in valid_keys:
                if key in params:
                    values = params[key]
                    if isinstance(values, list):
                        # 如果是order_id，必须使用ord_开头的值
                        if key == 'order_id':
                            creem_order_id = None
                            for value in values:
                                if isinstance(value, str) and value.startswith('ord_'):
                                    creem_order_id = value
                                    break
                            if creem_order_id:
                                redirect_params[key] = creem_order_id
                                logger.info(f"使用Creem订单ID进行签名验证: {creem_order_id}")
                            else:
                                # 如果没有找到Creem订单ID，尝试使用第一个值
                                redirect_params[key] = values[0]
                                logger.warning(f"未找到Creem订单ID，使用第一个值: {values[0]}")
                        else:
                            # 对于其他参数，使用第一个值
                            redirect_params[key] = values[0]
                    else:
                        redirect_params[key] = values
            
            # 尝试两种签名验证方式
            # 1. 标准验证方式
            ordered_keys = [k for k in ['checkout_id','order_id','customer_id','subscription_id','product_id','request_id'] if k in redirect_params]
            param_strings = [f"{key}={redirect_params[key]}" for key in ordered_keys]
            param_strings.append(f"salt={self.api_key}")
            payload = "|".join(param_strings)
            computed_signature = hashlib.sha256(payload.encode()).hexdigest()
            
            logger.info(f"标准验证方式 - 签名计算参数: {payload}")
            logger.info(f"标准验证方式 - 计算的签名: {computed_signature}")
            
            # 2. 直接使用原始参数验证方式（处理某些特殊情况）
            # 从原始参数中提取所需字段，但不做多值处理
            raw_params = {}
            for key in valid_keys:
                if key in request.args:
                    raw_params[key] = request.args.get(key)
            
            # 按照相同的方式构建签名
            raw_ordered_keys = [k for k in ['checkout_id','order_id','customer_id','subscription_id','product_id','request_id'] if k in raw_params]
            raw_param_strings = [f"{key}={raw_params[key]}" for key in raw_ordered_keys]
            raw_param_strings.append(f"salt={self.api_key}")
            raw_payload = "|".join(raw_param_strings)
            raw_computed_signature = hashlib.sha256(raw_payload.encode()).hexdigest()
            
            logger.info(f"原始参数验证方式 - 签名计算参数: {raw_payload}")
            logger.info(f"原始参数验证方式 - 计算的签名: {raw_computed_signature}")
            
            logger.info(f"接收的签名: {signature}")
            
            # 比较两种方式的签名结果
            if computed_signature == signature:
                logger.info("重定向签名验证成功（标准方式）")
                return True
            elif raw_computed_signature == signature:
                logger.info("重定向签名验证成功（原始参数方式）")
                return True
            else:
                # 为了调试，输出所有参数
                logger.error(f"重定向签名验证失败: \n标准方式期望={computed_signature}\n原始参数方式期望={raw_computed_signature}\n实际={signature}")
                logger.debug(f"所有URL参数: {params}")
                logger.debug(f"用于签名的参数: {redirect_params}")
                logger.debug(f"原始参数: {raw_params}")
                
                # 在调试模式下，可以尝试跳过签名验证
                is_production = os.environ.get('ENVIRONMENT') == 'production'
                if not is_production and os.environ.get('DEBUG_SKIP_SIGNATURE') == 'true':
                    logger.warning("调试模式: 跳过签名验证")
                    return True
                
                return False
        except Exception as e:
            logger.exception(f"验证重定向签名时发生异常: {str(e)}")
            return False
            
    def verify_payment_status(self, order_id, product_id):
        """通过API直接验证支付状态
        
        Args:
            order_id: 订单ID
            product_id: 产品ID
            
        Returns:
            dict: 包含验证结果的字典，格式为 {'verified': bool, 'reason': str}
        """
        logger.info(f"通过API验证支付状态: checkout_id={order_id}, 产品ID={product_id}")
        
        try:
            headers = {"x-api-key": self.api_key, "Content-Type": "application/json"}
            # 使用checkout_id查询支付状态
            api_endpoint = f"{self.api_url}/checkouts/{order_id}"
            logger.info(f"发送请求到: {api_endpoint}")
            
            response = requests.get(api_endpoint, headers=headers)
            
            # 任何非200响应均视为验证失败，保证安全性
            if response.status_code != 200:
                logger.error(f"API返回错误状态码: {response.status_code}")
                return {"verified": False, "reason": f"支付平台API返回错误状态码: {response.status_code}"}
            
            try:
                response_data = response.json()
            except Exception as e:
                logger.error(f"解析API响应失败: {str(e)}")
                return {
                    "verified": False,
                    "reason": "无法解析支付平台API响应"
                }
            
            # 验证支付状态是否为已支付
            if response_data.get('status') != 'paid':
                logger.error(f"支付状态不是已支付: {response_data.get('status')}")
                return {
                    "verified": False,
                    "reason": f"支付状态不是已支付: {response_data.get('status')}"
                }
            
            # 验证产品ID是否匹配
            if response_data.get('product') != product_id:
                logger.error(f"产品ID不匹配: 期望={product_id}, 实际={response_data.get('product')}")
                return {
                    "verified": False,
                    "reason": "产品ID不匹配"
                }
            
            logger.info(f"支付状态验证成功: checkout_id={order_id}")
            return {
                "verified": True,
                "reason": "支付状态验证成功"
            }
        except Exception as e:
            logger.exception(f"验证支付状态时发生异常: {str(e)}")
            return {
                "verified": False,
                "reason": f"验证支付状态时发生异常: {str(e)}"
            }