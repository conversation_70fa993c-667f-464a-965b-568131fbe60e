# 混淆配置修复说明

## 问题描述
混淆JS代码后，谷歌登录用户和邮箱登录用户在点击菜单-积分使用记录时都显示内容为空，没有读取加载转圈的状态。

## 问题分析

### 1. 前端JavaScript混淆问题
**根本原因**：关键的函数名和类名被混淆，导致全局对象赋值失效

**具体问题**：
- `CreditHistoryManager` 类名被混淆
- `getSupabaseClient` 函数名被混淆  
- `showModal` 方法名被混淆
- `supabaseClientInstance` 变量名被混淆

**影响**：
- `window.CreditHistoryManager = new CreditHistoryManager()` 全局赋值失效
- 其他文件无法通过 `window.CreditHistoryManager` 访问积分历史管理器
- Supabase客户端获取函数被混淆导致认证失败

### 2. 后端API问题
**根本原因**：使用了过时的Supabase Python客户端API参数

**具体问题**：
- 在 `api/credits.py` 第934行使用了 `head=True` 参数
- 新版Supabase Python客户端不支持此参数

**错误日志**：
```
SyncRequestBuilder.select() got an unexpected keyword argument 'head'
```

## 修复方案

### 1. 修复混淆配置

#### 全局保留名称配置 (config.js)
在 `reservedNames` 数组中添加：
```javascript
'showModal',             // 保留积分历史模态框方法
'getSupabaseClient',     // 保留Supabase客户端获取函数
'supabaseClientInstance', // 保留Supabase客户端实例
```

#### 文件特定保留名称配置 (obfuscate.js)
更新以下文件的保留名称：

**credit-history.js**:
```javascript
['supabaseClient', 'CreditHistoryManager', 'showModal', 'getSupabaseClient']
```

**supabase-client.js**:
```javascript
['supabase', 'supabaseClient', 'supabaseClientInstance', 'getSupabaseClient']
```

### 2. 修复后端API问题

#### api/credits.py
移除不支持的 `head=True` 参数：
```python
# 修复前
count_response = service_supabase_client.table('credit_operations') \
    .select('*', count='exact', head=True) \
    .eq('user_id', user_id) \
    .execute()

# 修复后  
count_response = service_supabase_client.table('credit_operations') \
    .select('*', count='exact') \
    .eq('user_id', user_id) \
    .execute()
```

## 修复结果

### 混淆验证
混淆后的代码正确保留了关键函数名：
- ✓ `CreditHistoryManager` 类名已保留
- ✓ `showModal` 方法名已保留
- ✓ `getSupabaseClient` 函数名已保留
- ✓ `supabaseClientInstance` 变量名已保留

### 功能验证
修复后的功能应该能够：
- ✓ 正确初始化积分历史管理器
- ✓ 正常显示加载状态
- ✓ 成功加载和显示积分使用记录
- ✓ 支持谷歌登录和邮箱登录用户

## 部署说明
1. 使用修复后的混淆配置重新混淆所有JS文件
2. 部署混淆后的JS文件到生产环境
3. 部署修复后的后端API代码
4. 验证功能正常工作

## 注意事项
- 关键的全局对象和函数名必须在混淆配置中明确保留
- 后端API需要与Supabase客户端版本保持兼容
- 定期检查混淆后的代码以确保关键功能未被破坏 