// 书签标记强制测试脚本
console.log('🔧 书签标记强制测试脚本已加载');

// 强制显示书签标记测试函数
window.forceShowBookmarkTest = function() {
    console.log('🧪 开始强制书签标记测试');
    
    // 获取进度条元素
    const progressBar = document.getElementById('uploadedProgressBar');
    console.log('🧪 进度条元素:', progressBar);
    
    if (!progressBar) {
        console.error('🧪 未找到进度条元素');
        return;
    }
    
    // 清除所有现有的测试标记
    const existingTests = progressBar.querySelectorAll('.force-test-marker');
    existingTests.forEach(marker => marker.remove());
    
    // 创建多个测试标记在不同位置
    const positions = [10, 30, 50, 70, 90];
    const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff'];
    
    positions.forEach((pos, index) => {
        const marker = document.createElement('div');
        marker.className = 'force-test-marker';
        marker.style.cssText = `
            position: absolute !important;
            top: -15px !important;
            left: ${pos}% !important;
            width: 16px !important;
            height: 24px !important;
            background: ${colors[index]} !important;
            border: 3px solid #000 !important;
            border-radius: 4px !important;
            z-index: 9999 !important;
            transform: translateX(-50%) !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.8) !important;
        `;
        marker.title = `测试标记 ${index + 1} - 位置: ${pos}%`;
        
        progressBar.appendChild(marker);
        console.log(`🧪 测试标记 ${index + 1} 已添加: 位置=${pos}%, 颜色=${colors[index]}`);
    });
    
    console.log('🧪 强制测试完成，如果看不到彩色标记，说明CSS层级或容器问题');
    
    // 10秒后清除测试标记
    setTimeout(() => {
        const testMarkers = progressBar.querySelectorAll('.force-test-marker');
        testMarkers.forEach(marker => marker.remove());
        console.log('🧪 测试标记已清除');
    }, 10000);
};

// 检查书签数量
window.checkBookmarks = function() {
    if (window.bookmarkManager) {
        console.log('📊 当前书签数量:', window.bookmarkManager.bookmarks.length);
        console.log('📊 书签详情:', window.bookmarkManager.bookmarks);
    } else {
        console.log('📊 书签管理器未初始化');
    }
};

// 强制刷新书签标记
window.forceRefreshBookmarks = function() {
    if (window.bookmarkManager) {
        console.log('🔄 强制刷新书签标记');
        window.bookmarkManager.updateBookmarkMarkers('uploaded');
        window.bookmarkManager.updateBookmarkMarkers('recording');
    } else {
        console.log('🔄 书签管理器未初始化，无法刷新');
    }
};

console.log('🔧 书签测试函数已准备：');
console.log('🔧 - forceShowBookmarkTest() : 强制显示彩色测试标记');
console.log('🔧 - checkBookmarks() : 检查书签数量');
console.log('🔧 - forceRefreshBookmarks() : 强制刷新书签标记'); 