/**
 * 支付处理模块
 * 处理积分充值相关的前端逻辑
 */

// 获取Supabase客户端
const supabase = getSupabaseClient();

/**
 * 检查当前是否使用安全连接
 * 如果不是HTTPS连接，给出警告并尝试跳转到HTTPS
 * @returns {boolean} 连接是否安全
 */
function checkSecureConnection() {
    // 检查是否在生产环境中使用HTTP
    if (window.location.protocol !== 'https:' && 
        !window.location.hostname.includes('localhost') && 
        !window.location.hostname.includes('127.0.0.1')) {
        
        console.warn('支付操作需要安全连接 (HTTPS)');
        showToast(UILanguage.getText('payment.secure_connection_required', '支付功能需要安全连接，正在跳转到安全页面...'), 'warning');
        
        // 尝试跳转到HTTPS版本
        window.location.href = window.location.href.replace('http:', 'https:');
        return false;
    }
    return true;
}

/**
 * 显示提示消息
 * @param {string} message 消息内容
 * @param {string} type 消息类型 (success, error, info, warning)
 */
function showToast(message, type = 'info') {
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加到文档
    document.body.appendChild(toast);
    
    // 显示toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    // 3秒后隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        // 动画结束后移除元素
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

/**
 * 显示加载状态
 * @param {string} text 加载提示文本
 */
function showLoading(text = UILanguage.getText('main.loading', '加载中...')) {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const loadingText = document.getElementById('loadingText');
    
    if (loadingText) {
        loadingText.textContent = text;
    }
    
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

/**
 * 显示充值套餐选择弹窗
 * @param {Function} onPackageSelected 套餐选择回调
 */
function showRechargeModal(onPackageSelected) {
    // 确保已引入支付套餐模块
    if (!window.paymentPackages || typeof window.paymentPackages.createRechargeModal !== 'function') {
        console.error('支付套餐模块未正确加载');
        showToast(UILanguage.getText('payment.cannot_show_options', '无法显示充值选项，请刷新页面或联系客服'), 'error');
        return;
    }
    
    // 使用统一的充值模态框
    window.paymentPackages.createRechargeModal(onPackageSelected);
}

/**
 * 关闭充值弹窗
 */
function closeRechargeModal() {
    if (window.paymentPackages && typeof window.paymentPackages.closeRechargeModal === 'function') {
        window.paymentPackages.closeRechargeModal();
    } else {
        // 备用关闭方法
        const modal = document.getElementById('rechargeModal');
        if (modal) {
            modal.style.opacity = '0';
            setTimeout(() => {
                if (modal.parentNode) {
                    document.body.removeChild(modal);
                }
            }, 300);
        }
    }
}

/**
 * 验证用户身份的有效性
 * @returns {Promise<Object|null>} 验证成功返回用户对象，失败返回null
 */
async function validateAndGetUserInfo() {
    try {
        // 获取localStorage中的用户信息
        const isAuthenticated = localStorage.getItem('isAuthenticated');
        const userId = localStorage.getItem('userId');
        const userEmail = localStorage.getItem('userEmail');
        const authToken = localStorage.getItem('authToken');
        
        if (isAuthenticated !== 'true' || !userId || !authToken) {
            console.log('localStorage中缺少必要的认证信息');
            return null;
        }
        
        // 通过后端API验证用户身份
        const response = await fetch('/api/credits/get', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            // 验证成功，返回用户对象
            return { 
                id: userId, 
                email: userEmail || '' 
            };
        } else if (response.status === 401) {
            // 认证失败，清除无效数据
            console.warn('用户认证已过期，清除本地数据');
            clearInvalidAuthData();
            return null;
        } else {
            // 其他错误，为了用户体验，暂时允许使用localStorage数据
            console.warn('验证请求失败，状态码:', response.status, '暂时使用本地数据');
            return { 
                id: userId, 
                email: userEmail || '' 
            };
        }
    } catch (error) {
        console.error('验证用户身份时出错:', error);
        // 网络错误等情况，为了用户体验，暂时允许使用localStorage数据
        const userId = localStorage.getItem('userId');
        const userEmail = localStorage.getItem('userEmail');
        if (userId) {
            return { 
                id: userId, 
                email: userEmail || '' 
            };
        }
        return null;
    }
}

/**
 * 清除无效的认证数据
 */
function clearInvalidAuthData() {
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('userId');
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('userData');
    console.log('已清除无效的认证数据');
}

/**
 * 创建支付订单
 * @param {Object} packageInfo 套餐信息
 * @returns {Promise<Object>} 订单创建结果
 */
async function createPaymentOrder(packageInfo) {
    try {        
        // 检查安全连接
        if (!checkSecureConnection()) {
            hideLoading();
            return { success: false, error: '不安全的连接' };
        }
        
        // 检测当前环境
        const isProduction = window.EnvConfig && window.EnvConfig.isProduction;
        console.log(`当前环境: ${isProduction ? '生产环境' : '开发环境'}`);
        
        // 验证并获取用户信息
        const user = await validateAndGetUserInfo();
        
        if (!user) {
            console.error('用户身份验证失败，无法创建订单');
            showToast(UILanguage.getText('payment.login_required', '请先登录后再进行充值'), 'error');
            return { success: false, error: '用户身份验证失败' };
        }
        
        console.log('用户身份验证成功，用户ID:', user.id);
        
        // 获取CSRF令牌
        const csrfToken = await getCsrfToken();
        if (!csrfToken) {
            showToast(UILanguage.getText('payment.security_error', '安全验证失败，请刷新页面重试'), 'error');
            return { success: false, error: '安全验证失败' };
        }
        
        // 准备订单数据
        const orderData = {
            packageId: packageInfo.id,
            userId: user.id,
            userEmail: user.email,  // 重新添加邮箱信息
            environment: isProduction ? 'production' : 'development' // 添加环境信息
        };
        
        // 记录日志
        console.log(`创建订单数据: 套餐=${packageInfo.id}, 用户=${user.id}, 环境=${orderData.environment}`);
        
        // 获取当前主机和端口
        const baseUrl = window.location.origin;
        
        // 调用API创建订单
        console.log(`发送订单请求到: ${baseUrl}/api/payment/create-order`);
        const response = await fetch(`${baseUrl}/api/payment/create-order`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': csrfToken
            },
            body: JSON.stringify(orderData),
            credentials: 'same-origin' // 确保发送cookies
        });
        
        // 记录响应状态
        console.log(`订单API响应状态码: ${response.status}`);
        
        const result = await response.json();
        console.log('订单API响应数据:', result);
        
        // 隐藏加载状态
        hideLoading();
        
        if (!result.success) {
            console.error('创建订单失败:', result.error);
            const errorMessage = UILanguage.getText('payment.order_failed', '创建订单失败: {0}').replace('{0}', result.error);
            showToast(errorMessage, 'error');
            return { success: false, error: result.error };
        }
        
        return result;
    } catch (error) {
        // 隐藏加载状态
        hideLoading();
        
        console.error('创建订单时发生错误:', error);
        showToast(UILanguage.getText('payment.order_error', '创建订单时发生错误'), 'error');
        return { success: false, error: error.message };
    }
}

/**
 * 获取CSRF令牌
 * @returns {Promise<string>} CSRF令牌
 */
async function getCsrfToken() {
    try {
        const response = await fetch('/api/payment/csrf-token');
        const data = await response.json();
        return data.csrfToken;
    } catch (error) {
        console.error('获取CSRF令牌失败:', error);
        return null;
    }
}

/**
 * 重定向到支付页面
 * @param {string} checkoutUrl Creem支付页面URL
 */
function redirectToPayment(checkoutUrl) {
    if (!checkoutUrl) {
        console.error('支付URL无效');
        showToast(UILanguage.getText('payment.cannot_redirect', '无法跳转到支付页面'), 'error');
        return;
    }
    
    // 获取环境配置
    const isProduction = !window._env_.DEBUG_MODE;
    console.log(`当前环境: ${isProduction ? '生产环境' : '开发环境'}`);
    
    // 记录支付URL (为了调试)
    console.log('支付页面URL:', checkoutUrl);
    
    // 直接跳转到支付页面
    window.location.href = checkoutUrl;
}

/**
 * 保存页面状态，以便支付后恢复
 */
function savePageState() {
    // 保存当前页面的一些状态到localStorage
    const pageState = {
        timestamp: Date.now(),
        // 添加其他需要保存的状态...
    };
    
    localStorage.setItem('paymentReturnState', JSON.stringify(pageState));
}

/**
 * 初始化支付结果处理
 * 检查URL参数中是否包含支付结果信息
 */
function initPaymentResultHandler() {
    // 防止重复初始化
    if (window.paymentResultHandlerInitialized) {
        console.log('支付结果处理器已初始化，跳过重复初始化');
        return;
    }
    
    // 标记已初始化
    window.paymentResultHandlerInitialized = true;
    
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const paymentSuccess = urlParams.get('payment_success');
    const paymentCancelled = urlParams.get('payment_cancelled');
    
    if (paymentSuccess === 'true') {
        // 处理支付成功
        console.log('检测到支付成功参数，准备触发支付完成事件');
        
        // 延迟执行，确保认证状态已恢复
        setTimeout(() => {
            // 检查认证状态是否已恢复
            if (localStorage.getItem('authToken')) {
                // 触发自定义事件，通知其他组件支付完成
                // 统一使用 credits.js 中的方法获取积分
                if (!window.paymentEventTriggered) {
                    window.paymentEventTriggered = true;
                    const paymentEvent = new CustomEvent('paymentCompleted', {
                        detail: { success: true }
                    });
                    document.dispatchEvent(paymentEvent);
                }
                
                // 显示成功消息
                showToast(UILanguage.getText('payment.recharge_success', '充值成功！'), 'success');
            } else {
                console.log('认证状态未恢复，无法触发支付完成事件');
                showToast(UILanguage.getText('payment.recharge_success', '充值成功！但需要刷新页面查看最新积分'), 'warning');
            }
            
            // 清除URL参数
            clearPaymentParams();
        }, 800); // 延长延迟时间，给认证状态恢复留出更多时间
    } else if (paymentCancelled === 'true') {
        // 处理支付取消
        showToast(UILanguage.getText('payment.payment_cancelled', '支付已取消'), 'info');
        
        // 清除URL参数
        clearPaymentParams();
    }
}

/**
 * 清除URL中的支付参数
 */
function clearPaymentParams() {
    // 使用History API清除URL参数，不刷新页面
    const url = new URL(window.location.href);
    url.searchParams.delete('payment_success');
    url.searchParams.delete('payment_cancelled');
    url.searchParams.delete('order_id');
    url.searchParams.delete('credits');
    window.history.replaceState({}, document.title, url.toString());
}

/**
 * 更新用户积分显示
 */
async function updateCreditsDisplay() {
    try {
        // 检查认证状态
        const authToken = localStorage.getItem('authToken');
        if (!authToken) {
            console.log('用户未登录，跳过积分更新');
            return;
        }
        
        // 显示加载动画
        const creditsElement = document.getElementById('userCredits');
        if (creditsElement) {
            creditsElement.innerHTML = '<span class="credits-loading"></span>';
        }
        
        // 通过API获取最新的用户积分，带上认证令牌
        const response = await fetch('/api/credits/get', {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (!response.ok) {
            console.error('获取用户积分失败: 未能识别用户身份，请重新登录');
            return;
        }
        
        const data = await response.json();
        
        if (!data.success) {
            console.error('获取用户积分失败:', data.error);
            return;
        }
        
        // 更新UI显示
        if (creditsElement) {
            creditsElement.textContent = data.credits;
        }
        
        // 更新全局变量
        if (window.userCredits !== undefined) {
            window.userCredits = data.credits;
        }
        
        // 更新本地缓存
        try {
            localStorage.setItem('userCredits', JSON.stringify({
                credits: data.credits,
                userId: JSON.parse(localStorage.getItem('userData') || '{}').id,
                timestamp: Date.now()
            }));
            console.log('已更新本地积分缓存:', data.credits);
        } catch (cacheError) {
            console.warn('缓存积分时出错:', cacheError);
        }
        
        // 如果是购买积分后的更新，触发购买后同步
        if (window.CreditsManager && typeof window.CreditsManager.syncCreditsAfterPurchase === 'function') {
            try {
                await window.CreditsManager.syncCreditsAfterPurchase();
            } catch (syncError) {
                console.error('购买后积分同步失败:', syncError);
            }
        }
    } catch (error) {
        console.error('更新积分显示时发生错误:', error);
    }
}

/**
 * 启动充值流程
 */
function startRechargeProcess() {
    showRechargeModal(async (selectedPackage) => {
        console.log('用户选择了套餐:', selectedPackage);
        
        // 不立即关闭模态框，而是显示加载状态
        showProcessingState(UILanguage.getText('payment.order_creating', '订单创建中，请稍候...'));
        
        try {
            // 创建订单 - 直接进行，不查询积分
            const orderResult = await createPaymentOrder(selectedPackage);
            
            if (orderResult.success && orderResult.checkout_url) {
                // 关闭模态框并立即跳转
                closeRechargeModal();
                
                // 直接跳转到支付页面，不使用redirectToPayment函数
                console.log('立即跳转到支付页面:', orderResult.checkout_url);
                window.location.href = orderResult.checkout_url;
            } else {
                // 支付创建失败，显示错误并允许用户关闭模态框
                showProcessingError(UILanguage.getText('payment.order_retry', '创建订单失败，请重试'));
            }
        } catch (error) {
            console.error('处理支付跳转时出错:', error);
            showProcessingError(UILanguage.getText('payment.order_retry', '创建订单出错，请重试'));
        }
    });
}

/**
 * 在充值模态框中显示处理状态
 * @param {string} message 显示的消息
 */
function showProcessingState(message) {
    const modal = document.getElementById('rechargeModal');
    if (!modal) return;
    
    // 查找是否已存在处理状态元素
    let processingElement = modal.querySelector('.payment-processing-state');
    
    if (!processingElement) {
        // 创建处理状态元素
        processingElement = document.createElement('div');
        processingElement.className = 'payment-processing-state';
        
        // 创建处理状态内容
        processingElement.innerHTML = `
            <div class="processing-spinner"></div>
            <p class="processing-message">${message}</p>
        `;
        
        // 找到套餐容器并隐藏它
        const packageContainer = modal.querySelector('.package-container');
        if (packageContainer) {
            packageContainer.style.display = 'none';
        }
        
        // 找到模态框主体并添加处理状态
        const modalBody = modal.querySelector('.recharge-modal-body');
        if (modalBody) {
            modalBody.appendChild(processingElement);
        }
    } else {
        // 更新已存在的处理状态消息
        const messageElement = processingElement.querySelector('.processing-message');
        if (messageElement) {
            messageElement.textContent = message;
        }
    }
    
    // 禁用关闭按钮
    const closeBtn = modal.querySelector('.close-modal');
    if (closeBtn) {
        closeBtn.style.opacity = '0.5';
        closeBtn.style.pointerEvents = 'none';
    }
}

/**
 * 显示处理错误
 * @param {string} errorMessage 错误消息
 */
function showProcessingError(errorMessage) {
    const modal = document.getElementById('rechargeModal');
    if (!modal) return;
    
    // 查找处理状态元素
    const processingElement = modal.querySelector('.payment-processing-state');
    
    if (processingElement) {
        // 更新为错误状态
        processingElement.innerHTML = `
            <div class="processing-error">
                <i class="fas fa-times-circle" style="color: #e74c3c; font-size: 32px;"></i>
                <p class="error-message">${errorMessage}</p>
                <button class="try-again-btn">${UILanguage.getText('payment.retry', '重试')}</button>
            </div>
        `;
        
        // 添加重试按钮事件
        const tryAgainBtn = processingElement.querySelector('.try-again-btn');
        if (tryAgainBtn) {
            tryAgainBtn.addEventListener('click', () => {
                // 恢复套餐选择界面
                const packageContainer = modal.querySelector('.package-container');
                if (packageContainer) {
                    packageContainer.style.display = 'flex';
                }
                
                // 移除处理状态元素
                processingElement.remove();
                
                // 恢复关闭按钮
                const closeBtn = modal.querySelector('.close-modal');
                if (closeBtn) {
                    closeBtn.style.opacity = '1';
                    closeBtn.style.pointerEvents = 'auto';
                }
            });
        }
    }
    
    // 恢复关闭按钮
    const closeBtn = modal.querySelector('.close-modal');
    if (closeBtn) {
        closeBtn.style.opacity = '1';
        closeBtn.style.pointerEvents = 'auto';
    }
}

// 导出模块函数
window.paymentModule = {
    showRechargeModal,
    startRechargeProcess,
    updateCreditsDisplay,
    initPaymentResultHandler,
    createPaymentOrder
};

// 页面加载完成后初始化支付结果处理
document.addEventListener('DOMContentLoaded', () => {
    initPaymentResultHandler();
    
    // 添加对语言变化的监听
    document.addEventListener('ui-language-changed', () => {
        // 如果有必要，这里可以处理语言变更后需要更新的UI元素
    });
    
    // 直接绑定充值按钮事件，确保即使credits.js中的绑定失败也能工作
    const rechargeButton = document.getElementById('rechargeButton');
    if (rechargeButton) {
        rechargeButton.addEventListener('click', () => {
            startRechargeProcess();
        });
    }
});