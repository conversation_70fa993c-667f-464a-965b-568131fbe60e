<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>登录 | AudioPilot - AI音频学习助手</title>
  
  <!-- 环境配置脚本 -->
  <script src="/js/env-config.js"></script>
  
  <link rel="stylesheet" href="css/log.css">
  <link rel="stylesheet" href="css/mobile-styles.css">
  <style>
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }
  </style>
  
  <!-- 动态预加载检测到的语言文件 -->
  <script>
    // 创建一个预加载链接，只加载检测到的语言文件
    const detectedLang = localStorage.getItem('uiLanguage') || 
      (navigator.language && navigator.language.toLowerCase().startsWith('zh') ? 'zh' : 'en');
    const preloadLink = document.createElement('link');
    preloadLink.rel = 'preload';
    preloadLink.href = `lang/${detectedLang}.json`;
    preloadLink.as = 'fetch';
    preloadLink.crossOrigin = 'anonymous';
    document.head.appendChild(preloadLink);
  </script>
  
  <!-- 语言初始化脚本 - 放在头部优先检测语言 -->
  <script>
    // 从localStorage获取保存的语言设置，或从浏览器语言检测
    const storedLang = localStorage.getItem('uiLanguage');
    const browserLang = navigator.language || navigator.userLanguage;
    
    // 确定要使用的语言
    const supportedLanguages = ['zh', 'en'];
    let initialLang;
    
    // 优先使用存储的语言偏好，否则基于浏览器语言检测
    if (storedLang && supportedLanguages.includes(storedLang)) {
      initialLang = storedLang;
      console.log(`使用存储的语言偏好: ${initialLang}`);
    } else if (browserLang.toLowerCase().startsWith('zh')) {
      initialLang = 'zh';
      console.log(`使用浏览器设置的中文`);
    } else {
      initialLang = 'en';
      console.log(`使用英文作为默认语言`);
    }
    
    // 根据检测到的语言设置html lang属性
    document.documentElement.lang = initialLang === 'en' ? 'en' : 'zh-CN';
    
    // 根据检测到的语言立即设置页面标题
    if (initialLang === 'en') {
      document.title = 'Login | AudioPilot - AI Audio Learning Assistant';
    } else {
      document.title = '登录 | AudioPilot - AI音频学习助手';
    }
    
    // 存储检测到的语言供ui-language.js使用
    window.initialDetectedLang = initialLang;
  </script>
  
  <!-- 修改 Turnstile 脚本加载方式，使用显式渲染 -->
  <script>
    // 预先设置验证码容器样式，避免加载时的布局抖动
    document.addEventListener('DOMContentLoaded', function() {
      document.querySelectorAll('.captcha-container, .captcha-placeholder').forEach(function(el) {
        el.style.cssText = 'height: 65px !important; min-height: 65px !important; max-height: 65px !important; overflow: hidden !important; margin: 0 !important;';
      });
    });
  </script>
  <script src="https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit" async defer></script>
  <script>
    // 设置全局超时标志
    window.turnstileTimeoutOccurred = false;
    
    // 添加Turnstile超时处理
    setTimeout(function() {
      if (typeof turnstile === 'undefined') {
        console.log('[WARN] Turnstile API failed to load after timeout');
        window.turnstileTimeoutOccurred = true;
        
        // 显示友好的错误消息，但保持按钮禁用
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = '<div class="captcha-error" style="color: #dc3545;">验证码加载超时，请刷新页面重试</div>';
        });
        
        // 如果页面已加载但验证码未加载，显示页面内容
        if (!document.body.classList.contains('loaded')) {
          document.body.classList.add('loaded');
          const loadingEl = document.getElementById('initial-loading');
          if (loadingEl) loadingEl.style.display = 'none';
        }
      }
    }, 8000); // 8秒超时，比页面加载超时短
  </script>
  
  <script>
    // 强制使用本地资源
    localStorage.setItem('useLocalLibs', 'true');
    localStorage.setItem('localFirst', 'true');
    
    // 尝试从本地优先加载Supabase
    function loadSupabase() {
      const sources = [
        "js/lib/supabase.min.js",                                // 本地资源优先
        "https://unpkg.com/@supabase/supabase-js@2",             // 备用 unpkg
        "https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2",  // 备用 jsdelivr
        "https://cdnjs.cloudflare.com/ajax/libs/supabase-js/2.0.0/supabase.min.js" // 备用 cdnjs
      ];
      
      let loadAttempted = false;
      
      function tryNextSource(index) {
        if (index >= sources.length) {
          console.error("[ERROR] 无法从任何来源加载 Supabase");
          
          // 即使没有Supabase也继续加载页面
          setTimeout(function() {
            if (!loadAttempted) {
              loadAttempted = true;
              document.body.classList.add('loaded');
              const loadingEl = document.getElementById('initial-loading');
              if (loadingEl) loadingEl.style.display = 'none';
              
              // 显示连接错误消息
              const loginError = document.getElementById('loginError');
              if (loginError) {
                loginError.textContent = '连接服务器失败，请检查网络连接或稍后再试';
                loginError.style.display = 'block';
              }
            }
          }, 3000);
          return;
        }
        
        const script = document.createElement('script');
        script.src = sources[index];
        script.onerror = function() {
          console.log(`[WARN] 无法从 ${sources[index]} 加载 Supabase，尝试下一个来源...`);
          tryNextSource(index + 1);
        };
        script.onload = function() {
          console.log(`[SUCCESS] 成功从 ${sources[index]} 加载 Supabase`);
          loadAttempted = true;
        };
        document.head.appendChild(script);
      }
      
      tryNextSource(0);
    }
    
    // 立即开始加载
    loadSupabase();
  </script>
  
  <!-- 确保环境配置先加载 -->
  <script src="/env.js"></script>
  
  <!-- 添加UI语言系统 -->
  <script src="js/ui-language.js"></script>
  
  <!-- 添加身份验证脚本 -->
  <script src="js/auth.js"></script>
  
  <style>
    /* 初始加载遮罩层样式 */
    #initial-loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top-color: #3498db;
      animation: spin 1s ease-in-out infinite;
    }
    
    /* 初始隐藏页面内容 */
    body > *:not(#initial-loading) {
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    body.loaded > *:not(#initial-loading) {
      opacity: 1;
    }
    
    /* 添加验证码加载状态样式 */
    .captcha-loader {
      padding: 10px;
      text-align: center;
      color: #666;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .captcha-loader::after {
      content: "";
      width: 20px;
      height: 20px;
      margin-left: 10px;
      border: 2px solid #f3f3f3;
      border-top: 2px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Google按钮加载状态样式 */
    .loading-spinner-small {
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: #ffffff;
      animation: spin 1s ease-in-out infinite;
      display: inline-block;
      margin-right: 8px;
      vertical-align: middle;
    }
    
    .social-btn {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    /* 优化验证码容器样式 */
    .captcha-container {
      min-height: 70px;
      margin: 15px 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    /* 添加错误提示样式 */
    .captcha-error {
      color: red;
      text-align: center;
      padding: 5px;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <!-- 初始加载遮罩层 -->
  <div id="initial-loading">
    <div class="loading-spinner"></div>
  </div>
  
  <!-- 左侧品牌区域 -->
  <section class="branding-section">
    <h1 data-i18n="landing.heading">AI音频学习助手</h1>
    <p data-i18n="landing.description">支持播客订阅和音频上传，边播边录，智能问答，随时提问，提升语言听力学习效果。</p>
    
    <div class="features">
      <div class="feature-item">
        <div class="feature-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 16C15.866 16 19 12.866 19 9C19 5.13401 15.866 2 12 2C8.13401 2 5 5.13401 5 9C5 12.866 8.13401 16 12 16ZM12 14C9.23858 14 7 11.7614 7 9C7 6.23858 9.23858 4 12 4C14.7614 4 17 6.23858 17 9C17 11.7614 14.7614 14 12 14ZM13 9C13 9.55228 12.5523 10 12 10C11.4477 10 11 9.55228 11 9C11 8.44772 11.4477 8 12 8C12.5523 8 13 8.44772 13 9ZM21.7518 19.8891L20.3464 21.2945C20.1512 21.4898 19.8346 21.4898 19.6393 21.2945L15.5 17.1554L16.9053 15.75L21.7518 20.5964C21.9471 20.7917 21.9471 21.1083 21.7518 21.3036V19.8891ZM8.5 17.1554L4.36071 21.2945C4.16545 21.4898 3.84878 21.4898 3.65353 21.2945L2.24815 19.8891V21.3036C2.05289 21.1083 2.05289 20.7917 2.24815 20.5964L7.09466 15.75L8.5 17.1554Z"></path></svg>
        </div>
        <div class="feature-text" data-i18n="log.features_section.feature1.desc">AI智能语音分析，提供发音和语法指导</div>
      </div>
      <div class="feature-item">
        <div class="feature-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 22H5C3.34315 22 2 20.6569 2 19V3C2 2.44772 2.44772 2 3 2H17C17.5523 2 18 2.44772 18 3V15H22V19C22 20.6569 20.6569 22 19 22ZM18 17V19C18 19.5523 18.4477 20 19 20C19.5523 20 20 19.5523 20 19V17H18ZM16 20V4H4V19C4 19.5523 4.44772 20 5 20H16ZM6 7H14V9H6V7ZM6 11H14V13H6V11ZM6 15H11V17H6V15Z"></path></svg>
        </div>
        <div class="feature-text" data-i18n="log.features_section.feature2.desc">个性化英语学习建议，提升学习效果</div>
      </div>
      <div class="feature-item">
        <div class="feature-icon">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 8H20C20.5523 8 21 8.44772 21 9V21C21 21.5523 20.5523 22 20 22H4C3.44772 22 3 21.5523 3 21V9C3 8.44772 3.44772 8 4 8H6V7C6 3.68629 8.68629 1 12 1C15.3137 1 18 3.68629 18 7V8ZM16 8V7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7V8H16ZM11 14V16H13V14H11ZM7 14V16H9V14H7ZM15 14V16H17V14H15Z"></path></svg>
        </div>
        <div class="feature-text" data-i18n="log.features_section.feature3.desc">无需每月付费，积分套餐，随用随买</div>
      </div>
    </div>
  </section>
  
  <!-- 右侧认证区域 -->
  <section class="auth-section">
    <div class="auth-container">
      <div class="auth-header">
        <h2 data-i18n="auth.welcome">欢迎</h2>
        <p data-i18n="auth.login_or_signup" style="display:none;">登录或注册以开启您的阅读之旅</p>
      </div>
      
      <div class="auth-tabs">
        <div class="tabs">
          <button class="tab-button active" id="loginTab" data-i18n="auth.login">登录</button>
          <button class="tab-button" id="signupTab" data-i18n="auth.signup">注册</button>
        </div>
        
        <div class="tab-content">
          <!-- 登录表单 -->
          <div class="form-container active" id="loginForm">
            <form id="login">
              <div class="input-group">
                <input type="email" id="loginEmail" data-i18n="auth.email" placeholder="邮箱" required data-clarity-unmask="false">
              </div>
              <div class="input-group">
                <input type="password" id="loginPassword" data-i18n="auth.password" placeholder="密码" required data-clarity-unmask="false">
              </div>
              <div class="forgot-password">
                <a href="#" id="forgotPasswordLink" data-i18n="auth.forgot_password">忘记密码？</a>
              </div>
              <div class="captcha-container" style="text-align: center; height: 65px; min-height: 65px; max-height: 65px; overflow: hidden; margin: 0; padding: 0; transition: none !important; animation: none !important;">
                <!-- 替换为显式渲染的容器 -->
                <div id="login-turnstile" class="captcha-placeholder" style="display: flex; justify-content: center; align-items: center; height: 65px; min-height: 65px; max-height: 65px; overflow: hidden; margin: 0; padding: 0; transition: none !important; animation: none !important;"></div>
                <div class="captcha-loader" data-i18n="auth.loading_captcha" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); transition: none !important; animation: none !important;">正在加载验证...</div>
              </div>
              <button type="submit" id="loginButton" data-i18n="auth.login" disabled>登录</button>
              <div class="error-message" id="loginError"></div>
              <div class="terms-privacy">
                <span data-i18n="auth.terms_agreement">点击继续即表示您同意我们的</span> <a href="terms.html" class="policy-link" data-i18n="footer.terms">服务条款</a> <span data-i18n="auth.and">和</span> <a href="privacy.html" class="policy-link" data-i18n="footer.privacy">隐私政策</a> 
              </div>
            </form>
            
            <div class="social-login">
              <div class="divider">
                <span data-i18n="auth.or_continue_with">或继续使用</span>
              </div>
              <button class="social-btn google-btn" id="googleLogin">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19.8055 10.2275C19.8055 9.51764 19.7516 8.83386 19.6516 8.16614H10.2055V11.9039H15.6258C15.3883 13.1118 14.6883 14.1431 13.6414 14.8275V17.2667H16.8578C18.7516 15.5451 19.8055 13.1118 19.8055 10.2275Z" fill="#4285F4"/>
                  <path d="M10.2055 20C12.9258 20 15.1883 19.1118 16.8617 17.2667L13.6453 14.8275C12.7414 15.4471 11.5719 15.8039 10.2094 15.8039C7.56016 15.8039 5.33984 14.0824 4.53984 11.7647H1.22656V14.2824C2.90547 17.6667 6.29766 20 10.2055 20Z" fill="#34A853"/>
                  <path d="M4.53594 11.7647C4.33594 11.1451 4.22657 10.4824 4.22657 9.8C4.22657 9.11765 4.33594 8.45491 4.53594 7.83531V5.31763H1.22266C0.563282 6.63531 0.205469 8.1647 0.205469 9.8C0.205469 11.4353 0.563282 12.9647 1.22266 14.2824L4.53594 11.7647Z" fill="#FBBC05"/>
                  <path d="M10.2055 3.79608C11.6836 3.79608 13.0094 4.30196 14.0563 5.30196L16.9094 2.44902C15.1883 0.932157 12.9258 0 10.2055 0C6.29766 0 2.90547 2.33333 1.22656 5.71765L4.53984 8.23529C5.33984 5.91765 7.56016 3.79608 10.2055 3.79608Z" fill="#EA4335"/>
                </svg>
                <span data-i18n="auth.login_with_google">使用谷歌账号登录</span>
              </button>
            </div>
          </div>
          
          <!-- 注册表单 -->
          <div class="form-container" id="signupForm">
            <form id="signup">
              <div class="input-group">
                <input type="email" id="signupEmail" data-i18n="auth.email" placeholder="邮箱" required data-clarity-unmask="false">
              </div>
              <div class="input-group">
                <input type="password" id="signupPassword" data-i18n="auth.password" placeholder="密码" required data-clarity-unmask="false">
              </div>
              <div class="input-group">
                <input type="password" id="signupConfirmPassword" data-i18n="auth.confirm_password" placeholder="确认密码" required data-clarity-unmask="false">
              </div>
              <div id="verificationSection">
                <input type="text" id="verificationCode" data-i18n="auth.verification_code" placeholder="验证码" required data-clarity-unmask="false">
                <button type="button" id="requestVerification" data-i18n="auth.request_verification" disabled>获取验证码</button>
                <div id="verificationStatus"></div>
              </div>
              <div class="captcha-container" style="text-align: center; height: 65px; min-height: 65px; max-height: 65px; overflow: hidden; margin: 0; padding: 0; transition: none !important; animation: none !important;">
                <!-- 替换为显式渲染的容器 -->
                <div id="signup-turnstile" class="captcha-placeholder" style="display: flex; justify-content: center; align-items: center; height: 65px; min-height: 65px; max-height: 65px; overflow: hidden; margin: 0; padding: 0; transition: none !important; animation: none !important;"></div>
                <div class="captcha-loader" data-i18n="auth.loading_captcha" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); transition: none !important; animation: none !important;">正在加载验证...</div>
              </div>
              <button type="submit" id="signupButton" data-i18n="auth.signup">注册</button>
              <div class="error-message" id="signupError"></div>
              <div class="terms-privacy">
                <span data-i18n="auth.terms_agreement">点击继续即表示您同意我们的</span> <a href="terms.html" class="policy-link" data-i18n="footer.terms">服务条款</a> <span data-i18n="auth.and">和</span> <a href="privacy.html" class="policy-link" data-i18n="footer.privacy">隐私政策</a> 
              </div>
            </form>
            
            <div class="social-login">
              <div class="divider">
                <span data-i18n="auth.or_continue_with">或继续使用</span>
              </div>
              <button class="social-btn google-btn" id="googleSignupBtn">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M19.8055 10.2275C19.8055 9.51764 19.7516 8.83386 19.6516 8.16614H10.2055V11.9039H15.6258C15.3883 13.1118 14.6883 14.1431 13.6414 14.8275V17.2667H16.8578C18.7516 15.5451 19.8055 13.1118 19.8055 10.2275Z" fill="#4285F4"/>
                  <path d="M10.2055 20C12.9258 20 15.1883 19.1118 16.8617 17.2667L13.6453 14.8275C12.7414 15.4471 11.5719 15.8039 10.2094 15.8039C7.56016 15.8039 5.33984 14.0824 4.53984 11.7647H1.22656V14.2824C2.90547 17.6667 6.29766 20 10.2055 20Z" fill="#34A853"/>
                  <path d="M4.53594 11.7647C4.33594 11.1451 4.22657 10.4824 4.22657 9.8C4.22657 9.11765 4.33594 8.45491 4.53594 7.83531V5.31763H1.22266C0.563282 6.63531 0.205469 8.1647 0.205469 9.8C0.205469 11.4353 0.563282 12.9647 1.22266 14.2824L4.53594 11.7647Z" fill="#FBBC05"/>
                  <path d="M10.2055 3.79608C11.6836 3.79608 13.0094 4.30196 14.0563 5.30196L16.9094 2.44902C15.1883 0.932157 12.9258 0 10.2055 0C6.29766 0 2.90547 2.33333 1.22656 5.71765L4.53984 8.23529C5.33984 5.91765 7.56016 3.79608 10.2055 3.79608Z" fill="#EA4335"/>
                </svg>
                <span data-i18n="auth.signup_with_google">使用谷歌账号注册</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- 密码重置模态框 -->
  <div class="modal" id="resetPasswordModal">
    <div class="modal-content">
      <span class="close-modal" id="closeResetModal">&times;</span>
      <h2 data-i18n="auth.reset_password">重置密码</h2>
      <form id="passwordResetForm">
        <div class="input-group">
          <input type="email" id="resetEmail" data-i18n="auth.email" data-i18n-placeholder="auth.please_enter_email" placeholder="输入您的邮箱" required>
        </div>
        <div class="captcha-container">
          <!-- 替换为显式渲染的容器 -->
          <div id="reset-turnstile" class="captcha-placeholder"></div>
          <div class="captcha-loader" data-i18n="auth.loading_captcha">正在加载验证...</div>
        </div>
        <button type="submit" id="sendResetLink" data-i18n="auth.send_reset_link" disabled>发送重置链接</button>
      </form>
      <div class="message" id="resetMessage" style="display: none;"></div>
    </div>
  </div>
  
  <script>
    // 监听语言加载完成事件
    document.addEventListener('ui-language-loaded', function() {
      console.log('语言加载完成，显示页面内容');
      document.body.classList.add('loaded');
      
      // 先显示页面内容，确保DOM完全可见
      const loadingEl = document.getElementById('initial-loading');
      if (loadingEl) {
        loadingEl.style.opacity = '0';
        setTimeout(() => {
          loadingEl.style.display = 'none';
        }, 300);
      }
    });
    
    // 直接在脚本加载完成后初始化 Turnstile
    window.onload = function() {
      // 等待所有资源加载完成后再初始化
      setTimeout(initializeTurnstile, 1000);
    };
    
    // 检查是否启用验证码
    function isTurnstileEnabled() {
      // 检查环境变量配置
      if (window._env_ && typeof window._env_.CLOUDFLARE_ENABLED === 'boolean') {
        return window._env_.CLOUDFLARE_ENABLED;
      }
      
      if (window.ENV && window.ENV.CLOUDFLARE && typeof window.ENV.CLOUDFLARE.ENABLED === 'boolean') {
        return window.ENV.CLOUDFLARE.ENABLED;
      }
      
      // 默认启用
      return true;
    }
    
    // 获取Turnstile站点密钥的函数
    function getTurnstileSiteKey() {
      console.log(`主机名: ${window.location.hostname}`);
      
      // 优先使用环境变量
      if (window._env_ && window._env_.CLOUDFLARE_SITE_KEY) {
        console.log('使用_env_中的Turnstile密钥');
        return window._env_.CLOUDFLARE_SITE_KEY;
      }
      
      // 其次使用ENV配置
      if (window.ENV && window.ENV.CLOUDFLARE && window.ENV.CLOUDFLARE.SITE_KEY) {
        console.log('使用ENV.CLOUDFLARE.SITE_KEY中的Turnstile密钥');
        return window.ENV.CLOUDFLARE.SITE_KEY;
      }
      
      // 根据环境返回默认密钥
      const isProduction = window.ENV && window.ENV.CLOUDFLARE && window.ENV.CLOUDFLARE.IS_PRODUCTION;
      const defaultKey = isProduction ? 
        "0x4AAAAAABkTvsQTpOrFmwv2" : // 生产环境默认密钥
        "0x4AAAAAABkTvsQTpOrFmwv2"; // 开发环境默认密钥
        
      console.log(`使用${isProduction ? '生产' : '开发'}环境默认Turnstile密钥`);
      return defaultKey;
    }
    
    // 分离 Turnstile 初始化为独立函数
    function initializeTurnstile() {
      console.log('尝试初始化 Turnstile 验证码');
      
      // 检查是否启用验证码
      if (!isTurnstileEnabled()) {
        console.log('[INFO] Cloudflare Turnstile 验证码已禁用');
        
        // 隐藏加载指示器
        document.querySelectorAll('.captcha-loader').forEach(loader => {
          loader.style.display = 'none';
        });
        
        // 直接启用按钮
        document.getElementById('loginButton').disabled = false;
        document.getElementById('requestVerification').disabled = false;
        document.getElementById('sendResetLink').disabled = false;
        
        // 显示禁用提示
        document.querySelectorAll('.captcha-container').forEach(container => {
          const disabledText = UILanguage.getText('auth.captcha_disabled') || '验证码已禁用';
          container.innerHTML = `<div class="captcha-disabled-notice" style="color: #6c757d; font-size: 12px; text-align: center; font-style: italic;">${disabledText}</div>`;
        });
        return;
      }
      
      console.log('[INFO] 开始初始化 Cloudflare Turnstile 验证码');
      
      // 隐藏加载指示器
      document.querySelectorAll('.captcha-loader').forEach(loader => {
        loader.style.display = 'none';
      });
      
      // 如果已经发生超时，不再尝试初始化
      if (window.turnstileTimeoutOccurred) {
        console.log('[INFO] Skipping Turnstile initialization due to previous timeout');
        return;
      }
      
      // 确保 turnstile 对象已加载
      if (typeof turnstile === 'undefined') {
        console.log('[ERROR] Turnstile API not available');
        
        // 显示友好的错误消息，但保持按钮禁用，要求完成验证码
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = '<div class="captcha-error" style="color: #dc3545;">' + (UILanguage.getText('auth.captcha_load_failed') || '验证码加载失败，请刷新页面重试') + '</div>';
        });
        return;
      }
      
      try {
        // 清除所有现有的验证码容器内容，避免重复渲染
        document.querySelectorAll('.captcha-placeholder').forEach(el => {
          el.innerHTML = '';
        });
        
        // 隐藏加载指示器
        document.querySelectorAll('.captcha-loader').forEach(loader => {
          loader.style.display = 'none';
        });
        
        // 设置更长的超时，避免过早超时
        const turnstileRenderTimeout = 10000; // 10秒
        
        // 获取当前环境的站点密钥
        const sitekey = getTurnstileSiteKey();
        console.log(`使用Turnstile站点密钥: ${sitekey}`);
        
        // 记录当前域名信息，用于调试
        console.log(`当前域名: ${window.location.hostname}`);
        console.log(`当前协议: ${window.location.protocol}`);
        console.log(`完整URL: ${window.location.href}`);
        
        // 添加额外的选项来提高兼容性
        const turnstileOptions = {
          sitekey: sitekey,
          retry: 'auto',           // 自动重试
          appearance: 'always',    // 始终显示
          'refresh-expired': 'auto', // 自动刷新过期的验证
          timeout: turnstileRenderTimeout
        };
        
        // 预先设置容器样式，避免加载时的布局抖动
        document.querySelectorAll('.captcha-container, .captcha-placeholder').forEach(el => {
          el.style.cssText = 'height: 65px !important; min-height: 65px !important; max-height: 65px !important; overflow: hidden !important; margin: 0 !important; padding: 0 !important; transition: none !important; animation: none !important;';
        });
        
        // 预先设置所有按钮的样式，避免加载时的布局抖动
        document.querySelectorAll('button[type="submit"]').forEach(btn => {
          btn.style.cssText = 'margin-top: 3px !important; margin-bottom: 3px !important; transition: none !important; animation: none !important;';
        });
        
        // 登录验证码
        const loginWidgetId = turnstile.render('#login-turnstile', {
          ...turnstileOptions,
          callback: function(token) {
            console.log('[SUCCESS] Login verification token received');
            if (typeof window.onLoginCaptchaVerified === 'function') {
              window.onLoginCaptchaVerified(token);
            }
            // 立即居中验证码，避免布局抖动
            centerTurnstileWidget('#login-turnstile');
          },
          'error-callback': function(error) {
            console.log(`[ERROR] Login verification failed to load: ${error}`);
            document.querySelector('#loginForm .captcha-container').innerHTML = 
              '<div class="captcha-error" style="color: #dc3545;">' + (UILanguage.getText('auth.captcha_error') || '验证失败，请刷新页面') + '</div>';
          },
          'timeout-callback': function() {
            console.log('[WARN] Login verification timed out');
            document.querySelector('#loginForm .captcha-container').innerHTML = 
              '<div class="captcha-error" style="color: #dc3545;">验证超时，请刷新页面</div>';
          }
        });
        
        // 注册验证码
        const signupWidgetId = turnstile.render('#signup-turnstile', {
          ...turnstileOptions,
          callback: function(token) {
            console.log('[SUCCESS] Signup verification token received');
            if (typeof window.onCaptchaVerified === 'function') {
              window.onCaptchaVerified(token);
            }
          },
          'error-callback': function(error) {
            console.log(`[ERROR] Signup verification failed to load: ${error}`);
            document.querySelector('#signupForm .captcha-container').innerHTML = 
              '<div class="captcha-error" style="color: #dc3545;">' + (UILanguage.getText('auth.captcha_error') || '验证失败，请刷新页面') + '</div>';
          },
          'timeout-callback': function() {
            console.log('[WARN] Signup verification timed out');
            document.querySelector('#signupForm .captcha-container').innerHTML = 
              '<div class="captcha-error" style="color: #dc3545;">验证超时，请刷新页面</div>';
          }
        });
        
        // 重置密码验证码
        const resetWidgetId = turnstile.render('#reset-turnstile', {
          ...turnstileOptions,
          callback: function(token) {
            console.log('[SUCCESS] Reset password verification token received');
            if (typeof window.onResetCaptchaVerified === 'function') {
              window.onResetCaptchaVerified(token);
            }
          },
          'error-callback': function(error) {
            console.log(`[ERROR] Reset password verification failed to load: ${error}`);
            document.querySelector('#resetPasswordModal .captcha-container').innerHTML = 
              '<div class="captcha-error" style="color: #dc3545;">' + (UILanguage.getText('auth.captcha_error') || '验证失败，请刷新页面') + '</div>';
          },
          'timeout-callback': function() {
            console.log('[WARN] Reset password verification timed out');
            document.querySelector('#resetPasswordModal .captcha-container').innerHTML = 
              '<div class="captcha-error" style="color: #dc3545;">验证超时，请刷新页面</div>';
          }
        });
        
        // 存储widget IDs以便后续可能的重置
        window.turnstileWidgets = {
          login: loginWidgetId,
          signup: signupWidgetId,
          reset: resetWidgetId
        };
        
        console.log('[SUCCESS] Turnstile widgets successfully rendered');
      } catch (error) {
        console.error(`[ERROR] Turnstile rendering failed: ${error.message}`);
        // 记录详细错误信息以便调试
        console.error('Error stack:', error.stack);
        
        // 显示错误信息，要求用户刷新页面
        document.querySelectorAll('.captcha-container').forEach(container => {
          container.innerHTML = `<div class="captcha-error" style="color: #dc3545;">验证码加载失败: ${error.message || '未知错误'}，请刷新页面重试</div>`;
        });
      }
    }
    
    // 添加验证码居中显示函数
    function centerTurnstileWidget(selector) {
      // 立即执行，不使用延时，避免布局抖动
      const container = document.querySelector(selector);
      if (!container) return;
      
      // 找到Turnstile生成的iframe
      const iframe = container.querySelector('iframe');
      if (!iframe) return;
      
      // 添加居中样式，使用!important确保样式优先级
      iframe.style.cssText = 'display: block !important; margin: 0 auto !important; height: 65px !important; position: static !important; transform: none !important; transition: none !important; animation: none !important;';
      
      // 确保容器样式固定
      container.style.cssText = 'display: flex !important; justify-content: center !important; align-items: center !important; height: 65px !important; min-height: 65px !important; max-height: 65px !important; overflow: hidden !important; margin: 0 !important; padding: 0 !important; transition: none !important; animation: none !important;';
      
      // 确保按钮不受影响
      const loginButton = document.getElementById('loginButton');
      if (loginButton) {
        loginButton.style.cssText = 'margin-top: 3px !important; margin-bottom: 3px !important; transition: none !important; animation: none !important;';
      }
      
      console.log('Turnstile widget centered immediately');
    }
    
    // 移除 window.onload 事件处理，改为使用更可靠的方法
    // 如果页面已经加载完成但语言还没加载完，添加一个超时处理
    setTimeout(function() {
      if (!document.body.classList.contains('loaded')) {
        console.log('[WARN] Language loading timeout, initializing Turnstile anyway');
        document.body.classList.add('loaded');
        const loadingEl = document.getElementById('initial-loading');
        if (loadingEl) {
          loadingEl.style.display = 'none';
        }
        initializeTurnstile();
      }
    }, 5000); // 5秒超时
    
    // 添加重试功能
    function retryTurnstile() {
      console.log('[INFO] Retrying Turnstile initialization');
      
      // 如果有存储的widget IDs，先重置它们
      if (window.turnstileWidgets) {
        try {
          for (const key in window.turnstileWidgets) {
            if (window.turnstileWidgets[key]) {
              turnstile.reset(window.turnstileWidgets[key]);
            }
          }
        } catch (e) {
          console.error('[ERROR] Failed to reset Turnstile widgets:', e);
        }
      }
      
      // 重新显示加载指示器
      document.querySelectorAll('.captcha-loader').forEach(loader => {
        loader.style.display = 'flex';
      });
      
      // 重新初始化
      setTimeout(initializeTurnstile, 500);
    }
  </script>
  
  <!-- Microsoft Clarity (放在body结束标签前，确保不干扰页面渲染) -->
  <script type="text/javascript">
    // 只在生产环境中加载Clarity，避免开发环境中的400错误
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
      (function(c,l,a,r,i,t,y){
          c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
          t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
          y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
      })(window, document, "clarity", "script", "rimwd810jk");
    }
  </script>
</body>
</html>