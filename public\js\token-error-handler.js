// Token错误处理模块
window.TokenErrorHandler = (function() {
    // 私有变量，存储当前模态框
    let currentModal = null;
    
    // 创建并显示Token限制错误模态框
    function handleTokenLimitError(errorData, operationType) {
        // 如果已有模态框，先移除
        if (currentModal && document.body.contains(currentModal)) {
            document.body.removeChild(currentModal);
        }
        
        // 获取当前UI语言
        const currentLang = window.UILanguage ? 
            window.UILanguage.getCurrentLanguage() : 
            (navigator.language.startsWith('zh') ? 'zh' : 'en');
        
        // 设置文本内容 - 支持国际化
        const texts = {
            title: {
                zh: '内容长度超出限制',
                en: 'Token Limit Error'
            },
            message: {
                zh: '输入内容超出模型处理能力，请减少内容或选择支持更长文本的模型。',
                en: 'Input exceeds model capacity. Please reduce content or choose a model that supports bigger context.'
            },
            estimated: {
                zh: '预计使用（token）:',
                en: 'Estimated usage(token):'
            },
            maxLimit: {
                zh: '最大允许(token):',
                en: 'Maximum limit(token):'
            },
            close: {
                zh: '关闭',
                en: 'Close'
            }
        };
        
        // 创建模态框容器
        const modal = document.createElement('div');
        modal.className = 'token-error-modal';
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.right = '0';
        modal.style.bottom = '0';
        modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        modal.style.display = 'flex';
        modal.style.justifyContent = 'center';
        modal.style.alignItems = 'center';
        modal.style.zIndex = '10000';
        
        // 创建模态框内容
        const modalContent = document.createElement('div');
        modalContent.className = 'token-error-content';
        modalContent.style.backgroundColor = 'white';
        modalContent.style.borderRadius = '8px';
        modalContent.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        modalContent.style.width = '400px';
        modalContent.style.maxWidth = '90%';
        modalContent.style.padding = '0';
        modalContent.style.position = 'relative';
        modalContent.style.overflow = 'hidden';
        
        // 创建标题区域
        const titleBar = document.createElement('div');
        titleBar.style.padding = '12px 15px';
        titleBar.style.backgroundColor = '#f5f5f5';
        titleBar.style.borderBottom = '1px solid #e0e0e0';
        titleBar.style.fontWeight = 'bold';
        titleBar.style.fontSize = '16px';
        titleBar.style.color = '#333';
        titleBar.style.textAlign = 'center';
        titleBar.textContent = texts.title[currentLang];
        
        // 创建关闭按钮
        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;'; // × 符号
        closeButton.style.position = 'absolute';
        closeButton.style.right = '10px';
        closeButton.style.top = '10px';
        closeButton.style.border = 'none';
        closeButton.style.background = 'transparent';
        closeButton.style.fontSize = '20px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#666';
        closeButton.style.fontWeight = 'bold';
        closeButton.style.padding = '0 5px';
        closeButton.style.lineHeight = '1';
        
        // 添加关闭按钮点击事件
        closeButton.addEventListener('click', function() {
            document.body.removeChild(modal);
            currentModal = null;
        });
        
        // 创建消息内容区
        const messageBody = document.createElement('div');
        messageBody.style.padding = '15px';
        messageBody.style.color = '#333';
        messageBody.style.fontSize = '14px';
        
        // 添加错误消息
        const messageText = document.createElement('p');
        messageText.style.margin = '0 0 15px 0';
        messageText.style.lineHeight = '1.5';
        messageText.textContent = texts.message[currentLang];
        
        // 添加token使用信息
        const tokenInfo = document.createElement('div');
        tokenInfo.style.backgroundColor = '#f9f9f9';
        tokenInfo.style.padding = '10px';
        tokenInfo.style.borderRadius = '4px';
        tokenInfo.style.fontSize = '13px';
        
        // 准备token使用数据
        let estimatedTokens = '?';
        let maxTokens = '?';
        
        if (errorData.error_type === 'input_exceeded' && errorData.details) {
            estimatedTokens = errorData.details.estimated_input_tokens || '?';
            maxTokens = errorData.details.max_input_tokens || '?';
        } else if (errorData.error_type === 'output_exceeded' && errorData.details) {
            estimatedTokens = errorData.details.estimated_output_tokens || '?';
            maxTokens = errorData.details.max_output_tokens || '?';
        }
        
        // 添加估计使用量
        const estimatedRow = document.createElement('div');
        estimatedRow.style.display = 'flex';
        estimatedRow.style.justifyContent = 'space-between';
        estimatedRow.style.marginBottom = '5px';
        
        const estimatedLabel = document.createElement('span');
        estimatedLabel.textContent = texts.estimated[currentLang];
        
        const estimatedValue = document.createElement('span');
        estimatedValue.textContent = estimatedTokens;
        estimatedValue.style.fontWeight = 'bold';
        
        estimatedRow.appendChild(estimatedLabel);
        estimatedRow.appendChild(estimatedValue);
        
        // 添加最大限制
        const maxRow = document.createElement('div');
        maxRow.style.display = 'flex';
        maxRow.style.justifyContent = 'space-between';
        
        const maxLabel = document.createElement('span');
        maxLabel.textContent = texts.maxLimit[currentLang];
        
        const maxValue = document.createElement('span');
        maxValue.textContent = maxTokens;
        maxValue.style.fontWeight = 'bold';
        
        maxRow.appendChild(maxLabel);
        maxRow.appendChild(maxValue);
        
        // 组合token信息
        tokenInfo.appendChild(estimatedRow);
        tokenInfo.appendChild(maxRow);
        
        // 创建底部区域
        const footer = document.createElement('div');
        footer.style.padding = '10px 15px';
        footer.style.textAlign = 'right';
        footer.style.borderTop = '1px solid #e0e0e0';
        
        // 创建关闭按钮
        const closeActionButton = document.createElement('button');
        closeActionButton.textContent = texts.close[currentLang];
        closeActionButton.style.backgroundColor = '#2196F3';
        closeActionButton.style.color = 'white';
        closeActionButton.style.border = 'none';
        closeActionButton.style.padding = '6px 15px';
        closeActionButton.style.borderRadius = '4px';
        closeActionButton.style.cursor = 'pointer';
        closeActionButton.style.fontSize = '14px';
        
        // 添加关闭按钮事件
        closeActionButton.addEventListener('click', function() {
            document.body.removeChild(modal);
            currentModal = null;
        });
        
        // 组合底部区域
        footer.appendChild(closeActionButton);
        
        // 组合消息内容区
        messageBody.appendChild(messageText);
        messageBody.appendChild(tokenInfo);
        
        // 组合整个模态框
        modalContent.appendChild(titleBar);
        modalContent.appendChild(closeButton);
        modalContent.appendChild(messageBody);
        modalContent.appendChild(footer);
        
        modal.appendChild(modalContent);
        document.body.appendChild(modal);
        
        // 保存当前模态框引用
        currentModal = modal;
        
        // 点击模态框外部区域关闭模态框
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                document.body.removeChild(modal);
                currentModal = null;
            }
        });
    }
    
    // 处理流式响应中的token错误
    function handleStreamTokenError(errorData, containerElement, operationType, abortFunction) {
        // 使用模态框展示错误信息
        handleTokenLimitError(errorData, operationType);
        
        // 在容器中显示友好的提示
        if (containerElement && typeof containerElement.innerHTML === 'string') {
            const message = window.UILanguage ? 
                window.UILanguage.getText('main.error.token_limit_exceeded', '内容超出模型处理能力限制') :
                '内容超出模型处理能力限制';
            
            containerElement.innerHTML = `<div class="info-message" style="padding: 15px; color: #555; text-align: center;">${message}</div>`;
        }
        
        // 如果提供了中止函数，调用它来清理资源
        if (typeof abortFunction === 'function') {
            abortFunction();
        }
        
        return true;
    }
    
    // 返回公共API
    return {
        handleTokenLimitError: handleTokenLimitError,
        handleStreamTokenError: handleStreamTokenError
    };
})();