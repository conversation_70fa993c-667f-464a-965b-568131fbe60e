.loading-spin-animation {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 时间戳功能样式 */

/* 整体应用容器 */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    padding-left: 8px; /* 添加左侧内边距，确保整体布局对称 */
}

/* 头部样式 - 横贯左右 */
.header {
    width: 100%;
    z-index: 200;
    border-bottom: 1px solid #E2E8F0;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-right: 4px; /* 调整右侧边距与新的间距保持一致 */
}

/* 主内容包装器 */
.main-wrapper {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 左侧边栏 */
.sidebar {
    width: 325px !important;
    background: #F7F8FA !important;
    border-right: 1px solid #E2E8F0 !important;
    display: flex !important;
    flex-direction: column !important;
    transition: transform 0.3s ease !important;
    z-index: 100 !important;
    overflow: hidden !important; /* 让内部区域自己控制滚动 */
    padding-bottom: 15px !important; /* 增加底部间距确保与页面底部有距离 */
    margin-left: 4px !important;
    margin-top: 4px !important;
    margin-bottom: 8px !important; /* 增加底部外边距与页面底部保持距离 */
    height: calc(100vh - 65px) !important; /* 增加总高度：80px → 60px，为内容提供更多空间 */
}

.sidebar.hidden {
    transform: translateX(-100%);
}

/* 边栏标题样式（已移除，不再需要） */

/* 时间戳上传按钮部分 */
.timestamp-upload-section {
    padding: 8px 12px; /* 进一步减少内边距 */
    border-bottom: 1px solid #E2E8F0;
}

/* 时间戳按钮组布局 */
.timestamp-buttons {
    display: flex;
    gap: 8px;
    width: 100%;
}

.timestamp-btn {
    flex: 1;
    padding: 8px 12px;
    font-size: 0.65rem;  /* 进一步减小字体大小以适应两个按钮 */
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 0; /* 允许按钮收缩 */
}

.timestamp-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.timestamp-btn.has-file {
    background: #48BB78;
    border-color: #48BB78;
    color: white;
}

.timestamp-btn.has-file:hover {
    background: #38A169;
    border-color: #38A169;
}

/* 生成按钮特殊样式 */
#generateTimestampBtn {
    background: #409EFF;
    border-color: #409EFF;
    color: white;
}

#generateTimestampBtn:hover:not(:disabled) {
    background: #337CC5;
    border-color: #337CC5;
}

#generateTimestampBtn.generating {
    background: #67C23A;
    border-color: #67C23A;
}

/* 上传按钮保持原有样式 */
#uploadTimestampBtn {
    background: #E5E7EB;
    border-color: #E5E7EB;
    color: #374151;
}

#uploadTimestampBtn:hover:not(:disabled) {
    background: #D1D5DB;
    border-color: #D1D5DB;
}

/* 保持向后兼容性 */
.timestamp-upload-btn {
    width: 100%;
    padding: 10px 16px;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.timestamp-upload-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.timestamp-upload-btn.has-file {
    background: #48BB78;
    border-color: #48BB78;
    color: white;
}

.timestamp-upload-btn.has-file:hover {
    background: #38A169;
    border-color: #38A169;
}

/* 时间戳部分 */
.timestamp-section {
    flex: 0 0 280px; /* 减少固定高度：350px → 280px，为章节摘要区腾出空间 */
    max-height: 280px; /* 设置最大高度限制 */
    min-height: 200px; /* 设置最小高度，确保基本可用性 */
    padding: 8px 12px;
    border-bottom: 1px solid #E2E8F0;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 容器不溢出 */
}

.timestamp-section h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
    color: #2D3748;
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0; /* 标题不被压缩 */
}

.timestamp-section h4 i {
    color: #409EFF;
}

.timestamp-list {
    flex: 1; /* 占剩余空间 */
    overflow-y: auto !important; /* 强制启用垂直滚动条 */
    overflow-x: hidden !important; /* 隐藏水平滚动条 */
    min-height: 0; /* 确保flex子元素可以缩小 */
    max-height: 220px; /* 调整最大高度以适应新的时间戳区域高度 */
}

.timestamp-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-bottom: 4px;
    background: rgba(64, 158, 255, 0.1);
    color: #409EFF;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
}

.timestamp-item:hover {
    background: rgba(64, 158, 255, 0.2);
}

.timestamp-item.active {
    background: #409EFF;
    color: white;
}

.timestamp-item .time {
    font-weight: 600;
}

.timestamp-item .title {
    flex: 1;
    margin-left: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.no-timestamps {
    padding: 16px;
    text-align: center;
    color: #718096;
    font-size: 13px;
    font-style: italic;
}

/* 章节内容部分 */
.chapter-content {
    flex: 1; /* 占据剩余空间 */
    min-height: 500px; /* 增加最小高度：400px → 500px，充分利用腾出的空间 */
    padding: 3px 12px 5px 12px; /* 增加底部内边距：8px → 20px */
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 容器不溢出 */
}

.chapter-content h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
    color: #2D3748;
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0; /* 标题不被压缩 */
}

.chapter-content h4 i {
    color: #409EFF;
}

.chapter-text {
    flex: 1; /* 占剩余空间 */
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow-y: auto !important; /* 强制启用垂直滚动条 */
    overflow-x: hidden !important; /* 隐藏水平滚动条 */
    font-size: 14px;
    line-height: 1.6;
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
    color: #4A5568;
    min-height: 480px !important; /* 强制设置最小高度 */
    height: auto !important; /* 强制自动高度 */
}

.chapter-text h1, .chapter-text h2, .chapter-text h3 {
    color: #2D3748;
    margin-top: 16px;
    margin-bottom: 8px;
}

.chapter-text h1 {
    font-size: 18px;
    font-weight: 600;
}

.chapter-text h2 {
    font-size: 16px;
    font-weight: 600;
}

.chapter-text h3 {
    font-size: 15px;
    font-weight: 600;
}

.chapter-text p {
    margin-bottom: 12px;
}

.chapter-text ul, .chapter-text ol {
    margin-bottom: 12px;
    padding-left: 20px;
}

.chapter-text li {
    margin-bottom: 4px;
}

.no-chapter {
    text-align: center;
    color: #718096;
    font-style: italic;
    padding: 32px 16px;
}

/* 调整原有容器样式以适应新布局 */
.main-wrapper .container {
    flex: 1 !important;
    width: 100% !important;
    max-width: none !important;
    padding: 0px 4px 0px 4px !important; /* 取中间值的内边距 */
    margin-right: 4px !important; /* 取中间值：在2px和6px之间 */
    margin-left: 2px !important; /* 取中间值：在0px和4px之间 */
    margin-top: 4px !important; /* 减少与header的间距 */
    gap: 2px !important; /* 进一步减少间距，让内容更紧凑 */
    display: flex !important;
    flex-direction: column !important;
    height: calc(100% - 4px) !important; /* 调整高度计算 */
    box-sizing: border-box !important;
}

/* 重写智能问答区域样式以确保生效 */
.main-wrapper .container .qa-section {
    flex: 1 !important;
    min-height: 200px !important;
    max-height: calc(100vh - 180px) !important; /* 调整高度，为底部留出间距 */
    margin: 0 !important; /* 移除所有外边距 */
    overflow: hidden !important;
    display: flex !important;
}

.main-wrapper .qa-section .card {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    width: 100% !important;
    overflow: hidden !important;
    position: relative !important;
    padding: 8px 12px 8px 12px; /* 增加内边距：上下8px 左右12px */
}

/* AI结果显示容器 - 适配时间戳布局，固定高度 */
.main-wrapper .chat-messages-container {
    height: 510px !important; /* 固定高度，不会随内容变化 */
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important;
    margin-bottom: 3px !important; /* 与输入框保持间距 */
    flex-shrink: 0 !important; /* 防止被压缩 */
}

.main-wrapper .chat-messages {
    flex: 1 !important;
    overflow-y: auto !important;
    padding: 10px !important;
    background: #f8f9fa !important;
    border-radius: 8px !important;
    border: 1px solid #e9ecef !important;
    min-height: 100px !important;
    max-height: 100% !important; /* 占用所有可用空间 */
}

/* 独立的输入框容器 - 固定位置 */
.main-wrapper .chat-input-container {
    display: flex !important;
    gap: 10px !important;
    width: 100% !important;
    padding: 8px !important;
    background: white !important;
    border-bottom-left-radius: 8px !important;
    border-bottom-right-radius: 8px !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important; /* 防止被压缩 */
}

/* 时间戳文件上传按钮样式 */
#timestampBtn {
    position: relative;
}

#timestampBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#timestampBtn.has-file {
    background: #48BB78;
    border-color: #48BB78;
}

#timestampBtn.has-file:hover {
    background: #38A169;
    border-color: #38A169;
}

/* 滚动条样式 */
.timestamp-list::-webkit-scrollbar,
.chapter-text::-webkit-scrollbar {
    width: 8px; /* 增加滚动条宽度使其更明显 */
}

.timestamp-list::-webkit-scrollbar-track,
.chapter-text::-webkit-scrollbar-track {
    background: #F7FAFC;
    border-radius: 4px;
}

.timestamp-list::-webkit-scrollbar-thumb,
.chapter-text::-webkit-scrollbar-thumb {
    background: #CBD5E0;
    border-radius: 4px;
    border: 1px solid #E2E8F0; /* 添加边框使滚动条更明显 */
}

.timestamp-list::-webkit-scrollbar-thumb:hover,
.chapter-text::-webkit-scrollbar-thumb:hover {
    background: #A0AEC0;
}

/* 确保Firefox也有滚动条样式 */
.timestamp-list,
.chapter-text {
    scrollbar-width: thin;
    scrollbar-color: #CBD5E0 #F7FAFC;
}

/* 强制修复章节摘要区白色框高度 - 高优先级规则 */
.sidebar .chapter-content .chapter-text,
#chapterText,
.sidebar .chapter-text {
    min-height: 500px !important;
    height: auto !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        padding-left: 0; /* 移动端移除左侧内边距 */
    }
    
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 1000;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        margin: 0; /* 移动端移除所有边距 */
        width: 280px; /* 移动端使用较小宽度 */
        padding-bottom: 10px; /* 移动端底部内边距 */
    }
    
    .sidebar.hidden {
        transform: translateX(-100%);
    }
    
    .main-wrapper {
        width: 100%;
    }
    
    .main-wrapper .container {
        margin-right: 0; /* 移动端移除右侧边距 */
    }
    
    .header {
        margin-right: 0; /* 移动端移除头部右侧边距 */
    }
    
    /* 添加遮罩层 */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }
    
    /* 移动端按钮布局调整 */
    .upload-controls {
        flex-direction: column;
        gap: 8px;
    }
    
    .upload-controls .btn {
        width: 100%;
        margin: 0;
    }
}

/* 加载状态样式 */
.timestamp-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #718096;
}

.timestamp-loading i {
    margin-right: 8px;
    animation: spin 1s linear infinite;
}

/* 流式生成状态样式 */
.timestamp-generating {
    padding: 20px;
    text-align: center;
    color: #409EFF;
    font-size: 14px;
}

.timestamp-generating .generating-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #E2E8F0;
    border-top: 2px solid #409EFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

.timestamp-generating .generating-text {
    color: #4A5568;
    font-weight: 500;
}

/* 流式时间戳项目样式 */
.timestamp-item.streaming {
    background: rgba(64, 158, 255, 0.05);
    border-left: 3px solid #409EFF;
    opacity: 0;
    animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 错误状态样式 */
.timestamp-error {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    flex-direction: column;
    background: #fff5f5;
    border: 1px solid #feb2b2;
    border-radius: 8px;
    margin: 10px;
    text-align: center;
}

.error-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.error-text {
    font-size: 14px;
    color: #e53e3e;
    font-weight: 500;
    margin-bottom: 5px;
}

.error-hint {
    font-size: 12px;
    color: #718096;
}

.chapter-error {
    text-align: center;
    color: #C53030;
    font-style: italic;
    padding: 32px 16px;
    background: #FED7E2;
    border-radius: 6px;
}

/* 强制覆盖间距规则 - 确保生效 */
body .app-container .main-wrapper .sidebar {
    margin-left: 4px !important;
    border-right: 1px solid #E2E8F0 !important;
}

body .app-container .main-wrapper .container {
    margin-left: 2px !important;
    margin-right: 4px !important;
    padding-left: 4px !important;
    padding-right: 4px !important;
}

/* 调试用：临时添加边框来验证间距变化 */
.debug-spacing .sidebar {
    border: 2px solid red !important;
}

.debug-spacing .main-wrapper .container {
    border: 2px solid blue !important;
}

/* Tab 切换导航样式 */
.sidebar-tabs {
    background: white;
    border-bottom: 1px solid #E2E8F0;
    padding: 8px 12px 0 12px;
    margin-bottom: 0;
}

.tab-nav {
    display: flex;
    gap: 2px;
}

.tab-btn {
    flex: 1;
    padding: 8px 16px;
    font-size: 0.75rem;  /* 增大字体大小 */
    font-weight: 500;
    color: #666;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    background: rgba(64, 158, 255, 0.1);
    color: #409EFF;
}

.tab-btn.active {
    background: #409EFF;
    color: white;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: #409EFF;
}

.tab-btn i {
    font-size: 12px;
}

/* Tab 内容样式 */
.tab-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
}

.tab-content.hidden {
    display: none;
}

/* 播客搜索区域样式 */
.podcast-search-section {
    padding: 8px 12px;
    border-bottom: 1px solid #E2E8F0;
}

.search-box {
    display: flex;
    background: white;
    border: 1px solid #E2E8F0;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 4px;
}

#podcastSearchBox, #subtitleSearchBox {
    flex: 1;
    padding: 8px 12px;
    border: none;
    outline: none;
    font-size: 13px;
    background: transparent;
    height: 100%;
    box-sizing: border-box;
}

#podcastSearchBox::placeholder {
    color: #A0AEC0;
}

.search-btn {
    padding: 8px 12px;
    background: #409EFF;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-btn:hover {
    background: #3182CE;
}

.search-loading {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #718096;
    padding: 8px 0;
}

.search-loading.hidden {
    display: none;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(64, 158, 255, 0.2);
    border-top: 2px solid #409EFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 播客收藏列表样式 */
.podcast-favorites {
    flex: 0 0 240px;
    padding: 8px 12px;
    border-bottom: 1px solid #E2E8F0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.podcast-favorites h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    color: #2D3748;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.podcast-favorites h4 i {
    color: #E53E3E;
}

.podcast-favorites-list {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
}

.podcast-favorites-list::-webkit-scrollbar {
    width: 4px;
}

.podcast-favorites-list::-webkit-scrollbar-track {
    background: transparent;
}

.podcast-favorites-list::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

.podcast-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    margin-bottom: 1px;
    background: rgba(64, 158, 255, 0.05);
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
}

.podcast-item:hover {
    background: rgba(64, 158, 255, 0.1);
}

.podcast-item.selected {
    background: #409EFF;
    color: white;
}

.podcast-item.selected .podcast-name,
.podcast-item.selected .podcast-author {
    color: white;
}

.podcast-item .podcast-artwork {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    margin-right: 8px;
    object-fit: cover;
}

.podcast-item .podcast-info {
    flex: 1;
    min-width: 0;
}

.podcast-item .podcast-name {
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.podcast-item .podcast-author {
    font-size: 11px;
    opacity: 0.7;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.podcast-item .favorite-btn {
    width: 20px;
    height: 20px;
    border: none;
    background: transparent;
    color: #E53E3E;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 4px;
    transition: all 0.2s ease;
}

.podcast-item .favorite-btn:hover {
    background: rgba(229, 62, 62, 0.1);
}

.no-favorites {
    text-align: center;
    color: #718096;
    font-size: 12px;
    font-style: italic;
    padding: 12px;
}

/* 播客节目列表样式 */
.podcast-episodes {
    flex: 1;
    padding: 6px 0px 0 10px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
    position: relative;
}

.podcast-episodes h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    color: #2D3748;
    display: flex;
    align-items: center;
    gap: 6px;
    flex-shrink: 0;
}

.podcast-episodes h4 i {
    color: #409EFF;
}

.podcast-episodes-list {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
    padding-bottom: 15px;
}

.podcast-episodes-list::-webkit-scrollbar {
    width: 3px; /* 减少一半：6px → 3px */
}

.podcast-episodes-list::-webkit-scrollbar-track {
    background: #F7F8FA;
}

.podcast-episodes-list::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 2px; /* 相应减少圆角：4px → 2px */
}

.episode-item {
    padding: 8px;
    margin-bottom: 6px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.episode-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.episode-header {
    margin-bottom: 6px;
}

.episode-title {
    font-size: 13px;
    font-weight: 600;
    color: #2D3748;
    line-height: 1.4;
    margin-bottom: 6px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.episode-meta {
    font-size: 11px;
    color: #718096;
    display: flex;
    align-items: center;
    gap: 8px;
}

.episode-description {
    font-size: 12px;
    color: #4A5568;
    line-height: 1.3;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.episode-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.episode-player-controls {
    display: flex;
    gap: 4px;
    align-items: center;
}

.episode-download-btn {
    padding: 6px 12px !important;
    background: #48BB78 !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 4px !important;
    min-width: 80px !important; /* 固定最小宽度，防止尺寸变化 */
    width: 80px !important; /* 固定宽度 */
    height: 32px !important; /* 与其他按钮保持相同高度 */
    box-sizing: border-box !important;
    outline: none !important; /* 移除所有焦点样式 */
    -webkit-tap-highlight-color: transparent !important; /* 移除移动端点击高亮 */
    -webkit-touch-callout: none !important; /* 移除长按菜单 */
    -webkit-user-select: none !important; /* 禁止文本选择 */
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    transform: none !important; /* 重置任何可能的transform */
    box-shadow: none !important; /* 重置任何可能的阴影 */
}

.episode-download-btn:hover {
    background: #38A169 !important;
    transform: none !important;
    box-shadow: none !important;
}

.episode-download-btn:disabled {
    background: #CBD5E0 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

.episode-play-btn {
    padding: 6px 12px !important;
    background: #409EFF !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 4px !important;
    min-width: 60px !important; /* 固定最小宽度，防止尺寸变化 */
    width: 60px !important; /* 固定宽度 */
    height: 32px !important; /* 与快退快进按钮保持相同高度 */
    box-sizing: border-box !important;
    outline: none !important; /* 移除所有焦点样式 */
    -webkit-tap-highlight-color: transparent !important; /* 移除移动端点击高亮 */
    -webkit-touch-callout: none !important; /* 移除长按菜单 */
    -webkit-user-select: none !important; /* 禁止文本选择 */
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    transform: none !important; /* 重置任何可能的transform */
    box-shadow: none !important; /* 重置任何可能的阴影 */
}

/* 移除播放按钮的hover效果 */
.episode-play-btn:hover {
    /* 不改变背景色，保持原有样式 */
    background: #409EFF !important;
    transform: none !important;
    box-shadow: none !important;
}

/* 播放按钮加载状态样式 - 高优先级 */
.episode-play-btn.loading-state {
    background: #409EFF !important;
    color: white !important;
    opacity: 1 !important;
    cursor: not-allowed;
}

.episode-play-btn.loading-state .loading-spin-animation {
    color: white !important;
}

/* 下载按钮加载状态样式 - 高优先级 */
.episode-download-btn.loading-state {
    background: #48BB78 !important;
    color: white !important;
    opacity: 1 !important;
    cursor: not-allowed;
}

.episode-download-btn.loading-state .loading-spin-animation {
    color: white !important;
}

/* 快退和快进按钮样式 */
.episode-rewind-btn,
.episode-forward-btn {
    padding: 6px 8px !important;
    background: #409EFF !important;
    color: white !important;
    border: none !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 42px !important; /* 增加1/3宽度：32px * 1.33 ≈ 42px */
    height: 32px !important;
    min-width: 42px !important;
    box-sizing: border-box !important;
    vertical-align: middle !important;
    outline: none !important; /* 移除所有焦点样式 */
    -webkit-tap-highlight-color: transparent !important; /* 移除移动端点击高亮 */
    -webkit-touch-callout: none !important; /* 移除长按菜单 */
    -webkit-user-select: none !important; /* 禁止文本选择 */
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    transform: none !important; /* 重置任何可能的transform */
    box-shadow: none !important; /* 重置任何可能的阴影 */
}

.episode-rewind-btn:hover,
.episode-forward-btn:hover {
    background: #3A8BDB !important;
    transform: scale(1.05) !important;
    box-shadow: none !important;
}

.episode-rewind-btn:disabled,
.episode-forward-btn:disabled {
    background: #CBD5E0 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

.episode-progress {
    width: 100%;
    height: 3px;
    background: #E2E8F0;
    border-radius: 2px;
    margin-top: 8px;
    overflow: hidden;
}

.episode-progress-fill {
    height: 100%;
    background: #48BB78;
    transition: width 0.3s ease;
}

.no-episodes {
    text-align: center;
    color: #718096;
    font-size: 12px;
    font-style: italic;
    padding: 16px;
}

.load-more-btn {
    width: calc(100% - 20px); /* 修改宽度计算，减少左右边距 */
    padding: 6px 6px;
    background: #F7F8FA;
    border: 1px solid #E2E8F0;
    color: #4A5568;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
    flex-shrink: 0;
    position: absolute;
    bottom: 1px;
    left: 15px; /* 减少左边距 */
    right: 6px; /* 减少右边距 */
}

.load-more-btn:hover {
    background: #EDF2F7;
}

/* 搜索结果模态框样式 */
.search-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.search-modal.show {
    opacity: 1;
    visibility: visible;
}

.search-modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.search-modal-header {
    padding: 16px 20px;
    border-bottom: 1px solid #E2E8F0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.search-modal-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2D3748;
}

.search-modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: #718096;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.search-modal-close:hover {
    background: #F7F8FA;
    color: #2D3748;
}

.search-modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.search-results-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.search-result-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #E2E8F0;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.search-result-item:hover {
    background: #F7F8FA;
    border-color: #409EFF;
}

.search-result-artwork {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    margin-right: 12px;
    object-fit: cover;
}

.search-result-info {
    flex: 1;
    min-width: 0;
}

.search-result-name {
    font-size: 14px;
    font-weight: 600;
    color: #2D3748;
    margin-bottom: 4px;
    line-height: 1.3;
}

.search-result-author {
    font-size: 12px;
    color: #718096;
    margin-bottom: 4px;
}

.search-result-genre {
    font-size: 11px;
    color: #A0AEC0;
}

.search-result-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-left: 12px;
}

.favorite-toggle-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    color: #E2E8F0;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.favorite-toggle-btn:hover {
    background: rgba(229, 62, 62, 0.1);
    color: #E53E3E;
}

.favorite-toggle-btn.favorited {
    color: #E53E3E;
} 