from flask import Flask, Blueprint, request, jsonify, redirect, session, url_for, current_app, make_response
import json
import uuid
import logging
import os
import secrets
from datetime import datetime, timezone, timedelta
import supabase
from api.creem import CreemAPI
from api.rate_limit import get_user_identifier  # 导入自定义速率限制函数
from api.rate_limit_config import get_limit  # 导入集中配置的速率限制

# 设置日志记录
logger = logging.getLogger(__name__)

# 创建支付蓝图
payment_bp = Blueprint('payment', __name__)

# 初始化标志
_payment_initialized = False

# 初始化Creem API客户端
creem_api = None

# 初始化Supabase客户端
supabase_client = None
service_supabase_client = None  # 服务角色客户端

# 添加安全的错误处理函数
def safe_error_response(exception, error_message="服务器处理请求时发生错误", status_code=500):
    """
    安全地处理异常，避免在响应中泄露敏感信息
    
    Args:
        exception: 捕获的异常
        error_message: 返回给客户端的通用消息
        status_code: HTTP状态码
        
    Returns:
        一个包含通用错误信息的JSON响应和HTTP状态码
    """
    # 记录详细的异常信息到日志
    logger.exception(f"处理请求时发生异常: {str(exception)}")
    
    # 返回通用错误消息给客户端
    return jsonify({
        "success": False,
        "error": error_message
    }), status_code

def init_payment_module(app, limiter):
    """初始化支付模块，接收limiter实例作为参数"""
    global creem_api, supabase_client, service_supabase_client, _payment_initialized
    
    # 防止重复初始化
    if _payment_initialized:
        logger.info("支付模块已初始化，跳过重复初始化")
        return
    
    # 从应用程序配置中获取Creem API配置
    creem_api_key = app.config.get('CREEM_API_KEY')
    creem_api_url = app.config.get('CREEM_API_URL')
    creem_webhook_secret = app.config.get('CREEM_WEBHOOK_SECRET')
    app_url = app.config.get('APP_URL')
    
    # 初始化Creem API客户端
    creem_api = CreemAPI(
        api_key=creem_api_key,
        api_url=creem_api_url,
        webhook_secret=creem_webhook_secret,
        app_url=app_url
    )
    logger.info(f"初始化Creem API客户端: {creem_api_url}")
    
    # 初始化普通Supabase客户端
    supabase_url = app.config.get('SUPABASE_URL')
    supabase_key = app.config.get('SUPABASE_ANON_KEY')
    
    if supabase_url and supabase_key:
        try:
            # 创建普通Supabase客户端
            supabase_client = supabase.create_client(supabase_url, supabase_key)
            logger.info(f"Supabase客户端初始化成功: {supabase_url}")
            
            # 初始化服务角色Supabase客户端
            service_key = app.config.get('SUPABASE_SERVICE_ROLE_KEY_AUDIO') 
            if service_key:
                service_supabase_client = supabase.create_client(supabase_url, service_key)
                logger.info("服务角色Supabase客户端初始化成功")
            else:
                logger.warning("服务角色密钥缺失，将无法执行需要高权限的操作")
        except Exception as e:
            logger.error(f"初始化Supabase客户端失败: {str(e)}")
    else:
        logger.warning("缺少Supabase配置，数据库功能将不可用")

    # 添加CSRF保护相关配置
    app.config['CSRF_SECRET'] = os.environ.get('CSRF_SECRET', secrets.token_hex(16))
    app.config['CSRF_COOKIE_NAME'] = 'csrf_token'
    app.config['CSRF_COOKIE_SECURE'] = True  # 强制在所有环境中使用安全连接
    app.config['CSRF_COOKIE_HTTPONLY'] = True
    app.config['CSRF_COOKIE_SAMESITE'] = 'Lax'  # 添加 SameSite 策略

    # 使用传入的limiter添加速率限制
    try:
        # --- Apply rate limits to payment routes ---
        limiter.limit(get_limit("payment", "payment_packages"))(get_payment_packages)
        limiter.limit(get_limit("payment", "create_order"))(create_payment_order)
        limiter.limit(get_limit("payment", "check_status"))(check_payment_status)
        limiter.limit(get_limit("payment", "payment_success"))(payment_success)
        limiter.limit(get_limit("payment", "payment_cancel"))(payment_cancel)
        limiter.limit(get_limit("payment", "payment_webhook"))(payment_webhook)  # 保持高限制，因为这是支付平台的回调
        limiter.limit(get_limit("payment", "payment_webhook"))(handle_creem_webhook)  # 保持高限制，因为这是支付平台的回调
        limiter.limit(get_limit("payment", "redirect_to_app"))(redirect_to_app)
        limiter.limit(get_limit("other", "csrf_token"))(get_csrf_token)  # CSRF令牌接口
        logger.info("已为支付路由添加速率限制")
    except Exception as e:
        logger.error(f"添加速率限制时出错: {str(e)}")
    
    # 标记支付模块已初始化
    _payment_initialized = True

def ensure_database_tables():
    """确保必要的数据库表存在"""
    if not supabase_client:
        logger.warning("Supabase客户端未初始化，无法确保表存在")
        return
    
    try:
        # 检查payment_orders表是否存在，如果不存在则创建
        logger.info("确保数据库表存在")
        
        # 尝试查询payment_orders表，如果失败则记录错误
        try:
            response = supabase_client.table('payment_orders').select('count').limit(1).execute()
            logger.info(f"payment_orders表存在，包含{len(response.data)}条记录")
        except Exception as e:
            logger.warning(f"查询payment_orders表失败: {str(e)}")
            logger.info("payment_orders表可能不存在，请在Supabase控制台创建")
        
        # 尝试查询user_profiles表，如果失败则记录错误
        try:
            response = supabase_client.table('user_profiles').select('count').limit(1).execute()
            logger.info(f"user_profiles表存在，包含{len(response.data)}条记录")
        except Exception as e:
            logger.warning(f"查询user_profiles表失败: {str(e)}")
            logger.info("user_profiles表可能不存在，请在Supabase控制台创建")
    
    except Exception as e:
        logger.error(f"确保数据库表存在时出错: {str(e)}")

    logger.info("支付模块已注册")

@payment_bp.route('/api/payment/csrf-token', methods=['GET'])
def get_csrf_token():
    """生成CSRF令牌并设置cookie"""
    try:
        # 生成CSRF令牌
        csrf_token = secrets.token_hex(16)
        
        # 准备响应
        response = make_response(jsonify({
            "success": True,
            "csrfToken": csrf_token
        }))
        
        # 设置CSRF cookie
        max_age = 3600  # 1小时过期
        expires = datetime.now() + timedelta(seconds=max_age)
        
        # 设置cookie选项，增强安全性
        response.set_cookie(
            current_app.config.get('CSRF_COOKIE_NAME', 'csrf_token'),
            value=csrf_token,
            max_age=max_age,
            expires=expires,
            httponly=True,
            secure=current_app.config.get('CSRF_COOKIE_SECURE', False),
            samesite='Lax'
        )
        
        logger.info("已生成CSRF令牌并设置cookie")
        return response
    except Exception as e:
        return safe_error_response(e, "生成CSRF令牌失败")

def verify_csrf_token():
    """验证CSRF令牌"""
    try:
        # 从请求头中获取令牌
        header_token = request.headers.get('X-CSRF-Token')
        
        # 从cookie中获取令牌
        cookie_token = request.cookies.get(current_app.config.get('CSRF_COOKIE_NAME', 'csrf_token'))
        
        if not header_token or not cookie_token:
            logger.warning("CSRF验证失败：缺少令牌")
            return False
        
        # 验证令牌是否匹配
        if header_token != cookie_token:
            logger.warning("CSRF验证失败：令牌不匹配")
            return False
        
        return True
    except Exception as e:
        logger.error(f"CSRF验证过程中出错：{str(e)}")
        return False

@payment_bp.route('/api/payment/packages', methods=['GET'])
def get_payment_packages():
    """获取可用的支付套餐"""
    try:
        # 定义套餐数据
        packages = {
            'package_basic': {
                'id': 'package_basic',
                'name': 'Basic Package',
                'credits': 3000,
                'price': 5,
                'currency': 'USD',
                'creem_product_id': 'prod_3lQSBxYagweAUJYRj7OAG7'
            },
            'package_standard': {
                'id': 'package_standard',
                'name': 'Standard Package',
                'credits': 6500,
                'price': 10,
                'currency': 'USD',
                'creem_product_id': 'prod_1VFKRW2iDGYBoK9vYOWZqU'
            },
            'package_premium': {
                'id': 'package_premium',
                'name': 'Premium Package',
                'credits': 10000,
                'price': 15,
                'currency': 'USD',
                'creem_product_id': 'prod_7cYNO6uLX4hNRwk8SG84H0'
            }
        }        
        
        return jsonify({
            "success": True,
            "packages": packages
        })
    except Exception as e:
        return safe_error_response(e, "获取支付套餐失败")

@payment_bp.route('/api/payment/create-order', methods=['POST'])
def create_payment_order():
    """创建支付订单"""
    try:
        # 验证CSRF令牌
        if not verify_csrf_token():
            return jsonify({
                "success": False,
                "error": "安全验证失败，请刷新页面重试"
            }), 403
        
        data = request.json
        
        # 验证必要参数
        required_fields = ['packageId', 'userId']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    "success": False,
                    "error": f"缺少必要参数: {field}"
                }), 400
        
        package_id_string = data['packageId']  # 字符串ID，如"package_standard"
        user_id = data['userId']
        
        # 从前端直接获取用户邮箱
        user_email = data.get('userEmail')
        
        # 如果前端没有提供邮箱，尝试从会话或数据库获取
        if not user_email:
            # 从会话获取用户邮箱
            user_data = session.get('user_data', {})
            user_email = user_data.get('email')
            
            # 如果会话中没有邮箱，尝试从payment_orders表查找该用户最近的订单获取邮箱
            if not user_email and service_supabase_client:
                try:
                    order_response = service_supabase_client.table('payment_orders').select('user_email').eq('user_id', user_id).order('created_at', desc=True).limit(1).execute()
                    if order_response.data and len(order_response.data) > 0:
                        user_email = order_response.data[0].get('user_email')
                        logger.info(f"从历史订单获取到用户邮箱: {user_email}")
                except Exception as order_error:
                    logger.warning(f"从历史订单获取邮箱失败: {str(order_error)}")
            
        if not user_email:
            return jsonify({
                "success": False,
                "error": "无法获取用户邮箱信息，请重新登录"
            }), 400
        
        logger.info(f"创建订单: 套餐ID={package_id_string}, 用户ID={user_id}, 邮箱={user_email}")
        
        # 套餐数据，包含对应的Creem产品ID
        # 根据环境选择正确的产品ID
        is_production = os.environ.get('ENVIRONMENT') == 'production'
        
        if is_production:
            # 生产环境产品ID
            packages = {
                'package_basic': {
                    'id': 'package_basic',
                    'name': 'Basic Package',
                    'credits': 3000,
                    'price': 5,
                    'currency': 'USD',
                    'creem_product_id': 'prod_3lQSBxYagweAUJYRj7OAG7'  # 生产环境 Basic
                },
                'package_standard': {
                    'id': 'package_standard',
                    'name': 'Standard Package',
                    'credits': 6500,
                    'price': 10,
                    'currency': 'USD',
                    'creem_product_id': 'prod_1VFKRW2iDGYBoK9vYOWZqU'  # 生产环境 Standard
                },
                'package_premium': {
                    'id': 'package_premium',
                    'name': 'Premium Package',
                    'credits': 10000,
                    'price': 15,
                    'currency': 'USD',
                    'creem_product_id': 'prod_7cYNO6uLX4hNRwk8SG84H0'  # 生产环境 Premium
                }
            }
            logger.info("使用生产环境产品ID")
        else:
            # 测试环境产品ID
            packages = {
                'package_basic': {
                    'id': 'package_basic',
                    'name': 'Basic Package',
                    'credits': 3000,
                    'price': 5,
                    'currency': 'USD',
                    'creem_product_id': 'prod_2oQ4GaxAW1IsndJysadjMo'  # 测试环境 Basic
                },
                'package_standard': {
                    'id': 'package_standard',
                    'name': 'Standard Package',
                    'credits': 6500,
                    'price': 10,
                    'currency': 'USD',
                    'creem_product_id': 'prod_2YEF12Y2Ryc7HAJqX1LA7V'  # 测试环境 Standard
                },
                'package_premium': {
                    'id': 'package_premium',
                    'name': 'Premium Package',
                    'credits': 10000,
                    'price': 15,
                    'currency': 'USD',
                    'creem_product_id': 'prod_GEiSCbhjrbsgd2767OxZX'  # 测试环境 Premium
                }
            }
            logger.info("使用测试环境产品ID")
        
        if package_id_string not in packages:
            return jsonify({
                "success": False,
                "error": "未找到指定的支付套餐"
            }), 404
        
        package = packages[package_id_string]
        
        # 严格的双重验证：检查前端传递的所有套餐参数是否与后端定义匹配
        # 获取前端传递的所有套餐相关参数
        frontend_price = data.get('price')
        frontend_credits = data.get('credits')
        frontend_currency = data.get('currency')
        frontend_product_id = data.get('product_id')
        
        # 1. 价格验证
        if frontend_price is not None and float(frontend_price) != float(package['price']):
            logger.warning(f"价格篡改尝试: 用户ID={user_id}, 套餐={package_id_string}, 前端价格={frontend_price}, 实际价格={package['price']}")
            # 记录安全事件
            try:
                if service_supabase_client:
                    service_supabase_client.table('security_logs').insert({
                        'user_id': user_id,
                        'event_type': 'price_tampering',
                        'details': f"套餐:{package_id_string}, 前端价格:{frontend_price}, 实际价格:{package['price']}",
                        'created_at': datetime.now().isoformat()
                    }).execute()
            except Exception as log_error:
                logger.error(f"记录安全日志失败: {str(log_error)}")
                
            return jsonify({
                "success": False,
                "error": "套餐价格验证失败，请稍后重试"
            }), 403
        
        # 2. 积分验证
        if frontend_credits is not None and int(frontend_credits) != int(package['credits']):
            logger.warning(f"积分篡改尝试: 用户ID={user_id}, 套餐={package_id_string}, 前端积分={frontend_credits}, 实际积分={package['credits']}")
            # 记录安全事件
            try:
                if service_supabase_client:
                    service_supabase_client.table('security_logs').insert({
                        'user_id': user_id,
                        'event_type': 'credits_tampering',
                        'details': f"套餐:{package_id_string}, 前端积分:{frontend_credits}, 实际积分:{package['credits']}",
                        'created_at': datetime.now().isoformat()
                    }).execute()
            except Exception as log_error:
                logger.error(f"记录安全日志失败: {str(log_error)}")
                
            return jsonify({
                "success": False,
                "error": "套餐积分验证失败，请稍后重试"
            }), 403
            
        # 3. 货币验证
        if frontend_currency is not None and frontend_currency != package['currency']:
            logger.warning(f"货币篡改尝试: 用户ID={user_id}, 套餐={package_id_string}, 前端货币={frontend_currency}, 实际货币={package['currency']}")
            # 记录安全事件
            try:
                if service_supabase_client:
                    service_supabase_client.table('security_logs').insert({
                        'user_id': user_id,
                        'event_type': 'currency_tampering',
                        'details': f"套餐:{package_id_string}, 前端货币:{frontend_currency}, 实际货币:{package['currency']}",
                        'created_at': datetime.now().isoformat()
                    }).execute()
            except Exception as log_error:
                logger.error(f"记录安全日志失败: {str(log_error)}")
                
            return jsonify({
                "success": False,
                "error": "套餐货币验证失败，请稍后重试"
            }), 403
            
        # 4. 产品ID验证
        if frontend_product_id is not None and frontend_product_id != package['creem_product_id']:
            logger.warning(f"产品ID篡改尝试: 用户ID={user_id}, 套餐={package_id_string}, 前端产品ID={frontend_product_id}, 实际产品ID={package['creem_product_id']}")
            # 记录安全事件
            try:
                if service_supabase_client:
                    service_supabase_client.table('security_logs').insert({
                        'user_id': user_id,
                        'event_type': 'product_id_tampering',
                        'details': f"套餐:{package_id_string}, 前端产品ID:{frontend_product_id}, 实际产品ID:{package['creem_product_id']}",
                        'created_at': datetime.now().isoformat()
                    }).execute()
            except Exception as log_error:
                logger.error(f"记录安全日志失败: {str(log_error)}")
                
            return jsonify({
                "success": False, 
                "error": "套餐信息验证失败，请稍后重试"
            }), 403

        # 生成唯一订单号
        order_id = f"order_{uuid.uuid4().hex[:12]}_{int(datetime.now().timestamp())}"
        
        # 查询或创建对应的套餐UUID
        package_uuid = None

        # 尝试将订单信息存储到数据库
        try:
            # 创建订单记录
            order_data = {
                'order_id': order_id,
                'user_id': user_id,
                'user_email': user_email,
                'amount': package['price'],
                'currency': package['currency'],  # 确保货币字段始终有值
                'credits': package['credits'],
                'status': 'pending',
                'created_at': datetime.now().isoformat()
            }
            
            # 如果有package_uuid，使用它；否则跳过package_id字段
            if package_uuid:
                order_data['package_id'] = package_uuid
            
            # 插入订单记录
            if service_supabase_client:
                service_supabase_client.table('payment_orders').insert(order_data).execute()
                logger.info(f"使用service_role创建订单记录: {order_id}")
            else:
                # 如果service_role不可用，则尝试使用普通客户端
                supabase_client.table('payment_orders').insert(order_data).execute()
                logger.info(f"使用普通权限创建订单记录: {order_id}")
            
        except Exception as db_error:
            # 如果数据库操作失败，记录错误但继续处理
            logger.warning(f"创建订单记录失败，但将继续处理支付: {str(db_error)}")
        
        # 创建支付会话，传递产品ID
        checkout_result = creem_api.create_checkout_session(
            order_id=order_id,
            amount=package['price'],
            currency=package['currency'],  # 确保货币字段始终有值
            user_email=user_email,
            product_id=package.get('creem_product_id')  # 传递套餐对应的Creem产品ID
        )
        
        if not checkout_result['success']:
            logger.error(f"创建支付会话失败: {checkout_result.get('error')}")
            if 'details' in checkout_result:
                logger.error(f"详细错误: {checkout_result.get('details')}")
            return jsonify({
                "success": False,
                "error": checkout_result.get('error')
            }), 500
        
        logger.info(f"订单创建成功: {order_id}, 支付会话ID: {checkout_result['checkout_id']}")
        
        # 将订单信息保存到会话中，以便在支付成功后使用
        session['current_order'] = {
            'order_id': order_id,
            'user_id': user_id,
            'package_id': package_id_string,
            'credits': package['credits']
        }
        
        return jsonify({
            "success": True,
            "order_id": order_id,
            "checkout_url": checkout_result['checkout_url']
        })
    except Exception as e:
        return safe_error_response(e)

@payment_bp.route('/api/payment/check-status', methods=['GET'])
def check_payment_status():
    """检查支付状态"""
    try:
        if not supabase_client:
            return jsonify({
                "success": False,
                "error": "Supabase客户端未初始化"
            }), 500
        
        order_id = request.args.get('order_id')
        if not order_id:
            return jsonify({
                "success": False,
                "error": "缺少订单ID"
            }), 400
        
        response = supabase_client.table('payment_orders') \
            .select('*') \
            .eq('order_id', order_id) \
            .execute()
        
        if not response.data:
            return jsonify({
                "success": False,
                "error": "未找到指定订单"
            }), 404
        
        order = response.data[0]
        
        return jsonify({
            "success": True,
            "order_id": order_id,
            "status": order['status'],
            "credits": order['credits'] if order['status'] == 'completed' else 0
        })
    except Exception as e:
        return safe_error_response(e)

# 新增API：获取用户积分
@payment_bp.route('/api/credits/get-user-credits', methods=['GET'])
def get_user_credits():
    """获取当前登录用户的积分"""
    try:
        # 获取当前用户ID
        user_id = None
        
        # 首先从会话中获取
        if 'user_id' in session:
            user_id = session['user_id']
        elif 'user_data' in session and 'id' in session['user_data']:
            user_id = session['user_data']['id']
        
        # 如果会话中没有，则尝试从请求头中的认证信息获取
        if not user_id and request.headers.get('Authorization'):
            # 这里需要实现从认证令牌中提取用户ID的逻辑
            # 实际实现取决于您的认证系统
            pass
        
        if not user_id:
            return jsonify({
                "success": False,
                "error": "未能识别用户身份，请重新登录"
            }), 401
        
        # 查询用户积分
        if service_supabase_client:
            # 优先使用service_role权限查询
            credits_response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
        else:
            # 备用：使用普通权限查询
            credits_response = supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
        
        # 处理查询结果
        if credits_response.data:
            credits = credits_response.data[0].get('credits', 0)
            return jsonify({
                "success": True,
                "credits": credits
            })
        else:
            # 用户没有积分记录
            return jsonify({
                "success": True,
                "credits": 0
            })
    
    except Exception as e:
        return safe_error_response(e, "获取用户积分失败")

# 添加支付重定向处理器
@payment_bp.route('/api/payment/redirect-to-app', methods=['GET'])
def redirect_to_app():
    """
    处理支付完成后的重定向请求
    将用户从支付平台重定向回应用，并处理支付结果
    """
    try:
        # 获取所有URL参数并处理多个order_id的情况
        all_params = request.args.to_dict(flat=False)  # 保留多值参数
        
        # 获取基本参数
        payment_success = request.args.get('payment_success') == 'true'
        payment_cancelled = request.args.get('payment_cancelled') == 'true'
        
        # 处理多个order_id的情况
        our_order_id = None
        creem_order_id = None
        
        # 检查order_id参数是否有多个值
        if 'order_id' in all_params and len(all_params['order_id']) > 0:
            for order_id in all_params['order_id']:
                if order_id.startswith('ord_'):
                    creem_order_id = order_id
                    logger.info(f"检测到Creem订单ID: {creem_order_id}")
                else:
                    our_order_id = order_id
                    logger.info(f"检测到我们系统的订单ID: {our_order_id}")
        
        # 获取其他参数
        signature = request.args.get('signature')
        checkout_id = request.args.get('checkout_id')
        customer_id = request.args.get('customer_id')
        subscription_id = request.args.get('subscription_id')
        product_id = request.args.get('product_id')
        
        logger.info(f"重定向处理: 支付成功={payment_success}, 我们的订单ID={our_order_id}, 产品ID={product_id}, Creem订单ID={creem_order_id}")
        
        # 确保我们有一个有效的订单ID用于后续处理
        order_id = our_order_id  # 使用我们的订单ID进行后续处理
        
        # 获取当前环境的基础URL
        is_production = os.environ.get('ENVIRONMENT') == 'production'
        if is_production:
            base_url = os.environ.get('APP_URL') or "https://reader-app.fly.dev"
        else:
            # 开发环境使用127.0.0.1:8080，这是实际的访问地址
            # 注意：创建支付会话时使用localhost:8080，但重定向处理时应使用127.0.0.1:8080
            base_url = "http://127.0.0.1:8080"
        
        logger.info(f"重定向处理使用基础URL: {base_url}")
        
        if not order_id:
            logger.error("支付缺少我们系统的订单ID，无法处理")
            return redirect(f"{base_url}/main.html?payment_error=true&error_message=missing_order_id")
        
        # 构建重定向URL，返回到main.html
        redirect_params = []
        
        if payment_success:
            # 检查产品ID是否存在，这是进行验证的必要条件
            if not product_id:
                logger.error(f"支付缺少产品ID，无法验证交易: 订单ID={order_id}")
                return redirect(f"{base_url}/main.html?payment_error=true&error_message=invalid_product")
            
            # 检查订单是否存在且状态是否已经是completed
            if not service_supabase_client:
                logger.error("服务角色客户端未初始化，无法验证和处理支付")
                return redirect(f"{base_url}/main.html?payment_error=true&error_message=service_unavailable")
            
            if not order_id:
                logger.error("支付缺少订单ID，无法处理")
                return redirect(f"{base_url}/main.html?payment_error=true&error_message=missing_order_id")
            
            try:
                # 验证签名（如果存在）
                if signature:
                    # 将request.args.to_dict(flat=False)传递给签名验证方法
                    if not creem_api.verify_redirect_signature(all_params, signature):
                        logger.error(f"重定向签名验证失败: 订单ID={order_id}")
                        
                        # 记录安全事件
                        try:
                            service_supabase_client.table('security_logs').insert({
                                'order_id': order_id,
                                'event_type': 'signature_verification_failed',
                                'details': f"签名验证失败: 订单ID={order_id}, Creem订单ID={creem_order_id}",
                                'created_at': datetime.now().isoformat()
                            }).execute()
                        except Exception as log_error:
                            logger.error(f"记录安全日志失败: {str(log_error)}")
                        
                        return redirect(f"{base_url}/main.html?payment_error=true&error_message=invalid_signature")
                    else:
                        # 签名验证通过，立即将订单状态更新为completed
                        # 这是重要的一步，因为签名有效意味着支付确实成功了
                        logger.info(f"签名验证通过，将订单状态更新为completed: {order_id}")
                
                # 一次性获取所有需要的订单数据
                order_data = service_supabase_client.table('payment_orders')\
                    .select('*')\
                    .eq('order_id', order_id)\
                    .execute()
                
                if not order_data.data:
                    logger.error(f"找不到订单: {order_id}")
                    return redirect(f"{base_url}/main.html?payment_error=true&error_message=order_not_found")
                
                # 获取订单信息
                order = order_data.data[0]
                user_id = order.get('user_id')
                order_amount = order.get('amount')
                order_currency = order.get('currency')
                order_credits = order.get('credits')
                order_status = order.get('status')
                
                # 如果订单已存在且状态为completed，则不再处理
                if order_status == 'completed':
                    logger.info(f"订单 {order_id} 已经处理过，跳过重复处理")
                    # 添加积分参数到重定向URL（从数据库获取当前积分）
                    try:
                        if user_id:
                            user_credits = service_supabase_client.table('user_credits')\
                                .select('credits')\
                                .eq('user_id', user_id)\
                                .execute()
                            if user_credits.data:
                                current_credits = user_credits.data[0].get('credits', 0)
                                redirect_params.append(f"credits={current_credits}")
                    except Exception as e:
                        logger.error(f"获取用户积分失败: {str(e)}")
                    
                    redirect_params.append("payment_success=true")
                    
                else:
                    # 根据产品ID获取套餐信息（使用硬编码的套餐数据而非数据库查询）
                    # 确定当前环境
                    is_production = os.environ.get('ENVIRONMENT') == 'production'
                    
                    # 根据环境选择正确的套餐数据
                    if is_production:
                        # 生产环境产品信息
                        packages_by_product_id = {
                            'prod_3lQSBxYagweAUJYRj7OAG7': {  # Basic
                                'id': 'package_basic',
                                'string_id': 'package_basic',
                                'credits': 3000,
                                'price': 5,
                                'currency': 'USD'
                            },
                            'prod_1VFKRW2iDGYBoK9vYOWZqU': {  # Standard
                                'id': 'package_standard',
                                'string_id': 'package_standard',
                                'credits': 6500,
                                'price': 10,
                                'currency': 'USD'
                            },
                            'prod_7cYNO6uLX4hNRwk8SG84H0': {  # Premium
                                'id': 'package_premium',
                                'string_id': 'package_premium',
                                'credits': 10000,
                                'price': 15,
                                'currency': 'USD'
                            }
                        }
                    else:
                        # 测试环境产品信息
                        packages_by_product_id = {
                            'prod_2oQ4GaxAW1IsndJysadjMo': {  # Basic
                                'id': 'package_basic',
                                'string_id': 'package_basic',
                                'credits': 3000,
                                'price': 5,
                                'currency': 'USD'
                            },
                            'prod_2YEF12Y2Ryc7HAJqX1LA7V': {  # Standard
                                'id': 'package_standard',
                                'string_id': 'package_standard',
                                'credits': 6500,
                                'price': 10,
                                'currency': 'USD'
                            },
                            'prod_GEiSCbhjrbsgd2767OxZX': {  # Premium
                                'id': 'package_premium',
                                'string_id': 'package_premium',
                                'credits': 10000,
                                'price': 15,
                                'currency': 'USD'
                            }
                        }
                    
                    # 查找匹配的套餐
                    package = packages_by_product_id.get(product_id)
                    
                    if not package:
                        logger.error(f"未找到匹配产品ID的套餐: {product_id}")
                        # 记录安全事件
                        try:
                            service_supabase_client.table('security_logs').insert({
                                'order_id': order_id,
                                'event_type': 'invalid_product_id',
                                'details': f"无效的产品ID: {product_id}",
                                'created_at': datetime.now().isoformat()
                            }).execute()
                        except Exception as log_error:
                            logger.error(f"记录安全日志失败: {str(log_error)}")
                            
                        return redirect(f"{base_url}/main.html?payment_error=true&error_message=invalid_product")
                    
                    # 获取套餐信息
                    actual_credits = package.get('credits')
                    actual_package_id = package.get('id')
                    package_string_id = package.get('string_id')
                    package_price = package.get('price')
                    package_currency = package.get('currency')
                    
                    logger.info(f"根据产品ID找到套餐: {package_string_id}, 积分: {actual_credits}")
                    
                    # 验证支付金额与套餐价格匹配
                    # 如果订单中的货币字段为None，记录错误并更新为正确的货币
                    if order_currency is None:
                        logger.warning(f"订单{order_id}的货币字段为空，将更新为{package_currency}")
                        # 更新订单的货币字段
                        try:
                            service_supabase_client.table('payment_orders').update({
                                'currency': package_currency,
                                'updated_at': datetime.now().isoformat(),
                                'notes': f"自动更新货币字段: None -> {package_currency}"
                            }).eq('order_id', order_id).execute()
                            # 更新本地变量以便后续比较
                            order_currency = package_currency
                        except Exception as update_error:
                            logger.error(f"更新订单货币字段失败: {str(update_error)}")
                    
                    # 金额验证 - 严格匹配
                    if float(order_amount) != float(package_price) or order_currency != package_currency:
                        logger.error(f"支付金额与套餐价格不匹配: 支付金额={order_amount}{order_currency}, 套餐价格={package_price}{package_currency}")
                        
                        # 记录安全事件
                        try:
                            service_supabase_client.table('security_logs').insert({
                                'order_id': order_id,
                                'event_type': 'payment_amount_mismatch',
                                'details': f"支付金额={order_amount}{order_currency}, 套餐价格={package_price}{package_currency}",
                                'created_at': datetime.now().isoformat()
                            }).execute()
                        except Exception as log_error:
                            logger.error(f"记录安全日志失败: {str(log_error)}")
                            
                        # 更新订单状态为可疑
                        service_supabase_client.table('payment_orders').update({
                            'status': 'suspicious',
                            'updated_at': datetime.now().isoformat(),
                            'notes': f"支付金额与套餐价格不匹配: {order_amount}{order_currency} vs {package_price}{package_currency}"
                        }).eq('order_id', order_id).execute()
                        
                        return redirect(f"{base_url}/main.html?payment_error=true&error_message=amount_mismatch")
                    
                    # 记录差异情况，如果发现积分不匹配，拒绝处理
                    if order_credits != actual_credits:
                        logger.warning(f"检测到积分不匹配: 订单积分={order_credits}, 实际套餐积分={actual_credits}")
                        
                        # 记录不匹配情况
                        try:
                            service_supabase_client.table('security_logs').insert({
                                'order_id': order_id,
                                'event_type': 'credits_mismatch',
                                'details': f"订单积分={order_credits}, 实际套餐积分={actual_credits}",
                                'created_at': datetime.now().isoformat()
                            }).execute()
                        except Exception as log_error:
                            logger.error(f"记录安全日志失败: {str(log_error)}")
                        
                        # 更新订单状态为可疑
                        service_supabase_client.table('payment_orders').update({
                            'status': 'suspicious',
                            'updated_at': datetime.now().isoformat(),
                            'notes': f"积分不匹配: 订单积分={order_credits}, 实际套餐积分={actual_credits}"
                        }).eq('order_id', order_id).execute()
                        
                        return redirect(f"{base_url}/main.html?payment_error=true&error_message=credits_mismatch")
                    
                    # 所有验证通过后，更新订单状态为已完成
                    update_data = {
                        'status': 'completed',
                        'completed_at': datetime.now().isoformat(),
                        'credits': actual_credits  # 使用从套餐获取的积分值
                    }
                    
                    # 记录套餐信息到notes字段，而不是尝试更新package_id
                    update_data['notes'] = f"支付成功，套餐ID: {package_string_id}, 积分: {actual_credits}"
                    
                    service_supabase_client.table('payment_orders').update(update_data).eq('order_id', order_id).execute()
                    logger.info(f"订单状态已更新为已完成: {order_id}")
                    
                    # 使用积分时限系统添加积分，确保创建积分批次
                    try:
                        # 使用新的积分批次系统添加积分
                        batch_response = service_supabase_client.rpc('add_credits_with_expiry', {
                            'p_user_id': user_id,
                            'p_credits': actual_credits,
                            'p_source_type': 'purchase',
                            'p_validity_days': 30,
                            'p_order_id': order_id,
                            'p_description': f'购买{actual_credits}积分，30天有效期'
                        }).execute()
                        
                        if batch_response.data:
                            # 获取更新后的积分
                            user_credits_response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
                            if user_credits_response.data:
                                new_credits = user_credits_response.data[0].get('credits', 0)
                            else:
                                new_credits = actual_credits
                            logger.info(f"积分时限系统添加积分成功: 用户ID={user_id}, 充值={actual_credits}, 新积分={new_credits}, 批次ID={batch_response.data}")
                            
                            # 重要：用最新积分更新缓存，避免前端显示过期数据
                            from .credits_cache import cache_user_credits
                            cache_user_credits(user_id, new_credits)
                            logger.info(f"已更新用户积分缓存: 用户ID={user_id}, 最新积分={new_credits}")
                            
                            # 添加积分到重定向URL
                            redirect_params.append(f"credits={new_credits}")
                        else:
                            logger.error(f"积分添加失败，函数返回空结果: 用户ID={user_id}")
                            raise Exception("积分添加函数返回空结果")
                    except Exception as batch_error:
                        logger.error(f"积分时限系统添加失败: {str(batch_error)}")
                        # 回退到原来的方式
                        logger.info("回退到传统积分更新方式")
                        user_credits_response = service_supabase_client.table('user_credits')\
                            .select('credits')\
                            .eq('user_id', user_id)\
                            .execute()
                        
                        if user_credits_response.data:
                            # 更新现有积分
                            current_credits = user_credits_response.data[0].get('credits', 0)
                            new_credits = current_credits + actual_credits
                            
                            service_supabase_client.table('user_credits').update({
                                'credits': new_credits,
                                'updated_at': datetime.now().isoformat()
                            }).eq('user_id', user_id).execute()
                            
                            logger.info(f"回退方式-用户积分已更新: 用户ID={user_id}, 旧积分={current_credits}, 新积分={new_credits}")
                        else:
                            # 创建新积分记录
                            new_credits = actual_credits
                            service_supabase_client.table('user_credits').insert({
                                'user_id': user_id,
                                'credits': new_credits,
                                'created_at': datetime.now().isoformat(),
                                'updated_at': datetime.now().isoformat()
                            }).execute()
                            
                            logger.info(f"回退方式-用户积分记录已创建: 用户ID={user_id}, 积分={new_credits}")
                        
                        # 重要：用最新积分更新缓存，避免前端显示过期数据
                        from .credits_cache import cache_user_credits
                        cache_user_credits(user_id, new_credits)
                        logger.info(f"已更新用户积分缓存: 用户ID={user_id}, 最新积分={new_credits}")
                        
                        # 添加积分到重定向URL
                        redirect_params.append(f"credits={new_credits}")
                    
                    # 添加积分操作记录到正确的表
                    try:
                        service_supabase_client.table('credit_operations').insert({
                            'user_id': user_id,
                            'operation_type': 'recharge',
                            'credits_amount': actual_credits,
                            'current_credits': new_credits,  # 统一使用current_credits字段
                            'model': 'payment',
                            'book_name': f'充值订单 {order_id}',
                            'operation_time': datetime.utcnow().isoformat() + 'Z'  # 统一使用UTC时间格式
                        }).execute()
                        
                        logger.info(f"充值积分记录已创建: 用户ID={user_id}, 充值金额={actual_credits}, 当前积分={new_credits}")
                    except Exception as history_error:
                        logger.error(f"记录积分操作失败: {str(history_error)}")
                    
                    redirect_params.append("payment_success=true")
            except Exception as e:
                logger.error(f"处理支付结果时出错: {str(e)}")
                return redirect(f"{base_url}/main.html?payment_error=true&error_message=general_error")
        elif payment_cancelled:
            redirect_params.append("payment_cancelled=true")
            
        # 构建最终重定向URL
        redirect_url = f"{base_url}/main.html"
        if redirect_params:
            redirect_url += "?" + "&".join(redirect_params)
        
        logger.info(f"最终重定向URL: {redirect_url}")
            
        # 设置缓存控制头，确保浏览器不缓存重定向结果
        response = redirect(redirect_url)
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        
        return response
    except Exception as e:
        logger.error(f"处理重定向时出错: {str(e)}")
        # 出错时也重定向回主页，但带上错误标记
        return redirect(f"{base_url}/main.html?payment_error=true&error_message=exception")

@payment_bp.route('/payment/success', methods=['GET'])
def payment_success():
    """支付成功回调处理"""
    try:
        order_id = request.args.get('order_id')
        logger.info(f"支付成功回调: 订单ID={order_id}")
        
        if not order_id:
            logger.warning("支付成功回调缺少订单ID")
            # 重定向到应用页面，确保使用127.0.0.1
            return redirect("http://127.0.0.1:3000/main.html?payment_error=true&error_message=missing_order_id")
        
        logger.info(f"处理支付成功回调: 订单ID={order_id}")
        
        # 从数据库获取订单信息
        order_info = None
        try:
            if service_supabase_client:
                order_response = service_supabase_client.table('payment_orders').select('*').eq('order_id', order_id).execute()
            else:
                order_response = supabase_client.table('payment_orders').select('*').eq('order_id', order_id).execute()
                
            if order_response.data and len(order_response.data) > 0:
                order_info = order_response.data[0]
                logger.info(f"获取到订单信息: {order_info}")
            else:
                logger.warning(f"未找到订单信息: {order_id}")
        except Exception as db_error:
            logger.error(f"查询订单信息时出错: {str(db_error)}")
        
        # 从Creem获取支付状态
        payment_status = None
        payment_amount = None
        try:
            status_result = creem_api.check_payment_status(order_id)
            if status_result['success']:
                payment_status = status_result.get('status')
                payment_amount = status_result.get('amount')
                logger.info(f"支付状态: {payment_status}, 金额: {payment_amount}")
            else:
                logger.warning(f"获取支付状态失败: {status_result.get('error')}")
        except Exception as api_error:
            logger.error(f"调用支付API时出错: {str(api_error)}")
        
        # 安全验证：确保支付金额与订单金额匹配
        if order_info and payment_amount is not None:
            expected_amount = order_info.get('amount')
            if payment_amount != expected_amount:
                logger.warning(f"支付金额不匹配: 订单金额={expected_amount}, 实际支付={payment_amount}")
                # 记录安全事件
                try:
                    if service_supabase_client:
                        service_supabase_client.table('security_logs').insert({
                            'user_id': order_info.get('user_id'),
                            'order_id': order_id,
                            'event_type': 'payment_amount_mismatch',
                            'details': f"订单金额={expected_amount}, 支付金额={payment_amount}",
                            'created_at': datetime.now().isoformat()
                        }).execute()
                except Exception as log_error:
                    logger.error(f"记录安全日志失败: {str(log_error)}")
                
                # 如果金额不匹配，拒绝处理并返回错误
                return redirect(f"{app_url}/main.html?payment_status=error&error=amount_mismatch")
        
        # 获取订单信息
        current_order = session.get('current_order', {})
        
        # 重要：保存当前用户的session信息，确保登录状态不会丢失
        user_session = {}
        for key in session:
            if key.startswith('user_') or key == 'auth_token' or key == 'logged_in':
                user_session[key] = session[key]
        
        if not current_order or current_order.get('order_id') != order_id:
            logger.warning(f"会话中没有找到匹配的订单信息: {order_id}")
            # 尝试从数据库中查询订单信息
            try:
                order_data = supabase_client.table('payment_orders').select('*').eq('order_id', order_id).execute()
                if order_data.data:
                    current_order = order_data.data[0]
                    logger.info(f"从数据库中找到订单信息: {current_order}")
                else:
                    logger.warning(f"数据库中没有找到订单信息: {order_id}")
            except Exception as db_error:
                logger.error(f"查询订单信息失败: {str(db_error)}")
        
        # 如果有订单信息，更新订单状态和用户积分
        new_credits = 0
        if current_order:
            user_id = current_order.get('user_id')
            credits = current_order.get('credits', 0)
            status = current_order.get('status')
            
            # 检查订单是否已经处理过
            if status == 'completed':
                logger.info(f"订单 {order_id} 已经处理过，跳过积分更新")
                # 恢复用户会话信息并重定向
                for key, value in user_session.items():
                    session[key] = value
                
                response = redirect('http://127.0.0.1:3000/main.html?payment_success=true&message=already_processed')
                response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                return response
            
            if user_id and credits > 0:
                try:
                    # 检查服务角色客户端是否可用
                    if not service_supabase_client:
                        logger.error("服务角色客户端未初始化，无法更新积分")
                        # 尝试使用普通客户端更新状态
                        supabase_client.table('payment_orders').update({
                            'status': 'completed',
                            'completed_at': datetime.now().isoformat(),
                            'updated_at': datetime.now().isoformat()
                        }).eq('order_id', order_id).execute()
                        
                        logger.warning("由于权限限制，无法更新积分。已更新订单状态。")
                        
                        # 恢复用户会话信息并重定向
                        for key, value in user_session.items():
                            session[key] = value
                        
                        response = redirect('http://127.0.0.1:3000/main.html?payment_success=true&error=credits_update_failed')
                        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                        return response
                    
                    # 使用服务角色客户端处理所有数据库操作
                    
                    # 1. 更新订单状态
                    service_supabase_client.table('payment_orders').update({
                        'status': 'completed',
                        'completed_at': datetime.now().isoformat(),
                        'updated_at': datetime.now().isoformat()
                    }).eq('order_id', order_id).execute()
                    
                    logger.info(f"订单状态已更新为已完成: {order_id}")
                    
                    # 2. 获取用户当前积分并更新 (user_credits表)
                    user_credits_response = service_supabase_client.table('user_credits')\
                        .select('credits')\
                        .eq('user_id', user_id)\
                        .execute()
                    
                    if user_credits_response.data:
                        # 更新现有积分
                        current_credits = user_credits_response.data[0].get('credits', 0)
                        new_credits = current_credits + credits
                        
                        service_supabase_client.table('user_credits').update({
                            'credits': new_credits,
                            'updated_at': datetime.now().isoformat()
                        }).eq('user_id', user_id).execute()
                        
                        logger.info(f"用户积分已更新: 用户ID={user_id}, 旧积分={current_credits}, 新积分={new_credits}")
                        
                        # 重要：立即更新缓存
                        from .credits_cache import cache_user_credits
                        cache_user_credits(user_id, new_credits)
                        logger.info(f"已更新用户积分缓存: 用户ID={user_id}, 最新积分={new_credits}")
                    else:
                        # 创建新积分记录
                        current_credits = 0  # 没有记录时当前积分为0
                        new_credits = credits
                        service_supabase_client.table('user_credits').insert({
                            'user_id': user_id,
                            'credits': new_credits,
                            'created_at': datetime.now().isoformat(),
                            'updated_at': datetime.now().isoformat()
                        }).execute()
                        
                        logger.info(f"用户积分记录已创建: 用户ID={user_id}, 积分={new_credits}")
                        
                        # 重要：立即更新缓存
                        from .credits_cache import cache_user_credits
                        cache_user_credits(user_id, new_credits)
                        logger.info(f"已更新用户积分缓存: 用户ID={user_id}, 最新积分={new_credits}")
                    
                    # 3. 重新查询确保获取最新的积分值
                    final_credits_response = service_supabase_client.table('user_credits')\
                        .select('credits')\
                        .eq('user_id', user_id)\
                        .execute()
                    
                    final_credits = new_credits  # 默认使用计算值
                    if final_credits_response.data:
                        final_credits = final_credits_response.data[0].get('credits', new_credits)
                        logger.info(f"确认最终积分: 用户ID={user_id}, 最终积分={final_credits}")
                    
                    # 4. 记录积分操作到 credit_operations 表
                    try:
                        from datetime import datetime, timezone
                        now = datetime.utcnow().isoformat() + 'Z'  # 统一使用UTC时间格式
                        
                        # 创建充值记录，使用确认后的最终积分
                        operation_record = {
                            'user_id': user_id,
                            'operation_type': 'recharge',
                            'model': None,
                            'credits_amount': credits,  # 正数表示充值
                            'operation_time': now,
                            'book_name': None,
                            'chapter_title': None,
                            'current_credits': final_credits  # 使用充值后的最终积分
                        }
                        
                        service_supabase_client.table('credit_operations').insert(operation_record).execute()
                        logger.info(f"充值积分记录已创建: 用户ID={user_id}, 充值金额={credits}, 当前积分={new_credits}")
                        
                    except Exception as operation_error:
                        logger.error(f"记录充值操作失败: {str(operation_error)}")
                        # 添加详细的错误日志
                        if hasattr(operation_error, 'response'):
                            logger.error(f"错误状态码: {operation_error.response.status_code if hasattr(operation_error.response, 'status_code') else 'unknown'}")
                            logger.error(f"错误响应: {operation_error.response.text if hasattr(operation_error.response, 'text') else 'no details'}")
                        # 充值记录失败不影响充值本身，继续执行
                    
                    # 清除会话中的订单信息，但保留用户登录信息
                    session.pop('current_order', None)
                    
                    # 恢复用户会话信息
                    for key, value in user_session.items():
                        session[key] = value
                    
                    response = redirect(f'http://127.0.0.1:3000/main.html?payment_success=true&credits={new_credits}')
                    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
                    response.headers['Pragma'] = 'no-cache'
                    response.headers['Expires'] = '0'
                    return response
                    
                except Exception as update_error:
                    logger.error(f"更新订单状态或用户积分失败: {str(update_error)}")
        
        # 如果无法处理，仍然返回成功页面，但不带积分信息
        # 恢复用户会话信息
        for key, value in user_session.items():
            session[key] = value
            
        response = redirect('http://127.0.0.1:3000/main.html?payment_success=true&message=payment_processed')
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        return response
        
    except Exception as e:
        return safe_error_response(e)

@payment_bp.route('/payment/cancel', methods=['GET'])
def payment_cancel():
    """支付取消回调页面"""
    order_id = request.args.get('order_id')
    logger.info(f"支付取消回调: 订单ID={order_id}")
    
    # 重定向到主应用页面，带上取消标志
    return redirect(f"http://127.0.0.1:3000/main.html?payment_cancelled=true&order_id={order_id}")

@payment_bp.route('/api/payment/webhook', methods=['POST'])
def payment_webhook():
    """处理Creem支付系统的Webhook回调"""
    try:
        # 获取请求头中的签名
        signature = request.headers.get('X-Creem-Signature')
        if not signature:
            logger.error("Webhook请求缺少签名")
            return jsonify({"success": False, "error": "Missing signature"}), 400
        
        # 获取请求体
        payload = request.data
        
        # 验证签名
        if not creem_api.verify_webhook_signature(payload, signature):
            logger.error("Webhook签名验证失败")
            return jsonify({"success": False, "error": "Invalid signature"}), 400
        
        # 解析请求体
        event_data = request.json
        event_type = event_data.get('type')
        
        logger.info(f"收到Creem Webhook事件: {event_type}")
        
        # 检查服务角色客户端是否可用
        if not service_supabase_client:
            logger.error("服务角色客户端未初始化，无法处理webhook")
            return jsonify({"success": False, "error": "Service role unavailable"}), 503
        
        # 处理支付成功事件
        if event_type == 'checkout.session.completed':
            session_data = event_data.get('data', {})
            order_id = session_data.get('metadata', {}).get('order_id')
            user_email = session_data.get('metadata', {}).get('user_email')
            
            if not order_id:
                logger.error("支付成功事件缺少订单ID")
                return jsonify({"success": False, "error": "Missing order ID"}), 400
            
            logger.info(f"处理支付成功事件: 订单ID={order_id}, 用户邮箱={user_email}")
            
            # 查询订单信息
            try:
                # 使用service_supabase_client查询订单信息
                order_response = service_supabase_client.table('payment_orders').select('*').eq('order_id', order_id).execute()
                
                if not order_response.data:
                    logger.error(f"未找到订单: {order_id}")
                    return jsonify({"success": False, "error": "Order not found"}), 404
                
                order = order_response.data[0]
                user_id = order.get('user_id')
                credits = order.get('credits', 0)
                
                # 检查订单状态，避免重复处理
                if order.get('status') == 'completed':
                    logger.info(f"订单已处理，跳过: {order_id}")
                    return jsonify({"success": True, "message": "Order already processed"}), 200
                
                # 更新订单状态为已完成 - 使用service_supabase_client
                service_supabase_client.table('payment_orders').update({
                    'status': 'completed',
                    'completed_at': datetime.now().isoformat()
                }).eq('order_id', order_id).execute()
                
                # 更新用户积分
                if user_id and credits > 0:
                    # 获取用户当前积分
                    user_credits_response = service_supabase_client.table('user_credits')\
                        .select('credits')\
                        .eq('user_id', user_id)\
                        .execute()
                    
                    if user_credits_response.data:
                        # 更新现有积分
                        current_credits = user_credits_response.data[0].get('credits', 0)
                        new_credits = current_credits + credits
                        
                        service_supabase_client.table('user_credits').update({
                            'credits': new_credits,
                            'updated_at': datetime.now().isoformat()
                        }).eq('user_id', user_id).execute()
                        
                        logger.info(f"用户积分已更新: 用户ID={user_id}, 旧积分={current_credits}, 新积分={new_credits}")
                        
                        # 重要：立即更新缓存
                        from .credits_cache import cache_user_credits
                        cache_user_credits(user_id, new_credits)
                        logger.info(f"已更新用户积分缓存: 用户ID={user_id}, 最新积分={new_credits}")
                    else:
                        # 创建新积分记录
                        new_credits = credits
                        service_supabase_client.table('user_credits').insert({
                            'user_id': user_id,
                            'credits': new_credits,
                            'created_at': datetime.now().isoformat(),
                            'updated_at': datetime.now().isoformat()
                        }).execute()
                        
                        logger.info(f"用户积分记录已创建: 用户ID={user_id}, 积分={new_credits}")
                        
                        # 重要：立即更新缓存
                        from .credits_cache import cache_user_credits
                        cache_user_credits(user_id, new_credits)
                        logger.info(f"已更新用户积分缓存: 用户ID={user_id}, 最新积分={new_credits}")
                    
                    # 重新查询确保获取最新的积分值
                    final_credits_response = service_supabase_client.table('user_credits')\
                        .select('credits')\
                        .eq('user_id', user_id)\
                        .execute()
                    
                    final_credits = new_credits  # 默认使用计算值
                    if final_credits_response.data:
                        final_credits = final_credits_response.data[0].get('credits', new_credits)
                        logger.info(f"Webhook确认最终积分: 用户ID={user_id}, 最终积分={final_credits}")
                    
                    # 记录充值操作到 credit_operations 表
                    try:
                        from datetime import datetime, timezone
                        now = datetime.utcnow().isoformat() + 'Z'  # 统一使用UTC时间格式
                        
                        # 创建充值记录，使用确认后的最终积分
                        operation_record = {
                            'user_id': user_id,
                            'operation_type': 'recharge',
                            'model': None,
                            'credits_amount': credits,  # 正数表示充值
                            'operation_time': now,
                            'book_name': None,
                            'chapter_title': None,
                            'current_credits': final_credits  # 使用充值后的最终积分
                        }
                        
                        service_supabase_client.table('credit_operations').insert(operation_record).execute()
                        logger.info(f"Webhook充值积分记录已创建: 用户ID={user_id}, 充值金额={credits}, 当前积分={new_credits}")
                        
                    except Exception as operation_error:
                        logger.error(f"Webhook记录充值操作失败: {str(operation_error)}")
                
                return jsonify({"success": True, "message": "Payment processed successfully"}), 200
            
            except Exception as e:
                logger.error(f"处理支付成功事件时出错: {str(e)}")
                return jsonify({"success": False, "error": "Failed to process payment"}), 500
        
        # 其他事件类型
        return jsonify({"success": True, "message": f"Event {event_type} received but not processed"}), 200
    
    except Exception as e:
        return safe_error_response(e)

@payment_bp.route('/api/webhook/creem', methods=['POST'])
def handle_creem_webhook():
    """处理Creem支付系统的Webhook回调"""
    try:
        # 获取请求头中的签名
        signature = request.headers.get('X-Creem-Signature')
        if not signature:
            logger.error("Webhook请求缺少签名")
            return jsonify({"error": "Missing signature"}), 401
        
        # 获取原始请求体 - 不转换为文本，保持原始格式
        payload = request.get_data()
        
        # 验证签名 - 如果验证失败立即返回错误
        if not creem_api.verify_webhook_signature(payload, signature):
            logger.error("Webhook签名验证失败")
            return jsonify({"error": "Invalid signature"}), 401
        
        # 在验证通过后解析JSON数据
        webhook_data = request.json
        
        logger.info(f"收到Creem webhook (已验证): {webhook_data}")
        
        # 从Creem返回的数据结构中正确获取订单ID
        metadata = webhook_data.get('object', {}).get('metadata', {})
        order_id = metadata.get('order_id')
        
        if not order_id:
            logger.warning("Webhook缺少订单ID，尝试从其他位置获取")
            # 尝试从其他可能的路径获取
            order_id = webhook_data.get('object', {}).get('metadata', {}).get('order_id')
        
        if not order_id:
            logger.warning("Webhook无法找到订单ID")
            return jsonify({"error": "Missing order_id"}), 400
            
        logger.info(f"处理webhook事件，订单ID: {order_id}")
        
        # 检查服务角色客户端是否可用
        if not service_supabase_client:
            logger.error("服务角色客户端未初始化，无法更新订单和积分")
            return jsonify({"error": "Service role unavailable"}), 503
        
        # 获取支付金额信息
        payment_amount = webhook_data.get('object', {}).get('amount')
        payment_currency = webhook_data.get('object', {}).get('currency')
        
        # 从数据库获取订单信息以验证金额
        try:
            order_query = service_supabase_client.table('payment_orders').select('*').eq('order_id', order_id).execute()
            
            if order_query.data and len(order_query.data) > 0:
                order_info = order_query.data[0]
                expected_amount = order_info.get('amount')
                expected_currency = order_info.get('currency')
                
                # 如果订单中的货币字段为None，记录错误并更新为正确的货币
                if expected_currency is None:
                    logger.warning(f"订单{order_id}的货币字段为空，将更新为{payment_currency}")
                    # 更新订单的货币字段
                    try:
                        service_supabase_client.table('payment_orders').update({
                            'currency': payment_currency,
                            'updated_at': datetime.now().isoformat(),
                            'notes': f"自动更新货币字段: None -> {payment_currency}"
                        }).eq('order_id', order_id).execute()
                        # 更新本地变量以便后续比较
                        expected_currency = payment_currency
                    except Exception as update_error:
                        logger.error(f"更新订单货币字段失败: {str(update_error)}")
                
                # 价格安全验证：确保支付金额与订单金额匹配
                if payment_amount is not None and expected_amount is not None:
                    # 转换为浮点数进行比较，避免字符串比较问题
                    if float(payment_amount) != float(expected_amount) or payment_currency != expected_currency:
                        logger.warning(f"支付金额不匹配: 订单金额={expected_amount}{expected_currency}, 实际支付={payment_amount}{payment_currency}")
                        
                        # 记录可疑交易
                        try:
                            service_supabase_client.table('suspicious_transactions').insert({
                                'order_id': order_id,
                                'expected_amount': expected_amount,
                                'actual_amount': payment_amount,
                                'expected_currency': expected_currency,
                                'actual_currency': payment_currency,
                                'created_at': datetime.now().isoformat(),
                                'details': '支付金额与订单金额不匹配(webhook)'
                            }).execute()
                            logger.info("已记录可疑交易")
                        except Exception as st_error:
                            logger.error(f"记录可疑交易失败: {str(st_error)}")
                        
                        # 如果金额不匹配，拒绝处理但返回成功（避免重复回调）
                        return jsonify({"status": "success", "message": "Payment amount mismatch, transaction flagged"}), 200
            else:
                logger.warning(f"无法找到订单信息进行金额验证: {order_id}")
        except Exception as verify_error:
            logger.error(f"验证支付金额时出错: {str(verify_error)}")
        
        # 更新订单状态
        try:
            # 将订单状态更新为已完成 - 使用service_supabase_client
            service_supabase_client.table('payment_orders').update({
                'status': 'completed',
                'completed_at': datetime.now().isoformat()
            }).eq('order_id', order_id).execute()
            
            logger.info(f"订单状态已更新为已完成: {order_id}")
            
            # 查询订单信息以获取用户ID和积分 - 使用service_supabase_client
            try:
                order_data = service_supabase_client.table('payment_orders').select('*').eq('order_id', order_id).execute()
                
                if order_data.data:
                    order = order_data.data[0]
                    user_id = order.get('user_id')
                    credits = order.get('credits', 0)
                    
                    # 打印用户ID以便检查
                    logger.info(f"从订单获取的用户ID: {user_id}, 类型: {type(user_id)}")
                    
                    # 如果有用户ID和积分，尝试更新用户积分
                    if user_id and credits > 0:
                        try:
                            # 获取用户当前积分 - 使用service_supabase_client
                            user_data = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
                            
                            current_credits = 0
                            if user_data.data:
                                current_credits = user_data.data[0].get('credits', 0)
                            
                            # 使用积分时限系统添加积分
                            try:
                                # 使用新的积分批次系统添加积分
                                batch_response = service_supabase_client.rpc('add_credits_with_expiry', {
                                    'p_user_id': user_id,
                                    'p_credits': credits,
                                    'p_source_type': 'purchase',
                                    'p_validity_days': 30,
                                    'p_order_id': order_id,
                                    'p_description': f'购买{credits}积分，30天有效期'
                                }).execute()
                                
                                if batch_response.data:
                                    # 获取更新后的积分
                                    user_credits_response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
                                    if user_credits_response.data:
                                        new_credits = user_credits_response.data[0].get('credits', 0)
                                    else:
                                        new_credits = credits
                                    logger.info(f"积分时限系统添加积分成功: 用户ID={user_id}, 充值={credits}, 新积分={new_credits}, 批次ID={batch_response.data}")
                                else:
                                    logger.error(f"积分添加失败，函数返回空结果: 用户ID={user_id}")
                                    raise Exception("积分添加函数返回空结果")
                            except Exception as batch_error:
                                logger.error(f"积分时限系统添加失败: {str(batch_error)}")
                                raise Exception(f"充值失败: {str(batch_error)}")
                            
                            # 查询auth.users表，确认用户ID是否存在 - 使用service_supabase_client
                            try:
                                auth_user = service_supabase_client.from_("auth.users").select("id").eq("id", user_id).execute()
                                logger.info(f"Auth用户查询结果: {auth_user.data}")
                                
                                if not auth_user.data:
                                    logger.warning(f"在auth.users表中找不到用户ID: {user_id}")
                                    # 尝试查找关联的auth用户ID
                                    # 这里需要根据您的数据模型添加逻辑
                            except Exception as auth_error:
                                logger.error(f"查询auth.users表出错: {str(auth_error)}")
                            
                            # 记录充值操作到 credit_operations 表
                            try:
                                from datetime import datetime, timezone
                                now = datetime.utcnow().isoformat() + 'Z'  # 统一使用UTC时间格式
                                
                                # 创建充值记录 - 使用兼容性插入
                                basic_record = {
                                    'user_id': user_id,
                                    'operation_type': 'recharge',
                                    'model': None,
                                    'credits_amount': credits,  # 正数表示充值
                                    'operation_time': now,
                                    'current_credits': new_credits
                                }
                                
                                # 尝试插入扩展字段，如果失败则使用基础字段
                                try:
                                    extended_record = basic_record.copy()
                                    extended_record['book_name'] = None
                                    extended_record['chapter_title'] = None
                                    service_supabase_client.table('credit_operations').insert(extended_record).execute()
                                except Exception as schema_error:
                                    if 'chapter_title' in str(schema_error) or 'book_name' in str(schema_error):
                                        service_supabase_client.table('credit_operations').insert(basic_record).execute()
                                    else:
                                        raise schema_error
                                logger.info(f"Webhook2充值积分记录已创建: 用户ID={user_id}, 充值金额={credits}, 当前积分={new_credits}")
                                
                            except Exception as operation_error:
                                logger.error(f"Webhook2记录充值操作失败: {str(operation_error)}")
                                # 尝试获取更详细的错误信息
                                if hasattr(operation_error, 'response'):
                                    logger.error(f"响应状态码: {operation_error.response.status_code if hasattr(operation_error.response, 'status_code') else 'unknown'}")
                                    logger.error(f"响应内容: {operation_error.response.text if hasattr(operation_error.response, 'text') else 'no details'}")
                                
                        except Exception as credit_error:
                            logger.error(f"更新用户积分时出错: {str(credit_error)}")
                else:
                    logger.warning(f"找不到订单信息: {order_id}")
            except Exception as e:
                logger.error(f"查询订单信息时出错: {str(e)}")
                
        except Exception as e:
            logger.error(f"更新订单状态失败: {str(e)}")
            
        # 返回成功响应
        return jsonify({"status": "success"}), 200
        
    except Exception as e:
        return safe_error_response(e)

# 替换update_user_credits_by_id函数，使用user_credits表而不是user_profiles表
def update_user_credits_by_id(user_id, credits_to_add, order_id):
    """通过用户ID更新积分"""
    try:
        # 检查服务角色客户端是否可用
        if not service_supabase_client:
            logger.error("服务角色客户端未初始化，无法更新积分")
            return jsonify({"success": False, "error": "Service role client not initialized"}), 500
        
        # 获取用户当前积分
        user_response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
        
        # 使用积分时限系统添加积分
        try:
            # 使用新的积分批次系统添加积分
            batch_response = service_supabase_client.rpc('add_credits_with_expiry', {
                'p_user_id': user_id,
                'p_credits': credits_to_add,
                'p_source_type': 'purchase',
                'p_validity_days': 30,
                'p_order_id': order_id,
                'p_description': f'购买{credits_to_add}积分，30天有效期'
            }).execute()
            
            if batch_response.data:
                # 获取更新后的积分
                user_credits_response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
                if user_credits_response.data:
                    new_credits = user_credits_response.data[0].get('credits', 0)
                else:
                    new_credits = credits_to_add
                logger.info(f"积分时限系统添加积分成功: 用户ID={user_id}, 充值={credits_to_add}, 新积分={new_credits}, 批次ID={batch_response.data}")
            else:
                logger.error(f"积分添加失败，函数返回空结果: 用户ID={user_id}")
                return jsonify({"success": False, "error": "积分添加失败"}), 500
        except Exception as batch_error:
            logger.error(f"积分时限系统添加失败: {str(batch_error)}")
            return jsonify({"success": False, "error": f"充值失败: {str(batch_error)}"}), 500
        
        # 重新查询确保获取最新的积分值
        final_credits_response = service_supabase_client.table('user_credits')\
            .select('credits')\
            .eq('user_id', user_id)\
            .execute()
        
        final_credits = new_credits  # 默认使用计算值
        if final_credits_response.data:
            final_credits = final_credits_response.data[0].get('credits', new_credits)
            logger.info(f"ID函数确认最终积分: 用户ID={user_id}, 最终积分={final_credits}")
        
        # 记录充值操作到 credit_operations 表
        try:
            from datetime import datetime, timezone
            now = datetime.utcnow().isoformat() + 'Z'  # 统一使用UTC时间格式
            
            # 创建充值记录，使用确认后的最终积分 - 兼容性插入
            basic_record = {
                'user_id': user_id,
                'operation_type': 'recharge',
                'model': None,
                'credits_amount': credits_to_add,  # 正数表示充值
                'operation_time': now,
                'current_credits': final_credits  # 使用充值后的最终积分
            }
            
            # 尝试插入扩展字段，如果失败则使用基础字段
            try:
                extended_record = basic_record.copy()
                extended_record['book_name'] = None
                extended_record['chapter_title'] = None
                service_supabase_client.table('credit_operations').insert(extended_record).execute()
            except Exception as schema_error:
                if 'chapter_title' in str(schema_error) or 'book_name' in str(schema_error):
                    service_supabase_client.table('credit_operations').insert(basic_record).execute()
                else:
                    raise schema_error
            logger.info(f"ID充值积分记录已创建: 用户ID={user_id}, 充值金额={credits_to_add}, 当前积分={new_credits}")
            
        except Exception as operation_error:
            logger.error(f"ID记录充值操作失败: {str(operation_error)}")
            # 充值记录失败不影响充值本身，继续执行
        
        return jsonify({"success": True}), 200
    except Exception as e:
        return safe_error_response(e)

def update_user_credits_by_email(user_email, credits_to_add, order_id):
    """通过用户邮箱更新积分"""
    try:
        # 检查服务角色客户端是否可用
        if not service_supabase_client:
            logger.error("服务角色客户端未初始化，无法更新积分")
            return jsonify({"success": False, "error": "Service role client not initialized"}), 500
            
        # 先通过邮箱查找用户ID
        user_auth_response = service_supabase_client.table('users').select('id').eq('email', user_email).execute()
        
        if user_auth_response.data:
            user_id = user_auth_response.data[0].get('id')
            logger.info(f"通过邮箱找到用户ID: {user_id}")
            return update_user_credits_by_id(user_id, credits_to_add, order_id)
        
        logger.error(f"未找到与邮箱{user_email}关联的用户")
        return jsonify({"success": False, "error": "User not found"}), 404
    
    except Exception as e:
        return safe_error_response(e)