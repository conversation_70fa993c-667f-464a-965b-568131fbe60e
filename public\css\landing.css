/* Reset some basic elements */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2196F3; /* Updated to match main.html blue */
    --primary-dark: #1976D2; /* Darker shade for hover states */
    --text-color: #333;
    --text-light: #666;
    --bg-color: #fff;
    --bg-light: #f5f7fa;
    --shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Match main.html shadow style */
    --shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.15);
    --radius: 8px; /* Match main.html rounded corners */
    --transition: all 0.3s ease;
}

body {
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif; /* Match main.html font */
    color: var(--text-color);
    line-height: 1.6;
    background: linear-gradient(135deg, #f5f7fa, #c3cfe2); /* Match main.html gradient background */
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

p {
    margin-bottom: 1rem;
    color: var(--text-light);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

/* Header styling - 确保header有固定高度 */
header {
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 0; /* 移除header的padding，由nav控制 */
    position: fixed;
    width: 100%;
    z-index: 1000;
    height: 80px; /* 设置固定高度 */
    display: flex;
    align-items: center; /* 垂直居中 */
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 5%;
    width: 100%;
    height: 100%; /* 填满header高度 */
}

/* 标题栏logo字号 */
.logo h1 {
    font-size: 32px; /* 增大字体大小 */
    color: var(--text-color);
    margin: 0; /* 移除默认外边距 */
    display: flex;
    align-items: center; /* 确保 logo 内部内容居中 */
    height: 100%; /* 让 logo 高度与 header 一致 */
}

.highlight {
    color: var(--primary-color);
}

.nav-links {
    display: flex;
    list-style: none;
    align-items: center; /* Ensure vertical alignment */
}

.nav-links li {
    margin-left: 25px;
    display: flex; /* Ensure consistent alignment */
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
}

/* Make all navigation buttons consistent */
.nav-links a.btn-primary, 
.nav-links a.btn-secondary {
    padding: 8px 20px;
    border-radius: 30px;
    font-weight: 600;
    display: inline-block;
}

.nav-links a.btn-primary {
    background-color: var(--primary-color);
    color: white; /* White text for primary button */
    border: 2px solid var(--primary-color);
}

.nav-links a.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

/* Hero section with image - COMPLETELY REVISED */
.hero {
    padding: 90px 20px 30px; /* 底部padding减半，减少与features间距 */
    background-color: #ffffff;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 50vh; /* 增加最小高度从60vh到70vh */
    width: 100%;
    margin-top: 0; /* 确保没有额外的margin */
}

.hero-container {
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px 20px; /* 增加内部padding */
    box-sizing: border-box;
}

.hero-content {
    width: 100%;
    margin: 0 auto;
    text-align: center;
}


.hero h1 {
    font-size: clamp(2rem, 4vw, 3rem);
    margin: 0 auto 20px;
    color: var(--text-color);
    text-align: center;
    max-width: 100%;
    line-height: 1.3;
    padding: 0 20px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: none; /* 禁用连字符断词 */
    -webkit-hyphens: none;
    -ms-hyphens: none;
    
    /* 在中文标点处优雅换行 */
    word-break: keep-all;
    white-space: pre-line; /* 支持手动换行符 */
}

.hero p {
    font-size: 1.2rem;
    color: var(--text-light);
    margin: 0 auto 30px;
    text-align: center;
    max-width: 800px;
    line-height: 1.8;
    padding: 0 20px;
    box-sizing: border-box;
    
    /* 中文排版 - 支持手动换行 */
    word-break: keep-all;
    white-space: pre-line; /* 保留手动换行符 */
    
    /* 英文排版 */
    word-wrap: break-word;
    overflow-wrap: break-word;
    
    /* 禁用连字符 */
    hyphens: none;
    -webkit-hyphens: none;
    -ms-hyphens: none;
    
    /* 优化文本渲染 */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    
    /* 优化中文标点 */
    line-break: normal;
    -webkit-line-break: normal;
    
    /* 优化标点压缩 */
    text-justify: distribute;
    -webkit-text-justify: distribute;
}


.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

/* Button styles updated to match main.html */
.btn-primary, .btn-secondary {
    padding: 9px 25px;
    border-radius: 10px; /* Match main.html rounded corners */
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
}

.btn-secondary {
    background-color: rgba(33, 150, 243, 0.1); /* Match main.html transparent blue */
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px); /* Match main.html hover effect */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-secondary:hover {
    background-color: rgba(33, 150, 243, 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Features section */
.features {
    padding: 30px 5% 60px 5%; /* 顶部padding减半，减少与hero间距 */
    margin-top: 0; /* 移除margin-top，使用padding控制间距 */
}

.features h2 {
    font-size: 2rem;
    margin-bottom: 50px;
    color: var(--text-color);
}

.feature-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
}

.feature-card {
    background-color: white;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    padding: 30px;
    width: 300px;
    transition: var(--transition);
    text-align: center; /* 让整个卡片内容居中 */
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-hover);
}

.feature-card.highlight {
    border-top: 4px solid var(--primary-color);
}

.icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    display: block; /* 确保图标独占一行 */
    text-align: center; /* 图标居中 */
}

.feature-card h3 {
    margin-bottom: 15px;
    color: var(--text-color);
    text-align: center; /* 标题居中 */
}

.feature-card p {
    color: var(--text-light);
    font-size: 0.9rem;
    text-align: center; /* 描述文字居中 */
}

/* Testimonials section */
.testimonials {
    padding: 60px 5% 60px 5%; /* 减小上下内边距 */
    margin-top: 0; /* 移除负外边距 */
}

.testimonial-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 30px;
    margin-top: 50px;
}

.testimonial-card {
    background-color: white;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    padding: 30px;
    width: 350px;
    text-align: center; /* 让整个testimonial卡片内容居中 */
}

.quote {
    font-style: italic;
    margin-bottom: 20px;
    color: var(--text-color);
    text-align: center; /* 引用文字居中 */
}

.user {
    display: flex;
    align-items: center;
    justify-content: center; /* 让用户信息水平居中 */
    flex-direction: column; /* 改为垂直布局，头像在上，信息在下 */
    gap: 15px; /* 头像和信息之间的间距 */
}

.user img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin: 0 auto; /* 头像居中，移除右边距 */
    display: block; /* 确保头像独占一行 */
}

.user-info {
    text-align: center; /* 用户信息文字居中 */
}

.user-info h4 {
    margin-bottom: 5px;
    color: var(--text-color);
    text-align: center; /* 用户名居中 */
}

.user-info p {
    font-size: 0.9rem;
    color: var(--text-light);
    margin: 0;
    text-align: center; /* 用户角色居中 */
}

/* Pricing section */
.pricing {
    padding: 60px 5% 60px 5%; /* 减小上下内边距 */
    margin-top: 0; /* 移除负外边距 */
}

.pricing-cards {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    gap: 30px;
    margin-top: 50px;
    align-items: stretch; /* 确保所有卡片高度一致 */
}

.pricing-card {
    background-color: white;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    padding: 30px;
    width: 350px;
    min-width: 350px; /* 确保卡片不会变窄 */
    white-space: nowrap; /* 禁止文字换行 */
    overflow: hidden; /* 防止溢出 */
    text-overflow: ellipsis; /* 如果文字过长，显示省略号 */
    text-align: center;
    position: relative;
    display: flex;
    flex-direction: column; /* 让内容垂直排列 */
    justify-content: space-between; /* 保持按钮在底部 */
    min-height: 500px; /* 设置统一的最小高度 */
}

.popular-tag {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-color);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-color);
}

.price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.price span {
    font-size: 1rem;
    color: var(--text-light);
}

.features-list {
    list-style: none;
    text-align: left;
    margin-bottom: 30px;
}

.features-list li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    font-size: 0.8rem; /* 调整为更大的字号，例如 1.2rem */
}

.features-list i {
    margin-right: 10px;
    color: var(--primary-color);
}

.features-list .disabled {
    color: #aaa;
}

.features-list .disabled i {
    color: #ddd;
}

/* FAQ Section */
.faq {
    padding: 80px 5%;
    background-color: #f8f9fa;
}

.faq .container {
    max-width: 800px;
    margin: 0 auto;
}

.faq h2 {
    text-align: center;
    color: var(--text-color);
    margin-bottom: 10px;
    font-size: 2.5rem;
    font-weight: 600;
}

.faq-subtitle {
    text-align: center;
    color: var(--text-light);
    margin-bottom: 50px;
    font-size: 1.1rem;
}

.faq-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.faq-item {
    background: white;
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.faq-item:hover {
    box-shadow: var(--shadow-hover);
}

.faq-question {
    padding: 25px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    transition: var(--transition);
    border: none;
    position: relative;
}

.faq-question:hover {
    background-color: #f8f9fa;
}

.faq-question span {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-color);
    flex: 1;
    text-align: left;
    line-height: 1.4;
}

.faq-question i {
    color: var(--primary-color);
    font-size: 1.2rem;
    transition: transform 0.3s ease;
    margin-left: 20px;
    flex-shrink: 0;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out, padding 0.3s ease-out;
    background: white;
}

.faq-item.active .faq-answer {
    max-height: 500px;
    padding: 0 25px 25px 25px;
}

.faq-answer p {
    color: var(--text-light);
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

/* Mobile responsiveness for FAQ */
@media (max-width: 768px) {
    .faq {
        padding: 60px 20px;
    }
    
    .faq h2 {
        font-size: 2rem;
    }
    
    .faq-question {
        padding: 20px;
    }
    
    .faq-question span {
        font-size: 1rem;
    }
    
    .faq-item.active .faq-answer {
        padding: 0 20px 20px 20px;
    }
}

/* CTA section updated to match main.html */
.cta {
    background-color: var(--primary-color);
    color: white;
    text-align: center;
    padding: 60px 20px;
}

.cta h2 {
    color: white;
    margin-bottom: 20px;
}

.cta p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 30px;
    font-size: 1.2rem;
}

.cta .btn-primary {
    background-color: white;
    color: var(--primary-color);
    border-color: white;
}

.cta .btn-primary:hover {
    background-color: transparent;
    color: white;
    transform: translateY(-2px); /* Match main.html hover effect */
}

/* Footer */
footer {
    background-color: #333;
    color: white;
    padding: 60px 5% 30px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 40px;
}

.footer-logo {
    margin-bottom: 30px;
}

.footer-logo h2 {
    text-align: center;
    margin-bottom: 10px;
}

.footer-logo p {
    color: rgba(255, 255, 255, 0.7);
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
}

.link-group h3 {
    color: white;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.link-group ul {
    list-style: none;
}

.link-group li {
    margin-bottom: 10px;
    font-size: 16px; /* 调整为合适的字号，例如 1rem 或 16px */
}

.link-group a {
    color: rgba(255, 255, 255, 0.7);
}

.link-group a:hover {
    color: white;
}

.footer-bottom {
    display: flex;
    flex-direction: column; /* Stack elements vertically */
    justify-content: center;
    align-items: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center; /* Center the text */
    gap: 15px; /* Add spacing between copyright and social links */
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    width: 100%; /* Ensure full width */
    text-align: center; /* Center the text */
}

/* Adjust social links to be centered as well */
.social-links {
    display: flex;
    gap: 15px;
    justify-content: center; /* Center the social icons */
}

/* Mobile Navigation Styles */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-color);
    cursor: pointer;
    padding: 8px;
    min-width: 44px;
    min-height: 44px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.mobile-menu-toggle:hover {
    color: var(--primary-color);
    background-color: rgba(33, 150, 243, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
    /* Navigation adjustments */
    nav {
        padding: 15px 20px;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .nav-links {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100vh;
        background: white;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 30px;
        transition: left 0.3s ease;
        z-index: 1001;
    }
    
    .nav-links.active {
        left: 0;
    }
    
    .nav-links li {
        margin: 0;
    }
    
    .nav-links a {
        font-size: 1.2rem;
        padding: 15px 30px;
    }
    
    /* Close button for mobile menu */
    .nav-links::before {
        content: "×";
        position: absolute;
        top: 20px;
        right: 30px;
        font-size: 2rem;
        cursor: pointer;
        color: var(--text-color);
    }
    
    /* Hero section adjustments */
    .hero {
        padding: 120px 20px 40px 20px;
    }
      .hero h1 {
        font-size: 1.8rem;
        white-space: normal !important;
        line-height: 1.3;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    /* 功能、评价、价格标题字号调整 - 确保比hero标题小 */
    .features h2,
    .testimonials h2,
    .pricing h2 {
        font-size: 1.8rem !important; /* 比hero h1的2.2rem小 */
    }
    
    .hero p {
        font-size: 1rem;
    }
    
    .cta-buttons {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }
    
    .btn-primary, .btn-secondary {
        width: 200px;
        text-align: center;
    }
    
    /* Cards adjustments */
    .feature-cards, .testimonial-cards, .pricing-cards {
        flex-direction: column;
        align-items: center;
        gap: 20px;
        width: 100%;
        padding: 0 15px;
    }
    
    .feature-card, .testimonial-card, .pricing-card {
        width: calc(100% - 40px);
        max-width: 350px;
        margin: 0 auto;
        text-align: center; /* 确保移动端也居中显示 */
    }
    
    .pricing-card.popular {
        transform: none;
        margin: 0;
    }
    
    /* Footer adjustments */
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 20px;
        align-items: center;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 1.5rem;
    }
    
    /* 功能、评价、价格标题字号调整 - 确保比hero标题小 */
    .features h2,
    .testimonials h2,
    .pricing h2 {
        font-size: 1.3rem !important; /* 比hero h1的1.5rem小 */
    }
    
    .hero p {
        font-size: 0.9rem;
    }
    
    nav {
        padding: 10px 15px;
    }
    
    .logo h1 {
        font-size: 24px;
    }
    
    .feature-card, .testimonial-card, .pricing-card {
        width: calc(100% - 30px);
        max-width: 100%;
        margin: 0 auto;
        text-align: center; /* 确保超小屏幕也居中显示 */
    }
}

/* 添加这些新的CSS规则到你的styles.css文件中 */

/* 调整滚动行为，减小滚动间距 */
html {
    scroll-padding-top: 70px; /* 与header高度匹配 */
    scroll-behavior: smooth;
}

/* 调整部分标题的间距 */
section h2 {
    margin-top: 0; /* 移除额外的顶部间距 */
    margin-bottom: 30px; /* 减小底部间距 */
}

/* Header styling with vertical centering */
header {
    padding: 0;
    height: 70px; /* Set a fixed height for the header */
    display: flex;
    align-items: center; /* Center items vertically */
}

header > * {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 5%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Style for the BookSum logo/title */
.logo {
    color: #2196F3; /* Updated to match main.html blue */
    font-weight: 600;
    font-size: 1.5rem;
    text-decoration: none;
}

/* Style for the navigation links */
.nav-links {
    display: flex;
    gap: 20px;
    align-items: center;
}

/* Style for regular nav links including "开始使用" */
.nav-link {
    color: #666;
    text-decoration: none;
    transition: color 0.3s;
}

.nav-link:hover {
    color: #333;
}

/* Special style for the "开始使用" link */
.nav-link.primary {
    color: #2196F3; /* Updated to match main.html blue */
    font-weight: 500;
}

.nav-link.primary:hover {
    text-decoration: underline;
}


/* 链接按钮样式 */
.button-link {
    display: inline-block;
    text-decoration: none;
    cursor: pointer;
    padding: 8px 20px;  /* 减小内边距 */
    text-align: center;
    width: auto;
    margin: 5px auto;   /* 减小外边距 */
}

.button-link:hover {
    cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-container {
        flex-direction: column;
    }
    
    .hero-content {
        text-align: center;
        max-width: 100%;
    }
    
    .hero-image {
        max-width: 100%;
        margin-top: 40px;
    }
}