(function() {
    'use strict';

    // 检查全局单例是否已存在
    if (window.CreditsManager && window.CreditsManager._initialized) {
        console.log('积分管理器已初始化，跳过重复初始化');
        return;
    }

    // 本地存储键名
    const STORAGE_KEYS = {
        credits: 'userCredits',
        pendingOperations: 'pendingCreditOperations'
    };

    /**
     * 检查URL是否包含支付成功参数，如果有则直接更新积分
     * @returns {boolean} 是否处理了支付参数
     */
    function checkAndHandlePaymentSuccess() {
        try {
            // 检查URL是否包含支付成功参数
            const urlParams = new URLSearchParams(window.location.search);
            const isPaymentSuccess = urlParams.get('payment_success') === 'true';
            const newCreditsFromUrl = urlParams.get('credits');
            
            // 支付成功后，优先使用URL中的积分值，并且不从本地缓存读取
            if (isPaymentSuccess && newCreditsFromUrl) {
                // 1. 解析URL中的积分
                const credits = parseInt(newCreditsFromUrl, 10);
                if (!isNaN(credits)) {
                    console.log('检测到支付成功参数，直接加载新积分值:', credits);
                    
                    // 2. 保存到全局变量
                    if (window.CreditsManager) {
                        window.CreditsManager.credits = credits;
                        
                        // 3. 更新UI
                        window.CreditsManager.updateCreditsDisplay();
                        
                        // 4. 更新本地缓存
                        window.CreditsManager.cacheCredits(credits);
                        
                        // 5. 触发积分更新事件
                        document.dispatchEvent(new CustomEvent('credits-updated', {
                            detail: { credits: credits }
                        }));
                        
                        // 6. 清除URL中的支付参数，防止刷新页面时重复处理
                        const newUrl = window.location.pathname;
                        window.history.replaceState({}, '', newUrl);
                        
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            console.error('处理支付参数时出错:', error);
            return false;
        }
    }

    // 初始化函数 - 从服务器获取模型配置信息
    async function initModelPricing() {
        try {
            // 使用新的后端API获取价格信息
            const response = await fetch('/api/credits/model-pricing');
            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status}`);
            }
            
            // 确认服务器返回了数据，但不在前端存储敏感信息
            const data = await response.json();
            
            // 触发事件通知配置已更新，但不保存敏感数据
            console.log("已从服务器获取模型配置信息");
            document.dispatchEvent(new CustomEvent('model-config-loaded'));
            
            return true;
        } catch (error) {
            console.error("获取模型配置信息失败:", error);
            return false;
        }
    }

    /**
     * 计算操作所需的积分（基于API调用）
     * @param {string} operation - 操作类型
     * @param {string} model - 使用的AI模型
     * @returns {Promise<number>} 所需积分数
     */
    async function calculateRequiredCredits(operation, model) {
        try {
            console.log(`请求后端计算积分: 操作=${operation}, 模型=${model}`);
            
            // 检查模型ID是否有效
            if (!model) {
                console.warn('未提供模型ID，使用默认模型 zhipu_flash');
                model = 'zhipu_flash';
            }
            
            // 使用专门的积分检查API端点
            const authToken = window.getAuthToken ? window.getAuthToken() : localStorage.getItem('authToken');
            const response = await fetch('/api/credits/check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify({
                    operation: operation,
                    model: model
                })
            });
            
            if (!response.ok) {
                // 如果是积分不足错误，正常处理
                if (response.status === 400) {
                    const data = await response.json();
                    if (data.error === '积分不足') {
                        console.warn(`积分不足: 当前=${data.current_credits}, 需要=${data.required_credits}`);
                        return data.required_credits;
                    }
                }
                throw new Error(`计算积分请求失败: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || '计算积分失败');
            }
            
            // 从响应中获取所需积分
            const finalCredits = data.required_credits || 1;
            
            console.log(`积分计算结果: 操作=${operation}, 模型=${model}, 积分=${finalCredits}`);
            
            return finalCredits;
        } catch (error) {
            console.error('计算积分时出错:', error);
            // 出错时返回默认值
            return DEFAULT_OPERATION_CREDITS[operation] || 5;
        }
    }

    /**
     * 根据实际令牌使用情况计算积分消耗 - 改为使用后端API
     * @param {Object} tokenUsageData - 令牌使用数据
     * @param {string} model - 使用的AI模型
     * @returns {number} 实际消耗的积分
     */
    async function calculateCreditsFromTokenUsage(tokenUsageData, model) {
        try {
            if (!tokenUsageData) {
                console.warn('没有提供令牌使用数据，无法计算精确积分');
                return null;
            }
            
            // 记录原始请求数据（避免记录敏感信息）
            console.log(`发送token使用数据到后端计算积分`);
            
            // 调用后端API计算积分
            const authToken = window.getAuthToken ? window.getAuthToken() : localStorage.getItem('authToken');
            const response = await fetch('/api/credits/calculate-from-usage', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify({
                    token_usage: tokenUsageData,
                    model: model
                })
            });
            
            if (!response.ok) {
                throw new Error(`计算积分请求失败: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.error || '计算积分失败');
            }
            
            // 确保返回至少1积分
            return Math.max(1, data.calculatedCredits);
        } catch (error) {
            console.error('根据令牌使用计算积分时出错:', error);
            return 5; // 发生错误时返回固定积分值，确保系统能继续运行
        }
    }

    /**
     * 检查用户是否已登录
     * @returns {boolean} 是否已登录
     */
    function isUserLoggedIn() {
        // 使用统一认证管理器检查登录状态
        if (window.isUserAuthenticated && window.getCurrentAuthUser) {
            const isAuthenticated = window.isUserAuthenticated();
            const currentUser = window.getCurrentAuthUser();
            return isAuthenticated && currentUser && currentUser.id;
        }
        
        // 降级到localStorage检查（兼容性）
        const isAuthenticated = localStorage.getItem('isAuthenticated');
        if (isAuthenticated !== 'true') return false;
        
        const userData = localStorage.getItem('userData');
        if (!userData) return false;
        
        try {
            const parsedUserData = JSON.parse(userData);
            return !!parsedUserData.id;
        } catch (e) {
            return false;
        }
    }

    // 全局共享的积分请求状态 - 新增
    let globalCreditsRequestInProgress = false;
    let globalCreditsLastRequestTime = 0;
    let globalCreditsPromise = null;
    
    // 全局积分事件机制 - 新增
    document.addEventListener('credits-fetch-started', function(event) {
        globalCreditsRequestInProgress = true;
    });
    
    document.addEventListener('credits-fetch-completed', function(event) {
        globalCreditsRequestInProgress = false;
        globalCreditsLastRequestTime = Date.now();
        globalCreditsPromise = null;
        
        // 如果积分 > 0，确保设置免检查时间
        if (event.detail && event.detail.credits > 0 && window.CreditsManager) {
            window.CreditsManager.setAuxiliarySkipCheckTime(60);
            console.log(`🚀 [积分获取完成] 积分${event.detail.credits} > 0，设置60分钟免检查时间`);
        }
    });

    /**
     * 检查用户积分是否足够，如果不足则提示充值
     * @param {string} operation - 操作类型
     * @param {string} model - 使用的AI模型
     * @returns {Promise<boolean>} 积分是否足够
     */
    async function checkCreditsAndPromptRecharge(operation, model = 'zhipu_flash') {
        const creditsManager = window.CreditsManager;
        if (!creditsManager) {
            console.error('积分管理器未初始化');
            return false;
        }

        // 如果用户未登录，返回false并提示登录
        if (!isUserLoggedIn()) {
            console.warn('用户未登录，无法检查积分');
            // 触发登录提示
            document.dispatchEvent(new CustomEvent('auth-login-required', {
                detail: { message: '请先登录后使用此功能' }
            }));
            return false;
        }

        try {
            // 通过API获取所需积分
            const requiredCredits = await calculateRequiredCredits(operation, model);
            const currentCredits = creditsManager.getCredits();
            
            console.log(`检查积分充足性: 当前=${currentCredits}, 需要=${requiredCredits}, 操作=${operation}, 模型=${model}`);
            
            if (currentCredits >= requiredCredits) {
                return true;
            }

            // 计算还需要多少积分
            const creditsNeeded = requiredCredits - currentCredits;
            
            // 显示积分不足提示
            showCreditsInsufficientModal(creditsNeeded, () => {
                // 充值按钮点击回调 - 使用统一的充值流程
                if (window.paymentModule && typeof window.paymentModule.startRechargeProcess === 'function') {
                    window.paymentModule.startRechargeProcess();
                } else {
                    console.error('支付模块未加载');
                    alert('支付模块未加载，请刷新页面后重试');
                }
            });

            return false;
        } catch (error) {
            console.error('检查积分时出错:', error);
            // 出错时为安全起见返回false，阻止操作
            return false;
        }
    }

    /**
     * 显示积分不足提示弹窗
     * @param {number} creditsNeeded 还需要的积分数量
     * @param {Function} onRechargeClick 充值按钮点击回调
     */
    function showCreditsInsufficientModal(creditsNeeded, onRechargeClick) {
        // 先移除可能存在的旧弹窗
        const existingModal = document.getElementById('creditsInsufficientModal');
        if (existingModal) {
            document.body.removeChild(existingModal);
        }
        
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'credits-insufficient-modal';
        modal.id = 'creditsInsufficientModal';
        
        // 临时隐藏模型选择器
        const modelSelector = document.querySelector('.model-selector');
        if (modelSelector) {
            modelSelector.dataset.originalDisplay = modelSelector.style.display || '';
            modelSelector.style.display = 'none';
        }
        
        // 检查UI语言系统是否可用
        const hasUILanguage = window.UILanguage && typeof window.UILanguage.getText === 'function' && typeof window.UILanguage.getCurrentLanguage === 'function';
        
        // 获取当前语言
        let currentLang = 'zh'; // 默认中文
        
        if (hasUILanguage) {
            currentLang = window.UILanguage.getCurrentLanguage();
        } else {
            // 如果无法使用UILanguage对象，尝试其他方法获取语言
            try {
                const storedLang = localStorage.getItem('uiLanguage');
                if (storedLang && (storedLang === 'en' || storedLang === 'zh')) {
                    currentLang = storedLang;
                } else if (navigator.language && !navigator.language.toLowerCase().startsWith('zh')) {
                    currentLang = 'en';
                }
            } catch (e) {
                console.error('获取语言设置失败', e);
            }
        }
        
        // 获取翻译文本的函数
        const getText = (key) => {
            if (hasUILanguage) {
                return window.UILanguage.getText(key);
            }
            
            // 默认翻译文本，如果UI语言系统不可用
            const defaultTexts = {
                'zh': {
                    'credits.insufficient_title': '积分不足',
                    'credits.insufficient_message': '您的积分不足，无法完成此操作',
                    'credits.current_credits': '当前积分',
                    'credits.needed_credits': '还需积分',
                    'credits.recharge_now': '立即充值',
                    'credits.close': '关闭'
                },
                'en': {
                    'credits.insufficient_title': 'Insufficient Credits',
                    'credits.insufficient_message': 'You don\'t have enough credits to complete this operation',
                    'credits.current_credits': 'Current Credits',
                    'credits.needed_credits': 'Credits Needed',
                    'credits.recharge_now': 'Recharge Now',
                    'credits.close': 'Close'
                }
            };
            
            // 如果当前语言不存在，返回中文
            const texts = defaultTexts[currentLang] || defaultTexts['zh'];
            return texts[key] || key;
        };
        
        // 获取翻译文本
        const title = getText('credits.insufficient_title');
        const message = getText('credits.insufficient_message');
        const currentCreditsLabel = getText('credits.current_credits');
        const neededCreditsLabel = getText('credits.needed_credits');
        const rechargeButtonText = getText('credits.recharge_now');
        const closeText = getText('credits.close');
        
        // 构建模态框内容 - 使用DOM API替代innerHTML以避免XSS风险
        const modalContent = document.createElement('div');
        modalContent.className = 'credits-insufficient-content';
        
        // 创建头部
        const header = document.createElement('div');
        header.className = 'credits-insufficient-header';
        
        const titleEl = document.createElement('h2');
        titleEl.textContent = title;
        
        const closeBtn = document.createElement('span');
        closeBtn.className = 'close-modal';
        closeBtn.textContent = '×';
        
        header.appendChild(titleEl);
        header.appendChild(closeBtn);
        
        // 创建主体
        const body = document.createElement('div');
        body.className = 'credits-insufficient-body';
        
        // 图标
        const iconDiv = document.createElement('div');
        iconDiv.className = 'credits-icon';
        const icon = document.createElement('i');
        icon.className = 'fas fa-coins';
        iconDiv.appendChild(icon);
        
        // 消息文本
        const messageP = document.createElement('p');
        messageP.textContent = message;
        
        // 当前积分
        const currCreditsP = document.createElement('p');
        const currCreditsSpan = document.createElement('span');
        currCreditsSpan.className = 'current-credits';
        currCreditsSpan.textContent = window.CreditsManager.getCredits();
        currCreditsP.textContent = currentCreditsLabel + ': ';
        currCreditsP.appendChild(currCreditsSpan);
        
        // 需要积分
        const neededCreditsP = document.createElement('p');
        const neededCreditsSpan = document.createElement('span');
        neededCreditsSpan.className = 'needed-credits';
        neededCreditsSpan.textContent = creditsNeeded;
        neededCreditsP.textContent = neededCreditsLabel + ': ';
        neededCreditsP.appendChild(neededCreditsSpan);
        
        // 按钮
        const rechargeBtn = document.createElement('button');
        rechargeBtn.className = 'recharge-now-btn';
        rechargeBtn.textContent = rechargeButtonText;
        
        // 添加所有元素到body
        body.appendChild(iconDiv);
        body.appendChild(messageP);
        body.appendChild(currCreditsP);
        body.appendChild(neededCreditsP);
        body.appendChild(rechargeBtn);
        
        // 组装所有元素
        modalContent.appendChild(header);
        modalContent.appendChild(body);
        modal.appendChild(modalContent);
        
        // 添加到文档
        document.body.appendChild(modal);
        
        // 显示模态框
        setTimeout(() => {
            modal.style.display = 'flex';
            // 添加show类以触发过渡动画
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }, 10);
        
        // 关闭按钮事件
        closeBtn.addEventListener('click', () => {
            closeCreditsInsufficientModal();
        });
        
        // 点击模态框外部关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeCreditsInsufficientModal();
            }
        });
        
        // 充值按钮点击事件
        rechargeBtn.addEventListener('click', () => {
            closeCreditsInsufficientModal();
            // 执行回调
            if (typeof onRechargeClick === 'function') {
                onRechargeClick();
            }
        });
    }

    /**
     * 关闭积分不足弹窗
     */
    function closeCreditsInsufficientModal() {
        const modal = document.getElementById('creditsInsufficientModal');
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                // 可能已经被其他代码移除，再次检查
                if (document.body.contains(modal)) {
                    document.body.removeChild(modal);
                }
                
                // 恢复模型选择器的显示
                const modelSelector = document.querySelector('.model-selector');
                if (modelSelector && modelSelector.dataset.originalDisplay !== undefined) {
                    modelSelector.style.display = modelSelector.dataset.originalDisplay;
                    delete modelSelector.dataset.originalDisplay;
                }
            }, 300);
        }
    }

    // 标记是否已经处理过支付完成事件
    let paymentCompletedHandled = false;

    // 监听支付完成事件，更新积分显示
    document.addEventListener('paymentCompleted', (event) => {
        console.log('检测到支付完成事件:', event.detail);
        
        // 防止重复处理
        if (paymentCompletedHandled) {
            console.log('支付完成事件已处理，跳过重复处理');
            return;
        }
        
        // 标记已处理
        paymentCompletedHandled = true;
        
        // 设置标记，防止其他地方显示缓存积分
        window.paymentJustCompleted = true;
        
        // 显示加载动画
        const creditsDisplay = document.getElementById('userCredits');
        if (creditsDisplay) {
            creditsDisplay.innerHTML = '<span class="credits-loading"></span>';
        }
        
        // 刷新积分
        if (window.CreditsManager && isUserLoggedIn()) {
            // 确保延迟足够长，让认证状态完全恢复
            setTimeout(() => {
                window.CreditsManager.fetchCreditsFromServer(true)
                    .then(() => {
                        console.log('支付完成后已更新积分');
                        // 5秒后清除标记，恢复正常流程
                        setTimeout(() => {
                            window.paymentJustCompleted = false;
                            paymentCompletedHandled = false; // 重置处理标记，允许下次处理
                        }, 5000);
                    })
                    .catch(error => {
                        console.error('支付完成后更新积分失败:', error);
                        // 清除标记
                        window.paymentJustCompleted = false;
                        paymentCompletedHandled = false; // 重置处理标记，允许下次处理
                    });
            }, 500); // 延迟500ms，确保认证状态已恢复
        }
    });

    // 添加充值按钮点击事件
    function setupRechargeButton() {
        const rechargeButtons = document.querySelectorAll('#rechargeButton, .recharge-button');
        rechargeButtons.forEach(button => {
            if (button && !button.hasAttribute('data-setup-done')) {
                button.setAttribute('data-setup-done', 'true');
                button.addEventListener('click', () => {
                    if (window.paymentModule && typeof window.paymentModule.startRechargeProcess === 'function') {
                        window.paymentModule.startRechargeProcess();
                    } else {
                        console.error('支付模块未加载');
                        alert('支付模块未加载，请刷新页面后重试');
                    }
                });
            }
        });
    }

    // 添加模型选择变化事件监听
    function setupModelSelectionListener() {
        const modelSelectors = document.querySelectorAll('.model-selector, #modelSelector');
        
        modelSelectors.forEach(selector => {
            if (selector && !selector.hasAttribute('data-credits-listener')) {
                selector.setAttribute('data-credits-listener', 'true');
                selector.addEventListener('change', function() {
                });
            }
        });
    }

    // 更新UI显示的积分消耗
    async function updateCreditCostDisplay() {
        const modelSelector = document.querySelector('.model-selector, #modelSelector');
        if (!modelSelector) return;
        let selectedModel = 'zhipu_flash';
        if (typeof window.convertModelNameToId === 'function') {
            selectedModel = window.convertModelNameToId(modelSelector.value);
        } else {
            const modelMap = { '智谱 GLM-4-Flash': 'zhipu_flash', 'DeepSeek V3': 'deepseek_api', 'Gemini Flash 2.0': 'gemini_api' };
            selectedModel = modelMap[modelSelector.value] || 'zhipu_flash';
        }
        // 使用后端计算所需积分
        try {
            const [sumResp, mindResp] = await Promise.all([
                fetch(`/api/credits/calculate?operation=summary&model=${selectedModel}`),
                fetch(`/api/credits/calculate?operation=mindmap&model=${selectedModel}`)
            ]);
            const sumData = await sumResp.json();
            const mindData = await mindResp.json();
            const summaryCost = sumData.success ? sumData.requiredCredits : '-';
            const mindmapCost = mindData.success ? mindData.requiredCredits : '-';
            document.querySelectorAll('.summary-cost').forEach(el => el.textContent = summaryCost);
            document.querySelectorAll('.mindmap-cost').forEach(el => el.textContent = mindmapCost);
        } catch (err) {
            console.error('更新积分消耗显示失败:', err);
        }
    }

    /**
     * 积分管理器类 - 与后端API集成
     */
    class CreditsManager {
        constructor() {
            this.credits = null;
            this.initialized = false;
            this.initPromise = null;
            this.lastFetchTime = 0;
            this.isInitializing = false;
            this.pendingInitRequest = null;
            
            // 添加同步控制变量
            this._lastSyncTime = 0;
            this._syncInProgress = false;
            
            // 新增：动态缓存管理状态
            this.dynamicCacheManagementStarted = false;
            this.auxiliaryConfig = null;
            this.autoSyncInProgress = false; // 防止重复自动同步
            
            // 新增：防重复调用标志
            this._loginRefreshInProgress = false;
            
            // 初始化时检查积分同步状态已禁用，由谷歌登录预加载或手动触发
            // this.checkAndSyncCreditsOnInit();
        }
        
        /**
         * 新增：初始化时检查并同步积分
         */
        async checkAndSyncCreditsOnInit() {
            // 延迟执行，确保页面完全加载
            setTimeout(async () => {
                try {
                    // 检查用户是否已登录
                    const userData = JSON.parse(localStorage.getItem('userData') || '{}');
                    const isLoggedIn = userData.id && localStorage.getItem('isAuthenticated') === 'true';
                    
                    if (isLoggedIn) {
                        const cachedCredits = this.getCachedCredits();
                        console.log(`🔄 [初始化同步] 用户已登录，当前缓存积分: ${cachedCredits}`);
                        
                        // 如果积分为0或null，主动同步
                        if (cachedCredits === null || cachedCredits === 0) {
                            console.log('🔄 [初始化同步] 检测到积分异常，启动后端同步...');
                            try {
                                // 先检查前后端用户ID一致性
                                const isConsistent = await this.checkUserIdConsistency();
                                if (!isConsistent) {
                                    console.warn('⚠️ [初始化同步] 前后端用户ID不一致，尝试修复...');
                                    const fixed = await this.fixUserIdInconsistency();
                                    if (!fixed) {
                                        console.error('❌ [初始化同步] 无法修复用户ID不一致，跳过同步');
                                        return;
                                    }
                                }
                                
                                const backendCredits = await this.fetchCreditsFromBackendCache();
                                console.log(`✅ [初始化同步] 同步完成，获得积分: ${backendCredits}`);
                                
                                // 如果后端有积分，自动设置免检查
                                if (backendCredits > 0) {
                                    console.log(`🚀 [初始化同步] 积分${backendCredits} > 0，自动设置60分钟免检查`);
                                    this.setAuxiliarySkipCheckTime(60);
                                    
                                    // 启动动态缓存管理
                                    if (!this.dynamicCacheManagementStarted) {
                                        await this.startDynamicCacheManagement();
                                        console.log(`✅ [初始化同步] 动态缓存管理已启动`);
                                    }
                                }
                            } catch (error) {
                                console.warn('⚠️ [初始化同步] 同步失败:', error);
                            }
                        } else {
                            console.log(`ℹ️ [初始化同步] 积分正常(${cachedCredits})，检查是否需要启动免检查机制`);
                            
                            // 积分正常，检查是否需要启动免检查
                            if (cachedCredits > 0) {
                                this.setAuxiliarySkipCheckTime(60);
                                if (!this.dynamicCacheManagementStarted) {
                                    await this.startDynamicCacheManagement();
                                }
                                console.log(`✅ [初始化同步] 积分${cachedCredits}正常，动态缓存管理已启动`);
                            }
                        }
                    } else {
                        console.log('ℹ️ [初始化同步] 用户未登录，跳过积分同步');
                    }
                } catch (error) {
                    console.warn('⚠️ [初始化同步] 检查过程出错:', error);
                }
            }, 2000); // 延迟2秒执行，确保页面完全加载
        }

        /**
         * 检查并处理OAuth重定向回调
         * 注意：此方法已被新的OAuthManager替代，保留用于兼容性
         */
        checkOAuthRedirect() {
            // OAuth处理现在由OAuthManager统一管理
            // 检查是否有OAuth管理器处理
            if (window.oauthManager && window.oauthManager.isOAuthCallback()) {
                console.log('[Credits] OAuth回调由OAuthManager处理，跳过本地处理');
                this.oauthProcessed = true;
                return;
            }
            
            // 传统逻辑作为fallback保留
            try {
                const url = new URL(window.location.href);
                if (url.hash && (url.hash.includes('access_token=') || url.hash.includes('provider=google'))) {
                    console.log('[Credits] 检测到OAuth回调，但已由统一管理器处理');
                    this.oauthProcessed = true;
                }
            } catch (error) {
                console.error('[Credits] 检查OAuth重定向参数失败:', error);
            }
        }

        /**
         * 处理登录状态变化
         * @param {Event} event - 事件对象
         */
        handleAuthChange(event) {
            try {
                // 根据不同的事件类型处理
                if (event === 'SIGNED_IN') {
                    console.log('[Credits] 认证状态变更: SIGNED_IN');
                    
                    // 用户登录时，使用缓存并行更新积分，不跳过缓存
                    this.fetchCreditsFromServer(false)
                        .then(() => {
                            console.log('[Credits] 登录后积分已更新 (使用缓存)');
                            
                            // 尝试同步本地积分记录
                            if (localStorage.getItem('creditsNeedSync') === 'true') {
                                console.log('检测到本地积分需要同步，尝试同步');
                                this.syncLocalCreditsDeductions()
                                    .catch(syncError => {
                                        console.error('同步本地积分记录失败:', syncError);
                                    });
                            }
                        })
                        .catch(error => {
                            console.error('[Credits] 登录后积分更新失败:', error);
                        });
                } else if (event === 'SIGNED_OUT') {
                    // 用户登出时，清除积分
                    this.credits = 0;
                    this.updateCreditsDisplay();
                    console.log('用户登出，已清除积分显示');
                }
            } catch (error) {
                console.error('处理认证状态变更时出错:', error);
            }
        }

        /**
         * 初始化积分系统
         * @returns {Promise<void>}
         */
        async init() {
            // 首先，检查并处理支付成功后的URL参数
            if (checkAndHandlePaymentSuccess()) {
                console.log('支付成功，积分已通过URL更新，跳过常规初始化');
                this._initialized = true;
                return;
            }

            console.log('积分管理器开始初始化过程...');
            if (this._initialized) {
                console.log('积分管理器已初始化，跳过');
                return;
            }

            console.log('初始化积分管理器...');

            // 新增逻辑：优先使用credits-loader.js设置的缓存
            try {
                const loaderCache = localStorage.getItem('cachedCredits');
                if (loaderCache) {
                    const parsedCache = JSON.parse(loaderCache);
                    // 检查缓存是否有效且不太旧（例如5分钟内）
                    if (parsedCache && typeof parsedCache.credits !== 'undefined' && (Date.now() - (parsedCache.timestamp || 0)) < 300000) {
                        this.credits = parsedCache.credits;
                        console.log(`[Credits] 初始化：成功使用来自credits-loader的缓存积分: ${this.credits}`);
                        this.updateCreditsDisplay();
                        this._initialized = true;
                        
                        // 后台静默更新已移除，避免重复请求积分
                        
                        // 设置OAuth事件监听器
                        this.setupOAuthManagerListener();
                        // 使用 loaderCache 后跳过后续抓取逻辑
                        return;
                    }
                }
            } catch (error) {
                console.warn('[Credits] 解析加载器缓存失败:', error);
            }


            // 检查登录状态
            const loggedIn = isUserLoggedIn();
            console.log(`用户登录状态: ${loggedIn ? '已登录' : '未登录'}`);

            // 如果初次检查失败，可能是认证状态还未完全同步，稍等一下再检查
            if (!loggedIn) {
                console.log('初次登录状态检查失败，等待认证状态同步...');
                await new Promise(resolve => setTimeout(resolve, 500));
                const loggedInRetry = isUserLoggedIn();
                console.log(`重试后用户登录状态: ${loggedInRetry ? '已登录' : '未登录'}`);
                
                if (loggedInRetry) {
                    console.log('重试检查成功，用户已登录');
                } else {
                    console.log('重试检查仍然失败，用户确实未登录');
                }
            }
            
            if (loggedIn || isUserLoggedIn()) {
                try {
                    // 获取有效令牌
                    await this.getValidToken();
                    
                    // 优先使用登录时缓存的积分，避免重复请求
                    const cachedCredits = this.getCachedCredits();
                    if (cachedCredits !== null && cachedCredits > 0) {
                        console.log('初始化：使用缓存积分，避免重复请求:', cachedCredits);
                        this.credits = cachedCredits;
                        this.updateCreditsDisplay();
                        
                        // 如果积分 > 0，立即设置免检查时间
                        this.setAuxiliarySkipCheckTime(60);
                        console.log(`🚀 [初始化] 积分${cachedCredits} > 0，设置60分钟免检查时间`);
                        
                        // 启动动态缓存管理
                        if (!this.dynamicCacheManagementStarted) {
                            setTimeout(async () => {
                                await this.startDynamicCacheManagement();
                                console.log(`✅ [初始化] 动态缓存管理已启动`);
                            }, 100);
                        }
                        
                        // 触发积分更新事件
                        document.dispatchEvent(new CustomEvent('credits-updated', {
                            detail: { credits: this.credits }
                        }));
                    } else {
                        console.log('初始化：缓存积分为空或为0，主动从服务器获取');
                        // 新用户或缓存失效时，主动获取积分
                        if (isUserLoggedIn()) {
                            // 异步获取积分，不阻塞页面初始化
                            setTimeout(async () => {
                                try {
                                    await this.fetchCreditsFromServer(true);
                                    console.log('初始化：积分已从服务器同步完成');
                                } catch (error) {
                                    console.warn('初始化：从服务器获取积分失败，使用默认值0:', error);
                                    this.credits = 0;
                                    this.updateCreditsDisplay();
                                }
                            }, 100);
                        } else {
                            this.credits = 0;
                        }
                    }
                } catch (error) {
                    console.warn('初始化过程中获取令牌失败:', error);
                    // 获取失败时使用本地缓存
                    this.credits = this.getCachedCredits() || 0;
                }
            } else {
                console.log('用户未登录，使用缓存积分');
                this.credits = this.getCachedCredits() || 0;
            }
            
            // 更新显示
            this.updateCreditsDisplay();
            
            // 设置异常退出处理
            this.setupUnloadHandler();
            
            // 设置模型选择监听
            setupModelSelectionListener();
            
            // 初始化充值按钮事件处理
            setupRechargeButton();
            
            // 设置OAuth管理器事件监听
            this.setupOAuthManagerListener();
            
            // 初始化完成标记
            this._initialized = true;
            
            console.log(`✅ 积分管理器初始化完成，状态:`, {
                initialized: this._initialized,
                credits: this.credits
            });
        }

        /**
         * 设置OAuth管理器事件监听
         */
        setupOAuthManagerListener() {
            // 监听OAuth积分请求事件
            document.addEventListener('oauth-credits-request', (event) => {
                console.log('[Credits] 收到OAuth积分请求事件:', event.detail);
                
                // 快速执行积分获取，与OAuth管理器的优化同步
                setTimeout(async () => {
                    try {
                        if (isUserLoggedIn()) {
                            console.log('[Credits] OAuth登录完成，获取最新积分');
                            await this.fetchCreditsFromServer();
                            
                            // 检查积分并设置免检查时间
                            const currentCredits = this.getCachedCredits() || 0;
                            if (currentCredits > 0) {
                                this.setAuxiliarySkipCheckTime(60);
                                console.log(`🚀 [OAuth登录] 积分${currentCredits} > 0，设置60分钟免检查时间`);
                                
                                // 启动动态缓存管理
                                if (!this.dynamicCacheManagementStarted) {
                                    await this.startDynamicCacheManagement();
                                    console.log(`✅ [OAuth登录] 动态缓存管理已启动`);
                                }
                            }
                        }
                    } catch (error) {
                        console.error('[Credits] OAuth积分获取失败:', error);
                    }
                }, 300); // 缩短到300ms，与OAuth管理器保持一致
            });

            // 已移除 auth-state-changed 事件监听，避免登录时重复拉取积分
            console.log('[Credits] OAuth管理器事件监听器已设置');
        }

        /**
         * 设置周期性检查用户登录状态和令牌有效性
         * 已删除 - 事件驱动的积分同步机制已经足够，不需要定时检查
         */
        // setupAuthCheckInterval() 函数已删除 - 使用事件驱动同步

        /**
         * 处理后端返回的积分信息，更新UI显示
         * @param {Object} creditsInfo - 积分信息对象
         */
        processCreditsInfo(creditsInfo) {
            if (!creditsInfo) return;
            
            console.log('处理积分信息:', creditsInfo);
            
            // 记录预估积分
            if (creditsInfo.estimated_credits) {
                this._lastEstimatedCredits = creditsInfo.estimated_credits;
            }
            
            // 如果包含积分余额信息，更新积分显示
            if (creditsInfo.remaining_credits !== undefined) {
                this.credits = creditsInfo.remaining_credits;
                this.cacheCredits(this.credits);
                this.updateCreditsDisplay();
                
                // 如果积分 > 0，确保设置免检查时间
                if (this.credits > 0) {
                    this.setAuxiliarySkipCheckTime(60);
                    console.log(`🚀 [积分处理] 积分${this.credits} > 0，设置60分钟免检查时间`);
                }
                
                // 显示消息
                if (creditsInfo.credits_deducted) {
                    const message = `操作完成，消耗了${creditsInfo.credits_deducted}积分`;
                    if (typeof showToast === 'function') {
                        showToast(message, 'success');
                    }
                }
            }
        }
        
        /**
         * 从服务器获取最新积分数据
         * @param {boolean} skipCache - 是否跳过本地缓存直接从服务器获取
         * @returns {Promise<number>} 返回最新积分数
         */
        async fetchCreditsFromServer(skipCache = false) {
            // 优化全局锁定机制，只对正常积分获取进行短时间锁定
            if (!skipCache && globalCreditsRequestInProgress && globalCreditsPromise && (Date.now() - globalCreditsLastRequestTime < 2000)) {
                console.log('检测到2秒内的重复积分请求，等待该请求完成');
                return globalCreditsPromise;
            }
            
            // 针对积分同步操作减少缓存失效时间，提高响应性
            const CREDITS_CACHE_TIMEOUT = skipCache ? 0 : 3000; // 跳过缓存时为0，否则3秒缓存失效时间
            
            // 只有在不跳过缓存时才检查缓存机制
            const now = Date.now();
            if (!skipCache && globalCreditsLastRequestTime && (now - globalCreditsLastRequestTime < CREDITS_CACHE_TIMEOUT) && this.credits !== null) {
                console.log('距离上次全局请求时间小于3秒，使用缓存积分');
                return Promise.resolve(this.credits);
            }
            
            // 如果跳过缓存，输出调试信息
            if (skipCache) {
                console.log('跳过积分缓存，强制从服务器获取最新积分');
            }
            
            // 创建新的请求Promise并保存到全局
            // 通知其他组件积分请求已开始 - 修改
            document.dispatchEvent(new CustomEvent('credits-fetch-started'));
            
            // 设置全局状态
            globalCreditsRequestInProgress = true;
            globalCreditsLastRequestTime = now;
            
            const requestPromise = this._doFetchCreditsFromServer(skipCache);
            globalCreditsPromise = requestPromise;
            
            try {
                const credits = await requestPromise;
                return credits;
            } finally {
                // 请求完成后重置全局状态，但保持较短的锁定时间
                setTimeout(() => {
                    globalCreditsRequestInProgress = false;
                    globalCreditsPromise = null;
                }, 1000); // 1秒后清除全局锁定
            }
        }
        
        /**
         * 实际执行积分获取的私有方法
         * @param {boolean} skipCache - 是否跳过缓存
         * @returns {Promise<number>} 积分数量
         */
        async _doFetchCreditsFromServer(skipCache) {
            try {
                // 首先检查URL中是否有积分参数，如果存在则优先使用
                const urlParams = new URLSearchParams(window.location.search);
                const creditsFromUrl = urlParams.get('credits');
                const isPaymentSuccess = urlParams.get('payment_success') === 'true';
                
                if (isPaymentSuccess && creditsFromUrl) {
                    const credits = parseInt(creditsFromUrl, 10);
                    if (!isNaN(credits) && credits > 0) {
                        console.log('从URL参数中获取到积分:', credits);
                        this.credits = credits;
                        this.updateCreditsDisplay();
                        this.cacheCredits(credits);
                        
                        // 清除URL中的参数，防止刷新页面时重复处理
                        const newUrl = window.location.pathname;
                        window.history.replaceState({}, '', newUrl);
                        
                        // 如果积分 > 0，确保设置免检查时间
                        if (credits > 0) {
                            this.setAuxiliarySkipCheckTime(60);
                            console.log(`🚀 [URL积分] 积分${credits} > 0，设置60分钟免检查时间`);
                        }
                        
                        // 通知其他组件积分请求已完成
                        document.dispatchEvent(new CustomEvent('credits-fetch-completed', {
                            detail: { credits: credits }
                        }));
                        
                        return credits;
                    }
                }
                
                // 检查是否是支付流程中的请求
                const isPaymentProcess = window.location.href.includes('/payment') || 
                                        document.referrer.includes('/payment') ||
                                        (window.location.pathname.includes('main.html') && 
                                         document.querySelector('.recharge-modal'));
                
                // 如果是支付流程中的请求，跳过积分查询，直接返回当前积分值
                if (isPaymentProcess) {
                    console.log('检测到支付流程，跳过积分查询以减少延迟');
                    
                    // 通知其他组件积分请求已完成
                    document.dispatchEvent(new CustomEvent('credits-fetch-completed', {
                        detail: { credits: this.credits || 0 }
                    }));
                    
                    return this.credits || 0;
                }
                
                // 如果不跳过缓存且当前积分已缓存，直接返回缓存的值
                if (!skipCache && this.credits !== null) {
                    // 即使有缓存，仍然在后台触发刷新（不阻塞UI）
                    this._fetchCreditsInBackground();
                    
                    // 通知其他组件积分请求已完成
                    document.dispatchEvent(new CustomEvent('credits-fetch-completed', {
                        detail: { credits: this.credits }
                    }));
                    
                    return this.credits;
                }
                
                console.log('从服务器获取最新积分数据...');
                
                // 获取认证令牌
                const authToken = await this.getValidToken();
                
                // 尝试使用原始端点
                console.log('尝试使用原始端点: /api/credits/get');
                console.log('认证令牌长度:', authToken ? authToken.length : 0);
                
                // 尝试使用原始端点
                let response;
                try {
                    response = await fetch('/api/credits/get', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });
                    
                    // 如果原始端点失败，尝试备用端点
                    if (!response.ok) {
                        console.log(`原始端点返回错误: ${response.status}. 尝试备用端点...`);
                        response = await fetch('/api/credits/get-user-credits', {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${authToken}`
                            }
                        });
                    }
                } catch (error) {
                    console.log('请求积分时出错，尝试备用端点:', error);
                    // 如果出错，尝试备用端点
                    response = await fetch('/api/credits/get-user-credits', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });
                }
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    // 保存服务器返回的积分数
                    this.credits = data.credits;
                    console.log(`服务器返回积分: ${this.credits}`);
                    
                    // 保存最新积分到本地缓存
                    this.cacheCredits(this.credits);
                    
                    // 标记这是从服务器刚获取的积分
                    this._justFetchedFromServer = true;
                    
                    // 更新UI显示
                    this.updateCreditsDisplay();
                    
                    // 通知其他组件积分请求已完成
                    document.dispatchEvent(new CustomEvent('credits-fetch-completed', {
                        detail: { credits: this.credits }
                    }));
                    
                    return this.credits;
                } else {
                    console.error('获取积分失败:', data.error);
                    
                    // 检查是否是令牌过期错误
                    if (data.error_code === 'TOKEN_EXPIRED' || 
                        (data.error && data.error.includes('令牌已过期'))) {
                        console.log('检测到令牌过期，尝试自动恢复...');
                        
                        // 检查页面是否正在初始化中
                        if (window.pageInitializing) {
                            console.log('🛡️ 页面正在初始化中，跳过令牌清理，稍后重试');
                            
                            // 通知其他组件积分请求已完成但失败
                            document.dispatchEvent(new CustomEvent('credits-fetch-completed', {
                                detail: { error: data.error }
                            }));
                            
                            return this.getCachedCredits() || 0;
                        }
                        
                        // 清理过期的令牌
                        localStorage.removeItem('authToken');
                        
                        // 尝试使用TokenRefreshHelper获取新令牌
                        if (window.TokenRefreshHelper && typeof window.TokenRefreshHelper.getValidAuthToken === 'function') {
                            try {
                                const newToken = await window.TokenRefreshHelper.getValidAuthToken(true); // 强制刷新
                                if (newToken) {
                                    console.log('成功获取新令牌，重试积分请求');
                                    // 使用新令牌重新请求积分
                                    const retryResponse = await fetch('/api/credits/get', {
                                        method: 'GET',
                                        headers: {
                                            'Authorization': `Bearer ${newToken}`
                                        }
                                    });
                                    
                                    if (retryResponse.ok) {
                                        const retryData = await retryResponse.json();
                                        if (retryData.success) {
                                            this.credits = retryData.credits;
                                            console.log(`重试成功，获取积分: ${this.credits}`);
                                            this.cacheCredits(this.credits);
                                            
                                            // 标记这是从服务器刚获取的积分
                                            this._justFetchedFromServer = true;
                                            
                                            this.updateCreditsDisplay();
                                            
                                            // 通知其他组件积分请求已完成
                                            document.dispatchEvent(new CustomEvent('credits-fetch-completed', {
                                                detail: { credits: this.credits }
                                            }));
                                            
                                            return this.credits;
                                        }
                                    }
                                }
                            } catch (refreshError) {
                                console.error('自动令牌刷新失败:', refreshError);
                            }
                        }
                    }
                    
                    // 通知其他组件积分请求已完成但失败
                    document.dispatchEvent(new CustomEvent('credits-fetch-completed', {
                        detail: { error: data.error }
                    }));
                    
                    return this.getCachedCredits() || 0;
                }
            } catch (error) {
                console.error('获取积分出错:', error);
                
                // 通知其他组件积分请求已完成但失败
                document.dispatchEvent(new CustomEvent('credits-fetch-completed', {
                    detail: { error: error.message }
                }));
                
                // 返回缓存的积分或0
                return this.getCachedCredits() || 0;
            } finally {
                // 记录本次请求时间
                this.lastFetchTime = Date.now();
            }
        }

        /**
         * 在后台静默获取积分（不阻塞UI）
         * @private
         */
        _fetchCreditsInBackground() {
            // 防止重复的后台请求
            if (this._backgroundFetching) {
                console.log('后台积分请求已在进行中，跳过重复请求');
                return;
            }
            
            this._backgroundFetching = true;
            
            setTimeout(async () => {
                try {
                    // 获取认证令牌
                    const authToken = await this.getValidToken();
                    
                    // 尝试原始端点
                    console.log('后台尝试原始端点: /api/credits/get');
                    console.log('后台认证令牌长度:', authToken ? authToken.length : 0);
                    
                    // 先尝试原始端点，如果失败再尝试备用端点
                    let response;
                    try {
                        response = await fetch('/api/credits/get', {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${authToken}`
                            }
                        });
                        
                        // 如果原始端点失败，尝试备用端点
                        if (!response.ok) {
                            console.log(`后台原始端点返回错误: ${response.status}. 尝试备用端点...`);
                            response = await fetch('/api/credits/get-user-credits', {
                                method: 'GET',
                                headers: {
                                    'Authorization': `Bearer ${authToken}`
                                }
                            });
                        }
                    } catch (error) {
                        console.log('后台请求积分时出错，尝试备用端点:', error);
                        // 如果出错，尝试备用端点
                        response = await fetch('/api/credits/get-user-credits', {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${authToken}`
                            }
                        });
                    }
                    
                    // 添加错误处理
                    if (!response.ok) {
                        console.error('获取积分失败:', response.status, response.statusText);
                        
                        // 如果是401错误，可能是令牌过期，尝试解析响应
                        if (response.status === 401) {
                            try {
                                const errorData = await response.json();
                                if (errorData.error_code === 'TOKEN_EXPIRED' || 
                                    (errorData.error && errorData.error.includes('令牌已过期'))) {
                                    console.log('后台检测到令牌过期，清理缓存');
                                    
                                    // 检查页面是否正在初始化中
                                    if (window.pageInitializing) {
                                        console.log('🛡️ 页面正在初始化中，跳过后台令牌清理');
                                        return;
                                    }
                                    
                                    localStorage.removeItem('authToken');
                                    
                                    // 触发令牌刷新
                                    if (window.TokenRefreshHelper && typeof window.TokenRefreshHelper.getValidAuthToken === 'function') {
                                        try {
                                            await window.TokenRefreshHelper.getValidAuthToken(true); // 强制刷新
                                            console.log('后台令牌刷新完成，下次请求将使用新令牌');
                                        } catch (refreshError) {
                                            console.error('后台令牌刷新失败:', refreshError);
                                        }
                                    }
                                }
                            } catch (parseError) {
                                console.error('解析401错误响应失败:', parseError);
                            }
                        }
                        
                        return; // 如果请求失败，直接返回
                    }
                    
                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        // 静默更新缓存和显示
                        this.credits = data.credits;
                        this.cacheCredits(this.credits);
                        this.updateCreditsDisplay();
                        console.log('后台刷新积分完成');
                    }
                } catch (error) {
                    console.error('后台获取积分出错:', error);
                } finally {
                    // 清除后台请求标记
                    this._backgroundFetching = false;
                }
            }, 500); // 延迟500ms执行，避免阻塞UI
        }

        /**
         * 从URL哈希中提取访问令牌（适用于OAuth回调）
         * @returns {string|null} 访问令牌或null
         */
        getTokenFromUrlHash() {
            try {
                const hash = window.location.hash.substring(1);
                const params = new URLSearchParams(hash);
                return params.get('access_token');
            } catch (e) {
                console.error('从URL提取令牌错误:', e);
                return null;
            }
        }
        
        /**
         * 从会话存储获取认证令牌
         * @returns {string|null} 认证令牌或null
         */
        getAuthTokenFromSession() {
            try {
                const session = JSON.parse(sessionStorage.getItem('authSession'));
                return session && session.token ? session.token : null;
            } catch (e) {
                console.error('从会话获取令牌错误:', e);
                return null;
            }
        }

        /**
         * 更新UI中的积分显示
         */
        updateCreditsDisplay() {
            try {
                const creditsDisplay = document.getElementById('userCredits');
                if (!creditsDisplay) {
                    console.warn('未找到积分显示元素(userCredits)');
                    return;
                }
                
                // 检查是否是OAuth重定向场景
                const isOAuthRedirect = window.location.hash && (
                    window.location.hash.includes('access_token=') || 
                    window.location.hash.includes('provider=google')
                );
                
                // 检查是否有加载指示器
                const hasLoadingIndicator = creditsDisplay.querySelector('.credits-loading');
                
                // 如果是OAuth重定向场景，积分为0，且当前正在显示加载指示器，则不更新显示
                if (isOAuthRedirect && this.credits === 0 && hasLoadingIndicator) {
                    console.log('OAuth登录中，保留加载指示器，不显示0积分');
                    return;
                }
                
                // 修复：如果当前积分为0，但本地缓存中有大于0的积分，则跳过这次更新
                // 但是如果积分是从服务器刚获取的，则应该显示真实值
                if (this.credits === 0 && !this._justFetchedFromServer) {
                    // 尝试从本地缓存获取积分
                    const cachedCredits = this.getCachedCredits();
                    if (cachedCredits && cachedCredits > 0) {
                        console.log('跳过显示0积分，保留缓存积分显示:', cachedCredits);
                        return;
                    }
                }
                
                // 重置服务器获取标记
                this._justFetchedFromServer = false;
                
                console.log('更新UI积分显示:', this.credits || 0);
                creditsDisplay.textContent = this.credits || 0;
                
                // 触发自定义事件，通知其他组件积分已更新
                document.dispatchEvent(new CustomEvent('credits-updated', {
                    detail: { credits: this.credits || 0 }
                }));
            } catch (error) {
                console.error('更新积分显示失败:', error);
            }
        }

        /**
         * 获取当前积分
         * @returns {number} 当前积分
         */
        getCredits() {
            return this.credits || 0;
        }

        /**
         * 检查积分是否足够
         * @param {string} operation - 操作类型
         * @param {string} model - 使用的AI模型
         * @returns {Promise<boolean>} 积分是否足够
         */
        async hasEnoughCredits(operation, model) {
            // 如果未提供model，尝试从全局获取当前选择的模型
            if (!model && window.currentModel) {
                // 使用convertModelNameToId函数转换，如果存在
                if (typeof window.convertModelNameToId === 'function') {
                    model = window.convertModelNameToId(window.currentModel);
                } else {
                    model = window.currentModel;
                }
                console.log(`hasEnoughCredits: 从全局获取当前模型ID: ${model}`);
            }
            
            try {
                // 计算所需积分 - 等待Promise解析，使用专门的检查积分API
                const requiredCredits = await calculateRequiredCredits(operation, model);
                console.log(`检查积分: 当前积分=${this.credits}, 需要积分=${requiredCredits}, 操作=${operation}, 模型=${model}`);
                
                // 确保当前积分是数字
                const currentCredits = this.credits || 0;
                
                // 进行正确的数值比较
                return currentCredits >= requiredCredits;
            } catch (error) {
                console.error(`检查积分时出错:`, error);
                // 出错时默认返回有足够积分，避免错误阻止用户操作
                return true;
            }
        }

        /**
         * 验证并获取有效的认证令牌
         * @returns {Promise<string>} 有效的认证令牌
         * @throws {Error} 如果无法获取有效令牌
         */
        async getValidToken() {
            console.log('获取有效令牌...');
            
            // 首先检查是否有TokenRefreshHelper
            if (window.TokenRefreshHelper && typeof window.TokenRefreshHelper.getValidAuthToken === 'function') {
                try {
                    console.log('使用TokenRefreshHelper获取有效令牌');
                    return await window.TokenRefreshHelper.getValidAuthToken();
                } catch (helperError) {
                    console.warn('TokenRefreshHelper获取令牌失败，尝试使用备用方法:', helperError);
                    // 如果帮助器失败，继续使用备用方法
                }
            }
            
            // 获取当前存储的令牌
            let authToken = localStorage.getItem('authToken');
            
            if (!authToken) {
                console.warn('本地存储中未找到令牌，尝试从其他来源获取');
                authToken = this.getAuthTokenFromSession() || this.getTokenFromUrlHash();
                
                if (authToken) {
                    // 如果从其他来源找到了令牌，保存到localStorage
                    localStorage.setItem('authToken', authToken);
                    console.log('已从其他来源获取并保存令牌');
                    
                    // 触发认证状态变化事件
                    window.dispatchEvent(new CustomEvent('auth-state-changed', { 
                        detail: {
                            status: 'authenticated',
                            message: '从其他来源获取令牌成功',
                            source: 'credits.js',
                            timestamp: new Date().toISOString()
                        },
                        bubbles: true,
                        cancelable: true
                    }));
                } else {
                    throw new Error('用户未登录');
                }
            }
            
            // 预检查令牌是否即将过期
            try {
                // 如果有TokenRefreshHelper，使用它的方法检查令牌是否即将过期
                if (window.TokenRefreshHelper && typeof window.TokenRefreshHelper.isTokenExpiringSoon === 'function') {
                    if (window.TokenRefreshHelper.isTokenExpiringSoon(authToken, 5)) { // 5分钟阈值
                        console.log('令牌即将过期，尝试刷新');
                        return await this.refreshToken();
                    }
                } else {
                    // 备用方法：手动解析令牌
                    const tokenParts = authToken.split('.');
                    if (tokenParts.length === 3) {
                        // 解码JWT payload
                        const payload = JSON.parse(atob(tokenParts[1].replace(/-/g, '+').replace(/_/g, '/')));
                        const expiryTime = payload.exp * 1000; // 转换为毫秒
                        const currentTime = Date.now();
                        const timeRemaining = expiryTime - currentTime;
                        
                        console.log(`令牌状态检查: 过期时间=${new Date(expiryTime).toLocaleString()}, 当前时间=${new Date(currentTime).toLocaleString()}, 剩余=${Math.floor(timeRemaining/1000)}秒`);
                        
                        // 如果令牌已过期或即将过期，尝试刷新
                        if (timeRemaining < 300000) { // 5分钟 = 300000毫秒
                            console.log('令牌即将过期，尝试刷新');
                            return await this.refreshToken();
                        }
                    }
                }
            } catch (parseError) {
                console.warn('解析令牌时出错，将继续验证:', parseError);
                // 解析错误不阻止继续验证
            }
            
            try {
                // 对于新注册用户，先检查令牌是否可能需要刷新
                const tokenAge = this.getTokenAge(authToken);
                if (tokenAge !== null && tokenAge > 30 * 60 * 1000) { // 如果令牌超过30分钟，主动刷新
                    console.log('令牌已存在30分钟以上，主动刷新');
                    return await this.refreshToken();
                }
                
                // 验证令牌有效性
                console.log('发送令牌验证请求，令牌长度:', authToken.length);
                const verifyResponse = await fetch('/api/auth/verify', {
                    method: 'GET',
                    headers: { 'Authorization': `Bearer ${authToken}` },
                    credentials: 'same-origin' // 确保发送cookie
                });
                
                const verifyData = await verifyResponse.json();
                
                if (verifyResponse.ok && verifyData.success) {
                    // 令牌有效，直接返回
                    return authToken;
                }
                
                // 令牌无效或过期，尝试刷新
                if (verifyResponse.status === 401 || verifyData.error_code === 'token_expired') {
                    console.log('令牌已过期，尝试刷新...', verifyData);
                    return await this.refreshToken();
                } else {
                    console.error('验证令牌失败:', verifyData.message || '未知错误');
                    throw new Error('验证用户身份失败');
                }
            } catch (error) {
                console.error('验证令牌时出错:', error);
                // 如果验证失败，也尝试刷新令牌
                console.log('验证令牌失败，尝试刷新令牌');
                try {
                    return await this.refreshToken();
                } catch (refreshError) {
                    console.error('刷新令牌也失败:', refreshError);
                    throw new Error('验证用户身份失败');
                }
            }
        }
        
        /**
         * 刷新认证令牌
         * @returns {Promise<string>} 新的有效令牌
         * @throws {Error} 如果刷新失败
         */
        async refreshToken() {
            console.log('刷新认证令牌...');
            
            // 首先检查是否有TokenRefreshHelper
            if (window.TokenRefreshHelper && typeof window.TokenRefreshHelper.refreshAuthToken === 'function') {
                try {
                    console.log('使用TokenRefreshHelper刷新令牌');
                    const newToken = await window.TokenRefreshHelper.refreshAuthToken();
                    console.log('使用TokenRefreshHelper刷新令牌成功');
                    return newToken;
                } catch (helperError) {
                    console.warn('TokenRefreshHelper刷新令牌失败，尝试使用备用方法:', helperError);
                    // 如果帮助器失败，继续使用备用方法
                }
            }
            
            // 如果有TokenRefresh全局对象，使用它的refreshToken方法
            if (window.TokenRefresh && typeof window.TokenRefresh.refreshToken === 'function') {
                try {
                    console.log('使用TokenRefresh刷新令牌');
                    const newToken = await window.TokenRefresh.refreshToken();
                    console.log('使用TokenRefresh刷新令牌成功');
                    return newToken;
                } catch (refreshError) {
                    console.warn('TokenRefresh刷新令牌失败，尝试使用备用方法:', refreshError);
                    // 如果刷新失败，继续使用备用方法
                }
            }
            
            // 备用方法：直接调用API
            try {
                console.log('使用备用方法刷新令牌');
                // 尝试使用刷新令牌获取新令牌
                const refreshToken = localStorage.getItem('refreshToken');
                if (!refreshToken) {
                    throw new Error('没有可用的刷新令牌');
                }
                
                const refreshResponse = await fetch('/api/auth/refresh', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ refresh_token: refreshToken })
                });
                
                const refreshData = await refreshResponse.json();
                
                if (refreshResponse.ok && refreshData.success) {
                    if (refreshData.access_token) {
                        // 保存新令牌
                        console.log('令牌刷新成功，已获取新令牌');
                        localStorage.setItem('authToken', refreshData.access_token);
                        if (refreshData.refresh_token) {
                            localStorage.setItem('refreshToken', refreshData.refresh_token);
                        }
                        
                        // 触发认证状态变化事件
                        window.dispatchEvent(new CustomEvent('auth-state-changed', { 
                            detail: {
                                status: 'authenticated',
                                message: '令牌刷新成功',
                                timestamp: new Date().toISOString()
                            },
                            bubbles: true,
                            cancelable: true
                        }));
                        
                        return refreshData.access_token;
                    }
                } else if (refreshData.use_client_refresh && window.supabaseClient) {
                    // 后端指示使用客户端方式刷新令牌
                    console.log('使用Supabase客户端刷新令牌');
                    
                    try {
                        // 使用Supabase客户端刷新令牌
                        const { data, error } = await window.supabaseClient.auth.refreshSession();
                        
                        if (error) {
                            throw new Error('客户端刷新令牌失败: ' + (error.message || '未知错误'));
                        }
                        
                        if (data && data.session) {
                            const newToken = data.session.access_token;
                            const newRefreshToken = data.session.refresh_token;
                            
                            // 保存新令牌
                            localStorage.setItem('authToken', newToken);
                            localStorage.setItem('refreshToken', newRefreshToken);
                            
                            // 触发认证状态变化事件
                            window.dispatchEvent(new CustomEvent('auth-state-changed', { 
                                detail: {
                                    status: 'authenticated',
                                    message: '通过Supabase客户端成功刷新令牌',
                                    timestamp: new Date().toISOString()
                                },
                                bubbles: true,
                                cancelable: true
                            }));
                            
                            console.log('通过Supabase客户端成功刷新令牌');
                            return newToken;
                        }
                    } catch (clientRefreshError) {
                        console.error('Supabase客户端刷新令牌失败:', clientRefreshError);
                        // 继续抛出异常，让外层处理
                        throw clientRefreshError;
                    }
                }
                
                // 如果刷新失败，尝试重新登录
                throw new Error('令牌刷新失败');
            } catch (refreshError) {
                console.error('令牌刷新失败:', refreshError);
                // 如果是在main.html页面，显示友好提示
                if (window.location.pathname.includes('main.html')) {
                    this.showAuthErrorMessage();
                }
                throw new Error('验证用户身份失败');
            }
        }
        
        /**
         * 使用积分函数 - 兼容旧版本调用，但推荐使用新版本
         * @param {string} operation - 操作类型
         * @param {string} model - 模型ID
         * @param {Object} tokenUsage - token使用情况
         */
        async useCredits(operation, model, tokenUsage = null) {
            console.log(`useCredits 函数被调用，但不再执行扣减操作（操作: ${operation}, 模型: ${model}）`);
            
            // 检查是否是新版本调用（新版本不需要额外的积分同步）
            const isNewVersionCall = this._lastSyncTime && (Date.now() - this._lastSyncTime < 5000);
            
            if (isNewVersionCall) {
                console.log('检测到新版本积分扣减，跳过useCredits中的积分同步');
                return;
            }
            
            // 为了兼容可能的旧版本调用，执行一次积分刷新
            try {
                console.log('执行兼容性积分刷新');
                await this.fetchCreditsFromServer(true);
                this.updateCreditsDisplay();
            } catch (error) {
                console.error('兼容性积分刷新失败:', error);
            }
        }

        /**
         * 基于实际token使用数据在本地扣减积分
         * @param {string} operation - 操作类型
         * @param {string} model - 使用的AI模型
         * @param {Object} tokenUsageData - 令牌使用数据
         * @param {string} bookName - 书籍名称
         * @param {string} chapterTitle - 章节标题
         * @returns {Promise<boolean>} 是否成功使用积分
         */
        async useCreditsLocally(operation, model, tokenUsageData, bookName = '', chapterTitle = '') {
            try {
                // 获取本地缓存的积分
                let currentCredits = this.getCachedCredits();
                if (currentCredits === null) {
                    console.warn('本地没有缓存积分，无法进行本地积分扣减');
                    return false;
                }
                
                // 基于token使用数据计算所需积分
                let creditsToDeduct = 1; // 默认至少扣1分
                
                try {
                    // 使用calculateCreditsFromTokenUsage函数计算实际所需积分
                    if (typeof calculateCreditsFromTokenUsage === 'function') {
                        creditsToDeduct = await calculateCreditsFromTokenUsage(tokenUsageData, model);
                    } else {
                        // 回退到基础计算
                        creditsToDeduct = await calculateRequiredCredits(operation, model);
                    }
                } catch (calcError) {
                    console.error('计算所需积分失败，使用默认值:', calcError);
                }
                
                // 检查积分是否足够
                if (currentCredits < creditsToDeduct) {
                    this.showInsufficientCreditsAlert();
                    return false;
                }
                
                // 在本地扣减积分
                const newCredits = currentCredits - creditsToDeduct;
                this.credits = newCredits;
                
                // 将更新后的积分保存到本地缓存
                this.cacheCredits(newCredits);
                
                // 保存待同步记录到本地存储
                this.saveLocalDeductionRecord(operation, model, tokenUsageData, bookName, chapterTitle, creditsToDeduct);
                
                console.log(`本地积分扣减: 从 ${currentCredits} 扣除 ${creditsToDeduct}，剩余 ${newCredits}`);
                this.updateCreditsDisplay();
                
                // 显示提示
                showToast('积分已在本地扣减，将在下次连接时与服务器同步', 'info', 3000);
                
                return true;
            } catch (error) {
                console.error('本地积分扣减失败:', error);
                return false;
            }
        }

        /**
         * 保存本地扣减记录用于后续同步
         */
        saveLocalDeductionRecord(operation, model, tokenUsageData, bookName, chapterTitle, creditsDeducted) {
            try {
                // 从本地存储获取现有记录
                const recordsJson = localStorage.getItem('pendingCreditsSync') || '[]';
                const records = JSON.parse(recordsJson);
                
                // 添加新记录
                records.push({
                    operation,
                    model,
                    token_usage: tokenUsageData,
                    book_name: bookName,
                    chapter_title: chapterTitle,
                    credits_deducted: creditsDeducted,
                    timestamp: new Date().toISOString()
                });
                
                // 保存回本地存储
                localStorage.setItem('pendingCreditsSync', JSON.stringify(records));
                localStorage.setItem('creditsNeedSync', 'true');
                
                console.log('已保存本地积分扣减记录，等待同步');
            } catch (error) {
                console.error('保存本地扣减记录失败:', error);
            }
        }

        /**
         * 同步本地积分扣减记录到服务器
         * 优化版：只在关键事件时同步，不定期同步
         */
        async syncLocalCreditsDeductions() {
            try {
                // 检查是否有待同步记录
                if (localStorage.getItem('creditsNeedSync') !== 'true') {
                    return;
                }
                
                // 获取认证令牌
                let authToken;
                try {
                    authToken = await this.getValidToken();
                } catch (tokenError) {
                    console.error('获取有效令牌失败，无法同步积分记录:', tokenError);
                    return;
                }
                
                // 获取待同步记录
                const recordsJson = localStorage.getItem('pendingCreditsSync') || '[]';
                const records = JSON.parse(recordsJson);
                
                if (records.length === 0) {
                    localStorage.removeItem('creditsNeedSync');
                    return;
                }
                
                console.log(`开始同步 ${records.length} 条本地积分扣减记录`);
                
                // 发送同步请求到服务器
                const response = await fetch('/api/credits/sync-local-deductions', {
                    method: 'POST',
                    headers: { 
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ records })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.success) {
                        // 同步成功，更新本地积分
                        this.credits = data.credits;
                        this.cacheCredits(this.credits);
                        this.updateCreditsDisplay();
                        
                        // 清除已同步的记录
                        localStorage.removeItem('pendingCreditsSync');
                        localStorage.removeItem('creditsNeedSync');
                        
                        console.log('本地积分扣减记录同步成功');
                        showToast('积分记录已成功同步到服务器', 'success', 3000);
                        
                        // 触发同步完成事件
                        document.dispatchEvent(new CustomEvent('credits-sync-completed', {
                            detail: { credits: data.credits, recordsCount: records.length }
                        }));
                    } else {
                        console.error('同步积分记录失败:', data.error);
                    }
                } else {
                    console.error('同步积分记录请求失败');
                }
            } catch (error) {
                console.error('同步本地积分扣减记录失败:', error);
            }
        }

        /**
         * API调用后立即同步积分 - 新增方法
         * 在每次API调用完成后调用此方法
         */
        async syncCreditsAfterApiCall() {
            try {
                console.log('🔄 [积分同步] API调用完成，开始积分同步');
                console.log(`🔄 [积分同步] 当前前端缓存积分: ${this.credits}`);
                
                // API调用后的积分同步具有更高优先级，跳过时间间隔检查
                // 但仍然检查并发保护
                if (this._syncInProgress) {
                    console.log('⏳ [积分同步] 积分同步正在进行中，等待完成后重新尝试');
                    // 等待当前同步完成后再执行
                    let retryCount = 0;
                    while (this._syncInProgress && retryCount < 10) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                        retryCount++;
                    }
                    if (this._syncInProgress) {
                        console.log('⚠️ [积分同步] 积分同步等待超时，强制执行');
                        this._syncInProgress = false;
                    }
                }
                this._syncInProgress = true;

                try {
                    console.log('🚀 [积分同步] 强制执行API调用后积分同步（跳过防重复检查）');
                    
                    // 1. 先同步本地扣减记录
                    await this.syncLocalCreditsDeductions();
                    
                    // 2. 从服务器获取最新积分状态，跳过缓存确保获取真实积分
                    console.log('🌐 [积分同步] 从服务器获取最新积分（跳过缓存）');
                    await this.fetchCreditsFromServer(true); // 跳过缓存，强制从服务器获取
                    console.log(`📊 [积分同步] 从服务器获取到积分: ${this.credits}`);
                    
                    // 3. 立即更新前端显示
                    console.log('🎨 [积分同步] 更新前端积分显示');
                    this.updateCreditsDisplay();
                    
                    // 4. 触发积分更新事件，通知其他组件
                    console.log('📡 [积分同步] 触发积分更新事件');
                    document.dispatchEvent(new CustomEvent('credits-updated', {
                        detail: { credits: this.credits, source: 'api-sync' }
                    }));
                    
                    // 5. 强制更新页面所有积分显示元素
                    console.log('🔄 [积分同步] 强制更新所有积分显示元素');
                    this.forceUpdateAllCreditsElements();
                    
                    // 6. 立即清除前端缓存并重新设置为最新积分
                    console.log('💾 [积分同步] 清除并重新设置前端缓存');
                    localStorage.removeItem('userCredits');
                    this.cacheCredits(this.credits);
                    
                    // 7. 通知积分历史管理器刷新（如果打开的话）
                    if (typeof window.refreshCreditHistoryAfterOperation === 'function') {
                        window.refreshCreditHistoryAfterOperation();
                    }
                    
                    // 8. 更新最后同步时间
                    this._lastSyncTime = Date.now();
                    
                    console.log(`✅ [积分同步] API调用后积分同步完成，当前积分: ${this.credits}`);
                } finally {
                    this._syncInProgress = false;
                }
            } catch (error) {
                console.error('API调用后积分同步失败:', error);
                this._syncInProgress = false;
                
                // 如果同步失败，至少尝试从服务器获取最新积分
                try {
                    await this.fetchCreditsFromServer(true);
                    this.updateCreditsDisplay();
                    this.forceUpdateAllCreditsElements();
                    
                    document.dispatchEvent(new CustomEvent('credits-updated', {
                        detail: { credits: this.credits, source: 'fallback-sync' }
                    }));
                } catch (fetchError) {
                    console.error('无法获取最新积分:', fetchError);
                }
            }
        }

        /**
         * 强制更新页面所有积分显示元素
         */
        forceUpdateAllCreditsElements() {
            try {
                console.log(`开始强制更新所有积分显示元素，目标积分: ${this.credits}`);
                
                // 更新所有可能的积分显示元素
                const creditsElements = document.querySelectorAll('#userCredits, .user-credits, .credits-display');
                console.log(`找到 ${creditsElements.length} 个普通积分显示元素`);
                
                creditsElements.forEach((element, index) => {
                    if (element) {
                        const oldValue = element.textContent;
                        element.textContent = this.credits || 0;
                        element.style.opacity = '1';
                        element.classList.remove('credits-loading');
                        console.log(`更新积分元素 ${index + 1}: ${oldValue} → ${this.credits}`);
                    }
                });
                
                // 特别更新菜单中的积分显示
                const menuCreditsElement = document.querySelector('.user-menu #userCredits');
                if (menuCreditsElement) {
                    const oldMenuValue = menuCreditsElement.textContent;
                    menuCreditsElement.textContent = this.credits || 0;
                    menuCreditsElement.style.opacity = '1';
                    menuCreditsElement.classList.remove('credits-loading');
                    console.log(`更新菜单积分显示: ${oldMenuValue} → ${this.credits}`);
                } else {
                    console.log('未找到菜单积分显示元素 (.user-menu #userCredits)');
                }
                
                // 也尝试更新通过ID直接查找的菜单积分元素
                const directMenuElement = document.getElementById('userCredits');
                if (directMenuElement) {
                    const oldDirectValue = directMenuElement.textContent;
                    directMenuElement.textContent = this.credits || 0;
                    directMenuElement.style.opacity = '1';
                    directMenuElement.classList.remove('credits-loading');
                    console.log(`更新直接查找的积分元素: ${oldDirectValue} → ${this.credits}`);
                }
                
                console.log(`✅ 已强制更新所有积分显示元素为: ${this.credits}`);
            } catch (error) {
                console.error('强制更新积分显示元素失败:', error);
            }
        }

        /**
         * 登录时的积分同步 - 新增方法
         * 在用户登录成功后调用
         */
        async syncCreditsOnLogin() {
            try {
                console.log('用户登录，开始积分同步');
                
                // 1. 先同步任何待处理的本地积分扣减记录
                await this.syncLocalCreditsDeductions();
                
                // 2. 优先使用登录时缓存的积分，不强制请求服务器
                const cachedCredits = localStorage.getItem('user_credits');
                if (cachedCredits && !isNaN(cachedCredits)) {
                    console.log('登录时使用缓存积分，避免重复请求:', cachedCredits);
                    this.credits = parseInt(cachedCredits);
                    this.updateCreditsDisplay();
                    // 触发积分更新事件
                    document.dispatchEvent(new CustomEvent('credits-updated', {
                        detail: { credits: this.credits }
                    }));
                } else {
                    console.log('登录时未找到缓存积分，从服务器获取');
                    await this.fetchCreditsFromServer(false); // 不强制，允许使用缓存
                }
                
                // 3. 清理可能存在的过期缓存
                this.cleanExpiredCache();
                
                console.log('登录时积分同步完成');
            } catch (error) {
                console.error('登录时积分同步失败:', error);
            }
        }

        /**
         * 退出时的积分同步 - 新增方法
         * 在用户退出前调用
         */
        async syncCreditsOnLogout() {
            try {
                console.log('用户退出，开始积分同步');
                
                // 1. 如果有待同步的记录，尝试立即同步
                if (localStorage.getItem('creditsNeedSync') === 'true') {
                    await this.syncLocalCreditsDeductions();
                }
                
                // 2. 确保当前积分状态被正确缓存
                if (this.credits !== null && this.credits !== undefined) {
                    this.cacheCredits(this.credits);
                    console.log(`退出前已缓存积分: ${this.credits}`);
                }
                
                console.log('退出时积分同步完成');
            } catch (error) {
                console.error('退出时积分同步失败:', error);
                
                // 即使同步失败，也要确保当前积分被缓存
                if (this.credits !== null && this.credits !== undefined) {
                    this.cacheCredits(this.credits);
                }
            }
        }

        /**
         * 购买积分后的同步 - 新增方法
         * 在积分充值成功后调用
         */
        async syncCreditsAfterPurchase() {
            try {
                console.log('Credit purchase completed, starting sync');
                
                // 购买积分后必须从服务器获取最新状态
                await this.fetchCreditsFromServer(true);
                
                // 清理任何可能的待同步记录（购买积分后应该重新计算）
                localStorage.removeItem('pendingCreditsSync');
                localStorage.removeItem('creditsNeedSync');
                
                console.log('购买后积分同步完成');
                
                // 触发购买完成事件
                document.dispatchEvent(new CustomEvent('credits-purchase-synced', {
                    detail: { credits: this.credits }
                }));
            } catch (error) {
                console.error('购买后积分同步失败:', error);
            }
        }

        /**
         * 清理过期缓存 - 新增方法
         */
        cleanExpiredCache() {
            try {
                const MAX_CACHE_AGE = 24 * 60 * 60 * 1000; // 24小时
                const now = Date.now();
                
                // 检查主缓存
                const cachedData = localStorage.getItem('userCredits');
                if (cachedData) {
                    try {
                        const parsed = JSON.parse(cachedData);
                        if (parsed.timestamp && (now - parsed.timestamp) > MAX_CACHE_AGE) {
                            localStorage.removeItem('userCredits');
                            console.log('已清理过期的积分缓存');
                        }
                    } catch (e) {
                        // 如果解析失败，删除无效缓存
                        localStorage.removeItem('userCredits');
                        console.log('已清理无效的积分缓存');
                    }
                }
                
                // 检查其他相关缓存
                const oldKeys = ['cachedCredits', 'userCreditsCache'];
                oldKeys.forEach(key => {
                    if (localStorage.getItem(key)) {
                        localStorage.removeItem(key);
                        console.log(`已清理旧缓存: ${key}`);
                    }
                });
            } catch (error) {
                console.error('清理缓存时出错:', error);
            }
        }

        /**
         * 异常退出处理 - 新增方法
         * 通过beforeunload事件处理异常退出情况
         */
        setupUnloadHandler() {
            // 页面卸载前的处理
            window.addEventListener('beforeunload', (event) => {
                try {
                    // 如果有待同步的积分扣减，尝试快速同步
                    if (localStorage.getItem('creditsNeedSync') === 'true') {
                        // 使用sendBeacon API进行快速同步（如果浏览器支持）
                        if (navigator.sendBeacon && this.credits !== null) {
                            const authToken = localStorage.getItem('authToken');
                            if (authToken) {
                                const recordsJson = localStorage.getItem('pendingCreditsSync') || '[]';
                                const records = JSON.parse(recordsJson);
                                
                                if (records.length > 0) {
                                    const payload = JSON.stringify({ records });
                                    const success = navigator.sendBeacon('/api/credits/sync-local-deductions', 
                                        new Blob([payload], { type: 'application/json' }));
                                    
                                    if (success) {
                                        console.log('异常退出前已发送积分同步请求');
                                    }
                                }
                            }
                        }
                    }
                    
                    // 确保当前积分被正确缓存
                    if (this.credits !== null && this.credits !== undefined) {
                        this.cacheCredits(this.credits);
                    }
                } catch (error) {
                    console.error('页面卸载处理出错:', error);
                }
            });
            
            // 页面隐藏时的处理（移动端友好）
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    try {
                        // 页面隐藏时缓存当前积分状态
                        if (this.credits !== null && this.credits !== undefined) {
                            this.cacheCredits(this.credits);
                        }
                    } catch (error) {
                        console.error('页面隐藏处理出错:', error);
                    }
                }
            });
        }

        /**
         * 显示积分不足提示
         * @param {number} currentCredits - 当前积分数（可选，默认使用this.credits）
         */
        showInsufficientCreditsAlert(currentCredits = null) {
            // 如果没有传入积分值，使用实例的积分值
            const creditsToShow = currentCredits !== null ? currentCredits : this.credits;
            console.log('尝试显示积分不足提示，当前积分:', creditsToShow);
            // 检查是否已存在提示框
            if (document.getElementById('creditsAlert')) {
                console.log('积分提示框已存在，不再创建');
                return;
            }
            
            // 创建提示框 - 使用DOM API替代innerHTML以避免XSS风险
            const alertDiv = document.createElement('div');
            alertDiv.id = 'creditsAlert';
            alertDiv.className = 'credits-alert';
            
            // 检查UI语言系统是否可用
            const hasUILanguage = window.UILanguage && typeof window.UILanguage.getText === 'function' && typeof window.UILanguage.getCurrentLanguage === 'function';
            
            // 获取当前语言
            let currentLang = 'zh'; // 默认中文
            
            if (hasUILanguage) {
                currentLang = window.UILanguage.getCurrentLanguage();
            } else {
                // 如果无法使用UILanguage对象，尝试其他方法获取语言
                try {
                    const storedLang = localStorage.getItem('uiLanguage');
                    if (storedLang && (storedLang === 'en' || storedLang === 'zh')) {
                        currentLang = storedLang;
                    } else if (navigator.language && !navigator.language.toLowerCase().startsWith('zh')) {
                        currentLang = 'en';
                    }
                } catch (e) {
                    console.error('获取语言设置失败', e);
                }
            }
            
            // 获取翻译文本的函数
            const getText = (key) => {
                if (hasUILanguage) {
                    return window.UILanguage.getText(key);
                }
                
                // 默认翻译文本，如果UI语言系统不可用
                const defaultTexts = {
                    'zh': {
                        'credits.insufficient_title': '积分不足',
                        'credits.insufficient_message': '您的积分不足，无法完成此操作',
                        'credits.current_credits': '当前积分',
                        'credits.close': '关闭',
                        'credits.recharge_now': '充值积分'
                    },
                    'en': {
                        'credits.insufficient_title': 'Insufficient Credits',
                        'credits.insufficient_message': 'You don\'t have enough credits to complete this operation',
                        'credits.current_credits': 'Current Credits',
                        'credits.close': 'Close',
                        'credits.recharge_now': 'Recharge Now'
                    }
                };
                
                // 如果当前语言不存在，返回中文
                const texts = defaultTexts[currentLang] || defaultTexts['zh'];
                return texts[key] || key;
            };
            
            // 获取翻译文本
            const title = getText('credits.insufficient_title');
            const message = getText('credits.insufficient_message');
            const currentCreditsLabel = getText('credits.current_credits');
            const closeButtonText = getText('credits.close');
            const rechargeButtonText = getText('credits.recharge_now');
            
            // 创建提示内容
            const alertContent = document.createElement('div');
            alertContent.className = 'credits-alert-content';
            
            // 标题
            const titleEl = document.createElement('h3');
            titleEl.textContent = title;
            
            // 消息
            const messageP = document.createElement('p');
            messageP.textContent = message;
            
            // 积分显示
            const creditsP = document.createElement('p');
            const creditsSpan = document.createElement('span');
            creditsSpan.className = 'credits-amount';
            creditsSpan.textContent = creditsToShow;
            creditsP.textContent = currentCreditsLabel + ': ';
            creditsP.appendChild(creditsSpan);
            
            // 按钮区域
            const buttonsDiv = document.createElement('div');
            buttonsDiv.className = 'credits-alert-buttons';
            
            // 关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.id = 'closeCreditsAlert';
            closeBtn.textContent = closeButtonText;
            
            // 充值按钮
            const rechargeBtn = document.createElement('button');
            rechargeBtn.id = 'rechargeCredits';
            rechargeBtn.textContent = rechargeButtonText;
            
            // 组装按钮
            buttonsDiv.appendChild(closeBtn);
            buttonsDiv.appendChild(rechargeBtn);
            
            // 组装所有内容
            alertContent.appendChild(titleEl);
            alertContent.appendChild(messageP);
            alertContent.appendChild(creditsP);
            alertContent.appendChild(buttonsDiv);
            
            // 添加到提示框
            alertDiv.appendChild(alertContent);
            
            // 添加到页面
            document.body.appendChild(alertDiv);
            console.log('积分不足提示框已创建并添加到页面');
            
            // 添加关闭按钮事件
            closeBtn.addEventListener('click', () => {
                // 确保在DOM中存在才移除
                const alert = document.getElementById('creditsAlert');
                if (alert && alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            });
            
            // 添加充值按钮事件
            rechargeBtn.addEventListener('click', () => {
                // 先移除积分不足提示框
                const alert = document.getElementById('creditsAlert');
                if (alert && alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
                
                // 跳转到充值页面或显示充值弹窗
                this.showRechargeModal();
            });
        }

        /**
         * 显示充值弹窗
         */
        showRechargeModal() {
            // 直接使用 paymentModule 的方法来启动完整的充值流程
            if (window.paymentModule && typeof window.paymentModule.startRechargeProcess === 'function') {
                // 使用统一的充值流程
                window.paymentModule.startRechargeProcess();
            } else {
                console.warn('支付模块未加载，无法启动充值流程');
                showToast('支付系统暂不可用，请稍后再试', 'error');
            }
        }

        /**
         * 计算操作所需的积分（基于模型和操作类型的预估）
         * @param {string} operation - 操作类型
         * @param {string} model - 使用的AI模型
         * @returns {number} 所需积分数
         */
        calculateRequiredCredits(operation, model = 'zhipu_api') {
            return calculateRequiredCredits(operation, model);
        }

        /**
         * 从本地缓存获取积分，并验证是否属于当前用户
         * @returns {number|null} 缓存的积分或null
         */
        getCachedCredits() {
            try {
                // 检查用户ID
                const userData = JSON.parse(localStorage.getItem('userData') || '{}');
                const currentUserId = userData.id;
                
                // 读取缓存积分 - 尝试从新格式读取
                let cachedData = null;
                
                // 尝试读取格式 (包含 credits 字段的对象)
                let rawData = localStorage.getItem(STORAGE_KEYS.credits);
                if (rawData) {
                    try {
                        const parsed = JSON.parse(rawData);
                        if (parsed && typeof parsed.credits === 'number') {
                            cachedData = parsed;
                        }
                    } catch (e) {
                        console.log('解析缓存失败:', e);
                    }
                }
                
                // 如果没有找到缓存，检查是否有旧格式数据
                if (!cachedData) {
                    // 旧格式检查，进行整合
                    const oldKey = 'userCredits'; // 旧的存储键
                    const oldData = localStorage.getItem(oldKey);
                    
                    if (oldData) {
                        try {
                            const parsed = JSON.parse(oldData);
                            
                            // 将旧格式转换为新格式并保存
                            if (parsed) {
                                let credits = null;
                                
                                // 检查是对象还是直接数值
                                if (typeof parsed === 'object') {
                                    // 对象格式
                                    if (typeof parsed.amount === 'number') {
                                        credits = parsed.amount;
                                    } else if (typeof parsed.credits === 'number') {
                                        credits = parsed.credits;
                                    }
                                } else if (typeof parsed === 'number') {
                                    // 直接数值格式
                                    credits = parsed;
                                }
                                
                                if (credits !== null) {
                                    // 转换为新格式并保存
                                    this.cacheCredits(credits);
                                    cachedData = { credits: credits };
                                }
                            }
                        } catch (e) {
                            console.log('解析旧格式缓存失败:', e);
                        }
                    }
                }
                
                // 去除自动同步逻辑，使用预加载或手动触发的积分同步
                const finalCredits = cachedData ? cachedData.credits : null;
                return finalCredits;
            } catch (error) {
                console.error('获取缓存积分失败:', error);
                return null;
            }
        }

        /**
         * 将积分保存到本地缓存
         * @param {number} credits - 要缓存的积分
         */
        cacheCredits(credits) {
            try {
                // 确保传入的是有效数字
                if (typeof credits !== 'number' || isNaN(credits)) {
                    console.error('尝试缓存无效的积分值');
                    return;
                }
                
                // 获取当前用户ID
                const userData = JSON.parse(localStorage.getItem('userData') || '{}');
                const userId = userData.id || '';
                
                // 准备缓存数据
                console.log(`准备缓存积分数据: ${credits}`);
                
                // 创建新格式数据结构
                const data = {
                    credits: credits,
                    userId: userId,
                    timestamp: Date.now()
                };
                
                // 序列化并保存到主存储位置
                const jsonData = JSON.stringify(data);
                localStorage.setItem(STORAGE_KEYS.credits, jsonData);
                
                // 同时保存到旧格式位置以确保兼容性
                // 注意: 这里我们使用完整的新格式，避免创建旧格式
                localStorage.setItem('userCredits', jsonData);
                
                console.log(`积分已缓存到本地 (${credits})`);
            } catch (error) {
                console.error('缓存积分失败', error);
            }
        }
        
        /**
         * 显示认证错误消息
         */
        showAuthErrorMessage() {
            console.log('显示认证错误消息');
            const creditsDisplay = document.getElementById('userCredits');
            if (creditsDisplay) {
                creditsDisplay.innerHTML = '<span class="credits-error" title="请刷新页面或重新登录">!</span>';
                creditsDisplay.style.cursor = 'pointer';
                creditsDisplay.onclick = () => {
                    showToast('请刷新页面或重新登录', 'error');
                };
            }
            
            // 显示模态框，提示用户刷新页面或重新登录
            this.showAuthErrorModal();
        }

        /**
         * 显示认证错误模态框
         */
        showAuthErrorModal() {
            // 先移除可能存在的旧模态框
            const existingModal = document.getElementById('authErrorModal');
            if (existingModal) {
                document.body.removeChild(existingModal);
            }
            
            // 检查UI语言系统是否可用
            const hasUILanguage = window.UILanguage && typeof window.UILanguage.getText === 'function' && typeof window.UILanguage.getCurrentLanguage === 'function';
            
            // 获取当前语言
            let currentLang = 'zh'; // 默认中文
            
            if (hasUILanguage) {
                currentLang = window.UILanguage.getCurrentLanguage();
            } else {
                // 如果无法使用UILanguage对象，尝试其他方法获取语言
                try {
                    const storedLang = localStorage.getItem('uiLanguage');
                    if (storedLang && (storedLang === 'en' || storedLang === 'zh')) {
                        currentLang = storedLang;
                    } else if (navigator.language && !navigator.language.toLowerCase().startsWith('zh')) {
                        currentLang = 'en';
                    }
                } catch (e) {
                    console.error('获取语言设置失败', e);
                }
            }
            
            // 翻译文本
            const texts = {
                'zh': {
                    title: '登录状态异常',
                    message: '系统检测到您的登录状态异常，积分显示可能错误。',
                    options: '您可以选择：',
                    refresh: '刷新页面',
                    relogin: '重新登录',
                    close: '关闭'
                },
                'en': {
                    title: 'Authentication Error',
                    message: 'The system detected an issue with your login status. Your displayed credits may not be correct.',
                    options: 'You can choose to:',
                    refresh: 'Refresh Page',
                    relogin: 'Login Again',
                    close: 'Close'
                }
            };
            
            // 获取当前语言的文本，如果不存在则使用中文
            const t = texts[currentLang] || texts['zh'];
            
            // 创建模态框
            const modal = document.createElement('div');
            modal.id = 'authErrorModal';
            modal.className = 'modal-overlay';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
            `;
            
            // 创建模态框内容
            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content';
            modalContent.style.cssText = `
                background: white;
                padding: 25px;
                border-radius: 8px;
                width: 400px;
                max-width: 90%;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            `;
            
            // 标题
            const title = document.createElement('h2');
            title.textContent = t.title;
            title.style.cssText = `
                margin-top: 0;
                color: #e74c3c;
                font-size: 20px;
                text-align: center;
            `;
            
            // 图标
            const icon = document.createElement('div');
            icon.innerHTML = '<i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #e74c3c; margin-bottom: 15px;"></i>';
            icon.style.textAlign = 'center';
            
            // 消息
            const message = document.createElement('p');
            message.textContent = t.message;
            
            // 选项文本
            const optionsText = document.createElement('p');
            optionsText.textContent = t.options;
            
            // 按钮容器
            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = `
                display: flex;
                justify-content: center;
                gap: 10px;
                margin-top: 20px;
            `;
            
            // 刷新按钮
            const refreshButton = document.createElement('button');
            refreshButton.textContent = t.refresh;
            refreshButton.className = 'primary-button';
            refreshButton.style.cssText = `
                padding: 10px 20px;
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            `;
            refreshButton.onclick = () => {
                window.location.reload();
            };
            
            // 重新登录按钮
            const reloginButton = document.createElement('button');
            reloginButton.textContent = t.relogin;
            reloginButton.className = 'secondary-button';
            reloginButton.style.cssText = `
                padding: 10px 20px;
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            `;
            reloginButton.onclick = () => {
                // 清除认证相关的本地存储
                localStorage.removeItem('authToken');
                localStorage.removeItem('userData');
                localStorage.removeItem('isAuthenticated');
                localStorage.removeItem('userEmail');
                localStorage.removeItem('userId');
                
                // 跳转到登录页
                window.location.href = 'index.html';
            };
            
            // 关闭按钮
            const closeButton = document.createElement('button');
            closeButton.textContent = t.close;
            closeButton.className = 'close-button';
            closeButton.style.cssText = `
                padding: 10px 20px;
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            `;
            closeButton.onclick = () => {
                document.body.removeChild(modal);
            };
            
            // 添加按钮到容器
            buttonContainer.appendChild(refreshButton);
            buttonContainer.appendChild(reloginButton);
            buttonContainer.appendChild(closeButton);
            
            // 组装模态框内容
            modalContent.appendChild(title);
            modalContent.appendChild(icon);
            modalContent.appendChild(message);
            modalContent.appendChild(optionsText);
            modalContent.appendChild(buttonContainer);
            
            // 添加内容到模态框
            modal.appendChild(modalContent);
            
            // 添加模态框到页面
            document.body.appendChild(modal);
            
            // 点击模态框外部关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        }

        /**
         * 显示服务器错误消息
         */
        showServerErrorMessage() {
            console.log('显示服务器错误消息');
            const creditsDisplay = document.getElementById('userCredits');
            if (creditsDisplay) {
                creditsDisplay.innerHTML = '<span class="credits-error" title="获取积分失败，请刷新页面">!</span>';
                creditsDisplay.style.cursor = 'pointer';
                creditsDisplay.onclick = () => {
                    showToast('获取积分失败，请刷新页面', 'error');
                };
            }
            showToast('获取积分失败，请刷新页面重试', 'error');
        }

        /**
         * 解析JWT令牌并提取信息
         * @param {string} token - JWT令牌
         * @returns {Object|null} 令牌数据对象或null
         */
        parseJwtToken(token) {
            try {
                if (!token || typeof token !== 'string' || !token.includes('.')) {
                    return null;
                }
                
                // 分割JWT令牌并解码中间部分(payload)
                const parts = token.split('.');
                if (parts.length !== 3) {
                    return null;
                }
                
                // Base64解码令牌载荷
                const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));
                return payload;
            } catch (error) {
                console.error('解析JWT令牌失败:', error);
                return null;
            }
        }

        /**
         * 获取令牌过期时间
         * @param {string} token - JWT令牌
         * @returns {number} 过期时间戳(毫秒)或0
         */
        getTokenExpiryTime(token) {
            try {
                const payload = this.parseJwtToken(token);
                if (payload && payload.exp) {
                    // JWT过期时间是秒级时间戳，转换为毫秒
                    return payload.exp * 1000;
                }
                return 0;
            } catch (error) {
                console.error('获取令牌过期时间失败:', error);
                return 0;
            }
        }

        /**
         * 获取令牌的年龄（存在时间）
         * @param {string} token - JWT令牌
         * @returns {number|null} 令牌年龄（毫秒），如果无法计算则返回null
         */
        getTokenAge(token) {
            try {
                const payload = this.parseJwtToken(token);
                if (payload && payload.iat) {
                    // JWT签发时间是秒级时间戳，转换为毫秒
                    const issuedTime = payload.iat * 1000;
                    return Date.now() - issuedTime;
                }
                return null;
            } catch (error) {
                console.error('获取令牌年龄失败:', error);
                return null;
            }
        }

        /**
         * 计算下次令牌检查的时间间隔
         * @param {string} token - JWT令牌
         * @returns {number} 检查间隔(毫秒)
         */
        calculateTokenCheckInterval(token) {
            try {
                if (!token) {
                    // 如果没有令牌，使用较短的标准间隔(5分钟)
                    return 5 * 60 * 1000;
                }
                
                const expiryTime = this.getTokenExpiryTime(token);
                if (!expiryTime) {
                    // 无法获取过期时间，使用标准间隔(15分钟)
                    return 15 * 60 * 1000;
                }
                
                const now = Date.now();
                const timeUntilExpiry = expiryTime - now;
                
                // 如果令牌已过期，使用1分钟的间隔
                if (timeUntilExpiry <= 0) {
                    return 60 * 1000;
                }
                
                // 如果令牌有效期还很长(>30分钟)，安排在过期前10分钟检查
                if (timeUntilExpiry > 30 * 60 * 1000) {
                    return timeUntilExpiry - 10 * 60 * 1000;
                }
                
                // 如果令牌接近过期(>5分钟，<=30分钟)，安排在过期前5分钟检查
                if (timeUntilExpiry > 5 * 60 * 1000) {
                    return timeUntilExpiry - 5 * 60 * 1000;
                }
                
                // 如果令牌即将过期(<=5分钟)，安排在过期前1分钟检查
                if (timeUntilExpiry > 2 * 60 * 1000) {
                    return timeUntilExpiry - 60 * 1000;
                }
                
                // 如果令牌有效期极短，马上检查(30秒后)
                return 30 * 1000;
            } catch (error) {
                console.error('计算令牌检查间隔时出错:', error);
                // 出错时使用标准间隔(10分钟)
                return 10 * 60 * 1000;
            }
        }

        /**
         * 设置智能令牌检查
         */
        setupSmartTokenCheck() {
            // 清除可能存在的旧计时器
            if (this.tokenCheckTimeout) {
                clearTimeout(this.tokenCheckTimeout);
                this.tokenCheckTimeout = null;
            }
            
            try {
                // 获取当前令牌
                const authToken = localStorage.getItem('authToken');
                if (!authToken) {
                    console.log('无令牌可检查，跳过设置智能检查');
                    return;
                }
                
                // 计算下次检查间隔
                const checkInterval = this.calculateTokenCheckInterval(authToken);
                const expiryTime = this.getTokenExpiryTime(authToken);
                
                if (expiryTime) {
                    const expiryDate = new Date(expiryTime);
                    const now = new Date();
                    console.log(`令牌过期时间: ${expiryDate.toLocaleString()}, 当前时间: ${now.toLocaleString()}, 下次检查间隔: ${Math.round(checkInterval/1000/60)}分钟`);
                } else {
                    console.log(`无法获取令牌过期时间，使用默认检查间隔: ${Math.round(checkInterval/1000/60)}分钟`);
                }
                
                // 设置下次检查的定时器
                this.tokenCheckTimeout = setTimeout(async () => {
                    console.log('执行智能令牌检查...');
                    
                    try {
                        const isLoggedIn = isUserLoggedIn();
                        if (!isLoggedIn) {
                            console.log('用户未登录，跳过令牌检查');
                            return;
                        }
                        
                        // 尝试验证/刷新令牌
                        try {
                            await this.getValidToken();
                            console.log('令牌验证成功');
                            
                            // 检查是否有本地积分需要同步
                            if (localStorage.getItem('creditsNeedSync') === 'true') {
                                try {
                                    console.log('智能检查检测到本地积分需要同步，尝试同步');
                                    await this.syncLocalCreditsDeductions();
                                } catch (syncError) {
                                    console.warn('智能检查同步本地积分失败:', syncError);
                                }
                            }
                            
                            // 移除自动积分刷新 - 积分现在只在用户点击菜单时更新
                            // 不再自动刷新积分，避免不必要的API调用
                        } catch (tokenError) {
                            console.warn('令牌验证失败，尝试刷新:', tokenError);
                            // 令牌验证/刷新失败的处理已在getValidToken里完成
                        }
                    } catch (error) {
                        console.error('智能令牌检查出错:', error);
                    } finally {
                        // 重新设置下一次检查
                        // 获取新的令牌信息（可能已经刷新了）
                        const updatedToken = localStorage.getItem('authToken');
                        this.setupSmartTokenCheck();
                    }
                }, checkInterval);
                
            } catch (error) {
                console.error('设置智能令牌检查时出错:', error);
                // 出错时使用较短的间隔(5分钟)作为备份
                this.tokenCheckTimeout = setTimeout(() => this.setupSmartTokenCheck(), 5 * 60 * 1000);
            }
        }

        /**
         * 从后端缓存获取积分（菜单专用）
         * 这是最快最直接的方式，避免前端缓存不一致问题
         * @returns {Promise<number>} 返回积分数
         */
        async fetchCreditsFromBackendCache() {
            try {
                console.log('⚡ [后端缓存] 直接从后端缓存获取积分');
                const authToken = await this.getValidToken();
                
                // 使用快速API，仅从后端缓存获取
                const response = await fetch('/api/credits/get-fast', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                
                                    if (response.ok && data.success) {
                        console.log('✅ [后端缓存] 从后端缓存获取成功:', data.credits);
                        this.credits = data.credits;
                        // 同时更新前端缓存，保持一致性
                        this.cacheCredits(this.credits);
                        this.updateCreditsDisplay();
                        
                        // 触发积分更新事件
                        document.dispatchEvent(new CustomEvent('credits-updated', {
                            detail: { credits: this.credits }
                        }));
                        
                        return this.credits;
                    } else if (data && !data.success) {
                        // 快速API失败，回退到完整API
                        console.log('⚠️ [后端缓存] 快速API失败，回退到完整API:', data.error);
                        return await this.fetchCreditsFromServer(false);
                    } else {
                        throw new Error('快速API响应格式错误');
                    }
            } catch (error) {
                console.error('❌ [后端缓存] 从后端缓存获取积分失败:', error);
                // 失败时回退到完整API
                return await this.fetchCreditsFromServer(false);
            }
        }

        /**
         * 快速获取积分（优先使用缓存和快速API）
         * 用于菜单点击等需要快速响应的场景
         * @returns {Promise<number>} 返回积分数
         */
        async fetchCreditsQuickly() {
            try {
                // 1. 优先使用本地缓存
                const cachedCredits = this.getCachedCredits();
                if (cachedCredits !== null) {
                    console.log('快速获取积分：使用本地缓存', cachedCredits);
                    // 后台异步刷新缓存，不阻塞UI
                    setTimeout(() => {
                        this.fetchCreditsFromServer(false).catch(err => {
                            console.log('后台积分刷新失败:', err);
                        });
                    }, 100);
                    return cachedCredits;
                }
                
                // 2. 本地缓存不可用，尝试快速API
                console.log('快速获取积分：本地缓存不可用，尝试快速API');
                const authToken = await this.getValidToken();
                
                try {
                    const response = await fetch('/api/credits/get-fast', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok && data.success) {
                        console.log('快速获取积分：从快速API获取成功', data.credits, data.from_cache ? '(缓存)' : '(数据库)');
                        this.credits = data.credits;
                        this.cacheCredits(this.credits);
                        this.updateCreditsDisplay();
                        return this.credits;
                    } else if (response.status === 404 || (data && data.error === 'cache_miss')) {
                        // 缓存未命中，降级到完整API
                        console.log('快速获取积分：缓存未命中，降级到完整API');
                        return await this.fetchCreditsFromServer(false);
                    } else if (response.status === 500 || (data && data.error === 'database_error')) {
                        // 数据库错误，降级到完整API
                        console.log('快速获取积分：数据库错误，降级到完整API');
                        return await this.fetchCreditsFromServer(false);
                    } else {
                        // 其他错误，尝试完整API
                        console.log('快速获取积分：未知错误，尝试完整API:', data.error);
                        return await this.fetchCreditsFromServer(false);
                    }
                } catch (apiError) {
                    console.log('快速获取积分：网络错误，回退到完整API', apiError);
                    // 网络错误，回退到完整API
                    return await this.fetchCreditsFromServer(false);
                }
            } catch (error) {
                console.error('快速获取积分失败:', error);
                // 最后尝试完整API
                try {
                    return await this.fetchCreditsFromServer(false);
                } catch (finalError) {
                    console.error('所有积分获取方式都失败:', finalError);
                    return this.getCachedCredits() || 0;
                }
            }
        }

        /**
         * 新增：动态免检查时间管理
         * @param {number} durationMinutes - 免检查持续分钟数，默认60分钟
         */
        setAuxiliarySkipCheckTime(durationMinutes = 60) {
            try {
                const now = Date.now();
                
                // 检查是否已存在有效的免检查时间，避免重复设置
                const existingData = localStorage.getItem('auxiliarySkipCheck');
                if (existingData) {
                    try {
                        const parsed = JSON.parse(existingData);
                        const remainingMs = parsed.expiresAt - now;
                        if (remainingMs > 30 * 60 * 1000) { // 如果剩余时间超过30分钟
                            const remainingMinutes = Math.ceil(remainingMs / (60 * 1000));
                            console.log(`🕒 [动态免检查] 已存在有效免检查时间(剩余${remainingMinutes}分钟)，跳过重复设置`);
                            return true;
                        }
                    } catch (e) {
                        // 解析失败，清除无效数据
                        localStorage.removeItem('auxiliarySkipCheck');
                    }
                }
                
                const expiresAt = now + (durationMinutes * 60 * 1000);
                
                const skipCheckData = {
                    canSkip: true,
                    expiresAt: expiresAt,
                    durationMinutes: durationMinutes,
                    setAt: now
                };
                
                localStorage.setItem('auxiliarySkipCheck', JSON.stringify(skipCheckData));
                console.log(`🕒 [动态免检查] 设置${durationMinutes}分钟辅助功能免检查，到期时间: ${new Date(expiresAt).toLocaleString()}`);
                
                return true;
            } catch (error) {
                console.error('设置辅助功能免检查时间失败:', error);
                return false;
            }
        }

        /**
         * 新增：检查辅助功能是否可以跳过积分检查
         * @returns {object} 返回检查结果 {canSkip: boolean, remainingMinutes: number}
         */
        checkAuxiliarySkipStatus() {
            try {
                // 1. 检查用户积分是否 > 0
                const currentCredits = this.getCachedCredits() || 0;
                if (currentCredits <= 0) {
                    return {
                        canSkip: false,
                        remainingMinutes: 0,
                        reason: 'no_credits',
                        currentCredits: currentCredits
                    };
                }

                // 2. 检查免检查时间是否有效
                const skipCheckData = localStorage.getItem('auxiliarySkipCheck');
                if (!skipCheckData) {
                    return {
                        canSkip: false,
                        remainingMinutes: 0,
                        reason: 'no_skip_time_set',
                        currentCredits: currentCredits
                    };
                }

                try {
                    const parsed = JSON.parse(skipCheckData);
                    const now = Date.now();
                    
                    if (now >= parsed.expiresAt) {
                        // 免检查时间已过期
                        localStorage.removeItem('auxiliarySkipCheck');
                        return {
                            canSkip: false,
                            remainingMinutes: 0,
                            reason: 'skip_time_expired',
                            currentCredits: currentCredits
                        };
                    }

                    // 计算剩余时间
                    const remainingMs = parsed.expiresAt - now;
                    const remainingMinutes = Math.ceil(remainingMs / (60 * 1000));

                    return {
                        canSkip: true,
                        remainingMinutes: remainingMinutes,
                        reason: 'valid_skip_time',
                        currentCredits: currentCredits,
                        expiresAt: parsed.expiresAt
                    };

                } catch (parseError) {
                    console.error('解析免检查时间数据失败:', parseError);
                    localStorage.removeItem('auxiliarySkipCheck');
                    return {
                        canSkip: false,
                        remainingMinutes: 0,
                        reason: 'invalid_skip_data',
                        currentCredits: currentCredits
                    };
                }

            } catch (error) {
                console.error('检查辅助功能免检查状态失败:', error);
                return {
                    canSkip: false,
                    remainingMinutes: 0,
                    reason: 'error',
                    currentCredits: 0
                };
            }
        }

        /**
         * 新增：动态延长免检查时间（积分验证通过后调用）
         * @param {number} durationMinutes - 延长的分钟数，默认60分钟
         */
        extendAuxiliarySkipCheck(durationMinutes = 60) {
            try {
                const currentCredits = this.getCachedCredits() || 0;
                
                // 只有积分 > 0 时才能设置免检查
                if (currentCredits > 0) {
                    this.setAuxiliarySkipCheckTime(durationMinutes);
                    console.log(`✅ [动态延长] 用户积分${currentCredits} > 0，延长${durationMinutes}分钟免检查时间`);
                    return true;
                } else {
                    console.log(`❌ [动态延长] 用户积分${currentCredits} <= 0，不设置免检查时间`);
                    // 清除现有的免检查状态
                    localStorage.removeItem('auxiliarySkipCheck');
                    return false;
                }
            } catch (error) {
                console.error('延长辅助功能免检查时间失败:', error);
                return false;
            }
        }

        /**
         * 新增：谷歌登录时强制从数据库刷新积分
         */
        async forceRefreshCreditsOnLogin() {
            // 防重复调用机制
            if (this._loginRefreshInProgress) {
                console.log('🔄 [登录积分同步] 正在进行中，跳过重复调用');
                return;
            }
            
            this._loginRefreshInProgress = true;
            const refreshStartTime = Date.now();
            
            try {
                console.log('🔄 [登录积分同步] 开始强制刷新积分...');
                
                // 优先使用登录页预加载的积分
                const preCredits = localStorage.getItem('userCredits');
                if (preCredits !== null) {
                    try {
                        const creditsData = JSON.parse(preCredits);
                        this.credits = creditsData.credits || parseInt(preCredits, 10);
                        this.updateCreditsDisplay();
                        console.log(`🔄 [登录积分同步] 使用登录页预加载积分: ${this.credits}`);
                    } catch (e) {
                        // 如果是旧格式的字符串，直接解析
                        this.credits = parseInt(preCredits, 10);
                        this.updateCreditsDisplay();
                        console.log(`🔄 [登录积分同步] 使用登录页预加载积分(旧格式): ${this.credits}`);
                    }
                } else {
                    // 清除本地缓存
                    localStorage.removeItem('userCredits');
                    localStorage.removeItem('cachedCredits');
                    localStorage.removeItem('userCreditsCache');
                    console.log('🔄 [登录积分同步] 已清除本地积分缓存');
                    // 重新获取积分（数据库查询）
                    await this.fetchCreditsFromServer(true);
                    console.log('🔄 [登录积分同步] 积分重新获取完成');
                }
                
                // 检查积分并设置免检查时间（只在有积分时设置）
                const currentCredits = this.getCachedCredits() || 0;
                console.log(`🔄 [登录积分同步] 当前积分: ${currentCredits}`);
                
                if (currentCredits > 0) {
                    // 设置辅助功能免检查时间
                    this.setAuxiliarySkipCheckTime(60);
                    console.log('✅ [登录积分同步] 积分2907 > 0，设置60分钟辅助功能免检查');
                    
                    // 启动动态缓存管理（防重复启动）
                    if (!this.dynamicCacheManagementStarted) {
                        await this.startDynamicCacheManagement();
                        console.log('✅ [登录积分同步] 动态缓存管理已启动');
                    } else {
                        console.log('✅ [登录积分同步] 动态缓存管理已存在，跳过启动');
                    }
                } else {
                    console.log('⚠️ [登录积分同步] 积分为0，跳过免检查设置');
                }
                
                const refreshDuration = Date.now() - refreshStartTime;
                console.log(`✅ [登录积分同步] 完成，耗时: ${refreshDuration}ms`);
                
            } catch (error) {
                console.error('❌ [登录积分同步] 失败:', error);
            } finally {
                // 延迟重置标志，确保短时间内不会重复调用
                setTimeout(() => {
                    this._loginRefreshInProgress = false;
                }, 2000); // 2秒内防重复
            }
        }

        /**
         * 新增：获取后端配置
         */
        async loadAuxiliaryConfig() {
            try {
                const response = await fetch('/api/auxiliary-config');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        this.auxiliaryConfig = data.config;
                        console.log('📋 [辅助配置] 已加载后端配置:', data.config);
                        return data.config;
                    }
                }
                
                // 使用默认配置
                this.auxiliaryConfig = {
                    skip_check_minutes: 60,
                    force_credits_refresh_on_login: true
                };
                console.log('📋 [辅助配置] 使用默认配置:', this.auxiliaryConfig);
                return this.auxiliaryConfig;
                
            } catch (error) {
                console.error('加载辅助功能配置失败:', error);
                // 使用默认配置
                this.auxiliaryConfig = {
                    skip_check_minutes: 60,
                    force_credits_refresh_on_login: true
                };
                return this.auxiliaryConfig;
            }
        }

        /**
         * 新增：设置自动定期检查
         */
        setupAutoPeriodicCheck() {
            try {
                // 清除之前的定时器
                if (this.periodicCheckTimer) {
                    clearTimeout(this.periodicCheckTimer);
                }

                const config = this.auxiliaryConfig || { skip_check_minutes: 60 };
                const checkIntervalMs = config.skip_check_minutes * 60 * 1000; // 转换为毫秒

                this.periodicCheckTimer = setTimeout(async () => {
                    try {
                        // 检查用户是否仍然登录
                        if (!isUserLoggedIn()) {
                            console.log('🔍 [自动检查] 用户未登录，跳过定期检查');
                            return;
                        }

                        console.log(`🔍 [自动检查] 执行${config.skip_check_minutes}分钟定期积分检查...`);
                        
                        // 强制从数据库刷新积分
                        await this.fetchCreditsFromServer(true);
                        
                        // 根据积分情况决定是否继续免检查
                        const currentCredits = this.getCachedCredits() || 0;
                        if (currentCredits > 0) {
                            // 继续设置免检查时间
                            this.setAuxiliarySkipCheckTime(config.skip_check_minutes);
                            console.log(`✅ [自动检查] 积分${currentCredits} > 0，继续设置${config.skip_check_minutes}分钟免检查`);
                            
                            // 设置下一次检查
                            this.setupAutoPeriodicCheck();
                        } else {
                            console.log(`❌ [自动检查] 积分${currentCredits} <= 0，停止自动免检查`);
                            // 清除免检查状态
                            localStorage.removeItem('auxiliarySkipCheck');
                        }
                        
                    } catch (error) {
                        console.error('自动定期检查失败:', error);
                        // 发生错误时也要重新设置定时器
                        this.setupAutoPeriodicCheck();
                    }
                }, checkIntervalMs);

                console.log(`🕒 [自动检查] 已设置${config.skip_check_minutes}分钟后自动检查积分`);

            } catch (error) {
                console.error('设置自动定期检查失败:', error);
            }
        }

        /**
         * 新增：启动动态缓存管理（在用户登录后调用）
         */
        async startDynamicCacheManagement() {
            try {
                // 防止重复启动
                if (this.dynamicCacheManagementStarted) {
                    console.log('🚀 [动态缓存] 动态缓存管理已经启动，跳过重复启动');
                    return true;
                }
                
                console.log('🚀 [动态缓存] 启动动态缓存时间管理机制...');
                
                // 1. 加载配置
                await this.loadAuxiliaryConfig();
                console.log('🚀 [动态缓存] 配置加载完成:', this.auxiliaryConfig);
                
                // 2. 检查当前积分状态
                const currentCredits = this.getCachedCredits() || 0;
                console.log(`🚀 [动态缓存] 当前积分状态: ${currentCredits}`);
                
                if (currentCredits > 0) {
                    // 3. 设置初始免检查时间
                    const duration = this.auxiliaryConfig?.skip_check_minutes || 60;
                    this.setAuxiliarySkipCheckTime(duration);
                    console.log(`🚀 [动态缓存] 设置${duration}分钟免检查时间`);
                    
                    // 4. 启动自动定期检查
                    this.setupAutoPeriodicCheck();
                    console.log(`🚀 [动态缓存] 自动定期检查已设置`);
                    
                    console.log(`✅ [动态缓存] 动态缓存管理已启动，免检查时间: ${duration}分钟`);
                } else {
                    console.log(`ℹ️ [动态缓存] 积分${currentCredits} <= 0，暂不启动免检查机制`);
                }
                
                // 标记为已启动
                this.dynamicCacheManagementStarted = true;
                return true;
            } catch (error) {
                console.error('启动动态缓存管理失败:', error);
                return false;
            }
        }

        // 新增：检查前后端用户ID一致性
        async checkUserIdConsistency() {
            try {
                // 获取前端用户ID
                const frontendUserId = this.getUserId();
                if (!frontendUserId) {
                    console.log('🔍 [ID一致性] 前端无用户ID，跳过检查');
                    return true;
                }
                
                // 获取后端用户ID（通过令牌验证接口）
                const authToken = await this.getValidToken();
                if (!authToken) {
                    console.log('🔍 [ID一致性] 无有效令牌，跳过检查');
                    return true;
                }
                
                const response = await fetch('/api/auth/verify', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                if (!response.ok) {
                    console.log('🔍 [ID一致性] 后端验证失败，跳过检查');
                    return true;
                }
                
                const data = await response.json();
                const backendUserId = data.user_id;
                
                console.log(`🔍 [ID一致性] 前端用户ID: ${frontendUserId}`);
                console.log(`🔍 [ID一致性] 后端用户ID: ${backendUserId}`);
                
                if (frontendUserId === backendUserId) {
                    console.log('✅ [ID一致性] 前后端用户ID一致');
                    return true;
                } else {
                    console.warn('⚠️ [ID一致性] 前后端用户ID不一致！');
                    return false;
                }
            } catch (error) {
                console.error('❌ [ID一致性] 检查失败:', error);
                return true; // 检查失败时不阻止后续流程
            }
        }
        
        // 新增：修复用户ID不一致问题
        async fixUserIdInconsistency() {
            try {
                console.log('🔧 [ID修复] 开始修复前后端用户ID不一致...');
                
                // 获取当前前端认证状态
                if (!window.UnifiedAuthManager) {
                    console.error('❌ [ID修复] 统一认证管理器不可用');
                    return false;
                }
                
                const authState = window.UnifiedAuthManager.getAuthState();
                if (!authState.isAuthenticated || !authState.session) {
                    console.error('❌ [ID修复] 无有效前端认证状态');
                    return false;
                }
                
                const session = authState.session;
                const accessToken = session.access_token;
                const userId = session.user.id;
                
                console.log(`🔧 [ID修复] 使用前端session重新设置后端cookie...`);
                console.log(`🔧 [ID修复] 用户ID: ${userId}`);
                
                // 重新设置后端session
                const response = await fetch('/api/auth/set-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        token: accessToken,
                        userId: userId,
                        expires: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
                    }),
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    console.error('❌ [ID修复] 重新设置session失败');
                    return false;
                }
                
                console.log('✅ [ID修复] 后端session已重新设置');
                
                // 等待500ms让cookie生效
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // 再次验证一致性
                const isConsistent = await this.checkUserIdConsistency();
                if (isConsistent) {
                    console.log('✅ [ID修复] 修复成功，前后端用户ID现在一致');
                    return true;
                } else {
                    console.error('❌ [ID修复] 修复失败，用户ID仍不一致');
                    return false;
                }
                
            } catch (error) {
                console.error('❌ [ID修复] 修复过程出错:', error);
                return false;
            }
        }

        /**
         * 获取当前前端用户ID
         * @returns {string|null}
         */
        getUserId() {
            // 优先使用统一认证管理器
            if (window.UnifiedAuthManager && typeof window.UnifiedAuthManager.getCurrentUser === 'function') {
                const user = window.UnifiedAuthManager.getCurrentUser();
                return user && user.id ? user.id : null;
            }
            // 备用：从localStorage获取
            try {
                const userData = JSON.parse(localStorage.getItem('userData') || '{}');
                return userData.id || null;
            } catch (e) {
                console.error('获取前端用户ID失败:', e);
                return null;
            }
        }
    }

    // 创建单例实例
    const creditsManagerInstance = new CreditsManager();

    // 向全局暴露API
    window.CreditsManager = creditsManagerInstance;
    window.calculateCreditsFromTokenUsage = calculateCreditsFromTokenUsage;
    window.calculateRequiredCredits = calculateRequiredCredits;

    // 标记全局单例已初始化: 手动初始化已禁用，使用 init() 自动触发
    // window.CreditsManager._initialized = true;

    // 当DOM加载完成后初始化积分系统
    document.addEventListener('DOMContentLoaded', function initCreditsOnceHandler() {
        // 延迟初始化以确保auth模块已经初始化
        setTimeout(() => {
            if (!window.CreditsManager.isInitialized && !window.CreditsManager.initializing) {
                creditsManagerInstance.init().catch(error => {
                    console.error('初始化积分系统失败:', error);
                });
            } else {
                console.log('积分系统已初始化或正在初始化中，跳过重复初始化');
            }
        }, 500);
        
        // 移除事件监听器，确保只执行一次
        document.removeEventListener('DOMContentLoaded', initCreditsOnceHandler);
    });

    // 在页面加载完成后设置充值按钮和模型选择监听
    document.addEventListener('DOMContentLoaded', function setupButtonsOnceHandler() {
        // 延迟执行以确保其他元素已加载
        setTimeout(() => {
            setupRechargeButton();
            setupModelSelectionListener();
        }, 1000);
        
        // 移除事件监听器，确保只执行一次
        document.removeEventListener('DOMContentLoaded', setupButtonsOnceHandler);
    });

    // 提供全局getCredits函数
    window.getCredits = function() {
        return window.CreditsManager ? window.CreditsManager.getCredits() : 0;
    };

    // 提供全局checkCredits函数 - 优化版：优先使用缓存，减少数据库访问
    window.checkCredits = async function(operation, modelId) {
        // 如果积分管理器不可用，默认返回true允许操作继续
        if (!window.CreditsManager) {
            console.warn('积分管理器未初始化，默认允许操作');
            return true;
        }
        
        try {
            // 1. 优先使用本地缓存进行快速检查
            const cachedCredits = window.CreditsManager.getCachedCredits();
            const requiredCredits = await calculateRequiredCreditsLocal(operation, modelId);
            
            console.log(`智能积分检查: 缓存积分=${cachedCredits}, 需要积分=${requiredCredits}, 操作=${operation}`);
            
            // 2. 如果缓存积分明显足够（有足够余量），直接通过
            if (cachedCredits !== null && cachedCredits >= (requiredCredits + 10)) {
                console.log('缓存积分充足，跳过后端检查');
                return true;
            }
            
            // 3. 如果缓存积分不足或为0，尝试后端快速API
            if (cachedCredits === null || cachedCredits < requiredCredits) {
                console.log('缓存积分不足或为null，尝试后端快速API');
                try {
                    // 尝试使用快速API获取积分
                    const fastApiCredits = await window.CreditsManager.fetchCreditsFromBackendCache();
                    if (fastApiCredits >= requiredCredits) {
                        console.log('后端快速API确认积分充足');
                        return true;
                    } else {
                        // 后端快速API确认积分不足
                        console.warn(`后端快速API确认积分不足: 当前=${fastApiCredits}, 需要=${requiredCredits}`);
                        if (typeof window.CreditsManager.showInsufficientCreditsAlert === 'function') {
                            window.CreditsManager.showInsufficientCreditsAlert(fastApiCredits);
                        } else {
                            const message = window.UILanguage ? 
                                window.UILanguage.getText('main.error.insufficient_credits') : 
                                '积分不足，请先充值';
                            alert(message);
                        }
                        return false;
                    }
                } catch (fastApiError) {
                    console.warn('快速API检查失败，将继续使用完整API:', fastApiError);
                    // 如果快速API失败但缓存积分明确不足，直接弹框
                    if (cachedCredits !== null && cachedCredits < requiredCredits) {
                        if (typeof window.CreditsManager.showInsufficientCreditsAlert === 'function') {
                            window.CreditsManager.showInsufficientCreditsAlert(cachedCredits);
                        } else {
                            const message = window.UILanguage ? 
                                window.UILanguage.getText('main.error.insufficient_credits') : 
                                '积分不足，请先充值';
                            alert(message);
                        }
                        return false;
                    }
                }
            }
            
            // 4. 如果快速API失败或积分仍不足，使用完整API
            console.log('使用完整API进行最终确认');
            const response = await fetch('/api/credits/check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${await window.CreditsManager.getValidToken()}`
                },
                body: JSON.stringify({
                    operation: operation,
                    model: modelId
                })
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                console.log(`完整API确认积分充足: 当前=${data.current_credits}, 需要=${data.required_credits}`);
                // 更新本地缓存
                window.CreditsManager.cacheCredits(data.current_credits);
                return true;
            } else {
                console.warn(`积分确实不足(最终确认): 当前=${data.current_credits}, 需要=${data.required_credits}`);
                // 更新本地缓存
                if (data.current_credits !== undefined) {
                    window.CreditsManager.cacheCredits(data.current_credits);
                }
                
                // 显示积分不足提示，传递正确的积分值
                if (typeof window.CreditsManager.showInsufficientCreditsAlert === 'function') {
                    const creditsToShow = data.current_credits !== undefined ? data.current_credits : (cachedCredits !== null ? cachedCredits : 0);
                    window.CreditsManager.showInsufficientCreditsAlert(creditsToShow);
                } else {
                    const message = window.UILanguage ? 
                        window.UILanguage.getText('main.error.insufficient_credits') : 
                        '积分不足，请先充值';
                    alert(message);
                }
                return false;
            }
        } catch (error) {
            console.error('检查积分时出错:', error);
            // 出错时返回true，避免积分系统故障影响用户体验
            return true;
        }
    };
    
    // 本地积分计算函数 - 避免频繁调用后端
    async function calculateRequiredCreditsLocal(operation, modelId) {
        // 基础积分需求映射
        const baseCreditsMap = {
            'audio_analysis': 1,
            'smart_qa': 1,
            'summary': 1,
            'mindmap': 1,
            'knowledge_graph': 1,
            'continue_conversation': 1,
            'subtitle_chat': 5,  // 字幕问答积分
            'grammar_analysis': 2  // 语法分析积分
        };
        
        return baseCreditsMap[operation] || 1;
    }

})();

/**
 * 显示提示信息
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型 (success, error, warning, info)
 * @param {number} duration - 显示时长(毫秒)
 */
function showToast(message, type = 'info', duration = 3000) {
    // 检查是否已有toast容器
    let toastContainer = document.getElementById('toast-container');
    
    // 如果没有，创建一个
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        `;
        document.body.appendChild(toastContainer);
    }
    
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 设置样式
    toast.style.cssText = `
        padding: 12px 20px;
        margin-bottom: 10px;
        border-radius: 4px;
        color: white;
        font-size: 14px;
        min-width: 250px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        animation: fadeIn 0.3s, fadeOut 0.3s ${duration/1000 - 0.3}s forwards;
        cursor: pointer;
    `;
    
    // 根据类型设置背景色
    switch(type) {
        case 'success':
            toast.style.backgroundColor = '#4CAF50';
            break;
        case 'error':
            toast.style.backgroundColor = '#F44336';
            break;
        case 'warning':
            toast.style.backgroundColor = '#FF9800';
            break;
        default:
            toast.style.backgroundColor = '#2196F3';
    }
    
    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-20px); }
        }
    `;
    document.head.appendChild(style);
    
    // 点击关闭
    toast.addEventListener('click', () => {
        toast.style.animation = 'fadeOut 0.3s forwards';
        setTimeout(() => {
            toastContainer.removeChild(toast);
        }, 300);
    });
    
    // 添加到容器
    toastContainer.appendChild(toast);
    
    // 自动移除
    setTimeout(() => {
        if (toastContainer.contains(toast)) {
            toastContainer.removeChild(toast);
        }
    }, duration);
}
