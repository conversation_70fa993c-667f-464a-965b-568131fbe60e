module.exports = {
  // 高强度混淆配置 - 用于认证、支付、OAuth等关键业务文件
  highSecurity: {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 0.8,
    deadCodeInjection: true,
    deadCodeInjectionThreshold: 0.6,
    stringArray: true,
    stringArrayEncoding: ['rc4'],
    stringArrayThreshold: 0.9,
    selfDefending: true,
    transformObjectKeys: true,
    splitStrings: true,
    splitStringsChunkLength: 8,
    numbersToExpressions: true,
    renameGlobals: false,
    disableConsoleOutput: true,
    sourceMap: false,
    target: 'browser',
    identifierNamesGenerator: 'hexadecimal',
    seed: Math.random() * 10000000
  },

  // 中等强度混淆配置 - 用于核心业务逻辑文件
  mediumSecurity: {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 0.6,
    deadCodeInjection: true,
    deadCodeInjectionThreshold: 0.4,
    stringArray: true,
    stringArrayEncoding: ['base64', 'rc4'],
    stringArrayThreshold: 0.8,
    selfDefending: true,
    splitStrings: true,
    splitStringsChunkLength: 10,
    numbersToExpressions: true,
    disableConsoleOutput: true,
    sourceMap: false,
    target: 'browser',
    identifierNamesGenerator: 'hexadecimal',
    seed: Math.random() * 10000000
  },

  // 轻度混淆配置 - 用于UI交互和性能敏感文件
  lightSecurity: {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 0.4,
    deadCodeInjection: true,
    deadCodeInjectionThreshold: 0.2,
    stringArray: true,
    stringArrayEncoding: ['base64'],
    stringArrayThreshold: 0.6,
    selfDefending: false,
    numbersToExpressions: false,
    disableConsoleOutput: true,
    sourceMap: false,
    target: 'browser',
    identifierNamesGenerator: 'hexadecimal',
    seed: Math.random() * 10000000
  },

  // 最轻度混淆配置 - 用于语言文件和配置文件
  minimalSecurity: {
    compact: true,
    controlFlowFlattening: false,
    deadCodeInjection: false,
    stringArray: true,
    stringArrayEncoding: ['base64'],
    stringArrayThreshold: 0.4,
    selfDefending: false,
    disableConsoleOutput: true,
    sourceMap: false,
    target: 'browser',
    identifierNamesGenerator: 'hexadecimal',
    seed: Math.random() * 10000000
  },
  
  // 标识符混淆
  identifiersPrefix: '',     // 可设置前缀，默认为空
  renameGlobals: false,      // 不重命名全局变量
  
  // 字符串混淆
  stringArray: true,         // 启用字符串数组
  stringArrayEncoding: ['base64'], // 使用base64编码字符串
  stringArrayThreshold: 0.75, // 字符串数组的阈值
  
  // 转换
  numbersToExpressions: true, // 将常量转换为表达式
  simplify: true,            // 简化代码
  splitStrings: true,        // 拆分字符串
  splitStringsChunkLength: 10, // 拆分字符串的块长度
  
  // 禁用的特性 - 为了保持功能性
  disableConsoleOutput: true, // 禁用控制台输出
  
  // 混淆源映射
  sourceMap: false,          // 不生成源映射
  sourceMapMode: 'separate',
  
  // 目标环境
  target: 'browser',        // 目标为浏览器环境
  
  // 保留关键函数名和全局变量
  reservedNames: [
    'UILanguage',            // 保留UI语言相关函数
    'loadLanguage',
    'translatePage',
    'getText',
    'getCurrentLanguage',
    'showToast',             // 保留通知函数
    'ENV',                   // 保留环境配置
    'CreditsManager',        // 保留积分管理相关函数
    'getCurrentCredits',
    'updateCreditsDisplay',
    'CreditHistoryManager',  // 保留积分历史管理器类名
    'showModal',             // 保留积分历史模态框方法
    'getSupabaseClient',     // 保留Supabase客户端获取函数
    'supabaseClient',        // 保留Supabase客户端
    'supabaseClientInstance', // 保留Supabase客户端实例
    'onCaptchaVerified',     // 保留验证码回调
    'onLoginCaptchaVerified',
    'onResetCaptchaVerified',
    'onTurnstileError',
    'marked',                // 保留外部库引用
    'mermaid',
    'turnstile',
    'supabase',
    'd3',
    'initializeGraph',      // 保留知识图谱相关函数
    'updateGraph',
    'addNode',
    'addLink',
    'handleOAuthRedirect',   // 保留OAuth相关函数
    'processOAuthSession',
    'initializeModelSelector', // 保留模型选择器相关函数
    'getSelectedModel',
    'initializePayment',     // 保留支付相关函数
    'handlePaymentRedirect'
  ],
  
  // 排除的域名和正则表达式
  domainLock: [],            // 不锁定域名
  
  // 调试选项
  debug: false,              // 关闭调试
  
  // 自我防护
  selfDefending: true,      // 启用自我防护
  
  // 种子
  seed: Math.random() * 10000000 // 使用随机种子
};