// 文本问答功能管理器
class TextChatManager {
    constructor() {
        this.modal = null;
        this.currentContext = null;
        this.selectedText = null;
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        this.createModal();
        this.bindEvents();
    }
    
    createModal() {
        if (document.getElementById('textChatModal')) {
            this.modal = document.getElementById('textChatModal');
            return;
        }
        
        const titleText = this.getLocalizedText('text_chat.title');
        const placeholderText = this.getLocalizedText('text_chat.placeholder');
        const sendText = this.getLocalizedText('text_chat.send');
        
        const modalHTML = `
            <div id="textChatModal" class="text-chat-modal">
                <div class="text-chat-content">
                    <div class="text-chat-header">
                        <h3 class="text-chat-title">${titleText}</h3>
                        <button class="text-chat-close">&times;</button>
                    </div>
                    <div class="text-chat-body">
                        <div class="text-chat-messages" id="textChatMessages"></div>
                        <div class="text-chat-input">
                            <div class="chat-input-container">
                                <textarea id="textChatInput" class="chat-input" placeholder="${placeholderText}" rows="1"></textarea>
                                <button id="textChatSendBtn" class="chat-send-btn">${sendText}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = document.getElementById('textChatModal');
    }
    
    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('text-chat-close')) {
                this.closeModal();
            }
            if (e.target.id === 'textChatModal') {
                this.closeModal();
            }
            if (e.target.id === 'textChatSendBtn') {
                this.handleSendMessage();
            }
        });
        
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal && this.modal.style.display === 'flex') {
                this.closeModal();
            }
            if (e.target.id === 'textChatInput' && e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.handleSendMessage();
            }
        });
    }
    
    async openModal(context, selectedText = null) {
        if (!context) {
            console.error('TextChatManager: 缺少上下文内容');
            return;
        }
        
        this.currentContext = context;
        this.selectedText = selectedText;
        
        const messagesContainer = document.getElementById('textChatMessages');
        messagesContainer.innerHTML = '';
        
        const chatInput = document.getElementById('textChatInput');
        chatInput.value = '';
        
        this.modal.style.display = 'flex';
        setTimeout(() => this.modal.classList.add('show'), 10);
        
        setTimeout(() => chatInput.focus(), 300);
        
        if (selectedText && selectedText.trim()) {
            const defaultQuestion = this.getLocalizedText('text_chat.explain_selected').replace('{text}', selectedText);
            await this.sendMessage(defaultQuestion, context, true);
        }
    }
    
    closeModal() {
        this.modal.classList.remove('show');
        setTimeout(() => this.modal.style.display = 'none', 300);
        this.isLoading = false;
        this.updateSendButton();
    }
    
    async handleSendMessage() {
        const chatInput = document.getElementById('textChatInput');
        const message = chatInput.value.trim();
        
        if (!message || this.isLoading) return;
        
        this.isLoading = true;
        this.updateSendButton();
        
        try {
            chatInput.value = '';
            await this.sendMessage(message, this.currentContext);
        } catch (error) {
            this.isLoading = false;
            this.updateSendButton();
        }
    }
    
    async sendMessage(message, context, isAutomatic = false) {
        const hasCredits = await this.checkCredits();
        if (!hasCredits) {
            this.isLoading = false;
            this.updateSendButton();
            // 已通过 checkCredits 弹出充值模态框，直接返回
            return;
        }
        
        if (!isAutomatic) {
            this.addMessage(message, 'user');
        }
        
        const loadingId = this.addLoadingMessage();
        
        try {
            if (isAutomatic) {
                this.isLoading = true;
                this.updateSendButton();
            }
            
            const fullPrompt = this.buildPrompt(message, context);
            await this.callAPI(fullPrompt, loadingId);
            
        } catch (error) {
            console.error('发送消息失败:', error);
            this.removeMessage(loadingId);
            this.showError(this.getLocalizedText('text_chat.error_api_failed'));
        } finally {
            this.isLoading = false;
            this.updateSendButton();
        }
    }
    
    buildPrompt(userQuestion, context) {
        const outputLanguage = this.getOutputLanguage();
        
        let systemPrompt;
        if (this.selectedText && this.selectedText.trim()) {
            systemPrompt = `你是一个专业的文本分析助手。用户选中了文本片段"${this.selectedText}"，请基于提供的上下文内容分析和回答用户的问题。使用${outputLanguage}回答。\n\n上下文内容：\n${context}\n\n用户选中的文本："${this.selectedText}"`;
        } else {
            systemPrompt = `你是一个专业的文本分析助手。请基于提供的上下文内容回答用户的问题。使用${outputLanguage}回答。\n\n上下文内容：\n${context}`;
        }
        
        return systemPrompt + '\n\n用户问题：' + userQuestion;
    }
    
    async callAPI(prompt, loadingId) {
        const response = await fetch('/api/generate-stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify({
                user_question: this.extractUserQuestion(prompt),
                context_content: this.currentContext,
                operation: 'text_chat',
                model: this.getCurrentModel(),
                temperature: 0.7,
                selected_text: this.selectedText,
                book_name: this.getBookName(),
                chapter_title: this.getChapterTitle()
            })
        });
        
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status}`);
        }
        
        this.removeMessage(loadingId);
        const aiMessageId = this.addMessage('', 'ai');
        await this.handleStreamResponse(response, aiMessageId);
    }
    
    async handleStreamResponse(response, messageId) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let accumulatedText = '';
        
        try {
            while (true) {
                const { value, done } = await reader.read();
                if (done) break;
                
                accumulatedText += decoder.decode(value, { stream: true });
                const displayText = this.cleanResponseText(accumulatedText);
                this.updateMessageContent(messageId, displayText);
                this.scrollToBottom();
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('处理流式响应失败:', error);
                this.updateMessageContent(messageId, this.getLocalizedText('text_chat.error_api_failed'));
            }
        }
    }
    
    cleanResponseText(rawText) {
        return rawText
            .replace(/<!-- CREDITS_INFO: .+? -->/g, '')
            .replace(/<!-- HIDDEN_TOKEN_DATA: .+? -->/g, '')
            .replace(/<!-- HIDDEN_ERROR_DATA: .+? -->/g, '')
            .replace(/<!-- CONNECTION_INTERRUPTED_AFTER_STREAM -->/g, '')
            .replace(/<!-- CREDITS_SKIP:.+?-->/g, '')
            .trim();
    }
    
    addMessage(content, type) {
        const messagesContainer = document.getElementById('textChatMessages');
        const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        const messageEl = document.createElement('div');
        messageEl.className = `chat-message message-${type}`;
        messageEl.id = messageId;
        
        const contentEl = document.createElement('div');
        contentEl.className = 'message-content';
        contentEl.textContent = content;
        
        messageEl.appendChild(contentEl);
        messagesContainer.appendChild(messageEl);
        this.scrollToBottom();
        
        return messageId;
    }
    
    addLoadingMessage() {
        const messagesContainer = document.getElementById('textChatMessages');
        const messageId = 'loading_' + Date.now();
        
        const messageEl = document.createElement('div');
        messageEl.className = 'chat-message message-ai loading-message';
        messageEl.id = messageId;
        
        const loadingText = this.getLocalizedText('text_chat.loading');
        const contentEl = document.createElement('div');
        contentEl.className = 'message-content';
        contentEl.innerHTML = `
            <div class="loading-indicator">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span class="loading-text">${loadingText}</span>
            </div>
        `;
        
        messageEl.appendChild(contentEl);
        messagesContainer.appendChild(messageEl);
        this.scrollToBottom();
        
        return messageId;
    }
    
    removeMessage(messageId) {
        const messageEl = document.getElementById(messageId);
        if (messageEl) messageEl.remove();
    }
    
    updateMessageContent(messageId, content) {
        const messageEl = document.getElementById(messageId);
        if (messageEl) {
            const contentEl = messageEl.querySelector('.message-content');
            if (contentEl) contentEl.textContent = content;
        }
    }
    
    scrollToBottom() {
        const messagesContainer = document.getElementById('textChatMessages');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }
    
    updateSendButton() {
        const sendBtn = document.getElementById('textChatSendBtn');
        if (sendBtn) {
            sendBtn.disabled = this.isLoading;
            sendBtn.textContent = this.isLoading ? 
                this.getLocalizedText('text_chat.loading') :
                this.getLocalizedText('text_chat.send');
        }
    }
    
    showError(message) {
        this.addMessage(message, 'ai');
    }
    
    async checkCredits() {
        const currentModel = this.getCurrentModel();
        
        // 简化的积分检查：只进行基本验证，详细检查在后端进行
        if (!window.checkCredits || typeof window.checkCredits !== 'function') {
            console.warn('积分检查函数不可用');
            return true;
        }
        
        try {
            // 使用简单的积分检查，详细检查在后端API中进行
            return await window.checkCredits('text_chat', currentModel);
        } catch (error) {
            console.error('检查积分时出错:', error);
            // 出错时允许操作，让后端进行最终判断
            return true;
        }
    }
    
    getCurrentModel() {
        return localStorage.getItem('selectedModel') || 'zhipu_flash';
    }
    
    getOutputLanguage() {
        if (window.LanguageSettings && window.LanguageSettings.getCurrentLanguage) {
            return window.LanguageSettings.getCurrentLanguage() === 'zh' ? '中文' : 'English';
        }
        return (localStorage.getItem('currentLanguage') || 'zh') === 'zh' ? '中文' : 'English';
    }
    
    getLocalizedText(key) {
        const texts = {
            zh: {
                'text_chat.title': '文本问答',
                'text_chat.placeholder': '请输入您的问题...',
                'text_chat.send': '发送',
                'text_chat.loading': '正在思考中...',
                'text_chat.explain_selected': '请解释选中的文本"{text}"在当前上下文中的含义和作用',
                'text_chat.error_api_failed': 'API调用失败，请重试',
                'text_chat.error_credits_insufficient': '积分不足，无法继续问答'
            },
            en: {
                'text_chat.title': 'Text Q&A',
                'text_chat.placeholder': 'Enter your question...',
                'text_chat.send': 'Send',
                'text_chat.loading': 'Loading...',
                'text_chat.explain_selected': 'Please explain the meaning and role of the selected text "{text}" in the current context',
                'text_chat.error_api_failed': 'API call failed, please try again',
                'text_chat.error_credits_insufficient': 'Insufficient credits to continue Q&A'
            }
        };
        
        const currentLang = (window.UILanguage && window.UILanguage.getCurrentLanguage) ? 
            window.UILanguage.getCurrentLanguage() : 'zh';
        
        return texts[currentLang]?.[key] || texts.zh[key] || key;
    }
    
    extractUserQuestion(prompt) {
        // 从完整的prompt中提取用户问题
        // 查找"用户问题："后的内容
        const userQuestionMatch = prompt.match(/用户问题：(.+)$/);
        if (userQuestionMatch) {
            return userQuestionMatch[1].trim();
        }
        
        // 如果没有找到标准格式，返回整个prompt（可能是直接的用户输入）
        return prompt;
    }
    
    getBookName() {
        // 获取书名信息
        return window.currentBookName || localStorage.getItem('currentBookName') || '';
    }
    
    getChapterTitle() {
        // 获取当前章节标题
        return window.currentChapterTitle || '';
    }
}

// 初始化
window.TextChatManager = new TextChatManager();

// 全局函数
window.openTextChat = function(context, selectedText = null) {
    return window.TextChatManager.openModal(context, selectedText);
};

window.getSelectedText = function() {
    if (window.getSelection) {
        return window.getSelection().toString();
    } else if (document.selection) {
        return document.selection.createRange().text;
    }
    return '';
}; 