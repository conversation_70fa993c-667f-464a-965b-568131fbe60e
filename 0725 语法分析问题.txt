我的这个项目可以选择字幕行句子进行问答

使用了app.py中的下面api端点

# 字幕问答API端点
@app.route('/api/subtitle-chat-stream', methods=['POST'])
def subtitle_chat_stream():
    """
    字幕问答流式响应API
    基于上下文（上一句+当前句+下一句）进行翻译和语法分析
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据无效"}), 400
        
        # 获取请求参数
        current_subtitle = data.get('current_subtitle', '')
        previous_subtitle = data.get('previous_subtitle', '')
        next_subtitle = data.get('next_subtitle', '')
        user_question = data.get('user_question', '')
        model_id = data.get('model', 'glm-4-flash')
        temperature = data.get('temperature', 0.3)
        
        if not current_subtitle:
            return jsonify({"error": "缺少当前字幕内容"}), 400
        
        # 获取用户信息 - 简化版本，参考问答参考的实现
        auth_header = request.headers.get('Authorization')
        user_id = None
        if auth_header and auth_header.startswith('Bearer '):
            try:
                token = auth_header.split(' ')[1]
                # 使用supabase验证token并获取真实用户ID
                from api.credits import supabase_client
                user_response = supabase_client.auth.get_user(token)
                if user_response.user:
                    user_id = user_response.user.id
                    logger.info(f"字幕问答验证用户成功: {user_id}")
                else:
                    logger.warning(f"字幕问答用户验证失败")
                    return jsonify({'error': '需要登录才能使用此功能'}), 401
            except Exception as e:
                logger.error(f"字幕问答获取用户ID失败: {str(e)}")
                return jsonify({'error': '用户认证失败'}), 401
        else:
            return jsonify({'error': '需要登录才能使用此功能'}), 401
        
        # 构建上下文
        context = []
        if previous_subtitle:
            context.append(f"上一句: {previous_subtitle}")
        context.append(f"当前句: {current_subtitle}")
        if next_subtitle:
            context.append(f"下一句: {next_subtitle}")
        
        context_text = '\n'.join(context)
        
        # 构建系统提示 - 专门针对字幕理解
        if user_question:
            # 用户追问模式
            system_message = f"""你是一个专业的语言学习助手。用户正在学习音频内容，需要你帮助理解字幕内容。

上下文信息：
{context_text}

请基于上述上下文回答用户的问题。注意：
1. 回答要准确、简洁
2. 如果涉及语法，请简要说明
3. 如果有习语或固定搭配，请解释含义
4. 使用中文回答"""
            
            prompt = user_question
        else:
            # 默认分析模式
            system_message = f"""你是一个专业的语言学习助手。用户正在学习音频内容，请帮助分析当前字幕句子。

上下文信息：
{context_text}

请对"当前句"进行详细分析，包括：
1. 中文翻译
2. 语法结构分析
3. 习语解释

请用中文回答，格式清晰易懂。"""
            
            prompt = f"请分析这个句子：{current_subtitle}"

对于这个句子：His secretary, John Hay, would note that he would sit for hours staring vacantly out the window, his face displaying the deepest sorrow imaginable.

分析结果如下：


1. 中文翻译
他的秘书约翰·海会记录下，他常常会坐上几个小时，空洞地望着窗外，他的脸上流露出无法想象的深切悲伤。

2. 语法结构分析
- 主句：His secretary, John Hay, would note that...
  - 主语：His secretary, John Hay（他的秘书，约翰·海）
  - 谓语：would note（会记录下）
  - 宾语从句：that he would sit for hours staring vacantly out the window, his face displaying the deepest sorrow imaginable.
    - 主语：he（他）
    - 谓语：would sit（会坐）
    - 时间状语：for hours（几个小时）
    - 现在分词短语作伴随状语：staring vacantly out the window（空洞地望着窗外）
    - 独立主格结构：his face displaying the deepest sorrow imaginable（他的脸上流露出无法想象的深切悲伤）
      - 独立主格结构中的主语：his face（他的脸）
      - 谓语：displaying（流露出）
      - 宾语：the deepest sorrow imaginable（无法想象的深切悲伤）

3. 习语解释
- "sit for hours"：长时间坐着，这里指的是林肯总统长时间地坐着。
- "staring vacantly"：空洞地望着，形容林肯总统的目光没有焦点，表现出一种迷茫或失神的状态。
- "out the window"：望着窗外，这里可能表示林肯总统在思考或沉浸在自己的悲伤中。
- "the deepest sorrow imaginable"：无法想象的深切悲伤，强调林肯总统的悲伤程度非常深，难以用言语表达。

上面的语法结构分析层次清晰

- 主句：His secretary, John Hay, would note that...
  - 主语：His secretary, John Hay（他的秘书，约翰·海）
  - 谓语：would note（会记录下）
  - 宾语从句：that he would sit for hours staring vacantly out the window, his face displaying the deepest sorrow imaginable.
    - 主语：he（他）
    - 谓语：would sit（会坐）
    - 时间状语：for hours（几个小时）
    - 现在分词短语作伴随状语：staring vacantly out the window（空洞地望着窗外）
    - 独立主格结构：his face displaying the deepest sorrow imaginable（他的脸上流露出无法想象的深切悲伤）
      - 独立主格结构中的主语：his face（他的脸）
      - 谓语：displaying（流露出）
      - 宾语：the deepest sorrow imaginable（无法想象的深切悲伤）

但是我还有一个“语法可视化按钮”，它调用了语法分析API端点

# 语法分析API端点
@app.route('/api/grammar-analysis-stream', methods=['POST'])
def grammar_analysis_stream():
    """
    英语语法结构分析流式响应API
    基于智谱模型进行语法结构分析和可视化
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据无效"}), 400
        
        # 获取请求参数
        sentence = data.get('sentence', '')
        system_message = data.get('system_message', '')
        user_prompt = data.get('user_prompt', '')
        analysis_type = data.get('analysis_type', 'comprehensive')
        model_id = data.get('model', 'zhipu_flash')
        temperature = data.get('temperature', 0.3)
        
        if not sentence:
            return jsonify({"error": "缺少待分析的句子"}), 400
        
        # 获取用户信息 - 使用与字幕问答相同的验证方式
        auth_header = request.headers.get('Authorization')
        user_id = None
        if auth_header and auth_header.startswith('Bearer '):
            try:
                token = auth_header.split(' ')[1]
                # 使用supabase验证token并获取真实用户ID
                from api.credits import supabase_client
                user_response = supabase_client.auth.get_user(token)
                if user_response.user:
                    user_id = user_response.user.id
                    logger.info(f"语法分析验证用户成功: {user_id}")
                else:
                    logger.warning(f"语法分析用户验证失败")
                    return jsonify({'error': '需要登录才能使用此功能'}), 401
            except Exception as e:
                logger.error(f"语法分析获取用户ID失败: {str(e)}")
                return jsonify({'error': '用户认证失败'}), 401
        else:
            return jsonify({'error': '需要登录才能使用此功能'}), 401
        
        # 简洁的系统角色定义
        if not system_message:
            system_message = """你是一个专业的英语语法分析专家。你需要对给定的英语句子进行准确、结构化的语法分析，用于教学和可视化展示。请严格按照用户要求的格式输出，使用中文回答。"""
        
        # 详细的用户指令和格式要求
        if not user_prompt:
            user_prompt = f"""请对以下英语句子进行详细的语法结构分析：

**待分析句子**：{sentence}

**输出格式要求**：
请严格按照以下Markdown格式输出：

# {sentence}

**句子类型**: [单句/并列句/复合句]

按以下格式分析句子成分：

## 句子成分1: [句子成分1的文本] - [语法作用和含义]
## 句子成分2: [句子成分2的文本] - [语法作用和含义]
## 句子成分3: [句子成分3的文本] - [语法作用和含义]
## 句子成分4: [句子成分4的文本] - [语法作用和含义]
## 句子成分5: [句子成分5的文本] - [语法作用和含义]


**分析原则**：
1. **准确识别句子类型**：单句（主谓宾、主系表、主谓）或复合句（并列句、复从句）
2. **复合句处理**：如果是并列句或复合句，按句子顺序分别分析每个分句的成分，用"第一分句"、"第二分句"等标明
3. **系动词标注**：对于系动词，在"语法作用"中明确标注为"系动词"
4. **状语准确分类**：只有真正的状语（时间、地点、方式、原因等）才标为状语，连词、介词短语要准确分类
5. **成分归属明确**：每个词汇只能属于一个句子成分，不能重复或交叉
6. 每个成分说明控制在15字以内
7. 用中文回答，禁止英文
8. **严格要求：只输出实际存在的句子成分**，如果某个成分不存在，请完全跳过该部分
9. 确保格式正确，所有标题前必须有空格（如`### 语法作用:`）

**示例**：
"Senior is a staff writer"应该分析为：
- 主语部分：Senior（句子的主体）
- 谓语部分：is（系动词，连接主语和表语）
- 表语部分：a staff writer（说明主语的身份）

请开始分析："""


但是结果很不好，它们使用的是相同的API,唯一区别是字幕问答直接采用文字输出，并没有进行任何可是化。

请你帮我修改，借鉴字幕问答API端点的简洁的prompt,修改语法分析API端点的prompt,以便能生成字幕问答中类似结构清晰的语法结构分析，并且修改grammar-analyzer用树状图将这种层次清晰在前端表现出来。grammar-analyzer.js可能需要修改，以适应上面的层次化输出。注意，保持前端流式输出渲染的特点。