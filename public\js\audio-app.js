// 音频理解Web应用前端JavaScript - 集成积分系统

class AudioApp {
    constructor() {
        // 文件管理
        this.uploadedFileId = null;
        this.uploadedAudioUrl = null;
        this.recordingFileId = null;
        this.recordingAudioUrl = null;
        this.currentSnippetUrl = null; // 新增：当前6秒片段URL
        this.currentSnippetFileId = null; // 新增：当前6秒片段文件ID
        this.activePlayer = null; // 'uploaded' 或 'recording'
        this.currentAudioContext = 'none'; // 新增：'recording', 'snippet', 'none'
        
        // 录音状态
        this.isRecording = false;
        this.recordingStartTime = null;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        
        // 进度条拖拽状态
        this.isDraggingUploaded = false;
        this.isDraggingRecording = false;
        
        // 简化录音相关
        this.mediaStream = null;
        
        // 问答数据存储
        this.qaResponses = []; // 存储所有ask about current position的响应数据
        this.currentUploadedFileName = null; // 当前上传的文件名
        
        this.initializeElements();
        this.attachEventListeners();
        this.initializeAudioPlayers();
        this.initializeUserMenu();
        this.initializeSpeedControl();
        this.initializeApp(); // 调用新的初始化方法
        
        // 初始化积分显示
        setTimeout(() => {
            this.initializeCreditsDisplay();
        }, 500); // 延迟500ms确保其他组件已初始化

        this.resetPlayerProgress('uploaded');
        this.resetPlayerProgress('recording');
    }

    resetPlayerProgress(playerType) {
        if (playerType === 'uploaded') {
            if (this.uploadedProgressFill) this.uploadedProgressFill.style.width = '0%';
            if (this.uploadedProgressHandle) this.uploadedProgressHandle.style.left = '0%';
            if (this.uploadedCurrentTime) this.uploadedCurrentTime.textContent = '00:00';
            if (this.uploadedTotalTime) this.uploadedTotalTime.textContent = '00:00';
            if (this.uploadedDuration) this.uploadedDuration.textContent = '00:00';
            if (this.uploadedPlayer) this.uploadedPlayer.currentTime = 0;
        } else if (playerType === 'recording') {
            if (this.recordingProgressFill) this.recordingProgressFill.style.width = '0%';
            if (this.recordingProgressHandle) this.recordingProgressHandle.style.left = '0%';
            if (this.recordingCurrentTime) this.recordingCurrentTime.textContent = '00:00';
            if (this.recordingTotalTime) this.recordingTotalTime.textContent = '00:00';
            if (this.recordingDurationDisplay) this.recordingDurationDisplay.textContent = '00:00';
            if (this.recordingPlayer) this.recordingPlayer.currentTime = 0;
        }
    }

    initializeElements() {
        // 文件输入和控制元素
        this.audioFileInput = document.getElementById('audioFileInput');
        this.uploadBtn = document.getElementById('uploadBtn');
        this.recordBtn = document.getElementById('recordBtn');
        this.recordingStatus = document.getElementById('recordingStatus');
        this.recordingDuration = document.getElementById('recordingDuration');

        // 上传进度条元素
        this.uploadProgress = document.getElementById('uploadProgress');
        this.uploadProgressPercent = document.getElementById('uploadProgressPercent');
        this.uploadProgressFill = document.getElementById('uploadProgressFill');

        // 简化上传状态提示元素
        this.uploadStatus = document.getElementById('uploadStatus');
        this.uploadStatusText = document.getElementById('uploadStatusText');

        // 使用简化上传提示模式（更稳定可靠）
        this.uploadIndicatorMode = 'simple';

        // 上传文件播放器元素
        this.uploadedPlayer = document.getElementById('uploadedPlayer');
        this.uploadedPlayerContainer = document.getElementById('uploadedPlayerContainer');
        this.uploadedFileName = document.getElementById('uploadedFileName');
        this.uploadedDuration = document.getElementById('uploadedDuration');
        this.uploadedPlayPauseBtn = document.getElementById('uploadedPlayPauseBtn');
        this.uploadedRewindBtn = document.getElementById('uploadedRewindBtn');
        this.uploadedFastForwardBtn = document.getElementById('uploadedFastForwardBtn');
        this.uploadedCurrentTime = document.getElementById('uploadedCurrentTime');
        this.uploadedTotalTime = document.getElementById('uploadedTotalTime');
        this.uploadedProgressBar = document.getElementById('uploadedProgressBar');
        this.uploadedProgressFill = document.getElementById('uploadedProgressFill');
        this.uploadedProgressHandle = document.getElementById('uploadedProgressHandle');

        // 播放速度控制器元素
        this.uploadedSpeedDisplay = document.getElementById('uploadedSpeedDisplay');
        this.uploadedSpeedOptions = document.getElementById('uploadedSpeedOptions');
        this.currentPlaybackSpeed = 1.0;

        // 当前音频播放器元素（原录音播放器）
        this.recordingPlayer = document.getElementById('recordingPlayer');
        this.recordingPlayerContainer = document.getElementById('recordingPlayerContainer');
        this.recordingFileName = document.getElementById('recordingFileName');
        this.recordingDurationDisplay = document.getElementById('recordingDuration');
        this.recordingPlayPauseBtn = document.getElementById('recordingPlayPauseBtn');
        this.recordingRewindBtn = document.getElementById('recordingRewindBtn');
        this.recordingFastForwardBtn = document.getElementById('recordingFastForwardBtn');
        this.recordingCurrentTime = document.getElementById('recordingCurrentTime');
        this.recordingTotalTime = document.getElementById('recordingTotalTime');
        this.recordingProgressBar = document.getElementById('recordingProgressBar');
        this.recordingProgressFill = document.getElementById('recordingProgressFill');
        this.recordingProgressHandle = document.getElementById('recordingProgressHandle');

        // 书签加载元素
        this.uploadedLoadBookmarkBtn = document.getElementById('uploadedLoadBookmarkBtn');
        this.uploadedBookmarkFileInput = document.getElementById('uploadedBookmarkFileInput');

        // AI功能元素
        this.questionBtn = document.getElementById('questionBtn');
        this.exportQABtn = document.getElementById('exportQABtn');

        // 聊天功能元素
        this.chatMessages = document.getElementById('chatMessages');
        this.questionInput = document.getElementById('questionInput');
        this.submitQuestionBtn = document.getElementById('submitQuestionBtn');
        this.continuePrompt = document.getElementById('continuePrompt');

        // 状态提示元素
        this.statusToast = document.getElementById('statusToast');
        this.toastMessage = document.getElementById('toastMessage');
        this.toastClose = document.getElementById('toastClose');
        this.loadingOverlay = document.getElementById('loadingOverlay');

        // 用户菜单元素
        this.userMenuBtn = document.getElementById('userMenuBtn');
        this.userDropdown = document.getElementById('userDropdown');
        this.creditsLink = document.getElementById('creditsLink');
        this.rechargeLink = document.getElementById('rechargeLink');
        this.logoutLink = document.getElementById('logoutLink');
        this.userCredits = document.getElementById('userCredits');
    }

    // 初始化用户菜单
    initializeUserMenu() {
        // 用户菜单事件
        if (this.userMenuBtn) {
            this.userMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.userDropdown.classList.toggle('show');
            });
        }

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
            if (this.userDropdown) {
                this.userDropdown.classList.remove('show');
            }
        });

        // 积分链接
        if (this.creditsLink) {
            this.creditsLink.addEventListener('click', (e) => {
                e.preventDefault();
                // 直接显示积分历史记录模态框
                if (window.CreditHistoryManager && typeof window.CreditHistoryManager.showModal === 'function') {
                    window.CreditHistoryManager.showModal();
                } else {
                    console.warn('积分历史管理器未初始化');
                    // 备用方案：显示原有的积分模态框
                    const creditsModal = document.getElementById('creditsModal');
                    if (creditsModal) {
                        creditsModal.classList.remove('hidden');
                        // 更新积分显示
                        if (window.CreditsManager && window.CreditsManager.getCurrentCredits) {
                            const currentCredits = window.CreditsManager.getCurrentCredits();
                            const creditsDisplay = document.getElementById('currentCredits');
                            if (creditsDisplay) {
                                creditsDisplay.textContent = currentCredits;
                            }
                        }
                    }
                }
            });
        }

        // 充值链接
        if (this.rechargeLink) {
            this.rechargeLink.addEventListener('click', (e) => {
                e.preventDefault();
                // 使用支付模块的充值流程
                if (window.paymentModule && typeof window.paymentModule.startRechargeProcess === 'function') {
                    window.paymentModule.startRechargeProcess();
                } else if (window.paymentPackages && typeof window.paymentPackages.createRechargeModal === 'function') {
                    // 直接使用payment-packages.js的创建充值模态框功能
                    window.paymentPackages.createRechargeModal(async (selectedPackage) => {
                        console.log('用户选择了套餐:', selectedPackage);
                        
                        // 显示处理状态
                        if (window.paymentPackages.showProcessingState) {
                            window.paymentPackages.showProcessingState('订单创建中，请稍候...');
                        }
                        
                        try {
                            // 调用支付模块创建订单
                            if (window.paymentModule && typeof window.paymentModule.createPaymentOrder === 'function') {
                                const orderResult = await window.paymentModule.createPaymentOrder(selectedPackage);
                                
                                console.log('订单创建结果:', orderResult);
                                
                                if (orderResult.success && orderResult.checkout_url) {
                                    // 关闭模态框并跳转到支付页面
                                    window.paymentPackages.closeRechargeModal();
                                    console.log('正在跳转到支付页面:', orderResult.checkout_url);
                                    window.location.href = orderResult.checkout_url;
                                } else {
                                    // 显示错误
                                    console.error('创建订单失败:', orderResult.error);
                                    if (window.paymentPackages.showProcessingError) {
                                        window.paymentPackages.showProcessingError(orderResult.error || '创建订单失败，请重试');
                                    } else {
                                        alert('创建订单失败，请重试');
                                    }
                                }
                            } else {
                                console.error('支付模块未初始化或createPaymentOrder方法不存在');
                                if (window.paymentPackages.showProcessingError) {
                                    window.paymentPackages.showProcessingError('支付功能暂未初始化，请刷新页面重试');
                                } else {
                                    alert('支付功能暂未初始化，请刷新页面重试');
                                }
                            }
                        } catch (error) {
                            console.error('处理支付跳转时出错:', error);
                            if (window.paymentPackages.showProcessingError) {
                                window.paymentPackages.showProcessingError('创建订单出错：' + error.message);
                            } else {
                                alert('创建订单出错，请重试');
                            }
                        }
                    });
                } else {
                    console.warn('支付套餐模块未初始化');
                    alert('支付功能暂未加载，请刷新页面后重试');
                }
            });
        }

        // 退出链接
        if (this.logoutLink) {
            this.logoutLink.addEventListener('click', (e) => {
                e.preventDefault();
                if (window.logout) {
                    window.logout();
                }
            });
        }

        // 监听积分变化事件
        window.addEventListener('creditsUpdated', (event) => {
            this.updateCreditsDisplay(event.detail.credits);
        });
    }

    // 更新积分显示
    updateCreditsDisplay(credits) {
        if (this.userCredits) {
            this.userCredits.textContent = credits;
        }
    }

    // 初始化积分显示
    async initializeCreditsDisplay() {
        try {
            // 从CreditsManager获取当前积分
            if (window.CreditsManager && typeof window.CreditsManager.getCurrentCredits === 'function') {
                const currentCredits = window.CreditsManager.getCurrentCredits();
                if (currentCredits !== null && currentCredits !== undefined) {
                    this.updateCreditsDisplay(currentCredits);
                    return;
                }
            }
            
            // 如果CreditsManager不可用，从localStorage获取
            const cachedCredits = localStorage.getItem('userCredits');
            if (cachedCredits) {
                try {
                    const creditsData = JSON.parse(cachedCredits);
                    if (creditsData && typeof creditsData.credits === 'number') {
                        this.updateCreditsDisplay(creditsData.credits);
                        return;
                    }
                } catch (e) {
                    console.warn('解析缓存积分数据失败:', e);
                }
            }
            
            // 默认显示0
            this.updateCreditsDisplay(0);
        } catch (error) {
            console.error('初始化积分显示失败:', error);
            this.updateCreditsDisplay(0);
        }
    }

    // 检查积分（AI操作前）
    async checkCredits(operation) {
        if (!window.checkCredits || typeof window.checkCredits !== 'function') {
            console.warn('积分检查函数未初始化，跳过积分检查');
            return true; // 如果积分检查函数未初始化，允许操作
        }

        try {
            const hasCredits = await window.checkCredits(operation, 'qwen_omni_turbo');
            if (!hasCredits) {
                // 积分不足时已在 window.checkCredits 中弹框提示，直接返回
                return false;
            }
            return true;
        } catch (error) {
            console.error('积分检查失败:', error);
            this.showToast('积分检查失败，请稍后重试', 'error');
            return false;
        }
    }

    // 扣减积分（AI操作后）
    async deductCredits(operation) {
        if (!window.CreditsManager || typeof window.CreditsManager.useCredits !== 'function') {
            console.warn('积分管理器未初始化，跳过积分扣减');
            return;
        }

        try {
            await window.CreditsManager.useCredits(operation, 'qwen_omni_turbo');
        } catch (error) {
            console.error('积分扣减失败:', error);
        }
    }

    attachEventListeners() {
        // 检查音频按钮是否已由统一事件处理机制绑定
        const uploadBtn = document.getElementById('uploadBtn');
        if (!uploadBtn || !uploadBtn.hasAttribute('data-audio-event-bound')) {
            // 文件上传事件 - 仅在未被统一机制绑定时才绑定
            if (this.uploadBtn) {
                this.uploadBtn.addEventListener('click', () => this.audioFileInput.click());
            }
        } else {
            console.log('音频按钮已由统一事件处理机制绑定，跳过AudioApp内部绑定');
        }
        
        // 文件选择事件 - 保持原有逻辑
        this.audioFileInput.addEventListener('change', (e) => this.handleFileUpload(e));

        // 录制事件
        this.recordBtn.addEventListener('click', () => this.toggleRecording());

        // 书签加载按钮事件
        if (this.uploadedLoadBookmarkBtn) {
            this.uploadedLoadBookmarkBtn.addEventListener('click', () => {
                this.uploadedBookmarkFileInput.click();
            });
        }

        if (this.uploadedBookmarkFileInput) {
            this.uploadedBookmarkFileInput.addEventListener('change', (event) => {
                this.handleBookmarkFileSelect(event, 'uploaded');
            });
        }



        // 上传文件播放器事件
        this.uploadedPlayPauseBtn.addEventListener('click', () => this.togglePlayPause('uploaded'));
        this.uploadedRewindBtn.addEventListener('click', () => this.rewind('uploaded'));
        this.uploadedFastForwardBtn.addEventListener('click', () => this.fastForward('uploaded'));
        this.uploadedProgressBar.addEventListener('mousedown', (e) => this.startProgressDrag(e, 'uploaded'));

        // 播放速度控制器事件
        if (this.uploadedSpeedDisplay) {
            this.uploadedSpeedDisplay.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleSpeedOptions();
            });
        }

        if (this.uploadedSpeedOptions) {
            // 速度选项点击事件
            this.uploadedSpeedOptions.addEventListener('click', (e) => {
                if (e.target.classList.contains('speed-option')) {
                    e.stopPropagation();
                    const speed = parseFloat(e.target.dataset.speed);
                    this.setPlaybackSpeed(speed);
                    this.hideSpeedOptions();
                }
            });

            // 鼠标滚轮事件
            this.uploadedSpeedDisplay.addEventListener('wheel', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleSpeedWheelChange(e);
            });
        }

        // 点击其他地方关闭速度选项
        document.addEventListener('click', () => {
            this.hideSpeedOptions();
        });

        // 录音文件播放器事件
        this.recordingPlayPauseBtn.addEventListener('click', () => this.togglePlayPause('recording'));
        this.recordingRewindBtn.addEventListener('click', () => this.rewind('recording'));
        this.recordingFastForwardBtn.addEventListener('click', () => this.fastForward('recording'));
        this.recordingProgressBar.addEventListener('mousedown', (e) => this.startProgressDrag(e, 'recording'));

        // 全局鼠标事件
        document.addEventListener('mousemove', (e) => this.updateProgressDrag(e));
        document.addEventListener('mouseup', () => this.endProgressDrag());

        // AI功能事件
        this.questionBtn.addEventListener('click', () => this.askQuestionAtCurrentTime());
        this.exportQABtn.addEventListener('click', () => this.exportQAResponses());

        // 聊天功能事件
        this.submitQuestionBtn.addEventListener('click', () => this.submitQuestion());
        
        // 添加Enter键发送功能
        this.questionInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault(); // 防止默认行为
                e.stopPropagation(); // 阻止事件冒泡
                this.submitQuestion();
            }
        });

        // 状态提示事件
        this.toastClose.addEventListener('click', () => this.hideToast());

        // 添加全局键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            // 忽略输入框和文本区域中的按键
            const activeTag = document.activeElement.tagName;
            if (activeTag === 'INPUT' || activeTag === 'TEXTAREA' || document.activeElement.isContentEditable) {
                return;
            }
            // 使用当前活跃播放器或默认uploaded
            const playerType = this.activePlayer || 'uploaded';
            switch (e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    this.rewind(playerType);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.fastForward(playerType);
                    break;
                case ' ': // 空格键
                case 'Spacebar':
                case 'Space':
                    e.preventDefault();
                    this.togglePlayPause(playerType);
                    break;
                case 'ArrowDown':
                    if (e.shiftKey) {
                        e.preventDefault();
                        if (window.bookmarkManager) {
                            window.bookmarkManager.addBookmark(playerType);
                        }
                    }
                    break;
                default:
                    break;
            }
        });
    }

    initializeAudioPlayers() {
        this.currentPlayingEpisodeIndex = -1; // -1表示没有播客在播放

        // 上传文件播放器事件
        this.uploadedPlayer.addEventListener('loadedmetadata', () => this.updateAudioInfo('uploaded'));
        this.uploadedPlayer.addEventListener('timeupdate', () => this.updateProgress('uploaded'));
        this.uploadedPlayer.addEventListener('ended', () => this.onAudioEnded('uploaded'));
        this.uploadedPlayer.addEventListener('play', () => {
            this.uploadedPlayPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            this.setActivePlayer('uploaded');
        });
        this.uploadedPlayer.addEventListener('pause', () => {
            this.uploadedPlayPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
        });

        // 录音文件播放器事件
        this.recordingPlayer.addEventListener('loadedmetadata', () => this.updateAudioInfo('recording'));
        this.recordingPlayer.addEventListener('timeupdate', () => this.updateProgress('recording'));
        this.recordingPlayer.addEventListener('ended', () => this.onAudioEnded('recording'));
        this.recordingPlayer.addEventListener('play', () => {
            this.recordingPlayPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            this.setActivePlayer('recording');
            if (window.PodcastManager && typeof window.PodcastManager.updatePlayButtons === 'function' && this.currentPlayingEpisodeIndex !== -1) {
                window.PodcastManager.updatePlayButtons(this.currentPlayingEpisodeIndex);
            }
        });
        this.recordingPlayer.addEventListener('pause', () => {
            this.recordingPlayPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            if (this.currentPlayingEpisodeIndex !== -1 && window.PodcastManager) {
                window.PodcastManager.updatePlayButtons(this.currentPlayingEpisodeIndex);
            }
        });
        this.recordingPlayer.addEventListener('ended', () => {
            this.onAudioEnded('recording');
            if (this.currentPlayingEpisodeIndex !== -1 && window.PodcastManager) {
                window.PodcastManager.updatePlayButtons(-1); // 重置按钮
                this.currentPlayingEpisodeIndex = -1;
            }
        });
        this.recordingPlayer.addEventListener('loadstart', () => {
             if (this.currentPlayingEpisodeIndex !== -1 && window.PodcastManager) {
                const playBtn = document.querySelector(`[data-index="${this.currentPlayingEpisodeIndex}"].episode-play-btn`);
                window.PodcastManager.showPlayLoading(true, playBtn);
            }
        });
         this.recordingPlayer.addEventListener('canplay', () => {
             if (this.currentPlayingEpisodeIndex !== -1 && window.PodcastManager) {
                const playBtn = document.querySelector(`[data-index="${this.currentPlayingEpisodeIndex}"].episode-play-btn`);
                // 不在这里更新按钮状态，只取消加载状态
                // 真正的播放/暂停状态由 'play' 和 'pause' 事件处理
                window.PodcastManager.showPlayLoading(false, playBtn);
            }
        });
    }

    // 设置当前活跃的播放器
    setActivePlayer(playerType) {
        this.activePlayer = playerType;
        // 暂停另一个播放器
        if (playerType === 'uploaded' && !this.recordingPlayer.paused) {
            this.recordingPlayer.pause();
        } else if (playerType === 'recording' && !this.uploadedPlayer.paused) {
            this.uploadedPlayer.pause();
        }
    }

    // 获取当前活跃播放器的信息 - 增强版
    getActivePlayerInfo() {
        if (this.activePlayer === 'uploaded') {
            return {
                fileId: this.uploadedFileId,
                player: this.uploadedPlayer,
                type: 'uploaded'
            };
        } else if (this.activePlayer === 'recording') {
            // 根据当前音频上下文返回相应的文件ID
            let fileId;
            if (this.currentAudioContext === 'snippet' && this.currentSnippetFileId) {
                fileId = this.currentSnippetFileId;
            } else if (this.currentAudioContext === 'recording' && this.recordingFileId) {
                fileId = this.recordingFileId;
            } else {
                fileId = this.recordingFileId; // 默认返回录音文件ID
            }
            
            return {
                fileId: fileId,
                player: this.recordingPlayer,
                type: 'recording',
                context: this.currentAudioContext
            };
        }
        return null;
    }

    // 文件上传处理
    async handleFileUpload(event) {
        if (!this.audioFileInput.files.length) {
            this.showToast('请选择音频文件', 'error');
            return;
        }

        const file = this.audioFileInput.files[0];
        
        // 添加详细的文件信息日志
        console.log(`🔍 文件上传检查:`, {
            name: file.name,
            size: file.size,
            sizeMB: (file.size / (1024 * 1024)).toFixed(2) + 'MB',
            type: file.type,
            lastModified: new Date(file.lastModified).toISOString()
        });
        
        // 检查文件大小 (100MB 限制，与后端保持一致)
        const maxSize = 100 * 1024 * 1024;
        if (file.size > maxSize) {
            console.error(`❌ 文件大小超限: ${(file.size / (1024 * 1024)).toFixed(2)}MB > 100MB`);
            this.showToast(`文件大小超过100MB限制 (当前: ${(file.size / (1024 * 1024)).toFixed(1)}MB)`, 'error');
            return;
        }
        
        console.log(`✅ 文件大小检查通过: ${(file.size / (1024 * 1024)).toFixed(2)}MB <= 100MB`);

        // 检查文件类型
        const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/mp3', 'audio/mp4', 'audio/m4a', 'audio/ogg'];
        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(mp3|wav|m4a|mp4|ogg)$/i)) {
            this.showToast('不支持的音频格式', 'error');
            return;
        }

        this.resetPlayerProgress('uploaded');

        // 新增：优化模式 - 直接在本地播放，不上传完整文件
        try {
            console.log(`🚀 开始本地文件处理: ${file.name}`);
            
            // 创建本地播放URL
            const audioUrl = URL.createObjectURL(file);
            const fileId = this.generateFileId(); // 生成本地文件ID
            
            console.log(`📁 创建本地播放URL成功: ${audioUrl}`);
            console.log(`🔑 生成文件ID: ${fileId}`);
            
            // 加载到播放器
            this.loadAudioFile(audioUrl, file.name, 'uploaded');
            this.uploadedFileId = fileId;
            this.uploadedAudioUrl = audioUrl;
            this.currentUploadedFileName = file.name;
            
            // 存储文件引用用于音频分析
            this.uploadedFileReference = file;
            
            console.log(`🎵 音频播放器加载完成，文件引用已存储`);
            
            // 触发音频文件加载事件
            const uploadEvent = new CustomEvent('audioFileUploaded', {
                detail: {
                    filename: file.name,
                    fileId: fileId,
                    audioUrl: audioUrl
                }
            });
            document.dispatchEvent(uploadEvent);

            this.showToast('文件加载成功', 'success');
            console.log(`✅ 本地音频文件加载完成: ${file.name} (${(file.size / (1024 * 1024)).toFixed(2)}MB)`);
            
            this.enableAIControls();
            
            // 启用时间戳按钮
            const timestampBtn = document.getElementById('timestampBtn');
            if (timestampBtn) {
                timestampBtn.disabled = false;
            }
            
            // 启用导出按钮检查
            this.updateExportButtonState();
            
        } catch (error) {
            console.error('❌ 文件加载失败:', error);
            console.error('错误详情:', {
                name: error.name,
                message: error.message,
                stack: error.stack
            });
            this.showToast('文件加载失败: ' + error.message, 'error');
        }
    }

    // 生成本地文件ID
    generateFileId() {
        return 'local_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 新增：分析上传的本地音频文件（异步优化版本）
    async analyzeUploadedFile() {
        if (!this.uploadedFileReference) {
            // 如果没有本地文件引用，回退到传统上传方式
            this.showToast('正在分析音频文件...', 'info');
            return this.analyzeUploadedFileTraditional();
        }

        // 立即显示AI分析指示器
        const analysisMessageId = this.showQAAnalysisIndicatorOnly();

        try {
            // 双阶段积分校验：前端缓存 -> 后端缓存
            const creditsManager = window.CreditsManager;
            const required = 1;
            let cached = creditsManager.getCachedCredits(); if (cached === null) cached = 0;
            if (cached < required) { 
                this.replaceAnalysisIndicatorWithError(analysisMessageId, '积分不足，无法进行AI分析');
                creditsManager.showInsufficientCreditsAlert(cached); 
                return; 
            }
            try { 
                const fast = await creditsManager.fetchCreditsFromBackendCache(); 
                if (fast < required) { 
                    this.replaceAnalysisIndicatorWithError(analysisMessageId, '积分不足，无法进行AI分析');
                    creditsManager.showInsufficientCreditsAlert(fast); 
                    return; 
                } 
            } catch (err) { 
                this.replaceAnalysisIndicatorWithError(analysisMessageId, '积分不足，无法进行AI分析');
                creditsManager.showInsufficientCreditsAlert(cached); 
                return; 
            }

            // 积分检查通过，开始文件处理
            const base64Audio = await this.fileToBase64(this.uploadedFileReference);

            // 获取文件格式
            const fileExtension = this.uploadedFileReference.name.split('.').pop().toLowerCase();
            const audioFormat = {
                'mp3': 'mp3', 'wav': 'wav', 'mp4': 'mp4', 
                'm4a': 'aac', 'ogg': 'ogg', 'webm': 'mp3'
            }[fileExtension] || 'mp3';

            // 发送请求
            const response = await this.streamLocalAudioAnalysis(base64Audio, audioFormat);
            
            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            this.handleRecordingAnalysisStreamData(data, analysisMessageId);
                        } catch (parseError) {
                            console.error('解析分析流式数据失败:', parseError);
                        }
                    }
                }
            }
            
            // 分析完成后显示继续对话提示
            this.showContinuePrompt();
            
            // AI分析完成后扣减积分
            await this.deductCredits('audio_analysis');
        } catch (error) {
            console.error('本地音频分析失败:', error);
            this.replaceAnalysisIndicatorWithError(analysisMessageId, this.getI18nText('audio.ai_analysis_failed'));
        }
    }

    // 优化：并行处理本地音频文件
    async analyzeLocalAudioFile(file) {
        try {
            // 并行执行文件转换和格式识别
            const [base64Audio, audioFormat] = await Promise.all([
                this.fileToBase64(file),
                Promise.resolve((() => {
                    const fileExtension = file.name.split('.').pop().toLowerCase();
                    return {
                        'mp3': 'mp3', 'wav': 'wav', 'mp4': 'mp4', 
                        'm4a': 'aac', 'ogg': 'ogg', 'webm': 'mp3'
                    }[fileExtension] || 'mp3';
                })())
            ]);
            
            // 直接调用分析API
            return await this.streamLocalAudioAnalysis(base64Audio, audioFormat);
            
        } catch (error) {
            console.error('本地音频分析失败:', error);
            throw error;
        }
    }

    // 文件转base64
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                // 移除data:audio/xxx;base64,前缀
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = error => reject(error);
        });
    }

    // 新增：本地音频流式分析
    async streamLocalAudioAnalysis(base64Audio, audioFormat) {
        const authToken = localStorage.getItem('authToken');
        
        const response = await fetch('/api/analyze-local-audio-stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`,
                'Accept-Language': UILanguage && UILanguage.getCurrentLanguage ? UILanguage.getCurrentLanguage() : 'zh'
            },
            body: JSON.stringify({
                audio_data: base64Audio,
                audio_format: audioFormat,
                prompt: "你是一个专业的英汉口译专家，能够准确理解并翻译音频内容，请先给出音频的transcript，然后进行翻译和解释，并就其中涉及的语法结构、习语和习惯用法、文化背景提供解释和分析。"
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response;
    }

    /**
     * 处理书签文件选择
     * @param {Event} event - 文件选择事件
     * @param {string} playerType - 播放器类型 ('uploaded' 或 'recording')
     */
    async handleBookmarkFileSelect(event, playerType) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            if (window.bookmarkManager) {
                await window.bookmarkManager.handleBookmarkFileLoad(file);
                this.showToast('书签文件加载成功', 'success');
            } else {
                this.showToast('书签管理器未初始化', 'error');
                console.error('书签管理器未找到 - window.bookmarkManager:', window.bookmarkManager);
            }
        } catch (error) {
            console.error('书签文件加载失败:', error);
            this.showToast('书签文件加载失败: ' + error.message, 'error');
        }

        // 清空文件选择器，允许重复选择同一文件
        event.target.value = '';
    }

    // 上传文件方法
    async uploadFile(file) {
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('file_type', 'upload'); // 标识为上传文件

            const xhr = new XMLHttpRequest();

            // 上传进度监听
            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable) {
                    const percentComplete = (event.loaded / event.total) * 100;
                    // 简化模式不需要更新进度，只保持上传中状态
                }
            });

            // 上传完成监听
            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    try {
                        // 显示服务器处理状态
                        this.updateUploadStatus('processing');
                        
                        const result = JSON.parse(xhr.responseText);
                        if (result.success) {
                            // 短暂显示完成状态
                            this.updateUploadStatus('completed');
                            
                            setTimeout(() => {
                                resolve(result.audio_url);
                            }, 500); // 延迟500ms让用户看到完成状态
                        } else {
                            reject(new Error(result.error || '文件上传失败'));
                        }
                    } catch (error) {
                        reject(new Error('响应解析失败'));
                    }
                } else {
                    reject(new Error(`上传失败，状态码: ${xhr.status}`));
                }
            });

            // 上传错误监听
            xhr.addEventListener('error', () => {
                reject(new Error('网络错误，上传失败'));
            });

            // 上传中止监听
            xhr.addEventListener('abort', () => {
                reject(new Error('上传被中止'));
            });

            // 发送请求
            xhr.open('POST', '/api/upload-audio', true);
            xhr.send(formData);
        });
    }

    // 从音频URL中提取文件ID
    extractFileIdFromUrl(audioUrl) {
        // 从类似 /api/audio-file/uuid 的URL中提取文件ID
        const match = audioUrl.match(/\/api\/audio-file\/([a-f0-9-]+)/);
        if (match) {
            return match[1];
        }
        
        // 兼容旧格式：从类似 /temp/audio_1234567890.wav 的URL中提取ID
        const legacyMatch = audioUrl.match(/audio_(\d+)/);
        return legacyMatch ? legacyMatch[1] : null;
    }

    // 切换录制状态
    async toggleRecording() {
        if (this.isRecording) {
            await this.stopRecording();
        } else {
            await this.startRecording();
        }
    }

    // 开始录制
    async startRecording() {
        try {
            await this.startOptimizedMediaRecorderRecording();
        } catch (error) {
            console.error('录音启动失败:', error);
            this.showToast('录音启动失败，请检查麦克风权限', 'error');
        }
    }

    // 使用MediaRecorder的优化录制方案（模仿PyAudio配置）
    async startOptimizedMediaRecorderRecording() {
        
        const constraints = {
            audio: {
                echoCancellation: true,          // 回声消除
                noiseSuppression: true,          // 噪音抑制
                autoGainControl: true,           // 自动增益控制
                sampleRate: { exact: 44100 },    // 强制44100Hz，与PyAudio一致
                channelCount: { exact: 1 },      // 强制单声道，与PyAudio一致
                sampleSize: { ideal: 16 },       // 16位采样，与PyAudio paInt16一致
                volume: 1.0,                     // 最大音量
                latency: { ideal: 0.01 }         // 低延迟
            }
        };

        try {
            this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
            

            // 优化的格式选择（优先无损或高质量格式）
            const formatPriority = [
                { mimeType: 'audio/wav', name: 'WAV无损', quality: '完美', bitrate: 1411200 },
                { mimeType: 'audio/mpeg', name: 'MP3高质量', quality: '优秀', bitrate: 320000 },
                { mimeType: 'audio/mp4', name: 'MP4优质', quality: '良好', bitrate: 256000 },
                { mimeType: 'audio/mp4;codecs=mp4a.40.2', name: 'AAC高质量', quality: '良好', bitrate: 256000 },
                { mimeType: 'audio/webm;codecs=opus', name: 'WebM+Opus', quality: '可用', bitrate: 192000 },
                { mimeType: 'audio/webm', name: 'WebM通用', quality: '可用', bitrate: 128000 },
                { mimeType: 'audio/ogg;codecs=opus', name: 'OGG+Opus', quality: '可用', bitrate: 128000 }
            ];
            
            let selectedFormat = null;
            for (const format of formatPriority) {
                if (MediaRecorder.isTypeSupported(format.mimeType)) {
                    selectedFormat = format;
                    break;
                }
            }
            
            if (!selectedFormat) {
                selectedFormat = { mimeType: '', name: '浏览器默认', quality: '未知', bitrate: 128000 };
            }

            // MediaRecorder配置
            const options = {
                audioBitsPerSecond: selectedFormat.bitrate,
            };
            
            if (selectedFormat.mimeType) {
                options.mimeType = selectedFormat.mimeType;
            }

            this.mediaRecorder = new MediaRecorder(this.mediaStream, options);
            this.recordedChunks = [];
            
            // 模仿PyAudio的frames_per_buffer=1024，计算对应的时间间隔
            // 1024样本 / 44100Hz ≈ 23ms，使用较小的间隔提高质量
            const dataCollectionInterval = 50; // 50ms采集间隔，保证流畅度

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.processRecordedAudio();
            };

            this.mediaRecorder.onerror = (event) => {
                this.showToast('录音出现错误: ' + event.error, 'error');
            };
            
            // 启动录制
            this.mediaRecorder.start(dataCollectionInterval);
            this.isRecording = true;
            this.recordingStartTime = Date.now();
            
            // 更新UI
            this.recordBtn.innerHTML = `<i class="fas fa-stop"></i> <span data-i18n="audio.stop_recording">${this.getI18nText('audio.stop_recording', '停止录音')}</span>`;
            this.recordBtn.classList.remove('btn-secondary');
            this.recordBtn.classList.add('btn-accent');
            this.recordingStatus.classList.remove('hidden');
            
            // 更新录音时长显示
            this.updateRecordingDuration();

            // 显示当前录音配置给用户
            this.showToast(`🎤 开始录音 (${selectedFormat.name}，${selectedFormat.quality}质量)`, 'success');
            
        } catch (error) {
            this.showToast('录音失败，请检查麦克风权限', 'error');
            throw error;
        }
    }

    // 停止录制
    async stopRecording() {
        
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
            this.mediaRecorder.stop();
        }
        
        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach(track => track.stop());
        }
        
        this.isRecording = false;
        
        // 暂停播客播放器（如果正在播放）
        if (window.PodcastManager && window.PodcastManager.podcastPlayer) {
            window.PodcastManager.podcastPlayer.pause();
            console.log('🎤 [录音停止] 已暂停播客播放器，避免干扰录音分析');
        }
        
        // 恢复UI
        this.recordBtn.innerHTML = `<i class="fas fa-microphone"></i> <span data-i18n="audio.start_recording">${this.getI18nText('audio.start_recording', '开始录音')}</span>`;
        this.recordBtn.classList.remove('btn-accent');
        this.recordBtn.classList.add('btn-secondary');
        this.recordingStatus.classList.add('hidden');
        
        this.showToast('录音已停止，正在处理...', 'info');
    }

    // 更新录音时长显示
    updateRecordingDuration() {
        if (this.isRecording && this.recordingStartTime) {
            const duration = (Date.now() - this.recordingStartTime) / 1000;
            this.recordingDuration.textContent = this.formatTime(duration);
            setTimeout(() => this.updateRecordingDuration(), 1000);
        }
    }

    // 处理录制完成的音频
    async processRecordedAudio() {
        
        if (this.recordedChunks.length === 0) {
            this.showToast('录音数据为空，请重新录制', 'error');
            return;
        }

        try {
            // 确定MIME类型
            const firstChunk = this.recordedChunks[0];
            const actualMimeType = firstChunk.type;

            // 创建音频文件
            const audioBlob = new Blob(this.recordedChunks, { 
                type: actualMimeType || 'audio/mp4'
            });

            // 根据MIME类型确定文件格式和扩展名
            let mimeType, fileName, fileExtension;
            
            if (actualMimeType === 'unknown' || !actualMimeType) {
                // 如果无法检测MIME类型，尝试用MediaRecorder的配置
                if (this.mediaRecorder && this.mediaRecorder.mimeType) {
                    actualMimeType = this.mediaRecorder.mimeType;
                }
            }
            
            // 根据MIME类型设置文件格式
            if (actualMimeType.includes('wav')) {
                mimeType = 'audio/wav';
                fileName = 'recording.wav';
                fileExtension = '.wav';
            } else if (actualMimeType.includes('mpeg') || actualMimeType.includes('mp3')) {
                mimeType = 'audio/mpeg';
                fileName = 'recording.mp3';
                fileExtension = '.mp3';
            } else if (actualMimeType.includes('mp4')) {
                mimeType = actualMimeType; // 保持原格式
                fileName = 'recording.mp4';
                fileExtension = '.mp4';
            } else if (actualMimeType.includes('webm')) {
                mimeType = actualMimeType;
                fileName = 'recording.webm';
                fileExtension = '.webm';
            } else if (actualMimeType.includes('ogg')) {
                mimeType = actualMimeType;
                fileName = 'recording.ogg';
                fileExtension = '.ogg';
            } else {
                // 未知格式，使用通用命名
                mimeType = actualMimeType;
                fileName = 'recording.audio';
                fileExtension = '.audio';
            }

            // 文件大小检查
            const fileSizeMB = (audioBlob.size / (1024 * 1024)).toFixed(1);
            if (audioBlob.size < 1000) {
                this.showToast('录音时间太短，请录制更长的音频', 'error');
                return;
            } else {
                console.log(`✅ 录音文件大小正常: ${fileSizeMB}MB`);
            }

            // 上传录音文件
            await this.uploadRecordedAudio(audioBlob, fileName);

        } catch (error) {
            this.showToast('录音处理失败，请重试', 'error');
        }
    }

    // 上传录制的音频 - 修改为在智能问答区显示分析
    async uploadRecordedAudio(audioBlob, fileName) {
        this.showLoadingOverlay('正在处理录音文件...');

        try {
            const formData = new FormData();
            formData.append('file', audioBlob, fileName);
            formData.append('file_type', 'recording'); // 标识为录音文件

            const response = await fetch('/api/upload-audio', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                
                // 更新当前音频播放器为录音
                await this.updateCurrentAudioPlayer(result.audio_url, fileName, result.file_id, 'recording');
                
                this.enableAIControls();
                this.showToast('录音完成！开始AI分析...', 'success');
                
                // 在智能问答区显示AI分析
                this.showRecordingAnalysisInChat();
                
            } else {
                this.showToast(`录音上传失败：${result.error}`, 'error');
            }
        } catch (error) {
            this.showToast('录音上传失败，请重试', 'error');
        } finally {
            this.hideLoadingOverlay();
        }
    }

    // 自动播放当前音频播放器的音频（如果有）
    autoPlayCurrentAudio() {
        try {
            // 检查是否有当前音频播放器
            if (this.recordingPlayer && this.recordingPlayer.src && !this.recordingPlayer.paused) {
                // 如果正在播放，重新开始播放
                this.recordingPlayer.currentTime = 0;
            } else if (this.recordingPlayer && this.recordingPlayer.src) {
                // 如果有音频源但未播放，开始播放
                this.recordingPlayer.currentTime = 0;
                this.recordingPlayer.play().then(() => {
                    console.log('✅ 自动播放当前音频片段');
                    this.showToast('开始播放音频片段', 'info');
                }).catch(error => {
                    console.warn('自动播放失败:', error);
                    // 不显示错误toast，避免打扰用户
                });
            }
        } catch (error) {
            console.warn('自动播放当前音频失败:', error);
        }
    }

    // 在智能问答区显示录音分析
    async showRecordingAnalysisInChat() {
        // 立即显示AI分析指示器，提供即时反馈
        const analysisMessageId = this.showQAAnalysisIndicatorOnly();

        // 自动播放当前音频片段，让用户在等待时可以重复听
        this.autoPlayCurrentAudio();

        try {
            // 前端积分初查（缓存检查）
            const creditsManager = window.CreditsManager;
            const required = 1;
            let cachedCredits = creditsManager ? creditsManager.getCachedCredits() : null;
            if (cachedCredits === null) cachedCredits = 0;
            if (cachedCredits < required) {
                // 前端缓存积分不足，直接弹框并返回
                this.replaceAnalysisIndicatorWithError(analysisMessageId, this.getI18nText('audio.insufficient_credits', '积分不足，无法进行AI分析'));
                creditsManager.showInsufficientCreditsAlert(cachedCredits);
                return;
            }
            // 前端缓存充足，判断是否跳过后端检查
            const skipCreditsCheck = cachedCredits >= (required + 10);
            console.log(`🎯 [录音分析] 前端积分检查通过，skipCreditsCheck=${skipCreditsCheck}`);
            // 统一使用全局检查，确保后端检查或跳过
            const hasCredits = await this.checkCredits('audio_analysis');
            if (!hasCredits) {
                // 全局检查会弹框，此处直接返回
                return;
            }

            // 获取录音文件信息
            const activeInfo = {
                fileId: this.recordingFileId,
                player: this.recordingPlayer,
                type: 'recording',
                context: 'recording'
            };

            // 验证fileId是否存在
            if (!activeInfo.fileId) {
                console.error('录音分析失败: 缺少录音文件ID');
                this.replaceAnalysisIndicatorWithError(analysisMessageId, '录音文件不存在，请重新录音');
                return;
            }

            // 发送API请求（传递积分检查状态）
            await this.streamRecordingAnalysis(activeInfo, analysisMessageId, skipCreditsCheck);
            
            // 分析完成后显示继续对话提示
            this.showContinuePrompt();
            
            // AI分析完成后扣减积分
            await this.deductCredits('audio_analysis');
        } catch (error) {
            console.error('录音AI分析失败:', error);
            this.replaceAnalysisIndicatorWithError(analysisMessageId, this.getI18nText('audio.ai_analysis_failed'));
        }
    }

    // 流式录音分析
    async streamRecordingAnalysis(activeInfo, messageId, skipCreditsCheck = false) {
        try {
            // 验证fileId参数
            if (!activeInfo || !activeInfo.fileId) {
                console.error('录音分析失败: 缺少有效的文件ID');
                this.replaceAnalysisIndicatorWithError(messageId, '录音文件不存在，请重新录音');
                return;
            }

            console.log(`🎤 [录音分析] 开始分析文件: ${activeInfo.fileId}`);
            console.log(`🚀 [录音分析] 跳过积分检查: ${skipCreditsCheck}`);

            // 获取认证令牌
            const authToken = localStorage.getItem('authToken');
            
            const response = await fetch('/api/analyze-audio-stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`,
                    'Accept-Language': UILanguage && UILanguage.getCurrentLanguage ? UILanguage.getCurrentLanguage() : 'zh'
                },
                body: JSON.stringify({
                    file_id: activeInfo.fileId,
                    prompt: "你是一个专业的英汉口译专家，能够准确理解并翻译音频内容，请先给出音频的transcript，然后进行翻译和解释，并就其中涉及的语法结构、习语和习惯用法、文化背景提供解释和分析。",
                    skip_credits_check: skipCreditsCheck
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            this.handleRecordingAnalysisStreamData(data, messageId);
                        } catch (parseError) {
                            console.error('解析录音分析流式数据失败:', parseError);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('录音流式分析失败:', error);
            this.replaceAnalysisIndicatorWithError(messageId, this.getI18nText('audio.ai_analysis_failed'));
            throw error;
        } finally {
            if (window.CreditsManager && typeof window.CreditsManager.syncCreditsAfterApiCall === 'function') {
                try {
                    await window.CreditsManager.syncCreditsAfterApiCall();
                } catch (syncError) {
                    console.error('API调用后积分同步失败:', syncError);
                }
            }
            // 启用聊天输入，支持连续问答
            this.questionInput.disabled = false;
            this.submitQuestionBtn.disabled = false;
            this.updateExportButtonState();
        }
    }

    // 处理录音分析的流式数据
    handleRecordingAnalysisStreamData(data, messageId) {
        if (data.type === 'start') {
            // 开始时保持加载指示器
        } else if (data.type === 'chunk') {
            // 第一次收到chunk时，替换加载指示器
            this.replaceLoadingIndicatorWithStreamContent(messageId, data.accumulated);
        } else if (data.type === 'complete') {
            this.replaceLoadingIndicatorWithStreamContent(messageId, data.final_content, true);
            // 启用后续问答输入
            this.questionInput.disabled = false;
            this.submitQuestionBtn.disabled = false;
            this.updateExportButtonState();
        } else if (data.type === 'error') {
            console.error('录音分析流式数据错误:', data.error);
            const errorPrefix = this.getI18nText('audio.analysis_failed_prefix');
            this.replaceLoadingIndicatorWithStreamContent(messageId, `${errorPrefix}${data.error}`, true);
        }
    }

    // 显示继续对话提示
    showContinuePrompt() {
        if (this.continuePrompt) {
            this.continuePrompt.classList.remove('hidden');
            // 启用输入框和发送按钮
            this.questionInput.disabled = false;
            this.submitQuestionBtn.disabled = false;
            this.updateExportButtonState();
            // 滚动到底部确保用户看到提示
            setTimeout(() => {
                this.scrollToBottom(this.chatMessages);
            }, 100);
        }
    }

    // 隐藏继续对话提示
    hideContinuePrompt() {
        if (this.continuePrompt) {
            this.continuePrompt.classList.add('hidden');
        }
    }

    // 基于当前播放位置提问 - 增强版，真正异步并行优化
    async askQuestionAtCurrentTime() {
        // 前端积分初查：快速缓存检查
        const creditsManager = window.CreditsManager;
        const required = 1;
        let cachedCredits = creditsManager ? creditsManager.getCachedCredits() : null;
        if (cachedCredits === null) cachedCredits = 0;
        if (cachedCredits < required) {
            // 前端缓存积分不足，直接弹框并返回
            creditsManager.showInsufficientCreditsAlert(cachedCredits);
            return;
        }
        // 后端统一检查
        const hasCredits = await this.checkCredits('smart_qa');
        if (!hasCredits) {
            // 全局检查会弹框，直接返回
            return;
        }
        const activeInfo = this.getActivePlayerInfo();
        if (!activeInfo) {
            this.showToast('请先选择音频文件', 'error');
            return;
        }

        const currentTime = activeInfo.player.currentTime;
        const snippetDuration = this.getSnippetDuration(); // 获取用户设置的音频片段时长 (用于回放)
        const extractionDuration = snippetDuration + 1; // 为VBR文件增加1秒缓冲 (用于提取)
        const vbr_offset = 2.0; // VBR补偿偏移（向后延迟2秒提取）

        // 🔍 关键参数记录
        const rewindStartTime = Math.max(0, currentTime - snippetDuration);
        const snippetEndTime = currentTime;
        
        console.log(`🔍 [音频片段分析] 关键参数:`);
        console.log(`  ▶️ 当前播放时间: ${currentTime.toFixed(3)}s (${this.formatTime(currentTime)})`);
        console.log(`  ⏱️ 用户设置时长: ${snippetDuration}s (用于回放)`);
        console.log(`  ➕ VBR容错缓冲: 1s (实际提取${extractionDuration}s)`);
        console.log(`  -> VBR补偿偏移: ${vbr_offset}s (提取窗口后移)`);

        // 🚀 操作1：立即回退并播放指定时长的音频片段（即时回放）
        console.log('🚀 [并行操作1] 开始即时回放...');
        console.log(`  ▶️ 即时回放范围: ${rewindStartTime.toFixed(3)}s - ${snippetEndTime.toFixed(3)}s (${snippetDuration}s)`);
        this.playRewindSnippet(activeInfo.player, snippetDuration);

        // 隐藏继续对话提示
        this.hideContinuePrompt();

        // 触发书签添加事件
        const askEvent = new CustomEvent('askCurrentPosition', {
            detail: {
                playerType: activeInfo.type,
                currentTime: currentTime
            }
        });
        document.dispatchEvent(askEvent);

        // 立即显示AI分析指示器，提供即时反馈
        const analysisMessageId = this.showQAAnalysisIndicatorOnly();

        try {
            // 🚀 异步并行启动三个操作
            console.log('🚀 开始三个并行操作...');
            const authToken = localStorage.getItem('authToken');
            
            // 并行操作2：后端积分检查
            const creditsCheckPromise = this.parallelCreditsCheck(authToken, extractionDuration);
            
            // 并行操作3：音频片段生成（应用VBR补偿）
            console.log('🚀 [并行操作3] 开始音频片段生成...');
            const audioProcessingPromise = this.parallelAudioProcessing(activeInfo, currentTime, extractionDuration, { vbr_offset: vbr_offset });
            
            // 等待积分检查完成（快速）
            console.log('🚀 [并行操作2] 等待积分检查结果...');
            const creditsCheckResult = await creditsCheckPromise;
            
            if (!creditsCheckResult.success) {
                // 积分检查失败，取消音频处理并显示错误
                console.log('❌ 积分检查失败，取消后续操作');
                this.replaceAnalysisIndicatorWithError(analysisMessageId, creditsCheckResult.error || '积分不足，无法进行AI分析');
                return;
            }

            console.log('✅ [并行操作2] 积分检查通过');
            
            // 积分检查通过，等待音频处理完成并开始AI分析
            console.log('🚀 [并行操作3] 等待音频处理完成...');
            const audioResult = await audioProcessingPromise;
            
            console.log('✅ [并行操作3] 音频处理完成，开始AI分析...');
            console.log(`  📄 生成文件名: ${audioResult.snippetFileName || 'N/A'}`);
            
            // 开始AI分析（使用处理好的音频数据）
            await this.streamQuestionWithProcessedAudio(activeInfo, currentTime, extractionDuration, analysisMessageId, audioResult);
            
            // 智能问答完成后扣减积分
            await this.deductCredits('smart_qa');
            // 恢复输入可用，允许连续问答
            this.showContinuePrompt();
            this.questionInput.disabled = false;
            this.submitQuestionBtn.disabled = false;
            this.updateExportButtonState();
        } catch (error) {
            console.error('智能问答失败:', error);
            this.showToast(this.getI18nText('audio.smart_qa_failed'), 'error');
            // 更新分析指示器为错误消息
            this.replaceAnalysisIndicatorWithError(analysisMessageId, this.getI18nText('audio.smart_qa_failed'));
        }
        // API调用完成后同步积分
        if (window.CreditsManager && typeof window.CreditsManager.syncCreditsAfterApiCall === 'function') {
            try {
                await window.CreditsManager.syncCreditsAfterApiCall();
            } catch (syncError) {
                console.error('API调用后积分同步失败:', syncError);
            }
        }
        // 启用聊天输入，支持连续问答
        this.questionInput.disabled = false;
        this.submitQuestionBtn.disabled = false;
        this.updateExportButtonState();
    }

    // 新增：安全的积分检查（基于后端缓存验证）
    async parallelCreditsCheck(authToken, snippetDuration) {
        console.log('⚡ [积分检查] 开始后端积分验证...');
        const startTime = performance.now();
        
        try {
            // 步骤1：先尝试快速后端缓存API
            const fastCheck = await this.tryFastBackendCreditsCheck(authToken, snippetDuration);
            if (fastCheck.success) {
                const duration = performance.now() - startTime;
                console.log(`🚀 [积分检查] 后端缓存验证通过，耗时: ${duration.toFixed(1)}ms`);
                return fastCheck;
            }

            // 步骤2：快速API失败，进行完整的后端检查
            console.log(`⚡ [积分检查] 快速验证失败，进行完整后端验证...`);
            const response = await fetch('/api/check-credits-for-qa', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`,
                },
                body: JSON.stringify({
                    operation: 'smart_qa',
                    snippet_duration: snippetDuration
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            const duration = performance.now() - startTime;
            console.log(`⚡ [积分检查] 完整验证完成，耗时: ${duration.toFixed(1)}ms`);
            return result;
        } catch (error) {
            const duration = performance.now() - startTime;
            console.error(`⚡ [积分检查] 失败，耗时: ${duration.toFixed(1)}ms`, error);
            // 网络错误时返回成功，让后续流程继续（由最终的API调用来决定）
            return { success: true };
        }
    }

    // 新增：快速后端缓存积分检查
    async tryFastBackendCreditsCheck(authToken, snippetDuration) {
        try {
            console.log('🚀 [积分检查] 尝试快速后端缓存验证...');
            
            // 调用快速积分检查API
            const response = await fetch('/api/credits/get-fast', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                }
            });

            if (!response.ok) {
                return { success: false, reason: 'fast_api_failed' };
            }

            const data = await response.json();
            if (!data.success) {
                return { success: false, reason: 'fast_api_error', error: data.error };
            }

            // 获取后端缓存的积分
            const backendCredits = data.credits;
            
            // 估算所需积分（基于音频时长，录音分析使用固定值）
            let estimatedRequired;
            if (snippetDuration > 0) {
                // 随时问：基于音频时长估算
                estimatedRequired = Math.max(1, Math.ceil(snippetDuration * 0.5));
            } else {
                // 录音分析：使用固定估算值
                estimatedRequired = 5; // 录音分析通常需要5积分
            }
            
            // 设置阈值：所需积分 + 安全缓冲区
            const threshold = estimatedRequired + 10;
            
            const operationType = snippetDuration > 0 ? '随时问' : '录音分析';
            console.log(`💰 [积分检查] 后端缓存验证 - 操作:${operationType}, 积分:${backendCredits}, 需要:${estimatedRequired}, 阈值:${threshold}`);

            // 检查积分是否充足
            if (backendCredits > threshold) {
                console.log(`🚀 [积分检查] ${operationType}后端缓存积分充足(${backendCredits}>${threshold})，跳过详细检查`);
                
                // 更新前端缓存
                if (window.CreditsManager) {
                    window.CreditsManager.credits = backendCredits;
                    window.CreditsManager.cacheCredits(backendCredits);
                    window.CreditsManager.updateCreditsDisplay();
                }
                
                return { 
                    success: true, 
                    skipped: true, 
                    reason: 'sufficient_credits',
                    current_credits: backendCredits,
                    required_credits: estimatedRequired
                };
            } else {
                console.log(`⚡ [积分检查] ${operationType}后端积分${backendCredits}不足阈值${threshold}，需要详细检查`);
                return { success: false, reason: 'insufficient_credits' };
            }
            
        } catch (error) {
            console.error('快速后端积分检查失败:', error);
            return { success: false, reason: 'fast_check_error' };
        }
    }



    // 新增：并行音频处理
    async parallelAudioProcessing(activeInfo, currentTime, duration, options = {}) {
        const vbr_offset = options.vbr_offset || 0;
        const effectiveCurrentTime = currentTime + vbr_offset;

        console.log('⚡ [音频处理] 开始音频片段生成...');
        const startTime = performance.now();
        
        // 🔍 时间范围计算和验证
        const snippetStartTime = Math.max(0, currentTime - duration);
        const snippetEndTime = currentTime;
        
        console.log(`🔍 [音频处理] 时间范围验证:`);
        console.log(`  ▶️ 原始时间点: ${currentTime.toFixed(3)}s`);
        console.log(`  -> VBR补偿偏移: ${vbr_offset}s`);
        console.log(`  = 有效时间点: ${effectiveCurrentTime.toFixed(3)}s`);
        console.log(`  ⏱️ 提取时长: ${duration}s`);
        console.log(`  🎯 意图内容范围: ${snippetStartTime.toFixed(3)}s - ${snippetEndTime.toFixed(3)}s`);
        console.log(`  🚀 实际请求范围: ${(effectiveCurrentTime - duration).toFixed(3)}s - ${effectiveCurrentTime.toFixed(3)}s`);
        
        try {
            // 检查是否为本地文件
            if (activeInfo.fileId.startsWith('local_') && this.uploadedFileReference) {
                // 本地文件：并行提取音频片段
                console.log('📁 [音频处理] 本地文件模式，开始提取音频片段...');
                const snippetBlob = await this.extractLocalAudioSnippet(this.uploadedFileReference, effectiveCurrentTime, duration);
                
                if (!snippetBlob) {
                    throw new Error('音频片段提取失败');
                }

                // 并行执行：base64转换 + 播放器准备
                const snippetBase64Promise = this.blobToBase64(snippetBlob);
                const snippetUrl = URL.createObjectURL(snippetBlob);

                // 文件名基于意图内容范围，保持UI直观
                const snippetFileName = `片段_${Math.floor(snippetStartTime)}s-${Math.floor(snippetEndTime)}s.mp3`;
                
                console.log(`✅ [音频处理] 生成文件信息:`);
                console.log(`  📄 文件名: ${snippetFileName}`);
                console.log(`  💾 文件大小: ${snippetBlob.size}字节`);
                
                // 立即加载片段到播放器（不等待base64转换）
                const playerUpdatePromise = this.updateCurrentAudioPlayer(snippetUrl, snippetFileName, 'local_snippet_' + Date.now(), 'snippet', false);
                
                // 等待所有并行操作完成
                const [snippetBase64] = await Promise.all([snippetBase64Promise, playerUpdatePromise]);
                
                const processDuration = performance.now() - startTime;
                console.log(`⚡ [音频处理] 本地文件处理完成，耗时: ${processDuration.toFixed(1)}ms`);
                this.showToast(`${duration}秒音频片段已生成，开始AI分析...`, 'success');
                
                return {
                    type: 'local',
                    snippetBase64,
                    snippetUrl,
                    snippetFileName,
                    timeRange: {
                        start: snippetStartTime,
                        end: snippetEndTime,
                        duration: duration
                    }
                };
            } else {
                // 服务器文件：无需预处理，直接返回
                const processDuration = performance.now() - startTime;
                console.log(`⚡ [音频处理] 服务器文件无需预处理，耗时: ${processDuration.toFixed(1)}ms`);
                
                return {
                    type: 'server',
                    fileId: activeInfo.fileId,
                    timeRange: {
                        start: snippetStartTime,
                        end: snippetEndTime,
                        duration: duration
                    }
                };
            }
        } catch (error) {
            const processDuration = performance.now() - startTime;
            console.error(`⚡ [音频处理] 失败，耗时: ${processDuration.toFixed(1)}ms`, error);
            throw error;
        }
    }

    // 新增：使用预处理的音频数据进行AI分析
    async streamQuestionWithProcessedAudio(activeInfo, currentTime, snippetDuration, messageId, audioResult) {
        const authToken = localStorage.getItem('authToken');
        
        if (audioResult.type === 'local') {
            // 本地文件：使用预处理的数据
            await this.streamQuestionWithLocalSnippetOptimized(activeInfo, currentTime, snippetDuration, messageId, authToken, audioResult);
        } else {
            // 服务器文件：使用原有逻辑
            await this.streamQuestionWithServerFile(activeInfo, currentTime, snippetDuration, messageId, authToken);
        }
    }

    // 新增：优化的本地文件音频分析（跳过重复处理）
    async streamQuestionWithLocalSnippetOptimized(activeInfo, currentTime, snippetDuration, messageId, authToken, audioResult) {
        try {
            console.log('⚡ [AI分析] 使用预处理的音频数据，跳过重复处理...');
            
            // 新增：检查辅助功能免检查状态
            let skipCreditsCheck = false;
            if (window.CreditsManager && typeof window.CreditsManager.checkAuxiliarySkipStatus === 'function') {
                const skipStatus = window.CreditsManager.checkAuxiliarySkipStatus();
                skipCreditsCheck = skipStatus.canSkip;
                
                if (skipCreditsCheck) {
                    console.log(`🚀 [AI分析] 辅助功能免检查生效，剩余${skipStatus.remainingMinutes}分钟，跳过积分检查`);
                } else {
                    console.log(`🔍 [AI分析] 辅助功能免检查不生效(${skipStatus.reason})，将进行后端积分验证`);
                }
            }
            
            // 发送给服务器进行分析（音频数据已预处理）
            const response = await fetch('/api/analyze-local-audio-stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`,
                    'Accept-Language': UILanguage && UILanguage.getCurrentLanguage ? UILanguage.getCurrentLanguage() : 'zh'
                },
                body: JSON.stringify({
                    audio_data: audioResult.snippetBase64,
                    audio_format: 'wav',  
                    skip_credit_check: skipCreditsCheck,  // 使用动态判断的结果
                    current_time: currentTime,
                    snippet_duration: snippetDuration
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            console.log('⚡ [AI分析] 开始接收流式AI回复...');

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop(); // 保留不完整的行

                for (const line of lines) {
                    if (line.trim() === '') continue;
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            await this.handleQuestionStreamData(data, messageId, currentTime);
                        } catch (parseError) {
                            console.warn('解析流式数据失败:', parseError, line);
                        }
                    }
                }
            }

            // 存储问答结果
            const messageElement = document.getElementById(messageId);
            if (messageElement) {
                const content = messageElement.textContent || messageElement.innerText || '';
                if (content.trim()) {
                    this.storeQAResponse(currentTime, content);
                }
            }

            // 在问答完成后检查是否需要语法分析
            const questionInput = document.getElementById('questionInput');
            if (questionInput && questionInput.value.trim()) {
                await this.addGrammarAnalysisIfNeeded(messageId, questionInput.value.trim());
            }

        } catch (error) {
            console.error('优化的本地音频分析失败:', error);
            throw error;
        }
    }

    // 保留原有方法作为备用
    async checkCreditsWithBackend(authToken, snippetDuration) {
        return await this.parallelCreditsCheck(authToken, snippetDuration);
    }

    // 添加快速积分检查方法（优化版本）
    async quickCheckCredits(operation, audioInfo = null) {
        try {
            // 只进行本地缓存检查，不调用后端API
            if (!window.CreditsManager) {
                return true; // 默认允许
            }
            
            const cachedCredits = window.CreditsManager.getCachedCredits();
            
            // 根据操作类型和音频信息设置不同的积分需求
            let requiredCredits = 3; // 默认值
            
            if (operation === 'smart_qa' && audioInfo) {
                // 基于音频片段时长估算积分需求
                const { snippetDuration } = audioInfo;
                // 每秒音频大约需要0.5积分，最少2积分
                requiredCredits = Math.max(2, Math.ceil(snippetDuration * 0.5));
            } else {
                // 使用固定的积分需求映射
                const requiredCreditsMap = {
                    'smart_qa': 3,          // 智能问答
                    'audio_analysis': 5,    // 音频分析（通常更消耗积分）
                    'continue_conversation': 2
                };
                requiredCredits = requiredCreditsMap[operation] || 3;
            }
            
            console.log(`积分检查: 操作=${operation}, 需要=${requiredCredits}积分, 缓存=${cachedCredits}积分`);
            
            if (cachedCredits !== null && cachedCredits >= requiredCredits) {
                return true;
            }
            
            // 如果缓存积分不足或为null，进行一次快速后端检查
            try {
                const fastCredits = await window.CreditsManager.fetchCreditsFromBackendCache();
                const result = fastCredits >= requiredCredits;
                console.log(`后端积分检查: 实际=${fastCredits}积分, 需要=${requiredCredits}积分, 结果=${result}`);
                return result;
            } catch (error) {
                console.warn('快速积分检查失败，默认允许操作:', error);
                return true; // 出错时默认允许，让后端最终决定
            }
        } catch (error) {
            console.error('快速积分检查异常:', error);
            return true; // 出错时默认允许
        }
    }

    // 基于时间位置的流式问答 - 增强版，支持snippet音频播放器更新
    async streamQuestionAtTime(activeInfo, currentTime, snippetDuration, messageId) {
        // 获取认证令牌
        const authToken = localStorage.getItem('authToken');
        
        // 检查是否为本地文件
        if (activeInfo.fileId.startsWith('local_') && this.uploadedFileReference) {
            // 本地文件：前端提取音频片段
            await this.streamQuestionWithLocalSnippet(activeInfo, currentTime, snippetDuration, messageId, authToken);
        } else {
            // 服务器文件：使用原有逻辑
            await this.streamQuestionWithServerFile(activeInfo, currentTime, snippetDuration, messageId, authToken);
        }
    }



    // 新增：处理本地文件的音频片段问答（修复重复积分检查）
    async streamQuestionWithLocalSnippet(activeInfo, currentTime, snippetDuration, messageId, authToken) {
        try {
            // 直接开始音频片段提取（积分检查已在上级函数完成）
            console.log('开始提取音频片段...');
            const snippetBlob = await this.extractLocalAudioSnippet(this.uploadedFileReference, currentTime, snippetDuration);

            // 检查音频片段提取结果
            if (!snippetBlob) {
                throw new Error('音频片段提取失败');
            }

            // 并行执行：base64转换 + 播放器准备
            const snippetBase64Promise = this.blobToBase64(snippetBlob);
            const snippetUrl = URL.createObjectURL(snippetBlob);
            // 修复：使用正确的文件名格式（向前截取）
            const snippetStartTime = Math.max(0, currentTime - snippetDuration);
            const snippetFileName = `片段_${Math.floor(snippetStartTime)}s-${Math.floor(currentTime)}s.mp3`;
            
            // 立即加载片段到播放器（不等待base64转换）
            const playerUpdatePromise = this.updateCurrentAudioPlayer(snippetUrl, snippetFileName, 'local_snippet_' + Date.now(), 'snippet', false);
            
            // 等待base64转换完成
            const snippetBase64 = await snippetBase64Promise;
            
            // 等待播放器更新完成
            await playerUpdatePromise;
            this.showToast(`${snippetDuration}秒音频片段已生成，开始AI分析...`, 'success');
            // 无需自动播放，即时回放已在点击时完成
            // this.autoPlayCurrentAudio();

            // 发送给服务器进行分析
            const response = await fetch('/api/analyze-local-audio-stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`,
                    'Accept-Language': UILanguage && UILanguage.getCurrentLanguage ? UILanguage.getCurrentLanguage() : 'zh'
                },
                body: JSON.stringify({
                    audio_data: snippetBase64,
                    audio_format: 'wav',  // 修正：前端生成的是WAV格式
                    skip_credit_check: true,  // 告诉后端跳过积分检查（前端已检查）
                    prompt: "你是一个专业的英汉口译专家，能够准确理解并翻译音频内容，请先给出音频的transcript，然后进行翻译和解释，并就其中涉及的语法结构、习语和习惯用法、文化背景提供解释和分析。"
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            await this.handleQuestionStreamData(data, messageId, currentTime);
                        } catch (parseError) {
                            console.error('解析问答流式数据失败:', parseError);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('本地音频片段问答失败:', error);
            this.replaceLoadingIndicatorWithStreamContent(messageId, `分析失败: ${error.message}`, true);
        }
    }

    // 新增：处理服务器文件的原有逻辑
    async streamQuestionWithServerFile(activeInfo, currentTime, snippetDuration, messageId, authToken) {
        const response = await fetch('/api/ask-question-stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                file_id: activeInfo.fileId,
                question: "你是一个专业的英汉口译专家，能够准确理解并翻译音频内容，请先给出音频的transcript，然后进行翻译和解释，并就其中涉及的语法结构、习语和习惯用法、文化背景提供解释和分析。",
                current_time: currentTime,
                snippet_duration: snippetDuration
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        await this.handleQuestionStreamData(data, messageId, currentTime);
                    } catch (parseError) {
                        console.error('解析问答流式数据失败:', parseError);
                    }
                }
            }
        }
    }

    // 新增：前端提取音频片段 - 激进优化版（分块处理）
    async extractLocalAudioSnippet(file, currentTime, snippetDuration) {
        try {
            // 🔍 时间范围计算和验证（与即时回放保持一致）
            const snippetStartTime = Math.max(0, currentTime - snippetDuration);
            const snippetEndTime = currentTime;
            
            console.log(`🔍 [音频提取] 时间范围验证:`);
            console.log(`  ▶️ (有效)输入currentTime: ${currentTime.toFixed(3)}s`);
            console.log(`  ▶️ (有效)输入snippetDuration: ${snippetDuration}s`);
            console.log(`  📊 提取范围: ${snippetStartTime.toFixed(3)}s - ${snippetEndTime.toFixed(3)}s`);
            
            // 性能监控
            const startTime = performance.now();
            
            // 激进优化：尝试分块读取，只解码需要的部分
            if (file.size > 5 * 1024 * 1024) { // 文件大于5MB时使用分块策略
                console.log(`🔄 大文件检测(${(file.size/1024/1024).toFixed(1)}MB)，尝试分块处理...`);
                return await this.extractAudioSnippetChunked(file, currentTime, snippetDuration, startTime);
            }
            
            // 标准处理：使用默认采样率，避免重采样开销
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 读取音频文件
            const readStart = performance.now();
            const arrayBuffer = await file.arrayBuffer();
            const readTime = performance.now() - readStart;
            
            const decodeStart = performance.now();
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            const decodeTime = performance.now() - decodeStart;
            
            // 计算提取范围：从current_time向前截取duration秒
            const sampleRate = audioBuffer.sampleRate;
            const endSample = Math.round(currentTime * sampleRate);
            const startSample = Math.max(0, Math.round(endSample - (snippetDuration * sampleRate)));
            const actualEndSample = Math.min(endSample, audioBuffer.length);
            
            // 验证采样点范围
            const actualStartTime = startSample / sampleRate;
            const actualEndTime = actualEndSample / sampleRate;
            console.log(`🔍 [音频提取] 采样点验证:`);
            console.log(`  📊 采样率: ${sampleRate}Hz`);
            console.log(`  📊 采样点范围: ${startSample} - ${actualEndSample} (${actualEndSample - startSample}个采样点)`);
            console.log(`  📊 实际时间范围: ${actualStartTime.toFixed(3)}s - ${actualEndTime.toFixed(3)}s`);
            
            // 优化：保持原始质量，避免不必要的转换
            const channels = audioBuffer.numberOfChannels;
            
            // 创建新的音频buffer用于片段
            const snippetLength = actualEndSample - startSample;
            const snippetBuffer = audioContext.createBuffer(
                channels,
                snippetLength,
                sampleRate  // 保持原始采样率
            );
            
            // 复制音频数据
            for (let channel = 0; channel < channels; channel++) {
                const sourceData = audioBuffer.getChannelData(channel);
                const snippetData = snippetBuffer.getChannelData(channel);
                for (let i = 0; i < snippetLength; i++) {
                    snippetData[i] = sourceData[startSample + i];
                }
            }
            
            // 将AudioBuffer转换为WAV格式的Blob
            const convertStart = performance.now();
            const wavBlob = this.audioBufferToWav(snippetBuffer);
            const convertTime = performance.now() - convertStart;
            
            const totalTime = performance.now() - startTime;
            console.log(`⚡ 前端音频片段提取完成: 总耗时=${totalTime.toFixed(2)}ms, 读取=${readTime.toFixed(2)}ms, 解码=${decodeTime.toFixed(2)}ms, 转换=${convertTime.toFixed(2)}ms, 片段大小=${wavBlob.size}字节`);
            
            return wavBlob;
            
        } catch (error) {
            console.error('音频片段提取失败:', error);
            return null;
        }
    }

    // 新增：分块处理大文件的音频片段提取
    async extractAudioSnippetChunked(file, currentTime, snippetDuration, startTime) {
        try {
            console.log(`🚀 启用分块处理模式，文件大小: ${(file.size/1024/1024).toFixed(1)}MB`);
            
            // 🔍 时间范围计算和验证（与即时回放保持一致）
            const snippetStartTime = Math.max(0, currentTime - snippetDuration);
            const snippetEndTime = currentTime;
            
            console.log(`🔍 [分块处理] 时间范围验证:`);
            console.log(`  ▶️ 输入currentTime: ${currentTime.toFixed(3)}s`);
            console.log(`  ▶️ 输入snippetDuration: ${snippetDuration}s`);
            console.log(`  📊 目标范围: ${snippetStartTime.toFixed(3)}s - ${snippetEndTime.toFixed(3)}s`);
            
            // 获取实际音频时长
            const activeInfo = this.getActivePlayerInfo();
            const approximateDuration = activeInfo ? activeInfo.player.duration : 2564; // 从播放器获取实际时长
            const avgBitrate = (file.size * 8) / approximateDuration; // bits per second
            
            console.log(`📊 音频信息: 时长=${approximateDuration.toFixed(1)}s, 文件大小=${(file.size/1024/1024).toFixed(1)}MB, 比特率=${(avgBitrate/1000).toFixed(1)}kbps`);
            
            // 计算需要读取的字节范围（添加缓冲区）
            const bufferTime = 2; // 前后各2秒缓冲
            const startTimeWithBuffer = Math.max(0, snippetStartTime - bufferTime);
            const endTimeWithBuffer = snippetEndTime + bufferTime;
            
            const startByte = Math.floor((startTimeWithBuffer / approximateDuration) * file.size);
            const endByte = Math.ceil((endTimeWithBuffer / approximateDuration) * file.size);
            const chunkSize = endByte - startByte;
            
            console.log(`📊 分块计算:`);
            console.log(`  ⏱️ 缓冲时间范围: ${startTimeWithBuffer.toFixed(1)}s - ${endTimeWithBuffer.toFixed(1)}s`);
            console.log(`  💾 字节范围: ${startByte} - ${endByte} (${(chunkSize/1024/1024).toFixed(1)}MB)`);
            
            // 读取分块数据
            const readStart = performance.now();
            const chunk = file.slice(startByte, endByte);
            const arrayBuffer = await chunk.arrayBuffer();
            const readTime = performance.now() - readStart;
            
            // 解码分块
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const decodeStart = performance.now();
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            const decodeTime = performance.now() - decodeStart;
            
            // 在分块中定位目标片段
            const chunkDuration = audioBuffer.duration;
            // 修复：应该从currentTime向前截取snippetDuration秒
            const targetEndInChunk = (snippetEndTime - startTimeWithBuffer);
            const targetStartInChunk = Math.max(0, targetEndInChunk - snippetDuration);
            
            console.log(`🎯 分块中的目标片段:`);
            console.log(`  📊 分块时长: ${chunkDuration.toFixed(3)}s`);
            console.log(`  📊 分块中位置: ${targetStartInChunk.toFixed(3)}s - ${targetEndInChunk.toFixed(3)}s`);
            console.log(`  📊 全局时间对应: ${snippetStartTime.toFixed(3)}s - ${snippetEndTime.toFixed(3)}s`);
            console.log(`  🔍 分块偏移验证: ${startTimeWithBuffer.toFixed(3)}s + ${targetStartInChunk.toFixed(3)}s = ${(startTimeWithBuffer + targetStartInChunk).toFixed(3)}s`);
            
            const sampleRate = audioBuffer.sampleRate;
            const startSample = Math.round(targetStartInChunk * sampleRate);
            const endSample = Math.round(targetEndInChunk * sampleRate);
            const snippetLength = endSample - startSample;
            
            // 验证采样点范围
            const actualStartTimeInChunk = startSample / sampleRate;
            const actualEndTimeInChunk = endSample / sampleRate;
            console.log(`🔍 [分块处理] 采样点验证:`);
            console.log(`  📊 采样率: ${sampleRate}Hz`);
            console.log(`  📊 采样点范围: ${startSample} - ${endSample} (${snippetLength}个采样点)`);
            console.log(`  📊 分块中时间: ${actualStartTimeInChunk.toFixed(3)}s - ${actualEndTimeInChunk.toFixed(3)}s`);
            
            // 计算全局时间范围
            const globalStartTime = startTimeWithBuffer + actualStartTimeInChunk;
            const globalEndTime = startTimeWithBuffer + actualEndTimeInChunk;
            console.log(`  🌍 全局时间验证: ${globalStartTime.toFixed(3)}s - ${globalEndTime.toFixed(3)}s`);
            console.log(`  ⚖️ 时间偏差: 开始${Math.abs(globalStartTime - snippetStartTime).toFixed(3)}s, 结束${Math.abs(globalEndTime - snippetEndTime).toFixed(3)}s`);
            
            // 创建片段buffer
            const channels = audioBuffer.numberOfChannels;
            const snippetBuffer = audioContext.createBuffer(channels, snippetLength, sampleRate);
            
            // 复制数据
            for (let channel = 0; channel < channels; channel++) {
                const sourceData = audioBuffer.getChannelData(channel);
                const snippetData = snippetBuffer.getChannelData(channel);
                for (let i = 0; i < snippetLength; i++) {
                    snippetData[i] = sourceData[startSample + i] || 0;
                }
            }
            
            // 转换为WAV
            const convertStart = performance.now();
            const wavBlob = this.audioBufferToWav(snippetBuffer);
            const convertTime = performance.now() - convertStart;
            
            const totalTime = performance.now() - startTime;
            console.log(`⚡ 分块处理完成: 总耗时=${totalTime.toFixed(2)}ms, 读取=${readTime.toFixed(2)}ms, 解码=${decodeTime.toFixed(2)}ms, 转换=${convertTime.toFixed(2)}ms, 片段大小=${wavBlob.size}字节`);
            console.log(`✅ [分块处理] 最终验证: 提取了全局时间 ${snippetStartTime.toFixed(3)}s - ${snippetEndTime.toFixed(3)}s 的音频片段`);
            
            return wavBlob;
            
        } catch (error) {
            console.error('分块处理失败，回退到标准模式:', error);
            // 回退到标准处理
            return await this.extractLocalAudioSnippetStandard(file, currentTime, snippetDuration, startTime);
        }
    }

    // 标准处理方法（作为回退）
    async extractLocalAudioSnippetStandard(file, currentTime, snippetDuration, startTime) {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            const readStart = performance.now();
            const arrayBuffer = await file.arrayBuffer();
            const readTime = performance.now() - readStart;
            
            const decodeStart = performance.now();
            const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
            const decodeTime = performance.now() - decodeStart;
            
            const sampleRate = audioBuffer.sampleRate;
            const endSample = Math.round(currentTime * sampleRate);
            const startSample = Math.max(0, Math.round(endSample - (snippetDuration * sampleRate)));
            const actualEndSample = Math.min(endSample, audioBuffer.length);
            const snippetLength = actualEndSample - startSample;
            
            const channels = audioBuffer.numberOfChannels;
            const snippetBuffer = audioContext.createBuffer(channels, snippetLength, sampleRate);
            
            for (let channel = 0; channel < channels; channel++) {
                const sourceData = audioBuffer.getChannelData(channel);
                const snippetData = snippetBuffer.getChannelData(channel);
                for (let i = 0; i < snippetLength; i++) {
                    snippetData[i] = sourceData[startSample + i];
                }
            }
            
            const convertStart = performance.now();
            const wavBlob = this.audioBufferToWav(snippetBuffer);
            const convertTime = performance.now() - convertStart;
            
            const totalTime = performance.now() - startTime;
            console.log(`⚡ 标准处理完成: 总耗时=${totalTime.toFixed(2)}ms, 读取=${readTime.toFixed(2)}ms, 解码=${decodeTime.toFixed(2)}ms, 转换=${convertTime.toFixed(2)}ms, 片段大小=${wavBlob.size}字节`);
            
            return wavBlob;
            
        } catch (error) {
            console.error('标准处理也失败:', error);
            return null;
        }
    }

    // 新增：将AudioBuffer转换为WAV格式
    audioBufferToWav(buffer) {
        const length = buffer.length;
        const numberOfChannels = buffer.numberOfChannels;
        const sampleRate = buffer.sampleRate;
        const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
        const view = new DataView(arrayBuffer);
        
        // WAV文件头
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };
        
        writeString(0, 'RIFF');
        view.setUint32(4, 36 + length * numberOfChannels * 2, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, numberOfChannels, true);
        view.setUint32(24, sampleRate, true);
        view.setUint32(28, sampleRate * numberOfChannels * 2, true);
        view.setUint16(32, numberOfChannels * 2, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, length * numberOfChannels * 2, true);
        
        // 音频数据
        let offset = 44;
        for (let i = 0; i < length; i++) {
            for (let channel = 0; channel < numberOfChannels; channel++) {
                const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }
        }
        
        return new Blob([arrayBuffer], { type: 'audio/wav' });
    }

    // 新增：Blob转base64
    blobToBase64(blob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(blob);
            reader.onload = () => {
                // 移除data:audio/wav;base64,前缀
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = error => reject(error);
        });
    }

    // 处理问答流式数据 - 增强版，支持snippet播放器更新
    async handleQuestionStreamData(data, messageId, currentTime) {
        const snippetDuration = this.getSnippetDuration(); // 获取用户设置的时长
        if (data.type === 'start') {
            // 开始时保持加载指示器
        } else if (data.type === 'snippet_ready') {
            // 立即加载snippet到current audio playback，让用户可以随时播放
            if (data.snippet_info && data.snippet_info.snippet_url) {
                // 修复：使用正确的文件名格式（向前截取）
                const snippetStartTime = Math.max(0, currentTime - snippetDuration);
                const snippetFileName = `片段_${Math.floor(snippetStartTime)}s-${Math.floor(currentTime)}s.mp3`;
                await this.updateCurrentAudioPlayer(
                    data.snippet_info.snippet_url, 
                    snippetFileName, 
                    data.snippet_info.snippet_file_id,
                    'snippet',
                    false // 不立即切换播放器，避免打断当前回放
                );
                console.log('✅ Snippet已自动加载到Current Audio Playback');
                this.showToast(`${snippetDuration}秒音频片段已生成，开始AI分析...`, 'success');
                
                // 无需自动播放，即时回放已在点击时完成
                // this.autoPlayCurrentAudio();
            }
        } else if (data.type === 'chunk') {
            // 第一次收到chunk时，替换加载指示器
            this.replaceLoadingIndicatorWithStreamContent(messageId, data.accumulated);
        } else if (data.type === 'complete') {
            this.replaceLoadingIndicatorWithStreamContent(messageId, data.final_content, true);
            // 存储问答响应数据
            this.storeQAResponse(currentTime, data.final_content);
            // 启用聊天输入和发送按钮，支持后续连续问答
            this.questionInput.disabled = false;
            this.submitQuestionBtn.disabled = false;
            this.updateExportButtonState();
        } else if (data.type === 'error') {
            console.error('问答流式数据错误:', data.error);
            const errorPrefix = this.getI18nText('audio.analysis_failed_prefix');
            this.replaceLoadingIndicatorWithStreamContent(messageId, `${errorPrefix}${data.error}`, true);
        }
    }

    // 提交问题 - 增强版，处理不同音频上下文
    async submitQuestion() {
        const question = this.questionInput.value.trim();
        if (!question) return;

        const activeInfo = this.getActivePlayerInfo();
        if (!activeInfo) {
            this.showToast('请先选择音频文件', 'error');
            return;
        }

        // 隐藏继续对话提示
        this.hideContinuePrompt();

        // 立即清空输入框并显示用户消息和AI分析指示器
        this.questionInput.value = '';
        this.addChatMessage('user', question);
        const analysisMessageId = this.showChatAnalysisIndicator();

        try {
            // 在后台检查积分
            const hasCredits = await this.checkCredits('smart_qa');
            if (!hasCredits) {
                // 积分不足时，替换AI分析指示器为错误消息
                this.replaceAnalysisIndicatorWithError(analysisMessageId, '积分不足，无法继续对话');
                return;
            }

            // 积分检查通过，开始AI对话
            await this.streamContinueConversation(activeInfo, question, analysisMessageId);
            // 智能问答完成后扣减积分
            await this.deductCredits('smart_qa');
        } catch (error) {
            console.error('对话失败:', error);
            this.showToast(this.getI18nText('audio.conversation_failed'), 'error');
            // 更新分析指示器为错误消息
            this.replaceAnalysisIndicatorWithError(analysisMessageId, this.getI18nText('audio.conversation_failed'));
        } finally {
            // API调用完成后同步积分
            if (window.CreditsManager && typeof window.CreditsManager.syncCreditsAfterApiCall === 'function') {
                try {
                    await window.CreditsManager.syncCreditsAfterApiCall();
                } catch (syncError) {
                    console.error('API调用后积分同步失败:', syncError);
                }
            }
        }
    }

    // 流式继续对话
    async streamContinueConversation(activeInfo, question, analysisMessageId) {
        // 获取认证令牌
        const authToken = localStorage.getItem('authToken');
        
        // 获取用户设置的音频片段时长
        const snippetDuration = this.getSnippetDuration();
        
        // 检查是否为本地文件
        if (activeInfo.fileId.startsWith('local_') && this.uploadedFileReference) {
            // 本地文件：使用完整音频进行对话
            await this.streamContinueConversationWithLocal(activeInfo, question, analysisMessageId, authToken);
        } else {
            // 服务器文件：使用原有逻辑
            await this.streamContinueConversationWithServer(activeInfo, question, analysisMessageId, authToken, snippetDuration);
        }
    }

    // 新增：本地文件的继续对话
    async streamContinueConversationWithLocal(activeInfo, question, analysisMessageId, authToken) {
        try {
            // 使用完整的音频文件进行对话
            const audioBase64 = await this.fileToBase64(this.uploadedFileReference);
            const fileExtension = this.uploadedFileReference.name.split('.').pop().toLowerCase();
            const audioFormat = {
                'mp3': 'mp3', 'wav': 'wav', 'mp4': 'mp4', 
                'm4a': 'aac', 'ogg': 'ogg', 'webm': 'mp3'
            }[fileExtension] || 'mp3';

            const response = await fetch('/api/analyze-local-audio-stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`,
                    'Accept-Language': UILanguage && UILanguage.getCurrentLanguage ? UILanguage.getCurrentLanguage() : 'zh'
                },
                body: JSON.stringify({
                    audio_data: audioBase64,
                    audio_format: audioFormat,
                    prompt: question
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            this.handleConversationStreamData(data, analysisMessageId);
                        } catch (parseError) {
                            console.error('解析对话流式数据失败:', parseError);
                        }
                    }
                }
            }
        } catch (error) {
            console.error('本地文件继续对话失败:', error);
            this.replaceLoadingIndicatorWithStreamContent(analysisMessageId, `对话失败: ${error.message}`, true);
        }
    }

    // 新增：服务器文件的继续对话（原有逻辑）
    async streamContinueConversationWithServer(activeInfo, question, analysisMessageId, authToken, snippetDuration) {
        const response = await fetch('/api/continue-conversation-stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`,
                'Accept-Language': UILanguage && UILanguage.getCurrentLanguage ? UILanguage.getCurrentLanguage() : 'zh'
            },
            body: JSON.stringify({
                file_id: activeInfo.fileId,
                message: question,
                snippet_duration: snippetDuration  // 传递音频片段时长
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 使用传入的analysisMessageId而不是创建新的消息
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    try {
                        const data = JSON.parse(line.slice(6));
                        this.handleConversationStreamData(data, analysisMessageId);
                    } catch (parseError) {
                        console.error('解析对话流式数据失败:', parseError);
                    }
                }
            }
        }
    }

    // 处理对话流式数据
    handleConversationStreamData(data, messageId) {
        if (data.type === 'start') {
            // 开始时保持加载指示器
        } else if (data.type === 'chunk') {
            // 第一次收到chunk时，替换加载指示器
            this.replaceLoadingIndicatorWithStreamContent(messageId, data.accumulated);
        } else if (data.type === 'complete') {
            this.replaceLoadingIndicatorWithStreamContent(messageId, data.final_content, true);
            
            // 智能问答完成后，检查是否需要语法分析
            this.checkForGrammarAnalysisAfterConversation(messageId);
        } else if (data.type === 'error') {
            this.replaceLoadingIndicatorWithStreamContent(messageId, `${this.getI18nText('audio.conversation_failed_prefix')}: ${data.error}`, true);
        }
    }



    // 添加聊天消息
    addChatMessage(type, content) {
        const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message message-${type}`;
        messageDiv.id = messageId;

        const bubbleDiv = document.createElement('div');
        bubbleDiv.className = `message-bubble bubble-${type}`;
        bubbleDiv.innerHTML = this.formatTextContent(content);

        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = new Date().toLocaleTimeString();

        messageDiv.appendChild(bubbleDiv);
        messageDiv.appendChild(timeDiv);
        this.chatMessages.appendChild(messageDiv);

        this.scrollToBottom(this.chatMessages);
        return messageId;
    }

    // 添加流式聊天消息
    addStreamingChatMessage(type, initialContent) {
        const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message message-${type}`;
        messageDiv.id = messageId;

        const bubbleDiv = document.createElement('div');
        bubbleDiv.className = `message-bubble bubble-${type}`;
        bubbleDiv.innerHTML = this.formatTextContent(initialContent);

        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = new Date().toLocaleTimeString();

        messageDiv.appendChild(bubbleDiv);
        messageDiv.appendChild(timeDiv);
        this.chatMessages.appendChild(messageDiv);

        this.scrollToBottom(this.chatMessages);
        return messageId;
    }

    // 更新音频信息
    updateAudioInfo(playerType) {
        const player = playerType === 'uploaded' ? this.uploadedPlayer : this.recordingPlayer;
        const durationElement = playerType === 'uploaded' ? this.uploadedDuration : this.recordingDurationDisplay;
        const totalTimeElement = playerType === 'uploaded' ? this.uploadedTotalTime : this.recordingTotalTime;

        if (!isNaN(player.duration)) {
            const duration = this.formatTime(player.duration);
            durationElement.textContent = duration;
            totalTimeElement.textContent = duration;
        }
    }

    // 播放/暂停切换
    togglePlayPause(playerType) {
        const player = playerType === 'uploaded' ? this.uploadedPlayer : this.recordingPlayer;
        
        if (player.paused) {
            player.play();
            this.setActivePlayer(playerType);
        } else {
            player.pause();
        }
    }

    // 快退5秒
    rewind(playerType) {
        const player = playerType === 'uploaded' ? this.uploadedPlayer : this.recordingPlayer;
        player.currentTime = Math.max(0, player.currentTime - 5);
    }

    // 快进10秒
    fastForward(playerType) {
        const player = playerType === 'uploaded' ? this.uploadedPlayer : this.recordingPlayer;
        player.currentTime = Math.min(player.duration, player.currentTime + 10);
    }

    // 更新进度
    updateProgress(playerType) {
        const player = playerType === 'uploaded' ? this.uploadedPlayer : this.recordingPlayer;
        const currentTimeElement = playerType === 'uploaded' ? this.uploadedCurrentTime : this.recordingCurrentTime;
        const progressFill = playerType === 'uploaded' ? this.uploadedProgressFill : this.recordingProgressFill;
        const progressHandle = playerType === 'uploaded' ? this.uploadedProgressHandle : this.recordingProgressHandle;

        if (!isNaN(player.duration) && player.duration > 0) {
            const progress = (player.currentTime / player.duration) * 100;
            progressFill.style.width = progress + '%';
            progressHandle.style.left = progress + '%';
            currentTimeElement.textContent = this.formatTime(player.currentTime);
        }
    }

    // 开始进度条拖拽
    startProgressDrag(event, playerType) {
        event.preventDefault();
        if (playerType === 'uploaded') {
            this.isDraggingUploaded = true;
        } else {
            this.isDraggingRecording = true;
        }
        this.updateProgressPosition(event, playerType);
    }

    // 更新进度条拖拽
    updateProgressDrag(event) {
        if (this.isDraggingUploaded) {
            this.updateProgressPosition(event, 'uploaded');
        } else if (this.isDraggingRecording) {
            this.updateProgressPosition(event, 'recording');
        }
    }

    // 结束进度条拖拽
    endProgressDrag() {
        this.isDraggingUploaded = false;
        this.isDraggingRecording = false;
    }

    // 更新进度条位置
    updateProgressPosition(event, playerType) {
        const progressBar = playerType === 'uploaded' ? this.uploadedProgressBar : this.recordingProgressBar;
        const player = playerType === 'uploaded' ? this.uploadedPlayer : this.recordingPlayer;

        const rect = progressBar.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const percentage = Math.max(0, Math.min(1, x / rect.width));
        
        if (!isNaN(player.duration)) {
            player.currentTime = percentage * player.duration;
        }
    }

    // 音频播放结束
    onAudioEnded(playerType) {
        const playPauseBtn = playerType === 'uploaded' ? this.uploadedPlayPauseBtn : this.recordingPlayPauseBtn;
        playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
    }

    // 更新流式内容
    updateStreamingContent(container, content) {
        // 移除加载动画
        const loadingElement = container.querySelector('.ai-loading');
        if (loadingElement) {
            loadingElement.remove();
        }

        // 更新内容
        let contentDiv = container.querySelector('.streaming-text');
        if (!contentDiv) {
            contentDiv = document.createElement('div');
            contentDiv.className = 'streaming-text';
            container.appendChild(contentDiv);
        }
        
        contentDiv.innerHTML = this.formatTextContent(content);
        
        // 滚动到底部
        container.scrollTop = container.scrollHeight;
    }

    // 滚动到底部
    scrollToBottom(container) {
        setTimeout(() => {
            container.scrollTop = container.scrollHeight;
        }, 10);
    }

    // 格式化时间
    formatTime(seconds) {
        if (!seconds || isNaN(seconds)) return '00:00';
        
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    // 显示提示
    showToast(message, type = 'info') {
        this.toastMessage.textContent = message;
        this.statusToast.className = `toast ${type}`;
        this.statusToast.classList.remove('hidden');
        
        // 自动隐藏
        setTimeout(() => {
            this.hideToast();
        }, 3000);
    }

    // 隐藏提示
    hideToast() {
        this.statusToast.classList.add('hidden');
    }

    // 显示加载遮罩
    showLoadingOverlay(message = '处理中...') {
        this.loadingOverlay.querySelector('p').textContent = message;
        this.loadingOverlay.classList.remove('hidden');
    }

    // 隐藏加载遮罩
    hideLoadingOverlay() {
        this.loadingOverlay.classList.add('hidden');
    }

    // 显示上传指示器（简化模式）
    showUploadIndicator() {
        this.showUploadStatus();
    }

    // 隐藏上传指示器
    hideUploadIndicator() {
        this.hideUploadStatus();
    }

    // 显示上传进度条
    showUploadProgress() {
        if (this.uploadProgress) {
            console.log('显示上传进度条'); // 调试日志
            this.uploadProgress.classList.remove('hidden');
            this.uploadProgress.style.display = 'block'; // 覆盖内联样式
            this.updateUploadProgress(0, 'uploading');
        }
    }

    // 隐藏上传进度条
    hideUploadProgress() {
        if (this.uploadProgress) {
            this.uploadProgress.classList.add('hidden');
            this.uploadProgress.style.display = 'none'; // 重新隐藏
        }
    }

    // 显示简化上传状态提示
    showUploadStatus() {
        if (this.uploadStatus) {
            console.log('显示简化上传状态'); // 调试日志
            this.uploadStatus.classList.remove('hidden');
            this.uploadStatus.style.display = 'block';
            this.updateUploadStatus('uploading');
        }
    }

    // 隐藏简化上传状态提示
    hideUploadStatus() {
        if (this.uploadStatus) {
            this.uploadStatus.classList.add('hidden');
            this.uploadStatus.style.display = 'none';
        }
    }

    // 更新简化上传状态
    updateUploadStatus(status) {
        if (this.uploadStatusText) {
            if (status === 'uploading') {
                this.uploadStatusText.textContent = this.getI18nText('audio.uploading', '上传中');
            } else if (status === 'processing') {
                this.uploadStatusText.textContent = this.getI18nText('audio.processing', '处理中');
            } else if (status === 'completed') {
                this.uploadStatusText.textContent = this.getI18nText('audio.upload_success', '上传完成');
            }
        }
    }

    // 更新上传进度
    updateUploadProgress(percent, status = 'uploading') {
        const percentage = Math.round(percent);
        if (this.uploadProgressPercent) {
            this.uploadProgressPercent.textContent = `${percentage}%`;
        }
        if (this.uploadProgressFill) {
            this.uploadProgressFill.style.width = `${percentage}%`;
        }
        
        // 更新状态文字
        const uploadLabel = this.uploadProgress?.querySelector('[data-i18n="audio.uploading"]');
        if (uploadLabel) {
            if (status === 'uploading') {
                uploadLabel.textContent = this.getI18nText('audio.uploading', '上传中');
            } else if (status === 'processing') {
                uploadLabel.textContent = this.getI18nText('audio.processing', '处理中');
            } else if (status === 'completed') {
                uploadLabel.textContent = this.getI18nText('audio.upload_success', '上传完成');
            }
        }
    }

    // 格式化文本内容
    formatTextContent(text) {
        if (!text) return '';
        
        // 处理换行
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    // 带重试机制的音频文件加载
    async loadAudioFileWithRetry(audioUrl, filename, playerType, maxRetries = 3) {
        return new Promise((resolve, reject) => {
            let retryCount = 0;
            
            const tryLoad = () => {
                this.loadAudioFile(audioUrl, filename, playerType);
                
                const player = playerType === 'uploaded' ? this.uploadedPlayer : this.recordingPlayer;
                
                const onLoadSuccess = () => {
                    player.removeEventListener('loadedmetadata', onLoadSuccess);
                    player.removeEventListener('error', onLoadError);
                    resolve();
                };
                
                const onLoadError = (error) => {
                    player.removeEventListener('loadedmetadata', onLoadSuccess);
                    player.removeEventListener('error', onLoadError);
                    
                    retryCount++;
                    if (retryCount < maxRetries) {
                        console.warn(`音频加载失败，第${retryCount}次重试...`);
                        setTimeout(tryLoad, 1000);
                    } else {
                        console.error('音频加载彻底失败:', error);
                        reject(new Error('音频文件加载失败'));
                    }
                };
                
                player.addEventListener('loadedmetadata', onLoadSuccess);
                player.addEventListener('error', onLoadError);
                
                // 强制加载
                player.load();
            };
            
            tryLoad();
        });
    }

    // 加载音频文件
    loadAudioFile(audioUrl, filename, playerType) {
        
        if (playerType === 'uploaded') {
            this.uploadedPlayer.src = audioUrl;
            this.forceUpdateFileName(this.uploadedFileName, filename);
            this.uploadedPlayerContainer.classList.remove('hidden');
            // 设置播放速度
            this.uploadedPlayer.playbackRate = this.currentPlaybackSpeed;
        } else if (playerType === 'recording') {
            this.recordingPlayer.src = audioUrl;
            this.forceUpdateFileName(this.recordingFileName, filename);
            this.recordingPlayerContainer.classList.remove('hidden');
        }
    }

    // 截断文件名并添加省略号
    truncateFileName(filename, maxLength = 40) {
        if (!filename || filename === 'undefined' || filename === 'null') {
            return 'Unknown File';
        }
        if (filename.length <= maxLength) return filename;
        
        // 保留文件扩展名
        const lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            const name = filename.substring(0, lastDotIndex);
            const ext = filename.substring(lastDotIndex);
            const maxNameLength = maxLength - ext.length - 3; // 3 for "..."
            if (maxNameLength > 0) {
                return name.substring(0, maxNameLength) + '...' + ext;
            }
        }
        
        // 如果没有扩展名或扩展名太长
        return filename.substring(0, maxLength - 3) + '...';
    }

    // 强制更新文件名显示
    forceUpdateFileName(element, filename) {
        const displayName = this.truncateFileName(filename);
        
        // 移除国际化属性
        element.removeAttribute('data-i18n');
        
        // 设置文件名
        element.textContent = displayName;
        
        // 延迟再次设置，确保国际化系统不会覆盖
        setTimeout(() => {
            element.textContent = displayName;
        }, 100);
        
        setTimeout(() => {
            element.textContent = displayName;
        }, 500);
    }

    // 更新当前音频播放器显示
    async updateCurrentAudioPlayer(audioUrl, fileName, fileId, context, switchImmediately = true, callback) {
        if (context !== 'podcast') {
            this.currentPlayingEpisodeIndex = -1;
            if (window.PodcastManager) {
                window.PodcastManager.updatePlayButtons(-1);
            }
        }
        
        this.recordingPlayer.src = audioUrl;
        this.forceUpdateFileName(this.recordingFileName, fileName);
        this.recordingFileName.title = fileName;
        this.recordingPlayerContainer.classList.remove('hidden');
        this.currentAudioContext = context;

        // 重要：设置录音文件ID，用于后续分析
        if (context === 'recording') {
            this.recordingFileId = fileId;
            console.log(`🎤 [录音播放器] 设置录音文件ID: ${fileId}`);
        }

        this.recordingPlayer.load(); // 加载音频
        
        // 根据switchImmediately参数决定是否自动播放
        if (switchImmediately) {
            try {
                await this.recordingPlayer.play(); // 立即播放
                if (callback) callback(true); // 成功, 回调并告知正在播放
            } catch (error) {
                console.warn("Autoplay was prevented:", error);
                if (callback) callback(false); // 失败, 回调并告知未在播放
            }
        } else {
            // 只加载不播放，等待用户手动操作
            console.log(`音频已加载到${context}播放器，等待手动播放: ${fileName}`);
            if (callback) callback(false); // 告知回调：音频已加载但未播放
        }
    }

    // 显示AI分析指示器
    showQAAnalysisIndicatorOnly() {
        const container = this.chatMessages;
        
        // 只添加AI分析指示器，不添加用户消息
        const messageId = Date.now().toString();
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message ai-message';
        messageDiv.setAttribute('data-message-id', messageId);
        
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'ai-loading';
        
        const dotsDiv = document.createElement('div');
        dotsDiv.className = 'loading-dots';
        for (let i = 0; i < 3; i++) {
            const dot = document.createElement('div');
            dot.className = 'dot';
            dotsDiv.appendChild(dot);
        }
        
        const textSpan = document.createElement('span');
        textSpan.className = 'loading-text';
        textSpan.textContent = this.getI18nText('audio.analyzing_audio');
        
        loadingDiv.appendChild(dotsDiv);
        loadingDiv.appendChild(textSpan);
        messageDiv.appendChild(loadingDiv);
        
        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
        
        return messageId;
    }

    // 移除分析指示器
    removeAnalysisIndicator(messageId) {
        const messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageDiv) {
            messageDiv.remove();
        }
    }

    // 将分析指示器替换为错误消息
    replaceAnalysisIndicatorWithError(messageId, errorMessage) {
        const messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageDiv) {
            // 替换为错误消息格式
            messageDiv.className = 'chat-message ai-message';
            
            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble bubble-ai error-bubble';
            bubbleDiv.innerHTML = errorMessage;

            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString();

            // 清空原内容并添加新内容
            messageDiv.innerHTML = '';
            messageDiv.appendChild(bubbleDiv);
            messageDiv.appendChild(timeDiv);
            
            this.scrollToBottom(this.chatMessages);
        }
    }

    // 显示智能问答分析指示器（保留原版本以备其他地方使用）
    showQAAnalysisIndicator(message) {
        const container = this.chatMessages;
        
        // 添加用户消息
        this.addChatMessage('user', message);
        
        // 添加AI分析指示器
        const messageId = Date.now().toString();
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message ai-message';
        messageDiv.setAttribute('data-message-id', messageId);
        
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'ai-loading';
        
        const dotsDiv = document.createElement('div');
        dotsDiv.className = 'loading-dots';
        for (let i = 0; i < 3; i++) {
            const dot = document.createElement('div');
            dot.className = 'dot';
            dotsDiv.appendChild(dot);
        }
        
        const textSpan = document.createElement('span');
        textSpan.className = 'loading-text';
        textSpan.textContent = this.getI18nText('audio.analyzing_audio');
        
        loadingDiv.appendChild(dotsDiv);
        loadingDiv.appendChild(textSpan);
        messageDiv.appendChild(loadingDiv);
        
        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
        
        return messageId;
    }

    // 显示聊天中的AI分析指示器（不包含用户消息）
    showChatAnalysisIndicator() {
        const container = this.chatMessages;
        
        // 只添加AI分析指示器
        const messageId = Date.now().toString();
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message ai-message';
        messageDiv.setAttribute('data-message-id', messageId);
        
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'ai-loading';
        
        const dotsDiv = document.createElement('div');
        dotsDiv.className = 'loading-dots';
        for (let i = 0; i < 3; i++) {
            const dot = document.createElement('div');
            dot.className = 'dot';
            dotsDiv.appendChild(dot);
        }
        
        const textSpan = document.createElement('span');
        textSpan.className = 'loading-text';
        textSpan.textContent = this.getI18nText('audio.thinking');
        
        loadingDiv.appendChild(dotsDiv);
        loadingDiv.appendChild(textSpan);
        messageDiv.appendChild(loadingDiv);
        
        container.appendChild(messageDiv);
        container.scrollTop = container.scrollHeight;
        
        return messageId;
    }

    // 启用AI控制
    enableAIControls() {
        this.questionBtn.disabled = false;
        // 仅启用 Ask Anytime 按钮，后续问答输入在分析完成后启用
        this.updateExportButtonState();
    }
    
    // 更新导出按钮状态
    updateExportButtonState() {
        if (this.exportQABtn) {
            this.exportQABtn.disabled = this.qaResponses.length === 0;
        }
    }

    // 替换加载指示器为流式内容
    replaceLoadingIndicatorWithStreamContent(messageId, content, isComplete = false) {
        
        // 查找加载指示器消息
        const messageDiv = document.querySelector(`[data-message-id="${messageId}"]`);
        if (messageDiv) {
            
            // 替换为正常的聊天消息格式
            messageDiv.className = 'chat-message ai-message';
            
            const bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'message-bubble bubble-ai';
            bubbleDiv.innerHTML = this.formatTextContent(content);

            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString();

            // 清空原内容并添加新内容
            messageDiv.innerHTML = '';
            messageDiv.appendChild(bubbleDiv);
            messageDiv.appendChild(timeDiv);
            
            // 如果是第一次替换，还需要给元素设置ID供后续更新使用
            if (!messageDiv.id) {
                messageDiv.id = 'msg_' + messageId;
            }
            
            this.scrollToBottom(this.chatMessages);
        } else {
            console.error('找不到加载指示器消息元素:', messageId);
        }
    }

    // 更新流式聊天消息
    updateStreamingChatMessage(messageId, content, isComplete = false) {
        const messageElement = document.getElementById(messageId);
        if (messageElement) {
            const bubbleElement = messageElement.querySelector('.message-bubble');
            if (bubbleElement) {
                // 如果内容以前缀开始，移除前缀
                const cleanContent = content.replace(/^(##\s*)?AI音频助手回复[:：]?\s*/, '');
                
                if (isComplete) {
                    // 完成时格式化内容并移除加载指示器
                    bubbleElement.innerHTML = this.formatTextContent(cleanContent);
                    
                    // 移除加载指示器
                    const loadingIndicator = messageElement.querySelector('.ai-loading');
                    if (loadingIndicator) {
                        loadingIndicator.remove();
                    }
                } else {
                    // 流式更新时保持简单的HTML更新
                    bubbleElement.innerHTML = this.formatTextContent(cleanContent) + '<span class="streaming-cursor">|</span>';
                }
            }
        }
    }

    // 播放速度控制方法
    toggleSpeedOptions() {
        if (this.uploadedSpeedOptions) {
            const isShowing = this.uploadedSpeedOptions.classList.contains('show');
            
            if (isShowing) {
                // 如果当前是显示状态，则隐藏
                this.uploadedSpeedOptions.classList.remove('show');
            } else {
                // 如果当前是隐藏状态，则显示并滚动到当前播放速度
                this.uploadedSpeedOptions.classList.add('show');
                
                // 使用多个时间点尝试滚动，确保成功
                setTimeout(() => this.scrollToCurrentSpeed(), 10);  // 更快的初始尝试
                setTimeout(() => this.scrollToCurrentSpeed(), 50);
                setTimeout(() => this.scrollToCurrentSpeed(), 150);
                setTimeout(() => this.scrollToCurrentSpeed(), 300);
            }
        }
    }

    hideSpeedOptions() {
        if (this.uploadedSpeedOptions) {
            this.uploadedSpeedOptions.classList.remove('show');
        }
    }

    scrollToCurrentSpeed() {
        if (!this.uploadedSpeedOptions) return;
        
        const activeOption = this.uploadedSpeedOptions.querySelector('.speed-option.active');
        if (!activeOption) return;
        
        const container = this.uploadedSpeedOptions;
        
        // 确保容器已经显示并且有尺寸
        if (container.offsetHeight === 0) {
            // 如果容器还没有显示，再次延迟执行
            setTimeout(() => this.scrollToCurrentSpeed(), 100);
            return;
        }
        
        // 使用 requestAnimationFrame 确保在下一帧执行
        requestAnimationFrame(() => {
            const containerHeight = container.clientHeight;
            const optionHeight = activeOption.offsetHeight;
            const optionTop = activeOption.offsetTop;
            
            // 计算滚动位置，使当前选项在容器中部显示
            const scrollTop = optionTop - (containerHeight / 2) + (optionHeight / 2);
            
            // 设置滚动位置，确保在有效范围内
            const maxScrollTop = container.scrollHeight - containerHeight;
            container.scrollTop = Math.max(0, Math.min(scrollTop, maxScrollTop));
            
            console.log('Scrolling to speed option:', {
                activeSpeed: activeOption.dataset.speed,
                optionTop: optionTop,
                containerHeight: containerHeight,
                calculatedScrollTop: scrollTop,
                finalScrollTop: container.scrollTop,
                maxScrollTop: maxScrollTop
            });
        });
    }

    setPlaybackSpeed(speed) {
        this.currentPlaybackSpeed = speed;
        
        // 更新音频播放速度
        if (this.uploadedPlayer) {
            this.uploadedPlayer.playbackRate = speed;
        }
        
        // 更新显示
        this.updateSpeedDisplay();
        
        // 更新活跃选项
        this.updateActiveSpeedOption(speed);
    }

    updateSpeedDisplay() {
        if (this.uploadedSpeedDisplay) {
            const speedText = this.uploadedSpeedDisplay.querySelector('[data-i18n="audio.speed"]');
            if (speedText) {
                speedText.textContent = window.UILanguage && window.UILanguage.getCurrentLanguage() === 'en' ? 'Speed' : '速度';
            }
            this.uploadedSpeedDisplay.innerHTML = `<span data-i18n="audio.speed">${speedText ? speedText.textContent : '速度'}</span>: ${this.currentPlaybackSpeed.toFixed(1)}x`;
        }
    }

    updateActiveSpeedOption(speed) {
        if (this.uploadedSpeedOptions) {
            // 移除所有活跃状态
            const options = this.uploadedSpeedOptions.querySelectorAll('.speed-option');
            options.forEach(option => option.classList.remove('active'));
            
            // 添加活跃状态到当前选项
            const activeOption = this.uploadedSpeedOptions.querySelector(`[data-speed="${speed}"]`);
            if (activeOption) {
                activeOption.classList.add('active');
            }
        }
    }

    handleSpeedWheelChange(event) {
        const speeds = [0.5, 0.6, 0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6, 2.7, 2.8, 2.9, 3.0];
        const currentIndex = speeds.indexOf(this.currentPlaybackSpeed);
        
        let newIndex;
        if (event.deltaY < 0) {
            // 向上滚动，增加速度
            newIndex = Math.min(currentIndex + 1, speeds.length - 1);
        } else {
            // 向下滚动，减少速度
            newIndex = Math.max(currentIndex - 1, 0);
        }
        
        if (newIndex !== currentIndex) {
            this.setPlaybackSpeed(speeds[newIndex]);
        }
    }

    // 初始化播放速度控制器
    initializeSpeedControl() {
        // 确保DOM完全加载后再初始化
        setTimeout(() => {
            this.updateSpeedDisplay();
            this.updateActiveSpeedOption(1.0);
        }, 50);
    }

    // 安全的国际化方法
    getI18nText(key, fallback = '') {
        try {
            // 检查UILanguage是否存在并且已加载
            if (typeof UILanguage !== 'undefined' && UILanguage && UILanguage.getText) {
                return UILanguage.getText(key, fallback);
            }
            console.warn('UILanguage not available, using fallback for key:', key);
            return fallback;
        } catch (error) {
            console.warn('Failed to get i18n text for key:', key, error);
            return fallback;
        }
    }
    
    // 存储问答响应数据
    storeQAResponse(currentTime, content) {
        const snippetDuration = this.getSnippetDuration(); // 获取用户设置的时长
        const startTime = Math.max(0, currentTime - snippetDuration);
        const endTime = Math.min(currentTime, startTime + snippetDuration);
        
        const qaData = {
            timestamp: new Date().toISOString(),
            timeRange: `${this.formatTime(startTime)}-${this.formatTime(endTime)}`,
            startTime: startTime,
            endTime: endTime,
            content: content
        };
        
        this.qaResponses.push(qaData);
        this.updateExportButtonState();
        
        console.log('存储问答数据:', qaData);
    }
    
    // 导出问答响应
    exportQAResponses() {
        if (this.qaResponses.length === 0) {
            this.showToast('暂无问答记录可导出', 'warning');
            return;
        }
        
        let exportContent = '';
        
        this.qaResponses.forEach((qa, index) => {
            exportContent += `${qa.timeRange}\n\n`;
            exportContent += qa.content;
            
            if (index < this.qaResponses.length - 1) {
                exportContent += '\n\n---\n\n';
            }
        });
        
        // 创建并下载文件
        const fileName = this.currentUploadedFileName ? 
            `${this.currentUploadedFileName}_qa.txt` : 
            'audio_qa.txt';
            
        this.downloadTextFile(exportContent, fileName);
        this.showToast(`已导出 ${this.qaResponses.length} 条问答记录`, 'success');
    }
    
    // 下载文本文件
    downloadTextFile(content, fileName) {
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }

    // 获取用户设置的音频片段时长
    getSnippetDuration() {
        const savedDuration = localStorage.getItem('snippetDuration');
        return savedDuration ? parseInt(savedDuration) : 6; // 默认6秒
    }

    // 切换上传指示器模式（用于调试和用户偏好）
    setUploadIndicatorMode(mode) {
        if (mode === 'progress' || mode === 'simple') {
            this.uploadIndicatorMode = mode;
            console.log(`上传指示器模式已切换到: ${mode}`);
        }
    }

    initializeApp() {
        console.log('AudioApp额外功能初始化完成');
        console.log(`当前上传指示器模式: ${this.uploadIndicatorMode}`);
    }

    // 传统的上传文件分析方式（作为回退）
    async analyzeUploadedFileTraditional() {
        const analysisMessageId = this.showQAAnalysisIndicatorOnly();
        
        try {
            // 双阶段积分校验：前端缓存 -> 后端缓存
            const creditsManager = window.CreditsManager;
            const required = 1;
            let cached = creditsManager.getCachedCredits(); if (cached === null) cached = 0;
            if (cached < required) { 
                this.replaceAnalysisIndicatorWithError(analysisMessageId, this.getI18nText('audio.insufficient_credits', '积分不足，无法进行AI分析'));
                creditsManager.showInsufficientCreditsAlert(cached); 
                return; 
            }
            try { 
                const fast = await creditsManager.fetchCreditsFromBackendCache(); 
                if (fast < required) { 
                    this.replaceAnalysisIndicatorWithError(analysisMessageId, this.getI18nText('audio.insufficient_credits', '积分不足，无法进行AI分析'));
                    creditsManager.showInsufficientCreditsAlert(fast); 
                    return; 
                } 
            } catch (err) { 
                this.replaceAnalysisIndicatorWithError(analysisMessageId, this.getI18nText('audio.insufficient_credits', '积分不足，无法进行AI分析'));
                creditsManager.showInsufficientCreditsAlert(cached); 
                return; 
            }

            // 积分检查通过，开始文件处理
            const base64Audio = await this.fileToBase64(this.uploadedFileReference);

            // 获取文件格式
            const fileExtension = this.uploadedFileReference.name.split('.').pop().toLowerCase();
            const audioFormat = {
                'mp3': 'mp3', 'wav': 'wav', 'mp4': 'mp4', 
                'm4a': 'aac', 'ogg': 'ogg', 'webm': 'mp3'
            }[fileExtension] || 'mp3';

            // 发送请求
            const response = await this.streamLocalAudioAnalysis(base64Audio, audioFormat);
            
            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            this.handleRecordingAnalysisStreamData(data, analysisMessageId);
                        } catch (parseError) {
                            console.error('解析分析流式数据失败:', parseError);
                        }
                    }
                }
            }
            
            // 分析完成后显示继续对话提示
            this.showContinuePrompt();
            
            // AI分析完成后扣减积分
            await this.deductCredits('audio_analysis');
        } catch (error) {
            console.error('传统音频分析失败:', error);
            this.replaceAnalysisIndicatorWithError(analysisMessageId, this.getI18nText('audio.ai_analysis_failed'));
        }
    }

    // 新增：自动回退播放指定时长并在结束后停止
    playRewindSnippet(player, snippetDuration) {
        try {
            if (!player) {
                console.warn('[即时回放] 播放器未找到，跳过即时回放');
                return;
            }

            // 清除之前的计时器和事件监听器
            if (this.rewindPlaybackTimeout) {
                clearTimeout(this.rewindPlaybackTimeout);
                console.log('[即时回放] 清除之前的计时器');
            }
            
            if (this.timeUpdateHandler) {
                player.removeEventListener('timeupdate', this.timeUpdateHandler);
            }
            if (this.pauseHandler) {
                player.removeEventListener('pause', this.pauseHandler);
            }

            const originalTime = player.currentTime;
            const startTime = Math.max(0, originalTime - snippetDuration);
            const endTime = originalTime;

            console.log(`🔍 [即时回放] 时间范围验证:`);
            console.log(`  ▶️ 原始时间: ${originalTime.toFixed(3)}s (${this.formatTime(originalTime)})`);
            console.log(`  ▶️ 回退时长: ${snippetDuration}s`);
            console.log(`  📊 回放范围: ${startTime.toFixed(3)}s - ${endTime.toFixed(3)}s`);
            console.log(`  📄 对应文件名: 片段_${Math.floor(startTime)}s-${Math.floor(endTime)}s.mp3`);
            console.log(`  🎵 音频总时长: ${player.duration ? player.duration.toFixed(1) : '未知'}s`);

            player.currentTime = startTime;
            player.play().then(() => {
                console.log(`✅ [即时回放] 开始播放成功，从 ${startTime.toFixed(3)}s 开始`);
            }).catch(err => {
                console.warn('[即时回放] 自动回退播放失败:', err);
            });

            // 添加播放状态监听
            this.timeUpdateHandler = () => {
                const progress = player.currentTime - startTime;
                const remaining = snippetDuration - progress;
                if (progress % 1 < 0.1) { // 每秒记录一次，避免日志过多
                    console.log(`🔄 [即时回放] 播放进度: ${player.currentTime.toFixed(1)}s (进度${progress.toFixed(1)}s/${snippetDuration}s, 剩余${remaining.toFixed(1)}s)`);
                }
            };
            this.pauseHandler = () => {
                const playedDuration = player.currentTime - startTime;
                console.log(`⏸️ [即时回放] 播放被暂停，时间: ${player.currentTime.toFixed(3)}s，已播放: ${playedDuration.toFixed(3)}s`);
                // 检查是否是其他代码暂停了播放
                console.trace('[即时回放] 暂停调用栈');
            };
            
            player.addEventListener('timeupdate', this.timeUpdateHandler);
            player.addEventListener('pause', this.pauseHandler);

            // 播放完成后自动暂停 - 使用用户设定的时长
            console.log(`⏰ [即时回放] 设置 ${snippetDuration}s 计时器，将在 ${(Date.now() + snippetDuration * 1000)} 触发`);
            this.rewindPlaybackTimeout = setTimeout(() => {
                const actualPlayedDuration = player.currentTime - startTime;
                console.log(`⏰ [即时回放] 计时器触发:`);
                console.log(`  ▶️ 当前播放时间: ${player.currentTime.toFixed(3)}s`);
                console.log(`  📊 实际播放时长: ${actualPlayedDuration.toFixed(3)}s / ${snippetDuration}s`);
                console.log(`  📍 目标结束时间: ${endTime.toFixed(3)}s`);
                
                if (!player.paused) {
                    player.pause();
                    console.log(`✅ [即时回放] 播放完成，已在 ${snippetDuration}s 后自动暂停`);
                    console.log(`  📊 最终播放范围: ${startTime.toFixed(3)}s - ${player.currentTime.toFixed(3)}s`);
                } else {
                    console.log(`⚠️ [即时回放] 播放已在计时器触发前被暂停，当前时间: ${player.currentTime.toFixed(3)}s`);
                }
                
                // 清理事件监听器
                player.removeEventListener('timeupdate', this.timeUpdateHandler);
                player.removeEventListener('pause', this.pauseHandler);
                this.timeUpdateHandler = null;
                this.pauseHandler = null;
                
                console.log(`🧹 [即时回放] 清理完成`);
            }, snippetDuration * 1000);

        } catch (error) {
            console.warn('[即时回放] playRewindSnippet错误:', error);
            console.error(error);
        }
    }
}

// 全局导出
window.AudioApp = AudioApp;