document.addEventListener('DOMContentLoaded', function() {
  // 初始化调试日志功能
  const debugInfo = document.getElementById('debug-info');
  
  // 支持两种环境配置格式
  const DEBUG_MODE = (window.ENV && window.ENV.APP && typeof window.ENV.APP.DEBUG_MODE !== 'undefined') 
    ? window.ENV.APP.DEBUG_MODE 
    : false; // 默认关闭调试模式
  
  function logDebug(message, type = 'info') {
    if (!DEBUG_MODE) return;
    
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // 只有在页面上显示重要日志，并确保 debugInfo 元素存在
    if ((type === 'error' || type === 'important') && debugInfo) {
      const timestamp = new Date().toLocaleTimeString();
      const logElement = document.createElement('div');
      logElement.innerHTML = `<span style="color: ${type === 'error' ? 'red' : 'yellow'}">[${timestamp}][${type}]</span> ${message}`;
      debugInfo.appendChild(logElement);
      debugInfo.style.display = 'block';
      
      // 滚动到最新日志
      debugInfo.scrollTop = debugInfo.scrollHeight;
    }
  }
  
  // 确保supabase已经加载
  function ensureSupabaseLoaded() {
    return new Promise((resolve, reject) => {
      // 检查是否已经有全局的supabase对象
      if (typeof supabase !== 'undefined') {
        resolve();
        return;
      }
      
      console.log("Waiting for Supabase to load...");
      
      // 设置最大等待时间为30秒（减少超时时间以提高用户体验）
      const maxWaitTime = 30000;
      const checkInterval = 500; // 增加检查间隔，减少检查频率
      let waitTime = 0;
      
      const checkSupabase = setInterval(() => {
        waitTime += checkInterval;
        
        if (typeof supabase !== 'undefined') {
          clearInterval(checkSupabase);
          console.log("Supabase loaded successfully!");
          resolve();
          return;
        }
        
        // 每5秒输出一次等待状态，减少日志噪音
        if (waitTime % 5000 === 0) {
          console.log(`Still waiting for Supabase... (${waitTime/1000}s elapsed)`);
        }
        
        // 15秒后尝试使用备用方法初始化Supabase
        if (waitTime === 15000) {
          console.log("Trying fallback Supabase initialization...");
          try {
            // 从动态配置获取Supabase配置，不使用硬编码
            const SUPABASE_URL = window._env_?.SUPABASE_URL || 
              (window.ENV && window.ENV.SUPABASE && window.ENV.SUPABASE.URL);
            
            const SUPABASE_ANON_KEY = window._env_?.SUPABASE_ANON_KEY || 
              (window.ENV && window.ENV.SUPABASE && window.ENV.SUPABASE.ANON_KEY);
            
            // 如果没有配置，不能继续
            if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
              throw new Error('Supabase配置缺失，请检查环境配置');
            }
            
            // 如果全局supabase对象存在但createClient不存在，可能是CDN加载不完整
            if (typeof supabase !== 'undefined' && typeof supabase.createClient === 'function') {
              window.supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
              console.log("Supabase initialized using fallback method!");
              clearInterval(checkSupabase);
              resolve();
              return;
            }
          } catch (error) {
            console.error("Fallback initialization failed:", error);
          }
        }
        
        if (waitTime >= maxWaitTime) {
          clearInterval(checkSupabase);
          const error = new Error('Supabase 加载超时');
          console.error(error);
          
          // 尝试最后的备用方案 - 使用最小化的Supabase客户端
          try {
            console.log("Attempting minimal Supabase client initialization...");
            
            // 创建一个最小化的模拟客户端，允许应用继续运行
            window.supabaseClient = {
              auth: {
                signIn: async function() { 
                  console.error("Supabase unavailable, authentication not possible");
                  return { error: { message: "服务暂时不可用，请稍后再试" } };
                },
                signUp: async function() {
                  console.error("Supabase unavailable, registration not possible");
                  return { error: { message: "服务暂时不可用，请稍后再试" } };
                },
                onAuthStateChange: function() {
                  return { data: { subscription: { unsubscribe: function() {} } } };
                }
              },
              from: function() {
                return {
                  select: function() { return this; },
                  eq: function() { return this; },
                  single: async function() {
                    return { error: { message: "数据库连接失败" } };
                  }
                };
              }
            };
            
            // 显示友好的错误消息
            const loginError = document.getElementById('loginError');
            if (loginError) {
              loginError.textContent = '服务器连接失败，请检查网络连接或稍后再试';
              loginError.style.display = 'block';
            }
            
            // 仍然解析Promise以允许UI继续初始化
            resolve();
          } catch (e) {
            reject(error);
          }
        }
      }, checkInterval);
    });
  }
  
  // 使用async/await等待supabase加载完成
  async function initializeAuth() {
    try {
      await ensureSupabaseLoaded();
      
      // 添加详细的配置调试日志
      logDebug('开始初始化Supabase客户端', 'important');
      logDebug(`window._env_存在: ${!!window._env_}`, 'important');
      logDebug(`window.ENV存在: ${!!window.ENV}`, 'important');
      
      // 初始化 Supabase 客户端 - 从动态环境配置获取，无硬编码
      const SUPABASE_URL = window._env_?.SUPABASE_URL || 
        (window.ENV && window.ENV.SUPABASE && window.ENV.SUPABASE.URL);
      
      const SUPABASE_ANON_KEY = window._env_?.SUPABASE_ANON_KEY || 
        (window.ENV && window.ENV.SUPABASE && window.ENV.SUPABASE.ANON_KEY);
      
      // 验证配置是否存在
      if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
        throw new Error('Supabase配置缺失，请检查.env文件或后端环境配置');
      }
      
      logDebug(`使用Supabase URL: ${SUPABASE_URL}`, 'important');
      logDebug(`Supabase全局对象可用: ${typeof supabase !== 'undefined'}`, 'important');
      
      // 确保supabase全局对象可用
      if (typeof supabase === 'undefined') {
        throw new Error('Supabase SDK未加载');
      }
      
      // 创建或重新创建客户端
      try {
        window.supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        logDebug('Supabase客户端创建成功', 'important');
        logDebug(`客户端auth方法可用: ${!!window.supabaseClient.auth}`, 'important');
      } catch (createError) {
        logDebug(`创建Supabase客户端失败: ${createError.message}`, 'error');
        throw createError;
      }
      
      // 继续执行后续代码
      initializeAuthUI();
    } catch (error) {
      console.error("初始化 Auth 失败:", error);
      logDebug(`Auth初始化失败: ${error.message}`, 'error');
      
      // 显示友好的错误消息
      const loginError = document.getElementById('loginError');
      if (loginError) {
        loginError.textContent = UILanguage.getText('auth.connection_error') || '连接服务器失败，请检查网络连接或稍后再试';
        loginError.style.display = 'block';
      }
      
      // 即使出错也尝试初始化UI，以便用户可以看到界面
      try {
        initializeAuthUI();
      } catch (e) {
        console.error("初始化UI失败:", e);
      }
    }
  }
  
  // 初始化认证UI和事件监听
  function initializeAuthUI() {
    // 检查是否启用验证码
    function isTurnstileEnabled() {
      // 检查环境变量配置
      if (window._env_ && typeof window._env_.CLOUDFLARE_ENABLED === 'boolean') {
        return window._env_.CLOUDFLARE_ENABLED;
      }
      
      if (window.ENV && window.ENV.CLOUDFLARE && typeof window.ENV.CLOUDFLARE.ENABLED === 'boolean') {
        return window.ENV.CLOUDFLARE.ENABLED;
      }
      
      // 默认启用
      return true;
    }
    
    // 获取Cloudflare Turnstile站点密钥的函数
    function getTurnstileSiteKey() {
      // 优先使用环境变量
      if (window._env_ && window._env_.CLOUDFLARE_SITE_KEY) {
        return window._env_.CLOUDFLARE_SITE_KEY;
      }
      
      // 其次使用ENV配置
      if (window.ENV && window.ENV.CLOUDFLARE && window.ENV.CLOUDFLARE.SITE_KEY) {
        return window.ENV.CLOUDFLARE.SITE_KEY;
      }
      
      // 根据环境返回默认密钥
      const isProduction = window.ENV && window.ENV.CLOUDFLARE && window.ENV.CLOUDFLARE.IS_PRODUCTION;
      const defaultKey = isProduction ? 
        "0x4AAAAAABkTvsQTpOrFmwv2" : // 生产环境默认密钥
        "0x4AAAAAABkTvsQTpOrFmwv2"; // 开发环境默认密钥
        
      return defaultKey;
    }
    
    // 获取当前环境的站点密钥
    const CLOUDFLARE_SITE_KEY = getTurnstileSiteKey();
    logDebug(`使用Turnstile站点密钥: ${CLOUDFLARE_SITE_KEY}`, 'important');
    
    let captchaToken = null;
    let loginCaptchaToken = null; // 新增登录验证码令牌
    let resetCaptchaToken = null;

    // 安全的认证状态管理函数
    function secureLogin(userData, token) {
      logDebug('设置安全的认证状态', 'important');
      
      // 只存储必要的非敏感信息到localStorage
      localStorage.setItem('isAuthenticated', 'true');
      localStorage.setItem('userEmail', userData.email);
      localStorage.setItem('userId', userData.id);
      
      // 创建会话对象，包含过期机制
      const session = {
        expires: Date.now() + (window._env_?.SESSION_DURATION || 
          (window.ENV && window.ENV.APP && window.ENV.APP.SESSION_DURATION) || 
          3600000), // 默认会话时长为1小时
        user: {
          id: userData.id,
          email: userData.email,
          email_confirmed_at: userData.email_confirmed_at,
          app_metadata: userData.app_metadata
        }
      };
      
      // 使用sessionStorage存储非敏感会话信息，浏览器关闭后自动清除
      sessionStorage.setItem('authSession', JSON.stringify(session));
      
      // 设置CSRF令牌
      const csrfToken = generateRandomToken();
      sessionStorage.setItem('csrfToken', csrfToken);
      
      // 发送请求到后端设置HttpOnly cookie
      fetch('/api/auth/set-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken
        },
        body: JSON.stringify({
          token: token,
          userId: userData.id,
          expires: session.expires
        })
      }).catch(err => {
        logDebug(`设置安全会话失败: ${err.message}`, 'error');
      });
    }
    
    // 生成随机令牌
    function generateRandomToken() {
      const array = new Uint8Array(24);
      window.crypto.getRandomValues(array);
      return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }
    
    // 检查会话是否有效
    function isSessionValid() {
      try {
        const session = JSON.parse(sessionStorage.getItem('authSession'));
        if (!session) return false;
        
        // 检查是否过期
        return session.expires > Date.now();
      } catch (e) {
        logDebug(`会话验证错误: ${e.message}`, 'error');
        return false;
      }
    }
    
    // 在需要认证的操作前检查授权状态
    function checkAuth() {
      if (!isSessionValid()) {
        // 清除认证信息
        logDebug('会话无效或已过期，清除认证状态', 'error');
        sessionStorage.removeItem('authSession');
        localStorage.removeItem('isAuthenticated');
        localStorage.removeItem('userEmail');
        localStorage.removeItem('userId');
        
        // 清除HttpOnly cookie
        fetch('/api/auth/clear-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        }).catch(err => {
          logDebug(`清除安全会话失败: ${err.message}`, 'error');
        });
        
        return false;
      }
      return true;
    }
    
    // 获取当前会话信息
    function getCurrentSession() {
      if (!isSessionValid()) return null;
      return JSON.parse(sessionStorage.getItem('authSession'));
    }
    
    // 获取当前用户令牌 - 从后端获取
    async function getAuthToken() {
      if (!isSessionValid()) return null;
      
      try {
        const csrfToken = sessionStorage.getItem('csrfToken');
        const response = await fetch('/api/auth/get-token', {
          method: 'GET',
          headers: {
            'X-CSRF-Token': csrfToken
          }
        });
        
        if (!response.ok) throw new Error('获取令牌失败');
        
        const data = await response.json();
        return data.token;
      } catch (error) {
        logDebug(`获取令牌失败: ${error.message}`, 'error');
        return null;
      }
    }
    
    // 刷新会话过期时间
    function refreshSession() {
      const session = getCurrentSession();
      if (session) {
        session.expires = Date.now() + (window._env_?.SESSION_DURATION || 
          (window.ENV && window.ENV.APP && window.ENV.APP.SESSION_DURATION) || 
          3600000); // 默认会话时长为1小时
        sessionStorage.setItem('authSession', JSON.stringify(session));
        
        // 刷新HttpOnly cookie
        const csrfToken = sessionStorage.getItem('csrfToken');
        fetch('/api/auth/refresh-session', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken
          },
          body: JSON.stringify({
            expires: session.expires
          })
        }).catch(err => {
          logDebug(`刷新安全会话失败: ${err.message}`, 'error');
        });
        
        const sessionDuration = (window._env_?.SESSION_DURATION || 
          (window.ENV && window.ENV.APP && window.ENV.APP.SESSION_DURATION) || 
          3600000);
        logDebug('会话已刷新，新的过期时间设置为' + sessionDuration/1000 + '秒后');
      }
    }
    
    // 每15分钟刷新一次会话，避免到达1小时限制
    const sessionRefreshInterval = 15 * 60 * 1000; // 15分钟
    setInterval(() => {
      if (isSessionValid()) {
        refreshSession();
        logDebug('定期会话刷新已执行');
      }
    }, sessionRefreshInterval);

    // Tab 切换逻辑
    const loginTab = document.getElementById('loginTab');
    const signupTab = document.getElementById('signupTab');
    const loginFormContainer = document.getElementById('loginForm');
    const signupFormContainer = document.getElementById('signupForm');

    // 初始化页面模态框
    const modal = document.getElementById('resetPasswordModal'); // Changed from 'passwordResetModal' to 'resetPasswordModal'
    const closeModalBtn = document.querySelector('.close-modal');
    
    logDebug('页面初始化完成', 'important');
    
    // 为 Turnstile 添加错误处理
    window.onTurnstileError = function(error) {
      logDebug(`Turnstile 错误: ${error}`, 'error');
    };
    
    // 如果存在忘记密码链接，添加点击事件
    const forgotPasswordLink = document.getElementById('forgotPasswordLink');
    if (forgotPasswordLink) {
      forgotPasswordLink.addEventListener('click', (e) => {
        e.preventDefault();
        modal.style.display = 'block';
        logDebug('打开密码重置模态框');
      });
    }
    
    // 关闭模态框
    if (closeModalBtn) {
      closeModalBtn.addEventListener('click', () => {
        modal.style.display = 'none';
        logDebug('关闭密码重置模态框');
      });
    }
    
    // 点击模态框外部关闭
    window.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.style.display = 'none';
        logDebug('通过点击外部关闭模态框');
      }
    });

    // Cloudflare Turnstile 回调函数 - 注册表单
    window.onCaptchaVerified = function(token) {
      captchaToken = token;
      logDebug(`注册验证码验证成功，Token 长度: ${token.length}`, 'important');
      
      // 启用获取验证码按钮
      const requestVerificationBtn = document.getElementById('requestVerification');
      if (requestVerificationBtn && requestVerificationBtn.disabled) {
        requestVerificationBtn.disabled = false;
        logDebug('启用获取验证码按钮');
      }
    };

    // Cloudflare Turnstile 回调函数 - 登录表单
    window.onLoginCaptchaVerified = function(token) {
      loginCaptchaToken = token;
      logDebug(`登录验证码验证成功，Token 长度: ${token.length}`, 'important');
      
      // 启用登录按钮
      const loginButton = document.getElementById('loginButton');
      if (loginButton && loginButton.disabled) {
        loginButton.disabled = false;
        logDebug('启用登录按钮');
      }
    };

    // Cloudflare Turnstile 回调函数 - 重置密码表单
    window.onResetCaptchaVerified = function(token) {
      resetCaptchaToken = token;
      logDebug(`重置密码验证码验证成功，Token 长度: ${token.length}`, 'important');
      
      // 启用重置密码按钮
      const sendResetLinkBtn = document.getElementById('sendResetLink');
      if (sendResetLinkBtn && sendResetLinkBtn.disabled) {
        sendResetLinkBtn.disabled = false;
        logDebug('启用重置密码按钮');
      }
    };

    loginTab.addEventListener('click', () => {
      loginTab.classList.add('active');
      signupTab.classList.remove('active');
      loginFormContainer.classList.add('active');
      signupFormContainer.classList.remove('active');
      logDebug('切换到登录选项卡');
    });

    signupTab.addEventListener('click', () => {
      signupTab.classList.add('active');
      loginTab.classList.remove('active');
      signupFormContainer.classList.add('active');
      loginFormContainer.classList.remove('active');
      logDebug('切换到注册选项卡');
    });

    // 登录提交逻辑
    const loginForm = document.getElementById('login');
    const loginButton = document.getElementById('loginButton');
    // 根据环境决定是否禁用登录按钮
    const isProd = !['localhost', '127.0.0.1'].includes(window.location.hostname);
    
    // 改善登录按钮初始状态：给验证码5秒加载时间，然后自动启用
    if (isProd) {
      loginButton.disabled = true;
      // 5秒后如果验证码还没加载成功，自动启用登录按钮
      setTimeout(() => {
        if (loginButton.disabled && !loginCaptchaToken) {
          loginButton.disabled = false;
          logDebug('验证码加载超时，自动启用登录按钮', 'important');
          
          // 显示提示信息
          const captchaContainer = document.querySelector('#loginForm .captcha-container');
          if (captchaContainer) {
            captchaContainer.innerHTML = '<div class="captcha-error" style="color: #666; font-size: 12px;">验证码加载失败，您仍可以继续登录</div>';
          }
        }
      }, 5000);
    } else {
      loginButton.disabled = false;
    }
    
    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      const email = document.getElementById('loginEmail').value;
      const password = document.getElementById('loginPassword').value;
      const loginError = document.getElementById('loginError');
      
      // 标记用户主动发起登录
      if (window.markUserInitiatedLogin) {
        window.markUserInitiatedLogin();
        logDebug('已标记用户主动发起登录');
      }
      
      // 清除错误信息
      loginError.textContent = '';
      loginError.style.display = 'none';
      
      logDebug(`开始登录流程: ${email}`, 'important');

      // 基础验证
      if (!email || !password) {
        loginError.textContent = '请填写邮箱和密码';
        loginError.style.display = 'block';
        return;
      }
      
      // 验证码检查（只在启用时检查）
      if (isTurnstileEnabled() && !loginCaptchaToken) {
        loginError.textContent = '请完成人机验证';
        loginError.style.display = 'block';
        return;
      }

      // 禁用按钮防止重复提交并显示加载动画
      loginButton.disabled = true;
      loginButton.classList.add('btn-loading');

      try {
        // 检查Supabase客户端
        if (!window.supabaseClient || !window.supabaseClient.auth) {
          throw new Error('认证服务未就绪，请刷新页面');
        }
        
        logDebug('调用Supabase登录API', 'important');
        
        // 登录请求（只在启用验证码时包含token）
        const signInOptions = {
          email: email,
          password: password
        };
        
        if (isTurnstileEnabled() && loginCaptchaToken) {
          signInOptions.options = {
            captchaToken: loginCaptchaToken
          };
        }
        
        const { data, error } = await window.supabaseClient.auth.signInWithPassword(signInOptions);

        if (error) {
          logDebug(`登录API错误: ${error.message}`, 'error');
          throw new Error(error.message);
        }

        if (!data || !data.user || !data.session) {
          throw new Error('登录响应无效');
        }

        logDebug(`登录成功: 用户ID ${data.user.id}`, 'important');
        
        // 登录成功后同步获取积分并缓存
        logDebug('登录成功，同步获取积分', 'important');
        try {
          const respCredits = await fetch('/api/credits/get-fast', {
            credentials: 'include',
            headers: {
              'Authorization': `Bearer ${data.session.access_token}`
            }
          });
          const creditsJson = await respCredits.json();
          if (respCredits.ok && creditsJson.success) {
            const credits = creditsJson.credits;
            // 使用与CreditsManager一致的格式和key存储积分
            const creditsData = {
              credits: credits,
              userId: data.user.id,
              timestamp: Date.now()
            };
            localStorage.setItem('userCredits', JSON.stringify(creditsData));
            logDebug(`提前同步积分成功: ${credits}`, 'important');
          } else {
            logDebug(`提前同步积分失败: ${creditsJson.error || creditsJson.message}`, 'error');
          }
        } catch (err) {
          logDebug(`提前同步积分异常: ${err}`, 'error');
        }
        // 登录完成后跳转到主页面
        logDebug('跳转到主页面', 'important');
        window.location.href = '/main.html';
        
      } catch (error) {
        logDebug(`登录失败: ${error.message}`, 'error');
        
        // 显示错误信息
        let errorMessage = '登录失败';
        if (error.message.includes('Invalid login credentials')) {
          errorMessage = '邮箱或密码错误';
        } else if (error.message.includes('Email not confirmed')) {
          errorMessage = '请先验证您的邮箱';
        } else if (error.message.includes('Too many requests')) {
          errorMessage = '请求过于频繁，请稍后再试';
        }
        
        loginError.textContent = errorMessage;
        loginError.style.display = 'block';
        
        // 只在出错时重置按钮状态
        loginButton.disabled = false;
        loginButton.classList.remove('btn-loading');
      }
    });

    // 重置密码表单提交逻辑
    const passwordResetForm = document.getElementById('passwordResetForm');
    if (passwordResetForm) {
      passwordResetForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const email = document.getElementById('resetEmail').value;
        const resetMessage = document.getElementById('resetMessage'); // 修正ID
        const resetButton = document.getElementById('sendResetLink');
        
        // 显示消息元素并清空内容
        resetMessage.style.display = 'block';
        resetMessage.textContent = UILanguage.getText('auth.processing');
        resetMessage.className = 'message';
        
        // 禁用提交按钮防止重复提交并显示加载动画
        resetButton.disabled = true;
        resetButton.classList.add('btn-loading');
        
        // 检查验证码状态（只在启用时检查）
        if (isTurnstileEnabled() && !resetCaptchaToken) {
          resetMessage.textContent = UILanguage.getText('auth.captcha_error');
          resetMessage.className = 'message error';
          logDebug('重置密码失败: 未完成人机验证', 'error');
          resetButton.disabled = false;
          resetButton.classList.remove('btn-loading');
          return;
        }
        
        logDebug(`尝试发送密码重置链接到: ${email}`);
        
        try {
          // 重置密码请求（只在启用验证码时包含token）
          const resetOptions = {
            redirectTo: `${window.location.origin}/reset-password.html`
          };
          
          if (isTurnstileEnabled() && resetCaptchaToken) {
            resetOptions.captchaToken = resetCaptchaToken;
          }
          
          const { data, error } = await window.supabaseClient.auth.resetPasswordForEmail(email, resetOptions);
          
          if (error) throw error;
          
          resetMessage.textContent = UILanguage.getText('auth.verification_code_sent');
          resetMessage.className = 'message success';
          document.getElementById('resetEmail').value = '';
          
          logDebug('密码重置链接已发送', 'important');
          
          // 3秒后自动关闭模态框
          setTimeout(() => {
            modal.style.display = 'none';
            resetButton.disabled = false;
            resetButton.classList.remove('btn-loading');
            // 重置消息
            resetMessage.style.display = 'none';
          }, 3000);
          
          // 重置验证码
          resetCaptchaToken = null;
          if (typeof turnstile !== 'undefined') {
            try {
              const resetWidgets = document.querySelectorAll('#resetPasswordModal .cf-turnstile');
              if (resetWidgets.length > 0) {
                turnstile.reset(resetWidgets[0]);
                logDebug('重置密码验证码已重置');
              }
            } catch (err) {
              logDebug(`重置验证码出错: ${err.message}`, 'error');
            }
          }
          
        } catch (error) {
          resetMessage.textContent = UILanguage.getText('auth.reset_link_failed');
          resetMessage.className = 'message error';
          logDebug(`发送重置链接失败: ${error.message}`, 'error');
          
          // 启用重置按钮
          resetButton.disabled = false;
          resetButton.classList.remove('btn-loading');
          
          // 重置验证码
          resetCaptchaToken = null;
          if (typeof turnstile !== 'undefined') {
            try {
              const resetWidgets = document.querySelectorAll('#resetPasswordModal .cf-turnstile');
              if (resetWidgets.length > 0) {
                turnstile.reset(resetWidgets[0]);
              }
            } catch (err) {
              logDebug(`重置验证码出错: ${err.message}`, 'error');
            }
          }
        }
      });
    }

    // 获取验证码按钮
    const requestVerificationBtn = document.getElementById('requestVerification');
    let registeredEmail = ''; // 保存注册邮箱以便重发验证码
    let countdownTimer = null;
    let isSubmitting = false; // 防止重复提交
    
    // 根据环境决定是否禁用获取验证码按钮
    if (requestVerificationBtn) {
      const isProd = !['localhost', '127.0.0.1'].includes(window.location.hostname);
      requestVerificationBtn.disabled = isProd; // 只在生产环境禁用，等待验证码验证
      logDebug(`${isProd ? '生产环境' : '开发环境'}: ${isProd ? '禁用' : '启用'}获取验证码按钮`);
    }
    
    // 新增: 检查邮箱是否有效（不是一次性邮箱）
    async function validateEmail(email) {
      try {
        logDebug(`检查邮箱有效性: ${email}`);
        
        // 第一步：本地基本格式验证
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(email)) {
          return { valid: false, message: '邮箱格式不正确' };
        }
        
        // 第二步：调用API进行验证（先本地黑名单，再商业API）
        const response = await fetch('/api/validate-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email })
        });
        
        const data = await response.json();
        
        if (!data.valid) {
          let errorMessage = '无效的邮箱地址';
          
          // 根据错误原因定制消息
          if (data.reason === 'email_format') {
            errorMessage = '邮箱格式不正确';
          } else if (data.reason === 'disposable_email') {
            errorMessage = '临时邮箱不允许注册，请使用常规邮箱';
          } else if (data.reason === 'server_error') {
            errorMessage = `服务器错误: ${data.details || '未知错误'}`;
          }
          
          return { valid: false, message: errorMessage };
        }
        
        return { valid: true };
      } catch (error) {
        logDebug(`邮箱验证请求失败: ${error.message}`, 'error');
        return { valid: false, message: '邮箱验证服务暂时不可用，请稍后再试' };
      }
    }
    
    // 获取验证码
    requestVerificationBtn.addEventListener('click', async () => {

      // 立即禁用按钮并显示加载动画
      requestVerificationBtn.disabled = true;
      requestVerificationBtn.classList.add('btn-loading');

      const email = document.getElementById('signupEmail').value;
      const password = document.getElementById('signupPassword').value;
      const confirmPassword = document.getElementById('signupConfirmPassword').value;
      const signupError = document.getElementById('signupError');
      const verificationStatus = document.getElementById('verificationStatus');
      
      // 清除之前的错误信息
      signupError.textContent = '';
      verificationStatus.textContent = '';
      
      // 验证邮箱格式
      if (!email || !email.includes('@')) {
        signupError.textContent = UILanguage.getText('auth.invalid_email');
        signupError.style.display = 'block';
        logDebug('请求验证码失败: 邮箱格式无效', 'error');
        // 重置按钮状态
        requestVerificationBtn.disabled = false;
        requestVerificationBtn.classList.remove('btn-loading');
        return;
      }
      
      // 检查密码
      if (!password) {
        signupError.textContent = UILanguage.getText('auth.password_required');
        signupError.style.display = 'block';
        logDebug('请求验证码失败: 未输入密码', 'error');
        // 重置按钮状态
        requestVerificationBtn.disabled = false;
        requestVerificationBtn.classList.remove('btn-loading');
        return;
      }
      
      // 检查两次输入的密码是否一致
      if (password !== confirmPassword) {
        signupError.textContent = UILanguage.getText('auth.password_mismatch');
        signupError.style.display = 'block';
        logDebug('请求验证码失败: 密码不一致', 'error');
        // 重置按钮状态
        requestVerificationBtn.disabled = false;
        requestVerificationBtn.classList.remove('btn-loading');
        return;
      }
      
      // 验证码验证（只在启用时检查）
      if (isTurnstileEnabled() && !captchaToken) {
        signupError.textContent = UILanguage.getText('auth.captcha_error');
        signupError.style.display = 'block';
        logDebug('请求验证码失败: 未完成人机验证', 'error');
        // 重置按钮状态
        requestVerificationBtn.disabled = false;
        requestVerificationBtn.classList.remove('btn-loading');
        return;
      }
      
      // 新增: 在发送验证码前检查邮箱是否有效
      const emailValidation = await validateEmail(email);
      if (!emailValidation.valid) {
        signupError.textContent = emailValidation.message;
        signupError.style.display = 'block';
        logDebug(`请求验证码失败: ${emailValidation.message}`, 'error');
        // 重置按钮状态
        requestVerificationBtn.disabled = false;
        requestVerificationBtn.classList.remove('btn-loading');
        return;
      }

      try {
        // 注意：不要重复设置按钮状态，已经在上面设置过了
        logDebug(`尝试发送验证码到: ${email}`);
        
        // 保存邮箱以便后续使用
        registeredEmail = email;
        
        // 注册用户并发送验证邮件
        const signupOptions = {
          email: email,
          password: password,
          options: {
            emailRedirectTo: `${window.location.origin}/auth/callback`,
            data: {
              email_confirmed: true
            }
          }
        };
        
        // 添加captchaToken到注册选项（只在启用时）
        if (isTurnstileEnabled() && captchaToken) {
          signupOptions.options.captchaToken = captchaToken;
        }
        
        const { data, error } = await window.supabaseClient.auth.signUp(signupOptions);

        if (error) {
          // 检查401错误，通常表示注册功能被禁用
          if (error.status === 401) {
            let errorMessage = UILanguage.getText('auth.signup_disabled');
            
            // 更详细的错误分析
            if (error.message.includes('email_provider_disabled') || 
                error.message.includes('Email provider') ||
                error.message.includes('provider_disabled')) {
              errorMessage = UILanguage.getText('auth.email_provider_disabled') || 
                           '邮箱注册功能被禁用，请联系管理员启用Email Provider';
            }
            
            signupError.textContent = errorMessage;
            signupError.style.display = 'block';
            requestVerificationBtn.disabled = false;
            requestVerificationBtn.classList.remove('btn-loading');
            logDebug(`注册功能被禁用 (401 Unauthorized): ${error.message}`, 'error');
            logDebug(`详细错误信息: ${JSON.stringify(error)}`, 'error');
            return;
          }
          
          // 检查是否是邮箱已存在的错误
          if (error.message && (error.message.includes('User already registered') || 
                                error.message.includes('already exists') || 
                                error.message.includes('email already exists'))) {
            signupError.textContent = UILanguage.getText('auth.email_already_registered');
            signupError.style.display = 'block';
            requestVerificationBtn.disabled = false;
            requestVerificationBtn.classList.remove('btn-loading');
            logDebug(`该邮箱已注册: ${email}, 错误信息: ${error.message}`, 'error');
            return;
          }
          throw error;
        }

        // 检查返回的用户数据
        if (!data.user) {
          throw new Error('注册失败，请稍后再试');
        }
        
        // 检查是否是已有用户 - 修复白名单用户注册问题
        // 当用户已存在时，Supabase可能会返回用户但不创建新的身份
        if (data.user.identities && data.user.identities.length === 0) {
          // 用户已存在但没有新身份创建，说明是重复注册
          signupError.textContent = UILanguage.getText('auth.email_already_registered');
          signupError.style.display = 'block';
          requestVerificationBtn.disabled = false;
          requestVerificationBtn.classList.remove('btn-loading');
          logDebug(`该邮箱已注册（无新身份创建）: ${email}`, 'error');
          return;
        }
        
        // 额外检查：如果用户已经验证过邮箱，也认为是已存在用户
        if (data.user && data.user.email_confirmed_at) {
          // 用户已经验证过邮箱，说明是已存在的用户
          signupError.textContent = UILanguage.getText('auth.email_already_registered');
          signupError.style.display = 'block';
          requestVerificationBtn.disabled = false;
          requestVerificationBtn.classList.remove('btn-loading');
          logDebug(`该邮箱已注册并验证: ${email}`, 'error');
          return;
        }

        // 提示验证码已发送
        verificationStatus.textContent = UILanguage.getText('auth.verification_code_sent');
        verificationStatus.style.color = 'green';
        
        logDebug('验证码已发送成功', 'important');
        
        // 开始倒计时
        startResendCountdown();
        
        // 防止自动登录 - 强制退出
        await window.supabaseClient.auth.signOut();
        
        // 重置验证码
        captchaToken = null;
        if (typeof turnstile !== 'undefined') {
          try {
            // 重置注册表单的验证码
            const signupWidgets = document.querySelectorAll('#signupForm .cf-turnstile');
            if (signupWidgets.length > 0) {
              turnstile.reset(signupWidgets[0]);
              logDebug('注册验证码已重置');
            }
          } catch (err) {
            logDebug(`重置验证码出错: ${err.message}`, 'error');
          }
        }
        
      } catch (error) {
        // 根据错误类型显示不同的消息
        let errorMessage = UILanguage.getText('auth.signup_error');
        
        if (error.message) {
          // 检查是否是邮箱已存在相关的错误
          if (error.message.includes('User already registered') || 
              error.message.includes('already exists') || 
              error.message.includes('email already exists')) {
            errorMessage = UILanguage.getText('auth.email_already_registered');
          } else if (error.message.includes('rate_limit')) {
            errorMessage = '请求过于频繁，请稍后再试';
          } else if (error.message.includes('Invalid email')) {
            errorMessage = '邮箱地址无效';
          } else if (error.message.includes('Password')) {
            errorMessage = '密码格式不符合要求';
          }
        }
        
        signupError.textContent = errorMessage;
        signupError.style.display = 'block';
        logDebug(`发送验证码失败: ${error.message}`, 'error');
        
        // 重置验证码
        captchaToken = null;
        if (typeof turnstile !== 'undefined') {
          try {
            // 重置注册表单的验证码（不是登录表单）
            const signupWidgets = document.querySelectorAll('#signupForm .cf-turnstile');
            if (signupWidgets.length > 0) {
              turnstile.reset(signupWidgets[0]);
              logDebug('已重置注册表单的验证码');
            }
          } catch (err) {
            logDebug(`重置验证码出错: ${err.message}`, 'error');
          }
        }
      } finally {
        // 恢复按钮状态
        requestVerificationBtn.disabled = false;
        requestVerificationBtn.classList.remove('btn-loading');
      }
    });

    // 注册提交逻辑
    const signupForm = document.getElementById('signup');
    signupForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      if (isSubmitting) return;
      
      const email = document.getElementById('signupEmail').value;
      const verificationCode = document.getElementById('verificationCode').value;
      const signupError = document.getElementById('signupError');
      const submitButton = document.getElementById('signupButton');
      
      // 清除之前的错误信息
      signupError.textContent = '';
      signupError.style.display = 'none';
      
      // 检查是否输入了验证码
      if (!verificationCode) {
        signupError.textContent = UILanguage.getText('auth.verification_code_required');
        signupError.style.display = 'block';
        logDebug('注册失败: 未输入验证码', 'error');
        return;
      }
      
      // 检查是否已获取过验证码
      if (!registeredEmail) {
        signupError.textContent = UILanguage.getText('auth.verification_required');
        signupError.style.display = 'block';
        logDebug('注册失败: 未获取验证码', 'error');
        return;
      }

      try {
        isSubmitting = true;
        submitButton.disabled = true;
        submitButton.classList.add('btn-loading');
        
        logDebug(`尝试验证邮箱: ${registeredEmail}`);
        
        // 验证验证码
        const { data, error } = await window.supabaseClient.auth.verifyOtp({
          email: registeredEmail,
          token: verificationCode,
          type: 'signup'
        });
        
        if (error) throw error;
        
        logDebug('邮箱验证成功', 'important');
        
        // 验证成功，初始化用户积分
        if (data.user) {
          try {
            // 初始化用户积分
            await initializeUserCredits(data.user.id, data.session.access_token);
            
            // 保存用户信息和token
            secureLogin(data.user, data.session.access_token);
            
            // 提示注册成功
            alert(UILanguage.getText('auth.registration_success'));
            
            // 跳转到主页
            logDebug('正在重定向到main.html...');
            window.location.replace('/main.html');
          } catch (creditsError) {
            console.error('初始化积分失败:', creditsError);
            logDebug(`初始化积分失败: ${creditsError.message}`, 'error');
            // 即使积分初始化失败，也允许用户继续使用
            alert(UILanguage.getText('auth.registration_success') + UILanguage.getText('auth.signup_error'));
            window.location.replace('/main.html');
          }
        } else {
          throw new Error('验证成功但未获取到用户信息');
        }
        
      } catch (error) {
        signupError.textContent = UILanguage.getText('auth.verification_failed');
        signupError.style.display = 'block';
        logDebug(`验证失败: ${error.message}`, 'error');
      } finally {
        submitButton.disabled = false;
        submitButton.classList.remove('btn-loading');
        isSubmitting = false;
      }
    });

    // 倒计时功能
    function startResendCountdown() {
      let countdown = 60;
      const requestVerificationBtn = document.getElementById('requestVerification');
      requestVerificationBtn.disabled = true;
      // 确保移除加载样式
      requestVerificationBtn.classList.remove('btn-loading');
      
      // 获取倒计时元素
      let countdownElement = document.querySelector('.countdown');
      
      // 清除现有倒计时
      if (countdownTimer) clearInterval(countdownTimer);
      
      // 创建倒计时元素（如果不存在）
      if (!countdownElement) {
        countdownElement = document.createElement('span');
        countdownElement.className = 'countdown';
        requestVerificationBtn.parentNode.appendChild(countdownElement);
      }
      
      // 更新倒计时文本
      countdownElement.textContent = ` (${countdown}秒后可重新发送)`;
      
      // 开始倒计时
      countdownTimer = setInterval(() => {
        countdown--;
        countdownElement.textContent = ` (${countdown}秒后可重新发送)`;
        
        if (countdown <= 0) {
          clearInterval(countdownTimer);
          requestVerificationBtn.disabled = false;
          countdownElement.textContent = '';
          logDebug('验证码倒计时结束，可重新发送');
        }
      }, 1000);
    }

    // 添加 Google 登录按钮事件监听器
    const googleLoginButton = document.getElementById('googleLogin');
    googleLoginButton.addEventListener('click', async () => {
      try {
        // 标记用户主动发起登录
        if (window.markUserInitiatedLogin) {
          window.markUserInitiatedLogin();
          logDebug('已标记用户主动发起Google登录');
        }
        
        // 禁用按钮并显示加载状态
        googleLoginButton.disabled = true;
        googleLoginButton.classList.add('btn-loading');
        logDebug('尝试 Google 登录');
        
        // 使用OAuth管理器处理Google登录
        if (window.oauthManager) {
          await window.oauthManager.startGoogleLogin();
          logDebug('Google 登录已通过OAuth管理器启动');
        } else {
          // Fallback到原有逻辑
          const currentOrigin = window.location.origin;
          logDebug(`Fallback: 使用当前域名作为重定向URL: ${currentOrigin}`, 'important');
          
          const { data, error } = await window.supabaseClient.auth.signInWithOAuth({
            provider: 'google',
            options: {
              redirectTo: `${currentOrigin}/main.html`,
              queryParams: {
                access_type: 'offline'
              }
            }
          });

          if (error) throw error;
          
          logDebug('Google 登录已重定向');
        }
        // Google 登录将重定向到 OAuth 提供者，不需要自己进行进一步处理
      } catch (error) {
        const loginError = document.getElementById('loginError');
        loginError.style.display = 'block';
        loginError.textContent = `Google 登录失败: ${error.message}`;
        logDebug(`Google 登录错误: ${error.message}`, 'error');
        
        // 恢复按钮原始状态
        googleLoginButton.classList.remove('btn-loading');
        googleLoginButton.disabled = false;
      }
    });

    // Google 注册按钮事件
    // Fix: Change from 'googleSignup' to 'googleSignupBtn' to match the ID in HTML
    const googleSignupBtn = document.getElementById('googleSignupBtn');
    if (googleSignupBtn) {
      googleSignupBtn.addEventListener('click', async () => {
        try {
          // 禁用按钮并显示加载状态
          googleSignupBtn.disabled = true;
          googleSignupBtn.classList.add('btn-loading');
          logDebug('尝试 Google 注册');
          
          // 使用OAuth管理器处理Google注册
          if (window.oauthManager) {
            await window.oauthManager.startGoogleLogin();
            logDebug('Google 注册已通过OAuth管理器启动');
          } else {
            // Fallback到原有逻辑
            const currentOrigin = window.location.origin;
            logDebug(`Fallback: 注册时使用当前域名作为重定向URL: ${currentOrigin}`, 'important');
            
            const { data, error } = await window.supabaseClient.auth.signInWithOAuth({
              provider: 'google',
              options: {
                redirectTo: `${currentOrigin}/main.html`,
                queryParams: {
                  access_type: 'offline'
                }
              }
            });
            
            if (error) throw error;
            
            logDebug('Google 注册已重定向');
          }
        } catch (error) {
          const signupError = document.getElementById('signupError');
          signupError.textContent = `Google 注册失败: ${error.message}`;
          signupError.style.display = 'block';
          logDebug(`Google 注册错误: ${error.message}`, 'error');
          
          // 恢复按钮原始状态
          googleSignupBtn.classList.remove('btn-loading');
          googleSignupBtn.disabled = false;
        }
      });
    }

    // 修改会话状态监听 - 处理 OAuth 登录后返回
    window.supabaseClient.auth.onAuthStateChange(async (event, session) => {
      logDebug(`认证状态更改: ${event}`);
      
      // 获取当前页面路径
      const currentPath = window.location.pathname;
      
      // 确定是否处于登录/注册页面
      const isAuthPage = currentPath.includes('log.html') || 
                        currentPath.includes('index.html') || 
                        currentPath === '/' || 
                        currentPath === '';
      
      if (event === 'SIGNED_IN') {
        if (session?.user) {
          logDebug(`已登录用户: ${session.user.email}`);
          
          // 判断是否是被直接登录流程处理的情况
          const directLoginInProgress = document.getElementById('loginButton')?.disabled || 
                                      document.getElementById('signupButton')?.disabled;
          
          // 如果已经在登录流程中，不要重复处理
          if (directLoginInProgress && isAuthPage) {
            logDebug('直接登录流程正在进行中，onAuthStateChange不执行重定向');
            return;
          }
          
          // 检查邮箱是否验证
          if (!session.user.email_confirmed_at) {
            // 用户未验证邮箱，强制退出 (仅针对邮箱注册，不是 OAuth 登录)
            if (session.user.app_metadata.provider === 'email') {
              await window.supabaseClient.auth.signOut();
              alert('请先验证您的邮箱后再登录');
              window.location.replace('/index.html');
              logDebug('用户未验证邮箱，强制退出', 'error');
              return;
            }
          }
          
          // 获取用户 ID 和 token
          const userId = session.user.id;
          const token = session.access_token;
          
          // 保存用户信息和token
          secureLogin(session.user, token);
          
          try {
            // 首先检查用户是否有积分记录，如无则初始化积分
            await checkAndInitUserCredits(userId, token);
            
            // 检查是否为用户主动登录（通过统一认证管理器）
            const isUserInitiated = window.unifiedAuthManager?.isUserInitiatedLogin || false;
            
            // 只有在用户主动登录且不在主页且在登录/注册页面时才重定向
            if (isUserInitiated && !currentPath.includes('main.html') && isAuthPage) {
              logDebug('用户主动登录成功，重定向到main.html');
              window.location.replace('/main.html');
            } else if (!isUserInitiated && isAuthPage) {
              logDebug('自动恢复session成功，但不执行重定向（需用户主动点击登录）');
            }
          } catch (error) {
            console.error('处理用户数据失败:', error);
            logDebug(`处理用户数据失败: ${error.message}`, 'error');
            
            // 检查是否为用户主动登录
            const isUserInitiated = window.unifiedAuthManager?.isUserInitiatedLogin || false;
            
            // 即使出错也继续登录流程（但只有用户主动登录时才重定向）
            if (isUserInitiated && !currentPath.includes('main.html') && isAuthPage) {
              logDebug('尽管处理用户数据出错，但用户主动登录，仍然重定向到main.html');
              window.location.replace('/main.html');
            }
          }
        }
      }
    });
    
    /**
     * 检查用户是否有积分记录，如无则初始化积分
     * @param {string} userId - 用户ID
     * @param {string} token - 访问令牌
     */
    async function checkAndInitUserCredits(userId, token) {
      try {
        logDebug(`检查用户积分记录: 用户ID ${userId}`);
        
        // 添加整体超时控制
        const timeoutPromise = new Promise((resolve, reject) => {
          setTimeout(() => reject(new Error('积分初始化超时')), 8000); // 8秒总超时
        });
        
        const initPromise = (async () => {
          // 先尝试获取用户积分
          const creditsResponse = await fetchUserCredits(userId, token);
          
          // 如果积分为0或未定义，调用初始化函数
          if (!creditsResponse || !creditsResponse.credits || creditsResponse.credits <= 0) {
            logDebug('积分不存在或为0，初始化用户积分');
            await initializeUserCredits(userId, token);
          } else {
            logDebug(`用户已有积分: ${creditsResponse.credits}`);
          }
          
          return true;
        })();
        
        // 使用超时控制
        await Promise.race([initPromise, timeoutPromise]);
        
      } catch (error) {
        logDebug(`检查积分记录失败: ${error.message}`, 'error');
        // 不抛出错误，允许登录流程继续
        console.warn('积分初始化失败，但不影响登录:', error.message);
      }
    }
    
    /**
     * 获取用户积分信息
     * @param {string} userId - 用户ID
     * @param {string} token - 访问令牌
     */
    async function fetchUserCredits(userId, token) {
      try {
        logDebug(`获取用户积分: 用户ID ${userId}`);
        
        // 添加超时控制，避免长时间阻塞
        const timeoutPromise = new Promise((resolve, reject) => {
          setTimeout(() => reject(new Error('积分查询超时')), 5000); // 5秒超时
        });
        
        // 优先尝试使用后端API获取积分
        const apiPromise = fetch('/api/credits/get', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }).then(async response => {
          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              // 缓存积分到本地存储
              localStorage.setItem('userCredits', JSON.stringify({
                credits: data.credits,
                userId: userId,
                timestamp: Date.now()
              }));
              
              logDebug(`通过API获取用户积分成功: ${data.credits}`);
              return { credits: data.credits };
            }
          }
          throw new Error('API获取积分失败');
        });
        
        // 使用Promise.race实现超时控制
        try {
          return await Promise.race([apiPromise, timeoutPromise]);
        } catch (apiError) {
          logDebug(`API获取积分失败: ${apiError.message}，尝试直接查询数据库`, 'error');
          
          // API失败时，尝试直接查询Supabase
          const dbPromise = window.supabaseClient
            .from('user_credits')
            .select('credits, updated_at')
            .eq('user_id', userId)
            .single();
            
          const dbResult = await Promise.race([dbPromise, timeoutPromise]);
          const { data, error } = dbResult;
          
          if (error) throw error;
          
          if (data) {
            localStorage.setItem('userCredits', JSON.stringify({
              credits: data.credits,
              userId: userId,
              timestamp: Date.now()
            }));
            
            logDebug(`数据库查询用户积分成功: ${data.credits}`);
            return { credits: data.credits };
          }
          
          throw new Error('数据库查询无结果');
        }
        
      } catch (error) {
        console.error('获取用户积分失败:', error);
        logDebug(`获取用户积分失败: ${error.message}`, 'error');
        
        // 如果获取失败，尝试使用本地缓存的积分（如果有）
        try {
          const cachedData = localStorage.getItem('userCredits');
          if (cachedData) {
            const parsed = JSON.parse(cachedData);
            if (parsed && parsed.credits !== undefined) {
              logDebug(`使用缓存积分: ${parsed.credits}`);
              return { credits: parsed.credits };
            }
          }
        } catch (cacheError) {
          logDebug(`读取缓存积分失败: ${cacheError.message}`, 'error');
        }
        
        return { credits: 0 }; // 默认返回0积分
      }
    }
    
    /**
     * 初始化新注册用户的积分
     * @param {string} userId - 用户ID
     * @param {string} token - 访问令牌
     */
    async function initializeUserCredits(userId, token) {
      try {
        logDebug(`初始化用户积分: 用户ID ${userId}`);
        
        // 首先检查用户是否已有积分记录
        const { data: existingData, error: checkError } = await window.supabaseClient
          .from('user_credits')
          .select('credits')
          .eq('user_id', userId)
          .single();
          
        // 如果已有记录且有积分，则不需要初始化
        if (existingData && existingData.credits > 0) {
          logDebug(`用户已有积分记录，无需初始化: ${existingData.credits}`);
          
          // 更新本地存储的积分
          localStorage.setItem('userCredits', JSON.stringify({
            amount: existingData.credits,
            lastUpdated: Date.now()
          }));
          
          return { credits: existingData.credits };
        }
        
        // 尝试通过API调用创建积分记录
        try {
          // 尝试使用后端API初始化积分
          const apiResponse = await fetch('/api/credits/get', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (apiResponse.ok) {
            const apiData = await apiResponse.json();
            if (apiData.success) {
              logDebug(`通过API初始化积分成功: ${apiData.credits}`);
              
              // 更新本地存储
              localStorage.setItem('userCredits', JSON.stringify({
                amount: apiData.credits,
                lastUpdated: Date.now()
              }));
              
              return { credits: apiData.credits };
            }
          }
          
          // 如果API调用失败，回退到直接操作数据库
          logDebug('API调用失败，尝试直接操作数据库');
        } catch (apiError) {
          logDebug(`API初始化积分失败: ${apiError.message}，尝试直接操作数据库`, 'error');
        }
        
        // 如果没有记录或API调用失败，创建新记录
        const now = new Date().toISOString();
        const { data, error } = await window.supabaseClient
          .from('user_credits')
          .upsert([{
            user_id: userId, 
            credits: window._env_?.DEFAULT_CREDITS || 
              (window.ENV && window.ENV.APP && window.ENV.APP.DEFAULT_CREDITS) || 
              100, // 默认积分
            created_at: now,
            updated_at: now
          }])
          .select();
          
        if (error) {
          console.error('积分初始化错误:', error);
          logDebug(`积分初始化错误: ${error.message}`, 'error');
          throw error;
        }
        
        if (data && data[0]) {
          localStorage.setItem('userCredits', JSON.stringify({
            amount: data[0].credits,
            lastUpdated: Date.now()
          }));
          
          logDebug(`用户积分初始化成功: ${data[0].credits}`);
          return { credits: data[0].credits };
        }
        
        throw new Error('初始化积分失败，未返回数据');
      } catch (error) {
        console.error('初始化用户积分失败:', error);
        logDebug(`初始化用户积分失败: ${error.message}`, 'error');
        throw error; // 向上传递错误，让调用者处理
      }
    }
  }
  
  // 启动认证初始化
  initializeAuth();
});

// 修改 Turnstile 初始化
window.onload = function() {
  console.log('auth.js 加载完成，跳过重复初始化 Turnstile');
  
  // 确保回调函数已定义，让验证码能正常工作
  if (!window.onCaptchaVerified) {
    window.onCaptchaVerified = function(token) {
      console.log(`注册验证码验证成功，Token 长度: ${token.length}`);
    };
  }
  
  if (!window.onLoginCaptchaVerified) {
    window.onLoginCaptchaVerified = function(token) {
      console.log(`登录验证码验证成功，Token 长度: ${token.length}`);
      const loginButton = document.getElementById('loginButton');
      if (loginButton && loginButton.disabled) {
        loginButton.disabled = false;
      }
    };
  }
  
  if (!window.onResetCaptchaVerified) {
    window.onResetCaptchaVerified = function(token) {
      console.log(`重置密码验证码验证成功，Token 长度: ${token.length}`);
    };
  }
};