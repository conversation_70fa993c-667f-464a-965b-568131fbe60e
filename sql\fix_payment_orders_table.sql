-- ========================================
-- 修复 payment_orders 表结构
-- 添加缺失的 notes 字段
-- ========================================

-- 1. 添加 notes 字段到 payment_orders 表
ALTER TABLE public.payment_orders 
ADD COLUMN IF NOT EXISTS notes TEXT;

-- 2. 为了更好的支付记录，同时添加一些其他有用字段
ALTER TABLE public.payment_orders 
ADD COLUMN IF NOT EXISTS payment_method VARCHAR(50),
ADD COLUMN IF NOT EXISTS transaction_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS failure_reason TEXT;

-- 3. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_payment_orders_transaction_id ON public.payment_orders(transaction_id);
CREATE INDEX IF NOT EXISTS idx_payment_orders_payment_method ON public.payment_orders(payment_method);

-- 4. 验证表结构
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_schema = 'public' 
    AND table_name = 'payment_orders'
ORDER BY ordinal_position;

-- 显示成功消息
SELECT 'payment_orders table structure fixed successfully!' as status; 