# Cloudflare Turnstile 验证码配置说明

## 概述

本项目已集成 Cloudflare Turnstile 验证码，支持通过环境变量灵活控制验证码的启用/禁用，并区分开发环境和生产环境的不同配置。

## 环境变量配置

### 主要配置项

在 `.env` 文件中配置以下参数：

```bash
# 是否启用验证码 (true/false)
CLOUDFLARE_TURNSTILE_ENABLED="true"

# 开发环境密钥 (localhost/127.0.0.1使用)
CLOUDFLARE_DEV_SITE_KEY="0x4AAAAAABkTvsQTpOrFmwv2"
CLOUDFLARE_DEV_SECRET_KEY="0x4AAAAAABkTvvrBrXQF8YhPDjqV3DTT0_M"

# 生产环境密钥 (生产域名使用)
CLOUDFLARE_PROD_SITE_KEY="您的生产环境Site Key"
CLOUDFLARE_PROD_SECRET_KEY="您的生产环境Secret Key"
```

### 配置说明

- **CLOUDFLARE_TURNSTILE_ENABLED**: 
  - `"true"`: 启用验证码
  - `"false"`: 完全禁用验证码
  
- **开发环境密钥**: 用于 localhost 和 127.0.0.1
- **生产环境密钥**: 用于您的生产域名

## 生产环境密钥申请

### 为什么需要单独申请？

当前配置的密钥只适用于 `localhost` 和 `127.0.0.1`，生产环境需要为您的实际域名申请专用密钥。

### 申请步骤

1. 访问 [Cloudflare Turnstile 控制台](https://dash.cloudflare.com/?to=/:account/turnstile)
2. 点击 "Add site" 创建新站点
3. 填写配置：
   - **Site name**: 您的网站名称
   - **Domain**: 您的生产域名（如 `booksum.vip`）
   - **Widget type**: 选择 "Managed"
4. 获取新的 Site Key 和 Secret Key
5. 更新 `.env` 文件中的生产环境配置

### Supabase 配置

在 Supabase 控制台中：
1. 进入 "Authentication" > "Settings"
2. 找到 "Attack Protection" 部分
3. 在 "Captcha secret" 中输入对应环境的 Secret Key

## 使用场景

### 启用验证码（推荐生产环境）

```bash
CLOUDFLARE_TURNSTILE_ENABLED="true"
```

- 所有登录、注册、重置密码操作都需要完成验证码
- 有效防止恶意攻击和机器人滥用
- 需要确保 Supabase 中的 "Enable Captcha protection" 开启

### 禁用验证码（开发/测试环境）

```bash
CLOUDFLARE_TURNSTILE_ENABLED="false"
```

- 跳过所有验证码检查，提高开发效率
- 按钮不会因验证码未完成而禁用
- 建议同时在 Supabase 中关闭 "Enable Captcha protection"

## 技术实现

### 前端逻辑

- 自动检测 `CLOUDFLARE_TURNSTILE_ENABLED` 配置
- 禁用时直接启用所有按钮，显示 "验证码已禁用" 提示
- 启用时正常加载 Turnstile 组件

### 后端处理

- API 根据配置返回验证码状态
- Supabase 在数据库层面进行最终验证
- 支持开发和生产环境的密钥切换

### 环境自动识别

系统通过 `APP_URL` 环境变量自动识别当前环境：
- 包含 `localhost` 或 `127.0.0.1`: 开发环境
- 其他域名: 生产环境

## 最佳实践

### 开发环境

```bash
CLOUDFLARE_TURNSTILE_ENABLED="false"
# 或者使用开发环境密钥进行测试
CLOUDFLARE_TURNSTILE_ENABLED="true"
```

### 生产环境

```bash
CLOUDFLARE_TURNSTILE_ENABLED="true"
CLOUDFLARE_PROD_SITE_KEY="您申请的生产环境Site Key"
CLOUDFLARE_PROD_SECRET_KEY="您申请的生产环境Secret Key"
```

### 安全注意事项

1. **Secret Key 保密**: 绝不在前端代码中暴露 Secret Key
2. **环境隔离**: 开发和生产环境使用不同的密钥对
3. **定期更新**: 建议定期更换密钥以提高安全性
4. **监控日志**: 关注验证码验证失败的日志，及时发现异常

## 常见问题

### Q: 验证码显示 "验证失败" 怎么办？

A: 检查以下几点：
1. 确认当前域名是否在 Cloudflare 中正确配置
2. 验证 Site Key 是否正确
3. 检查 Supabase 中的 Secret Key 配置
4. 确认网络连接正常

### Q: 如何在开发中临时禁用验证码？

A: 在 `.env` 文件中设置：
```bash
CLOUDFLARE_TURNSTILE_ENABLED="false"
```

### Q: 生产环境迁移到新域名怎么办？

A: 需要重新申请密钥：
1. 在 Cloudflare 控制台添加新域名
2. 获取新的密钥对
3. 更新环境变量和 Supabase 配置
4. 部署更新

## 支持的功能

- ✅ 登录验证码
- ✅ 注册验证码  
- ✅ 重置密码验证码
- ✅ 环境变量控制启用/禁用
- ✅ 开发/生产环境密钥分离
- ✅ 多语言错误提示
- ✅ 自动环境识别
- ✅ 优雅降级处理 