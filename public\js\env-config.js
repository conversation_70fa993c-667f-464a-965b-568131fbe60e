/**
 * 环境配置模块
 * 自动检测当前运行环境并提供相应的配置
 */

(function() {
    'use strict';
    
    // 检查全局单例是否已存在
    if (window.EnvConfig && window.EnvConfig._initialized) {
        console.log('环境配置已初始化，跳过重复初始化');
        return;
    }
    
    // 环境检测
    const isProduction = (function() {
        const hostname = window.location.hostname;
        // 检查是否在生产域名上运行
        return hostname.includes('reader-app.fly.dev') || 
               hostname.includes('booksum.vip') || 
               hostname.includes('creem-reader.vercel.app') ||
               (!hostname.includes('localhost') && 
                !hostname.includes('127.0.0.1') &&
                !hostname.includes('192.168.') &&
                !hostname.includes('10.0.') &&
                hostname !== 'localhost');
    })();
    
    // 基础URL配置
    const baseUrl = window.location.origin;
    
    // API端点配置
    const apiConfig = {
        // Creem支付API配置
        payment: {
            // 生产环境使用正式API，开发环境使用测试API
            apiUrl: isProduction ? 'https://api.creem.io/v1' : 'https://test-api.creem.io/v1',
            // 产品ID配置
            productIds: {
                basic: isProduction ? 'prod_3lQSBxYagweAUJYRj7OAG7' : 'prod_2oQ4GaxAW1IsndJysadjMo',
                standard: isProduction ? 'prod_1VFKRW2iDGYBoK9vYOWZqU' : 'prod_2YEF12Y2Ryc7HAJqX1LA7V',
                premium: isProduction ? 'prod_7cYNO6uLX4hNRwk8SG84H0' : 'prod_GEiSCbhjrbsgd2767OxZX'
            }
        }
    };
    
    // 创建环境配置对象
    const envConfig = {
        isProduction: isProduction,
        baseUrl: baseUrl,
        apiConfig: apiConfig,
        _initialized: true,
        
        // 获取当前环境名称
        getEnvironmentName: function() {
            return this.isProduction ? 'production' : 'development';
        },
        
        // 获取API URL
        getApiUrl: function(service) {
            if (service && this.apiConfig[service]) {
                return this.apiConfig[service].apiUrl;
            }
            return this.baseUrl;
        },
        
        // 获取产品ID
        getProductId: function(packageType) {
            if (this.apiConfig.payment && this.apiConfig.payment.productIds) {
                return this.apiConfig.payment.productIds[packageType] || null;
            }
            return null;
        },
        
        // 记录环境信息
        logEnvironmentInfo: function() {
            console.log(`当前环境: ${this.getEnvironmentName()}`);
            console.log(`基础URL: ${this.baseUrl}`);
            console.log(`是否生产环境: ${this.isProduction}`);
        }
    };
    
    // 记录环境信息
    envConfig.logEnvironmentInfo();
    
    // 向全局暴露环境配置
    window.EnvConfig = envConfig;
    
    console.log('环境配置初始化完成');
})();
