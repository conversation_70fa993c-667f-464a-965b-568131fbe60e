/**
 * Supabase 客户端单例管理
 */
(function() {
    // 确保环境配置已加载
    const env = window.ENV_CONFIG || window._env_;
    if (!env || !env.SUPABASE_URL || !env.SUPABASE_ANON_KEY) {
        console.error('Supabase 配置未找到。确保在使用此脚本前加载 env.js');
        return;
    }
    
    // 获取当前域名，用于正确设置重定向URL
    const currentOrigin = window.location.origin;
    const redirectUrl = currentOrigin + '/main.html';
    
    // 创建单个共享实例，添加正确的重定向URL
    if (!window.supabaseClientInstance && window.supabase) {
        window.supabaseClientInstance = supabase.createClient(
            env.SUPABASE_URL, 
            env.SUPABASE_ANON_KEY,
            {
                auth: {
                    redirectTo: redirectUrl,
                    flowType: 'pkce' // 统一使用pkce流程，与OAuth管理器保持一致
                }
            }
        );
        
        // 为了向后兼容，同时设置window.supabaseClient
        window.supabaseClient = window.supabaseClientInstance;
        
        console.log(`已创建共享 Supabase 客户端实例，重定向URL: ${redirectUrl}, 流程类型: pkce`);
    }
})();

/**
 * 获取共享的 Supabase 客户端实例
 * @returns {Object} Supabase 客户端实例
 */
function getSupabaseClient() {
    if (!window.supabaseClientInstance) {
        console.error("Supabase 客户端尚未初始化");
        // 尝试初始化
        const env = window.ENV_CONFIG || window._env_;
        if (env && env.SUPABASE_URL && env.SUPABASE_ANON_KEY && window.supabase) {
            // 获取当前域名，用于正确设置重定向URL
            const currentOrigin = window.location.origin;
            const redirectUrl = currentOrigin + '/main.html';
            
            window.supabaseClientInstance = supabase.createClient(
                env.SUPABASE_URL, 
                env.SUPABASE_ANON_KEY,
                {
                    auth: {
                        redirectTo: redirectUrl,
                        flowType: 'pkce' // 统一使用pkce流程，与OAuth管理器保持一致
                    }
                }
            );
            
            // 为了向后兼容，同时设置window.supabaseClient
            window.supabaseClient = window.supabaseClientInstance;
            
            console.log(`重新创建共享 Supabase 客户端实例，重定向URL: ${redirectUrl}, 流程类型: pkce`);
        }
    }
    return window.supabaseClientInstance;
}