// 登出功能 - 确保在DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('登出脚本已加载');
    // 查找登出按钮
    const logoutBtn = document.getElementById('logoutLink');
    if (!logoutBtn) {
        console.error('未找到登出按钮！');
        return;
    }
    console.log('找到登出按钮，正在添加点击事件');
    // 为登出按钮添加点击事件
    logoutBtn.addEventListener('click', async function(event) {
        console.log('登出按钮被点击');
        event.preventDefault();

        try {
            // 0. 立即设置登出标志，阻止其他API请求
            window.isLoggingOut = true;
            console.log('设置登出标志，阻止新的API请求');

            // 1. 执行退出时积分同步
            console.log('用户退出，开始积分同步');
            if (window.CreditsManager && typeof window.CreditsManager.syncCreditsOnLogout === 'function') {
                try {
                    // 使用新的退出同步方法
                    await window.CreditsManager.syncCreditsOnLogout();
                    console.log('退出时积分同步完成');
                } catch (error) {
                    console.error('退出时积分同步失败:', error);
                    
                    // 如果同步失败，至少尝试缓存当前积分
                    const currentCredits = window.CreditsManager.getCredits();
                    if (currentCredits > 0) {
                        try {
                            const cachedData = {
                                credits: currentCredits,
                                timestamp: Date.now()
                            };
                            localStorage.setItem('cachedCredits', JSON.stringify(cachedData));
                            console.log(`退出前已缓存积分: ${currentCredits}`);
                        } catch (cacheError) {
                            console.error('缓存积分失败:', cacheError);
                        }
                    }
                }
            }

            // 2. 立即清理本地数据（提前清理，避免竞态条件）
            console.log('开始清理本地认证数据');
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('userData');
            localStorage.removeItem('userCredits');
            localStorage.removeItem('cachedCredits');
            localStorage.removeItem('sb-ewlpneznhlwxuapaibiu-auth-token');
            
            // 清理任何可能存在的supabase session数据
            const supabaseKeys = Object.keys(localStorage).filter(key => 
                key.startsWith('sb-') || key.includes('supabase') || key.includes('auth')
            );
            supabaseKeys.forEach(key => localStorage.removeItem(key));
            console.log('本地认证数据清理完成');

            // 3. 使用统一认证管理器进行登出
            if (window.unifiedAuthManager && window.unifiedAuthManager.supabaseClient) {
                console.log('使用统一认证管理器登出');
                await window.unifiedAuthManager.supabaseClient.auth.signOut();
                console.log('Supabase登出完成');
            } else if (window.supabaseClient) {
                console.log('使用Supabase客户端直接登出');
                await window.supabaseClient.auth.signOut();
                console.log('Supabase登出完成');
            }

            // 4. 通知其他组件用户已登出
            window.dispatchEvent(new CustomEvent('user-logged-out', {
                detail: { reason: 'manual_logout' }
            }));
            
            // 5. 延迟跳转，确保清理完成
            setTimeout(() => {
                window.location.href = '/index.html';
            }, 100);
            
        } catch (error) {
            console.error('登出过程中发生错误:', error);
            
            // 即使发生错误也强制清理本地数据并跳转
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('userData');
            
            window.location.href = '/index.html';
        }
    });
});