@echo off
echo ============================================================
echo 部署1000用户规模的统一Rate Limit配置到Fly.io
echo ============================================================
echo.

echo 正在设置用户规模环境变量...
fly secrets set USER_SCALE=1000

echo.
echo 正在设置API速率限制环境变量...
echo.

fly secrets set ZHIPU_API_RATE_LIMIT=25
fly secrets set GEMINI_API_RATE_LIMIT=25  
fly secrets set DEEPSEEK_API_RATE_LIMIT=25
fly secrets set QWEN_API_RATE_LIMIT=25
fly secrets set GROK_API_RATE_LIMIT=25
fly secrets set CLAUDE_API_RATE_LIMIT=25
fly secrets set OPENROUTER_API_RATE_LIMIT=25

echo.
echo ============================================================
echo 统一Rate Limit配置部署完成！
echo ============================================================
echo.
echo 配置详情:
echo - 适用规模: 1,000用户
echo - 预期月AI操作: 136,000次
echo - 用户层限制: 已通过统一配置文件管理
echo - API层限制: 25次/秒 (含50%缓冲)
echo.
echo 请检查设置是否成功:
echo   fly secrets list
echo.
pause 