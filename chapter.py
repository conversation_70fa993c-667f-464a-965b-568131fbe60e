# 字幕时间戳生成脚本 - 使用智谱AI替换Gemini
import pysrt
import os
import json
import time
import pickle
import requests
import sys
import threading
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 设置文件日志
file_handler = logging.FileHandler('chapter_generator.log', encoding='utf-8')
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

# 全局变量
api_call_count = 0
api_limit = 100  # 可调整的 API 调用限制

# 智谱API配置
def get_api_key():
    """获取智谱API密钥"""
    api_key = os.environ.get('ZHIPU_API_KEY')
    if not api_key:
        # 尝试从多个密钥中获取
        multi_keys = os.environ.get('ZHIPU_API_KEYS', '')
        if multi_keys:
            keys = [key.strip() for key in multi_keys.split(',') if key.strip()]
            if keys:
                api_key = keys[0]  # 使用第一个密钥
    return api_key

# 智谱API调用配置
prompt = """
You are tasked with organizing a insightful study guide purely based on the provided transcript. Read carefully the input text and identify the key topics, points, insight, and interesting facts, stories or anecdotes. Your output should extract the most important and insightful content from the guest, structured in a clear manner that makes use of timestamps, when available, to help others study the content effectively, ignoring any ad or promotion content and including the bare minimum of others for context purposes only.

Here is the context of this transcript: 

Write in an engaging and informative way with a storytelling approach, based on the key points and insights. Use a casual tone and include any necessary context or explanations to make the content accessible to a general audience. Incorporate insightful quotes from interviewees within the paragraphs to add authenticity and credibility.

To format your study guide, pls strictly follow below structure:

# HH:MM:SS - 描述性标题

摘要:用于概述这段播客的核心内容，概述部分要避免举例说明、提供细节。
- 要先给出内容要点或分论点，再辅以例子、故事、类比/比喻，或嘉宾的精彩话语。
- 每个内容要点或分论点都要使用引号来提供嘉宾的精彩话语，但不要引用主持人，以增强可信度.
- 避免内容要点或分论点与引用内容的简单重复。
- 只使用简体中文，不要出现任何外文单词。
- ...

# HH:MM:SS - 描述性标题

摘要:用于概述这段播客的核心内容，概述部分要避免举例说明、提供细节。
- 要先给出内容要点或分论点，再辅以例子、故事、类比/比喻，或嘉宾的精彩话语。
- 每个内容要点或分论点都要使用引号来提供嘉宾的精彩话语，但不要引用主持人，以增强可信度.
- 避免内容要点或分论点与引用内容的简单重复。
- 只使用简体中文，不要出现任何外文单词。
- ...

请严格遵循以下格式要求:
- 描述性标题应简明扼要地总结该段内容的关键信息。
- 时间戳必须是"HH:MM:SS"格式，例如"00:01:03"，不要包含毫秒（不要用逗号）
- 在"HH:MM:SS - 描述性标题"中，短横线"-"与"描述性标题"之间只保留一个空格。
- 仅使用所提供的文本中的内容，不要添加任何额外信息。
- 严格区分主持人或嘉宾，提到嘉宾的观点必须使用嘉宾的姓名（无法确定则称嘉宾），不要使用其它词汇比如"说话者/讲述者/演讲者"。
- 支持性细节部分要先给出内容要点或观点，再给出例证、故事、类比/比喻和嘉宾的精彩话语。嘉宾的精彩话语必须使用流畅自然的中文。
- 支持性细节部分，要避免嘉宾的精彩话语与内容要点或分论点的简单重复。内容要点或分论点应该是嘉宾的精彩话语的概括浓缩，精彩话语是为了分论点提供支持，增强可信度。避免出现"一些战略方面是永恒的，并非新概念。'一些战略方面是永恒的，并非新概念。'"之类的车轱辘话。
- 不要引用主持人的话语，只引用嘉宾的精彩话语。
- 所有输出文字必须使用简体中文，不要出现任何外文。
- 避免文本之间出现多余的空格。

以下是一个示例:

00:00:00 - 战略的本质并非优先级清单

摘要:这段播客强调了战略制定中"优先级"的误区，并以航空管制的例子说明战略应是连贯一致的行动，而非简单的愿望堆砌。

- 嘉宾指出，许多公司将战略误认为是优先级清单。"这与战略相反，这更像是一个包含所有我们希望在未来一年发生的事情的彩票清单。"
-  嘉宾以航空管制为例，说明"优先级"的真正含义。"塔台对飞行员说，我优先考虑跑道5上的三架飞机。当出现问题时，'优先级'这个词意味着首先要处理，而不是所有你想到的可能重要的事情的集合。"

00:03:48 - 如何找到企业的竞争优势

摘要: 这段播客讨论了如何找到企业在竞争中的优势，嘉宾强调了寻找自身独特性和不对称性的重要性。

-  嘉宾建议从自身与其他公司的差异入手，以及团队的独特性。"我从对称性开始，我的公司与其他公司有何不同？我的团队有何不同？我们知道什么而其他人不知道？我们拥有什么而其他人没有？"
-  他还强调了重新定义自身市场的重要性，尤其对于规模较小的公司。"有时你必须重新定义你的空间，足够小，你就能真正看到它，因为尤其是一家较小的公司，它并不拥有全球力量，而是在某个特定市场或特定客户群体中拥有力量。"

请根据以下文本生成简体中文学习指南。请务必遵循上述说明并以简体中文回复。

字幕内容：
{chunk_text}

请开始生成章节时间戳："""

def count_tokens(text):
    """估算文本的token数量"""
    if not text:
        return 0
    # 中文约为1.5倍字符数，英文约为0.25倍
    chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
    other_chars = len(text) - chinese_chars
    return int(chinese_chars * 1.5 + other_chars * 0.25)

def open_file(filepath):
    """读取文件内容"""
    with open(filepath, 'r', encoding='utf-8') as infile:
        return infile.read()

def save_file(filepath, content):
    """保存内容到文件"""
    try:
        with open(filepath, 'w', encoding='utf-8') as outfile:
            outfile.write(content)
        pass
    except Exception as e:
        logger.warning(f"保存文件失败: {e}")

def call_zhipu_api(chunk_text, prompt, timeout=30, max_retries=2):
    """调用智谱API生成章节内容，支持真正的流式输出"""
    global api_call_count
    
    # 转换chunk_text为字符串
    if isinstance(chunk_text, (list, tuple)):
        chunk_text_str = ' '.join(str(item) for item in chunk_text)
    else:
        chunk_text_str = str(chunk_text)
    
    actual_prompt = prompt.replace("{chunk_text}", chunk_text_str)
    
    api_key = get_api_key()
    if not api_key:
        logger.error("未找到智谱API密钥，请设置ZHIPU_API_KEY环境变量")
        return ""
    
    # 智谱API端点
    api_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    data = {
        "model": "glm-4-flash",  # 使用快速模型
        "messages": [
            {"role": "system", "content": "你是一个专业的内容分析专家，能够准确分析音频字幕内容并生成合理的章节时间戳。"},
            {"role": "user", "content": actual_prompt}
        ],
        "temperature": 0.3,
        "stream": True,
        "max_tokens": 8000
    }
    
    retries = 0
    while retries < max_retries:
        current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        logger.debug(f"正在尝试第 {retries + 1}/{max_retries} 次智谱API请求...")
        
        try:
            logger.debug("正在连接智谱API服务器...")
            start_time = time.time()
            
            # 发送流式请求
            response = requests.post(
                api_url,
                headers=headers,
                json=data,
                stream=True,
                timeout=(5, timeout)
            )
            
            if response.status_code != 200:
                error_msg = f"API请求失败，状态码: {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f", 详细错误: {error_detail}"
                except:
                    error_msg += f", 响应内容: {response.text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            
            logger.debug("开始接收流式响应...")
            
            # 实时收集流式响应 - 增强版流式解析
            collected_response = ""
            buffer = ""  # 用于累积不完整的内容
            
            for chunk in response.iter_lines():
                if chunk:
                    chunk_str = chunk.decode('utf-8')
                    
                    # 跳过"data: "前缀
                    if chunk_str.startswith('data: '):
                        chunk_str = chunk_str[6:]
                    
                    # 跳过心跳消息
                    if chunk_str == '[DONE]':
                        continue
                    
                    try:
                        chunk_data = json.loads(chunk_str)
                        
                        if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                            delta = chunk_data['choices'][0].get('delta', {})
                            content = delta.get('content', '')
                            if content:
                                collected_response += content
                                buffer += content
                                
                                # 检查buffer中是否包含完整的时间戳行
                                lines = buffer.split('\n')
                                for i, line in enumerate(lines[:-1]):  # 最后一行可能不完整，先跳过
                                    line = line.strip()
                                    if line and ':' in line and '-' in line:
                                        import re
                                        # 检查是否符合时间戳格式
                                        if re.match(r'^\d{2}:\d{2}:\d{2}(?:,\d+)?\s*-\s*.+', line):
                                            # 将完整时间戳行立即输出到stdout供上层实时解析
                                            print(line, flush=True)
                                            buffer = lines[-1]  # 保留最后一行（可能不完整）
                                            break
                                else:
                                    # 如果没有找到完整时间戳行，保持buffer
                                    if len(lines) > 10:  # 避免buffer过长
                                        buffer = '\n'.join(lines[-5:])  # 只保留最后5行
                                
                    except json.JSONDecodeError:
                        continue  # 跳过无效的JSON
            
            elapsed = time.time() - start_time
            logger.debug(f"API流式请求成功! 耗时: {elapsed:.2f}秒")
            
            api_call_count += 1
            logger.debug(f"当前API调用计数: {api_call_count}/{api_limit}")
            
            # 实时保存API结果
            try:
                with open('api_results.pkl', 'ab') as f:
                    pickle.dump(collected_response, f)
            except Exception as e:
                logger.warning(f"保存API结果失败: {e}")
            
            return collected_response or ""
            
        except Exception as e:
            logger.error(f"API调用失败: {e}")
            
            # 记录错误到文件
            try:
                with open('error.txt', 'a', encoding='utf-8') as error_file:
                    error_file.write(f"Error in Zhipu API call: {str(e)}, Time: {current_time}\n")
            except Exception as log_error:
                logger.warning(f"记录错误日志失败: {log_error}")
            
            retries += 1
            if retries < max_retries:
                logger.info(f"在 {timeout} 秒后重试...")
                time.sleep(timeout)
            else:
                logger.error(f"已达到最大重试次数 {max_retries}")
                return ""
    
    return ""

def main():
    """主函数：处理字幕文件并生成时间戳"""
    try:
        logger.info("智谱AI时间戳生成脚本启动")
        
        # 检查必要文件是否存在
        srt_file = 'test.srt'
        if not os.path.exists(srt_file):
            logger.error(f"字幕文件不存在: {srt_file}")
            logger.error(f"当前目录文件列表: {os.listdir('.')}")
            return
        
        logger.debug(f"开始读取字幕文件: {srt_file}")
        
        # 读取SRT字幕文件
        try:
            subs = pysrt.open(srt_file, encoding='utf-8')
            logger.debug(f"成功读取字幕文件，共 {len(subs)} 条字幕")
        except Exception as e:
            logger.error(f"读取字幕文件失败: {e}")
            return

        # 提取字幕文本
        transcript_lines = []
        for sub in subs:
            line = f"{sub.start} --> {sub.end}\n{sub.text}"
            transcript_lines.append(line)
        
        logger.debug(f"提取字幕文本完成，共 {len(transcript_lines)} 行")

        # 简化处理：将所有字幕合并为一个块
        full_text = '\n'.join(transcript_lines)
        
        # 检查文本长度
        token_count = count_tokens(full_text)
        logger.debug(f"文本token数: {token_count}")
        
        if token_count > 30000:  # 智谱API限制，分块处理
            logger.debug("文本较长，进行分块处理")
            chunks = split_text_into_chunks(transcript_lines, 8000)
        else:
            logger.debug("文本长度适中，整体处理")
            chunks = [full_text]
        
        logger.debug(f"分为 {len(chunks)} 个块进行处理")
        
        # 处理每个块
        results = []
        for i, chunk in enumerate(chunks):
            logger.debug(f"正在处理第 {i+1}/{len(chunks)} 块...")
            try:
                response = call_zhipu_api(chunk, prompt, timeout=30, max_retries=2)
                if response:
                    results.append(response)
                    logger.debug(f"第 {i+1} 块处理完成")
                else:
                    logger.warning(f"第 {i+1} 块处理失败，跳过")
            except Exception as e:
                logger.error(f"第 {i+1} 块处理异常: {e}")
                continue

        # 保存结果
        if results:
            output_content = '\n\n'.join(results)
            save_file('chapters.txt', output_content)
            logger.info(f"生成的章节内容已保存到 chapters.txt，共 {len(results)} 个章节")
        else:
            logger.error("没有成功处理任何内容")
            
    except Exception as e:
        logger.critical(f"脚本执行失败: {e}")
        import traceback
        traceback.print_exc()

def split_text_into_chunks(transcript_lines, chunk_size):
    """将文本分割为指定大小的块"""
    chunks = []
    current_chunk = []
    current_token_count = 0

    for line in transcript_lines:
        line_token_count = count_tokens(line)
        if current_token_count + line_token_count > chunk_size and current_chunk:
            chunks.append('\n'.join(current_chunk))
            current_chunk = [line]
            current_token_count = line_token_count
        else:
            current_chunk.append(line)
            current_token_count += line_token_count

    if current_chunk:
        chunks.append('\n'.join(current_chunk))
    
    return chunks

if __name__ == '__main__':
    main()