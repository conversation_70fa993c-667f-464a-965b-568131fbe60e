"""
集中配置的速率限制模块 - 统一版本

此模块提供了一个集中的配置点，用于管理应用程序中所有API端点的速率限制。
整合了用户层面和API层面的速率限制配置，支持不同用户规模的动态切换。
基于用户分析和流量预测，为每个操作类型设置了合理的限制值。
"""
from functools import wraps
from flask import request, current_app
import logging
import os

logger = logging.getLogger(__name__)

# ============ 当前激活的用户规模配置 ============
# 可选值: 500, 1000, 2000
CURRENT_SCALE = int(os.environ.get('USER_SCALE', 500))

# ============ 多规模用户层面速率限制配置 ============
USER_RATE_LIMITS_BY_SCALE = {
    # 500用户规模配置
    500: {
        # 认证相关
        "auth": {
            "register": "8 per minute, 50 per hour, 200 per day",
            "email_verification": "8 per minute, 50 per hour, 200 per day",
            "login": "15 per minute, 100 per hour, 400 per day",
            "password_reset": "5 per minute, 20 per hour, 40 per day",
        },
        
        # 内容处理相关
        "content": {
            "upload_epub": "10 per minute, 60 per hour, 300 per day",
            "summarize": "20 per minute, 150 per hour, 800 per day",
            "mindmap": "20 per minute, 150 per hour, 800 per day",
            "knowledgegraph": "20 per minute, 150 per hour, 800 per day",
            "node_chat": "30 per minute, 200 per hour, 1000 per day",
    "graph_node_chat": "30 per minute, 200 per hour, 1000 per day",
            "upload_audio": "15 per minute, 100 per hour, 500 per day",
            "audio_analysis": "20 per minute, 120 per hour, 600 per day",
            "smart_qa": "25 per minute, 150 per hour, 800 per day",
            "podcast_search": "10 per minute, 60 per hour, 300 per day",
            "podcast_episodes": "15 per minute, 80 per hour, 400 per day",
            "podcast_download": "5 per minute, 30 per hour, 100 per day",
        },
        
        # 积分相关
        "credits": {
            "get_credits": "30 per minute, 200 per hour, 1000 per day",
            "deduct_credits": "25 per minute, 180 per hour, 900 per day",
            "add_credits": "5 per minute, 20 per hour, 50 per day",
            "credits_history": "15 per minute, 80 per hour, 300 per day",
        },
        
        # 支付相关
        "payment": {
            "payment_packages": "10 per minute, 50 per hour, 200 per day",
            "create_order": "5 per minute, 25 per hour, 50 per day",
            "check_status": "10 per minute, 40 per hour, 100 per day",
            "payment_success": "10 per minute, 40 per hour, 100 per day",
            "payment_cancel": "10 per minute, 40 per hour, 100 per day",
            "payment_webhook": "15 per minute, 100 per hour",
            "redirect_to_app": "10 per minute, 40 per hour, 100 per day",
        },
        
        # 其他API
        "other": {
            "default": "50 per minute, 500 per hour, 2000 per day",
            "model_config": "20 per minute, 100 per hour, 400 per day",
            "csrf_token": "20 per minute, 80 per hour, 300 per day",
        },
        
        # 书签相关
        "bookmark_save": {
            "per_minute": 10,  # 每分钟最多保存10个书签
            "per_hour": 60,    # 每小时最多保存60个书签
            "burst": 5         # 允许连续保存5个书签
        },
        "bookmark_load": {
            "per_minute": 30,  # 每分钟最多加载30次书签
            "per_hour": 180,   # 每小时最多加载180次书签
            "burst": 10        # 允许连续加载10次书签
        }
    },
    
    # 1000用户规模配置
    1000: {
        "auth": {
            "register": "15 per minute, 80 per hour, 350 per day",
            "email_verification": "15 per minute, 80 per hour, 350 per day",
            "login": "25 per minute, 180 per hour, 700 per day",
            "password_reset": "8 per minute, 35 per hour, 70 per day",
        },
        "content": {
            "upload_epub": "18 per minute, 120 per hour, 500 per day",
            "summarize": "35 per minute, 250 per hour, 1400 per day",
            "mindmap": "35 per minute, 250 per hour, 1400 per day",
            "knowledgegraph": "35 per minute, 250 per hour, 1400 per day",
            "node_chat": "50 per minute, 350 per hour, 1800 per day",
    "graph_node_chat": "50 per minute, 350 per hour, 1800 per day",
            "upload_audio": "25 per minute, 180 per hour, 900 per day",
            "audio_analysis": "35 per minute, 220 per hour, 1100 per day",
            "smart_qa": "40 per minute, 250 per hour, 1400 per day",
            "podcast_search": "18 per minute, 120 per hour, 500 per day",
            "podcast_episodes": "25 per minute, 150 per hour, 700 per day",
            "podcast_download": "8 per minute, 50 per hour, 180 per day",
        },
        "credits": {
            "get_credits": "50 per minute, 350 per hour, 1800 per day",
            "deduct_credits": "45 per minute, 320 per hour, 1600 per day",
            "add_credits": "8 per minute, 35 per hour, 80 per day",
            "credits_history": "25 per minute, 150 per hour, 500 per day",
        },
        "payment": {
            "payment_packages": "18 per minute, 80 per hour, 350 per day",
            "create_order": "8 per minute, 40 per hour, 80 per day",
            "check_status": "18 per minute, 70 per hour, 180 per day",
            "payment_success": "18 per minute, 70 per hour, 180 per day",
            "payment_cancel": "18 per minute, 70 per hour, 180 per day",
            "payment_webhook": "25 per minute, 180 per hour",
            "redirect_to_app": "18 per minute, 70 per hour, 180 per day",
        },
        "other": {
            "default": "80 per minute, 800 per hour, 3500 per day",
            "model_config": "35 per minute, 180 per hour, 700 per day",
            "csrf_token": "35 per minute, 150 per hour, 500 per day",
        },
        
        # 书签相关
        "bookmark_save": {
            "per_minute": 10,  # 每分钟最多保存10个书签
            "per_hour": 60,    # 每小时最多保存60个书签
            "burst": 5         # 允许连续保存5个书签
        },
        "bookmark_load": {
            "per_minute": 30,  # 每分钟最多加载30次书签
            "per_hour": 180,   # 每小时最多加载180次书签
            "burst": 10        # 允许连续加载10次书签
        }
    },
    
    # 2000用户规模配置
    2000: {
        "auth": {
            "register": "25 per minute, 150 per hour, 600 per day",
            "email_verification": "25 per minute, 150 per hour, 600 per day",
            "login": "45 per minute, 320 per hour, 1200 per day",
            "password_reset": "12 per minute, 60 per hour, 120 per day",
        },
        "content": {
            "upload_epub": "30 per minute, 200 per hour, 800 per day",
            "summarize": "60 per minute, 450 per hour, 2500 per day",
            "mindmap": "60 per minute, 450 per hour, 2500 per day",
            "knowledgegraph": "60 per minute, 450 per hour, 2500 per day",
            "node_chat": "80 per minute, 600 per hour, 3000 per day",
    "graph_node_chat": "80 per minute, 600 per hour, 3000 per day",
            "upload_audio": "40 per minute, 300 per hour, 1500 per day",
            "audio_analysis": "60 per minute, 400 per hour, 2000 per day",
            "smart_qa": "70 per minute, 450 per hour, 2500 per day",
            "podcast_search": "30 per minute, 200 per hour, 800 per day",
            "podcast_episodes": "40 per minute, 250 per hour, 1200 per day",
            "podcast_download": "15 per minute, 80 per hour, 300 per day",
        },
        "credits": {
            "get_credits": "80 per minute, 600 per hour, 3200 per day",
            "deduct_credits": "75 per minute, 550 per hour, 2800 per day",
            "add_credits": "15 per minute, 60 per hour, 150 per day",
            "credits_history": "40 per minute, 250 per hour, 800 per day",
        },
        "payment": {
            "payment_packages": "30 per minute, 150 per hour, 600 per day",
            "create_order": "15 per minute, 70 per hour, 150 per day",
            "check_status": "30 per minute, 120 per hour, 300 per day",
            "payment_success": "30 per minute, 120 per hour, 300 per day",
            "payment_cancel": "30 per minute, 120 per hour, 300 per day",
            "payment_webhook": "40 per minute, 300 per hour",
            "redirect_to_app": "30 per minute, 120 per hour, 300 per day",
        },
        "other": {
            "default": "120 per minute, 1200 per hour, 6000 per day",
            "model_config": "60 per minute, 300 per hour, 1200 per day",
            "csrf_token": "60 per minute, 250 per hour, 800 per day",
        },
        
        # 书签相关
        "bookmark_save": {
            "per_minute": 10,  # 每分钟最多保存10个书签
            "per_hour": 60,    # 每小时最多保存60个书签
            "burst": 5         # 允许连续保存5个书签
        },
        "bookmark_load": {
            "per_minute": 30,  # 每分钟最多加载30次书签
            "per_hour": 180,   # 每小时最多加载180次书签
            "burst": 10        # 允许连续加载10次书签
        }
    }
}

# ============ 多规模API层面速率限制配置 ============
API_RATE_LIMITS_BY_SCALE = {
    500: {
        "ZHIPU_API_RATE_LIMIT": 15,
        "GEMINI_API_RATE_LIMIT": 15,  
        "DEEPSEEK_API_RATE_LIMIT": 15,
        "QWEN_API_RATE_LIMIT": 15,
        "GROK_API_RATE_LIMIT": 15,
        "CLAUDE_API_RATE_LIMIT": 15,
        "OPENROUTER_API_RATE_LIMIT": 15,
    },
    1000: {
        "ZHIPU_API_RATE_LIMIT": 25,
        "GEMINI_API_RATE_LIMIT": 25,  
        "DEEPSEEK_API_RATE_LIMIT": 25,
        "QWEN_API_RATE_LIMIT": 25,
        "GROK_API_RATE_LIMIT": 25,
        "CLAUDE_API_RATE_LIMIT": 25,
        "OPENROUTER_API_RATE_LIMIT": 25,
    },
    2000: {
        "ZHIPU_API_RATE_LIMIT": 40,
        "GEMINI_API_RATE_LIMIT": 40,  
        "DEEPSEEK_API_RATE_LIMIT": 40,
        "QWEN_API_RATE_LIMIT": 40,
        "GROK_API_RATE_LIMIT": 40,
        "CLAUDE_API_RATE_LIMIT": 40,
        "OPENROUTER_API_RATE_LIMIT": 40,
    }
}

# ============ 动态获取当前配置 ============
def get_current_user_rate_limits():
    """获取当前规模的用户层面速率限制配置"""
    return USER_RATE_LIMITS_BY_SCALE.get(CURRENT_SCALE, USER_RATE_LIMITS_BY_SCALE[500])

def get_current_api_rate_limits():
    """获取当前规模的API层面速率限制配置"""
    return API_RATE_LIMITS_BY_SCALE.get(CURRENT_SCALE, API_RATE_LIMITS_BY_SCALE[500])

# ============ 兼容性接口 ============
# 保持与原配置的兼容性
RATE_LIMITS = get_current_user_rate_limits()

def get_limit(category, operation):
    """
    获取指定类别和操作的速率限制
    
    Args:
        category: 限制类别，如 'auth', 'content', 'credits', 'payment', 'other'
        operation: 操作名称，如 'login', 'summarize', 'get_credits' 等
        
    Returns:
        str: 速率限制字符串，如 "5 per minute, 40 per hour, 200 per day"
    """
    current_limits = get_current_user_rate_limits()
    category_limits = current_limits.get(category, {})
    return category_limits.get(operation, current_limits["other"]["default"])

def get_api_rate_limit(api_name):
    """
    获取API层面的速率限制 - 新增函数
    
    Args:
        api_name: API名称，如 'ZHIPU', 'GEMINI' 等
        
    Returns:
        int: 每秒请求数限制
    """
    current_api_limits = get_current_api_rate_limits()
    limit_key = f"{api_name.upper()}_API_RATE_LIMIT"
    
    # 优先从环境变量读取，如果没有则使用配置文件中的值
    env_value = os.environ.get(limit_key)
    if env_value:
        return int(env_value)
    
    return current_api_limits.get(limit_key, 10)

def create_rate_limiter(category, operation):
    """
    创建适合特定操作类型的速率限制装饰器
    
    根据操作类型使用不同的标识符策略：
    - 认证相关操作（auth类别）：始终使用IP地址作为标识符
    - 其他操作（内容、积分、支付等）：优先使用用户ID，无法获取时回退到IP地址
    
    Args:
        category: 限制类别，如 'auth', 'content', 'credits', 'payment', 'other'
        operation: 操作名称，如 'login', 'summarize', 'get_credits' 等
        
    Returns:
        function: 装饰器函数
    """
    # 获取限制值
    limit_value = get_limit(category, operation)
    
    # 返回一个简单的装饰器，它只保存操作类型和限制值
    def decorator(f):
        # 在函数上保存操作类型和限制值，供后续使用
        f._rate_limit_category = category
        f._rate_limit_operation = operation
        f._rate_limit_value = limit_value
        return f
    
    return decorator

def get_auth_limits():
    """
    返回认证相关操作的速率限制配置（兼容旧版API）
    
    Returns:
        dict: 包含各种认证操作的速率限制
    """
    current_limits = get_current_user_rate_limits()
    
    # 根据当前规模调整邮箱限制
    email_multiplier = CURRENT_SCALE // 500
    base_email_limit = 20
    base_domain_limit = 40
    
    return {
        # 登录尝试限制 - 防止暴力破解
        'login': {
            'ip': current_limits["auth"]["login"],  # IP级别限制
            'email': f"{base_email_limit * email_multiplier} per hour, {base_email_limit * email_multiplier * 2} per day"
        },
        # 注册限制 - 防止批量注册
        'register': {
            'ip': current_limits["auth"]["register"],
            'email_domain': f"{base_domain_limit * email_multiplier} per hour, {base_domain_limit * email_multiplier * 3} per day"
        },
        # 密码重置限制 - 防止滥用
        'password_reset': {
            'ip': current_limits["auth"]["password_reset"],
            'email': f"{5 * email_multiplier} per hour, {10 * email_multiplier} per day"
        },
        # 邮箱验证限制
        'email_verification': {
            'ip': current_limits["auth"]["email_verification"],
            'email': f"{10 * email_multiplier} per hour, {20 * email_multiplier} per day"
        }
    }

def get_deployment_commands():
    """
    获取当前规模的部署命令
    
    Returns:
        list: fly secrets set 命令列表
    """
    current_api_limits = get_current_api_rate_limits()
    commands = []
    for key, value in current_api_limits.items():
        commands.append(f"fly secrets set {key}={value}")
    
    return commands

def print_current_config():
    """打印当前配置信息"""
    current_api_limits = get_current_api_rate_limits()
    
    print("=" * 60)
    print(f"当前Rate Limit配置 - {CURRENT_SCALE}用户规模")
    print("=" * 60)
    
    expected_operations = {
        500: 68000,
        1000: 136000,
        2000: 272000
    }
    
    print(f"\n📊 当前配置:")
    print(f"- 用户规模: {CURRENT_SCALE}人")
    print(f"- 预期月AI操作: {expected_operations.get(CURRENT_SCALE, '未知')}次")
    print(f"- 环境变量 USER_SCALE: {os.environ.get('USER_SCALE', '未设置(使用默认500)')}")
    
    print(f"\n🔧 API层面限制 (每秒请求数):")
    for key, value in current_api_limits.items():
        api_name = key.replace("_API_RATE_LIMIT", "")
        env_value = os.environ.get(key)
        status = f"(环境变量: {env_value})" if env_value else "(使用配置文件)"
        print(f"- {api_name}: {value}次/秒 {status}")
    
    print(f"\n🚀 建议执行的Fly.io部署命令:")
    for cmd in get_deployment_commands():
        print(f"  {cmd}")

# ============ 规模切换函数 ============
def switch_scale(new_scale):
    """
    切换用户规模配置（仅在开发环境使用）
    
    Args:
        new_scale: 新的用户规模 (500, 1000, 2000)
    """
    global CURRENT_SCALE, RATE_LIMITS
    
    if new_scale in USER_RATE_LIMITS_BY_SCALE:
        CURRENT_SCALE = new_scale
        RATE_LIMITS = get_current_user_rate_limits()
        logger.info(f"已切换到 {new_scale} 用户规模配置")
        return True
    else:
        logger.error(f"不支持的用户规模: {new_scale}")
        return False
