/**
 * 认证修复脚本 - 解决前后端认证状态不同步问题
 */
(function() {
    'use strict';
    
    let isFixing = false; // 防止重复修复
    
    /**
     * 检测并修复认证状态不同步问题
     */
    async function fixAuthSync() {
        if (isFixing) {
            console.log('[Auth Fix] 修复已在进行中，跳过');
            return;
        }
        
        const authToken = localStorage.getItem('authToken');
        const refreshToken = localStorage.getItem('refreshToken');
        const userData = localStorage.getItem('userData');
        
        // 检查是否有本地认证数据
        if (!authToken || !refreshToken || !userData) {
            console.log('[Auth Fix] 没有本地认证数据，跳过修复');
            return false;
        }
        
        try {
            isFixing = true;
            console.log('[Auth Fix] 检测到认证状态不同步，开始修复...');
            
            // 显示修复提示
            showFixingToast();
            
            // 解析用户数据
            let user;
            try {
                user = JSON.parse(userData);
            } catch (e) {
                console.error('[Auth Fix] 用户数据解析失败:', e);
                clearAuthAndRedirect();
                return false;
            }
            
            // 尝试与后端同步session
            const syncResponse = await fetch('/api/auth/set-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    token: authToken,
                    userId: user.id,
                    expires: Math.floor(Date.now() / 1000) + 3600 // 临时设置1小时有效期
                }),
                credentials: 'include'
            });
            
            if (syncResponse.ok) {
                console.log('[Auth Fix] ✅ 认证状态修复成功');
                hideFixingToast();
                showSuccessToast();
                
                // 触发页面刷新认证状态
                if (window.CreditsManager && typeof window.CreditsManager.forceRefresh === 'function') {
                    window.CreditsManager.forceRefresh();
                }
                
                return true;
            } else {
                console.warn('[Auth Fix] ⚠️ 后端同步失败，尝试刷新token');
                return await tryTokenRefresh();
            }
            
        } catch (error) {
            console.error('[Auth Fix] 修复过程发生错误:', error);
            return await tryTokenRefresh();
        } finally {
            isFixing = false;
        }
    }
    
    /**
     * 尝试刷新token
     */
    async function tryTokenRefresh() {
        const refreshToken = localStorage.getItem('refreshToken');
        if (!refreshToken) {
            clearAuthAndRedirect();
            return false;
        }
        
        try {
            console.log('[Auth Fix] 尝试刷新token...');
            
            const refreshResponse = await fetch('/api/auth/refresh', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ refresh_token: refreshToken }),
                credentials: 'include'
            });
            
            if (refreshResponse.ok) {
                const refreshData = await refreshResponse.json();
                if (refreshData.success && refreshData.access_token) {
                    // 更新localStorage
                    localStorage.setItem('authToken', refreshData.access_token);
                    if (refreshData.refresh_token) {
                        localStorage.setItem('refreshToken', refreshData.refresh_token);
                    }
                    
                    console.log('[Auth Fix] ✅ Token刷新成功');
                    hideFixingToast();
                    showSuccessToast();
                    
                    // 触发页面刷新认证状态
                    if (window.CreditsManager && typeof window.CreditsManager.forceRefresh === 'function') {
                        window.CreditsManager.forceRefresh();
                    }
                    
                    return true;
                }
            }
            
            console.warn('[Auth Fix] Token刷新失败');
            clearAuthAndRedirect();
            return false;
            
        } catch (error) {
            console.error('[Auth Fix] Token刷新出错:', error);
            clearAuthAndRedirect();
            return false;
        }
    }
    
    /**
     * 清理认证数据并重定向到登录页
     */
    function clearAuthAndRedirect() {
        console.log('[Auth Fix] 清理认证数据并重定向到登录页');
        localStorage.clear();
        hideFixingToast();
        showErrorToast();
        
        setTimeout(() => {
            window.location.href = '/index.html';
        }, 2000);
    }
    
    /**
     * 显示修复中的提示
     */
    function showFixingToast() {
        showToast('正在修复认证状态...', 'info', 0);
    }
    
    /**
     * 显示成功提示
     */
    function showSuccessToast() {
        showToast('认证状态已修复！', 'success', 3000);
    }
    
    /**
     * 显示错误提示
     */
    function showErrorToast() {
        showToast('认证已失效，正在返回登录页...', 'error', 3000);
    }
    
    /**
     * 隐藏修复提示
     */
    function hideFixingToast() {
        const toast = document.getElementById('auth-fix-toast');
        if (toast) {
            toast.remove();
        }
    }
    
    /**
     * 显示Toast提示
     */
    function showToast(message, type = 'info', duration = 0) {
        // 移除已存在的toast
        hideFixingToast();
        
        const toast = document.createElement('div');
        toast.id = 'auth-fix-toast';
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        `;
        
        // 根据类型设置颜色
        switch (type) {
            case 'success':
                toast.style.backgroundColor = '#4CAF50';
                break;
            case 'error':
                toast.style.backgroundColor = '#f44336';
                break;
            case 'warning':
                toast.style.backgroundColor = '#ff9800';
                break;
            default:
                toast.style.backgroundColor = '#2196F3';
        }
        
        toast.textContent = message;
        document.body.appendChild(toast);
        
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, duration);
        }
    }
    
    /**
     * 检测API调用失败并自动修复
     */
    function setupAutoFix() {
        // 监听fetch请求
        const originalFetch = window.fetch;
        window.fetch = async function(...args) {
            try {
                const response = await originalFetch.apply(this, args);
                
                // 检查是否是API调用且返回401或认证错误
                if (args[0] && typeof args[0] === 'string' && args[0].includes('/api/')) {
                    if (response.status === 401) {
                        console.log('[Auth Fix] 检测到401错误，尝试修复认证');
                        await fixAuthSync();
                    } else if (response.ok) {
                        try {
                            const data = await response.clone().json();
                            if (data && !data.success && data.error && data.error.includes('认证')) {
                                console.log('[Auth Fix] 检测到认证错误，尝试修复');
                                await fixAuthSync();
                            }
                        } catch (e) {
                            // 不是JSON响应，忽略
                        }
                    }
                }
                
                return response;
            } catch (error) {
                return Promise.reject(error);
            }
        };
        
        console.log('[Auth Fix] 自动修复监听器已设置');
    }
    
    // 导出到全局
    window.AuthFix = {
        fixAuthSync,
        setupAutoFix
    };
    
    // 页面加载完成后自动设置
    document.addEventListener('DOMContentLoaded', function() {
        setupAutoFix();
        
        // 延迟检查一次，处理页面加载时的认证问题
        setTimeout(async () => {
            const authToken = localStorage.getItem('authToken');
            if (authToken) {
                // 快速检测认证状态
                try {
                    const testResponse = await fetch('/api/credits/get-fast', {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    });
                    if (!testResponse.ok) {
                        console.log('[Auth Fix] 页面加载时检测到认证问题，开始修复');
                        await fixAuthSync();
                    }
                } catch (e) {
                    console.log('[Auth Fix] 页面加载时认证检测失败，开始修复');
                    await fixAuthSync();
                }
            }
        }, 1000);
    });
    
    console.log('[Auth Fix] 认证修复脚本已加载');
})(); 