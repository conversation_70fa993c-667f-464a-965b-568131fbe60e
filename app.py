# 在文件最顶部添加防重复初始化机制
_app_initialized = False

from flask import Flask, request, jsonify, send_file, session, Response, make_response, stream_with_context
from flask_cors import CORS
from werkzeug.exceptions import ClientDisconnected
import os
import tempfile
import base64
import mimetypes
import json
import re
import traceback
import atexit
from io import BytesIO
import logging
import requests
from pathlib import Path
import threading
import time
from dotenv import load_dotenv
import uuid
import shutil
import importlib
from api.models_config import MODEL_CONFIG, FEATURE_TOKEN_ESTIMATES, CREDIT_COST_RATE
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from api.auth import register_routes as register_auth_routes
from api.rate_limit import get_user_identifier
from api.rate_limit_config import get_limit, create_rate_limiter
import warnings
from api.email_validator import register_routes as register_email_routes
import asyncio
from werkzeug.utils import secure_filename
from pydub import AudioSegment
import glob
from typing import Optional, Dict, Any
from datetime import datetime

# 导入新的模块化组件
from api.audio_manager import AudioFileManager
from api.audio_routes import init_audio_routes

# 配置日志（必须在导入音频服务之前）
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入日期解析库（播客功能需要）
try:
    from dateutil import parser as date_parser
    DATEUTIL_AVAILABLE = True
    logger.info("播客功能：dateutil已成功加载，支持日期排序")
except ImportError:
    DATEUTIL_AVAILABLE = False
    logger.warning("播客功能：dateutil不可用，将使用降级模式（无日期排序）")

# 移除audio文件夹依赖，直接实现音频功能
logger.info("直接实现音频功能，不依赖audio文件夹")

# 抑制相关警告
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# 添加安全的错误处理函数
def handle_error(exception, error_message="服务器处理请求时发生错误", status_code=500):
    """安全地处理异常，避免在响应中泄露敏感信息"""
    logger.error(f"处理请求时发生异常: {str(exception)}\n{traceback.format_exc()}")
    return jsonify({"error": error_message}), status_code

# 强制设置环境变量，关闭调试模式
os.environ['DEBUG'] = 'false'
os.environ['FLASK_ENV'] = 'production'

# 加载环境变量
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path)
    logger.info(f"已从 {dotenv_path} 加载环境变量")
else:
    logger.warning("未找到.env文件，将使用系统环境变量")

# 全局变量声明
app = None
limiter = None
audio_manager = None  # 添加音频管理器
PORT = 8080

# 异步函数的同步包装器
def run_async(func):
    """同步执行异步函数的装饰器"""
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(func(*args, **kwargs))
        finally:
            loop.close()
    return wrapper

# 防重复初始化的核心代码
if not _app_initialized:
    logger.info("开始应用核心初始化...")
    
    # 导入支付相关蓝图
    try:
        from api.payment import payment_bp, init_payment_module
        from api.credits import credits_bp, init_credits_module
    except ImportError as e:
        logger.warning(f"无法导入模块: {str(e)}")
        payment_bp = None
        init_payment_module = None
        credits_bp = None
        init_credits_module = None

    # 导入模型相关
    from api.models_config import get_available_models as get_model_configs
    from api.model_facade import call_model_api
    from api.config import get_or_generate_flask_secret_key

    # 创建Flask应用
    app = Flask(__name__, static_folder='public', static_url_path='')
    app.secret_key = get_or_generate_flask_secret_key()

    # CORS配置
    default_origins = 'http://127.0.0.1:8080' if os.environ.get('FLASK_ENV') == 'development' else 'https://booksum.vip,https://reader-app.fly.dev'
    allowed_origins = os.environ.get('ALLOWED_ORIGINS', default_origins).split(',')

    CORS(app, resources={
        r"/api/*": {
            "origins": allowed_origins,
            "methods": ["GET", "POST", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization", "X-Requested-With"]
        },
        r"/(js|css|img|fonts)/*": {
            "origins": "*",
            "methods": ["GET"]
        },
        r"/": {
            "origins": allowed_origins,
            "methods": ["GET"]
        }
    })

    # 初始化 Flask-Limiter
    limiter = Limiter(
        get_user_identifier,
        app=app,
        default_limits=["500 per day", "100 per hour"],
        storage_uri="memory://",
        strategy="fixed-window",
        key_prefix="reader_app"
    )
    app.limiter = limiter

    # 注册支付蓝图
    if payment_bp:
        app.register_blueprint(payment_bp)
        
        app.config['CREEM_API_KEY'] = os.environ.get('CREEM_API_KEY')
        app.config['CREEM_API_URL'] = os.environ.get('CREEM_API_URL')
        app.config['CREEM_WEBHOOK_SECRET'] = os.environ.get('CREEM_WEBHOOK_SECRET')
        app.config['APP_URL'] = os.environ.get('APP_URL')
        app.config['SUPABASE_URL'] = os.environ.get('SUPABASE_URL')
        app.config['SUPABASE_ANON_KEY'] = os.environ.get('SUPABASE_ANON_KEY')
        app.config['SUPABASE_SERVICE_ROLE_KEY_AUDIO'] = os.environ.get('SUPABASE_SERVICE_ROLE_KEY_AUDIO') 
        
        if init_payment_module:
            init_payment_module(app, limiter)
        
        logging.info("支付模块已注册")

    # 注册积分蓝图
    if credits_bp:
        app.register_blueprint(credits_bp)
        
        if init_credits_module:
            init_credits_module(app, limiter)
        
        logging.info("积分模块已注册")

    # 注册认证路由
    register_auth_routes(app)

    # 注册邮箱验证路由
    register_email_routes(app)

    # 初始化音频管理器
    audio_manager = AudioFileManager()
    
    # 执行启动清理
    run_async(audio_manager.startup_cleanup)()
    logger.info("音频管理器已初始化，启动清理完成")

    # 注册音频路由
    init_audio_routes(app, audio_manager)
    logger.info("音频路由已注册")

    # 标记为已初始化
    _app_initialized = True
    logger.info("应用核心初始化完成，防重复初始化标志已设置")

    # 注册应用关闭清理函数
    @atexit.register
    def cleanup_on_exit():
        """应用关闭时的清理工作"""
        logger.info("应用正在关闭，执行清理...")
        # 可以在这里添加其他清理工作

    # 注册API服务关闭函数
    atexit.register(lambda: logger.info("已注册API服务关闭函数"))

# 简单的国际化函数
def get_i18n_text(key, lang='zh'):
    """根据语言返回对应的文本"""
    i18n_dict = {
        'zh': {
            'extract_audio_segment_failed': '提取音频片段失败'
        },
        'en': {
            'extract_audio_segment_failed': 'Failed to extract audio segment'
        }
    }
    return i18n_dict.get(lang, i18n_dict['zh']).get(key, key)

# 基本路由
@app.route('/')
def index():
    """返回主页面"""
    try:
        logger.info("尝试加载index.html文件")
        return send_file('public/index.html')
    except Exception as e:
        logger.error(f"加载index.html失败: {e}")
        return f"页面加载失败: {str(e)}", 500

@app.route('/<path:filename>')
def serve_static_files(filename):
    """处理静态HTML和其他文件"""
    try:
        # 检查public目录下的文件
        file_path = os.path.join('public', filename)
        if os.path.exists(file_path):
            logger.info(f"加载文件: public/{filename}")
            return send_file(file_path)
        
        # 检查根目录下的HTML文件
        if filename.endswith('.html') and os.path.exists(filename):
            logger.info(f"加载根目录HTML文件: {filename}")
            return send_file(filename)
        
        # 如果都找不到，返回404
        logger.warning(f"文件不存在: {filename}")
        return jsonify({"error": "请求的资源不存在"}), 404
        
    except Exception as e:
        logger.error(f"加载文件失败 {filename}: {e}")
        return jsonify({"error": f"加载文件失败: {e}"}), 500

@app.route('/env.js')
def serve_env_js():
    """
    动态生成前端环境配置文件
    从.env文件和系统环境变量读取配置，确保前端使用最新配置
    """
    try:
        # 从环境变量获取Supabase配置
        supabase_url = os.getenv('SUPABASE_URL', '')
        supabase_anon_key = os.getenv('SUPABASE_ANON_KEY', '')
        
        # 其他配置
        app_url = os.getenv('APP_URL', 'https://booksum.vip')
        stripe_key = os.getenv('STRIPE_PUBLISHABLE_KEY', '')
        creem_api_url = os.getenv('CREEM_API_URL', 'https://test-api.creem.io/v1')
        debug_mode = os.getenv('DEBUG', 'false').lower() == 'true'
        
        # 积分检查配置
        from api.config import (
            CHECK_CREDITS_PODCAST_SEARCH,
            CHECK_CREDITS_UPLOAD_SUBTITLE,
            CHECK_CREDITS_BOOKMARK_SAVE,
            CHECK_CREDITS_BOOKMARK_LOAD
        )
        
        # Cloudflare Turnstile配置
        cloudflare_enabled = os.getenv('CLOUDFLARE_TURNSTILE_ENABLED', 'true').lower() == 'true'
        
        # 检测当前环境
        is_production = not any(host in os.environ.get('APP_URL', '') for host in ['localhost', '127.0.0.1'])
        
        # 根据环境选择相应的密钥
        if is_production:
            cloudflare_site_key = os.getenv('CLOUDFLARE_PROD_SITE_KEY', '0x4AAAAAABkTvsQTpOrFmwv2')
            cloudflare_secret_key = os.getenv('CLOUDFLARE_PROD_SECRET_KEY', '0x4AAAAAABkTvvrBrXQF8YhPDjqV3DTT0_M')
        else:
            cloudflare_site_key = os.getenv('CLOUDFLARE_DEV_SITE_KEY', '0x4AAAAAABkTvsQTpOrFmwv2')
            cloudflare_secret_key = os.getenv('CLOUDFLARE_DEV_SECRET_KEY', '0x4AAAAAABkTvvrBrXQF8YhPDjqV3DTT0_M')
        
        # 生成JavaScript配置文件
        env_js_content = f'''// 环境配置文件 - 由后端动态生成
// 修改配置请编辑.env文件，不要直接修改此文件
window.ENV = {{
  // Supabase配置
  SUPABASE: {{
    URL: "{supabase_url}",
    ANON_KEY: "{supabase_anon_key}"
  }},
  // Cloudflare Turnstile配置
  CLOUDFLARE: {{
    // 是否启用验证码
    ENABLED: {str(cloudflare_enabled).lower()},
    // 当前环境的站点密钥
    SITE_KEY: "{cloudflare_site_key}",
    // 环境类型
    IS_PRODUCTION: {str(is_production).lower()}
  }},
  // 应用配置
  APP: {{
    DEBUG_MODE: {str(debug_mode).lower()}
  }},
  // 积分检查配置
  CREDITS_CHECK: {{
    PODCAST_SEARCH: {str(CHECK_CREDITS_PODCAST_SEARCH).lower()},
    UPLOAD_SUBTITLE: {str(CHECK_CREDITS_UPLOAD_SUBTITLE).lower()},
    BOOKMARK_SAVE: {str(CHECK_CREDITS_BOOKMARK_SAVE).lower()},
    BOOKMARK_LOAD: {str(CHECK_CREDITS_BOOKMARK_LOAD).lower()}
  }}
}};

// 为了兼容auth.js，同时设置_env_格式
window._env_ = {{
  SUPABASE_URL: "{supabase_url}",
  SUPABASE_ANON_KEY: "{supabase_anon_key}",
  CLOUDFLARE_ENABLED: {str(cloudflare_enabled).lower()},
  CLOUDFLARE_SITE_KEY: "{cloudflare_site_key}",
  APP_URL: "{app_url}",
  STRIPE_PUBLISHABLE_KEY: "{stripe_key}",
  CREEM_API_URL: "{creem_api_url}"
}};

// 日志记录验证码配置
console.log(`Cloudflare Turnstile配置: 启用=${str(cloudflare_enabled).lower()}, 环境=${{str(is_production).lower() ? '生产' : '开发'}}, 站点密钥={cloudflare_site_key}`);

console.log("环境配置已动态加载", window._env_);
console.log("配置来源: .env文件和系统环境变量");
'''
        
        # 返回JavaScript文件
        response = make_response(env_js_content)
        response.headers['Content-Type'] = 'application/javascript; charset=utf-8'
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        
        logger.info(f"动态生成env.js配置 - Supabase URL: {supabase_url[:30]}...")
        return response
        
    except Exception as e:
        logger.error(f"生成env.js时出错: {str(e)}")
        # 返回一个基本的配置，避免前端完全无法工作
        fallback_content = '''// 配置加载失败，使用后备配置
window.ENV = { SUPABASE: { URL: "", ANON_KEY: "" }, CLOUDFLARE: { SITE_KEY: "" }, APP: { DEBUG_MODE: false }};
window._env_ = { SUPABASE_URL: "", SUPABASE_ANON_KEY: "", CLOUDFLARE_SITE_KEY: "", APP_URL: "", STRIPE_PUBLISHABLE_KEY: "", CREEM_API_URL: "" };
console.error("环境配置加载失败，请检查后端配置");'''
        
        response = make_response(fallback_content)
        response.headers['Content-Type'] = 'application/javascript; charset=utf-8'
        return response

# 音频相关路由已移至 api/audio_routes.py 模块

# 新增：本地音频分析API（无需文件上传）
@app.route('/api/analyze-local-audio-stream', methods=['POST'])
@create_rate_limiter("content", "audio_analysis")
def analyze_local_audio_stream():
    """分析本地音频文件（不需要上传）- 支持跳过积分检查"""
    try:
        data = request.json
        audio_data = data.get('audio_data')  # base64编码的音频数据
        audio_format = data.get('audio_format', 'wav')  # 优化：默认WAV格式（更快）
        prompt = data.get('prompt', '请分析这段音频内容,先给出音频的transcript，尽可能使用正确的标点进行断句，然后给出直译和意译。')
        skip_credit_check = data.get('skip_credit_check', False)  # 是否跳过积分检查
        
        if not audio_data:
            return jsonify({"error": "缺少音频数据"}), 400
            
        from api.credits import check_user_credits_for_operation, deduct_user_credits
        auth_token = request.headers.get('Authorization', '').replace('Bearer ', '')

        def on_completion(user_id, usage_info):
            """AI操作完成后的回调，用于扣减积分"""
            try:
                audio_filename = '本地音频文件'
                
                result = deduct_user_credits(
                    user_id,
                    'audio_analysis',
                    'qwen_omni_turbo',
                    token_usage=usage_info,
                    audio_info=audio_filename
                )
                
                if result['success']:
                    logger.info(f"本地音频分析积分扣减成功: {result}")
                else:
                    logger.error(f"本地音频分析积分扣减失败: {result}")
                    
            except Exception as e:
                logger.error(f"本地音频分析积分扣减异常: {e}")

        def generate():
            async def async_generate():
                import re
                accumulated_content = ""
                user_id = None
                
                try:
                    # 🛡️ 安全积分检查：实施三层检查机制
                    user_id = None
                    
                    # 首先获取用户ID（无论是否跳过积分检查都需要）
                    try:
                        from api.auth import get_user_from_token
                        user_info = get_user_from_token(auth_token)
                        if not user_info or not user_info.get('id'):
                            yield f"data: {json.dumps({'type': 'error', 'error': '用户认证失败'}, ensure_ascii=False)}\n\n"
                            return
                        user_id = user_info['id']  # 正确提取user_id字符串
                        logger.info(f"随时问用户身份验证成功: {user_id}")
                    except Exception as auth_error:
                        logger.error(f"随时问用户认证失败: {auth_error}")
                        yield f"data: {json.dumps({'type': 'error', 'error': '用户认证失败'}, ensure_ascii=False)}\n\n"
                        return
                    
                    # 将skip_credit_check变量移到正确的作用域
                    current_skip_credit_check = skip_credit_check
                    
                    # 积分检查：即使前端要求跳过，后端也要验证阈值
                    if current_skip_credit_check:
                        logger.info(f"🔍 [随时问积分] 前端要求跳过积分检查，进行后端阈值验证...")
                        
                        # 第一层：后端缓存检查（最快）
                        from api.credits import get_user_credits_from_cache
                        cached_credits = get_user_credits_from_cache(user_id)
                        
                        # 估算所需积分（随时问通常需要1-3积分）
                        estimated_duration = len(audio_data) / 40000  # 粗略估算音频时长
                        estimated_required = max(1, int(estimated_duration * 0.5))
                        threshold = estimated_required + 10  # 阈值：估算需要 + 10积分缓冲
                        
                        logger.info(f"🔍 [随时问积分] 后端验证: 缓存积分={cached_credits}, 阈值={threshold}")
                        
                        if cached_credits is not None and cached_credits >= threshold:
                            logger.info(f"🚀 [随时问积分] 后端阈值验证通过({cached_credits}>={threshold})，跳过详细检查")
                            
                            # 新增：验证通过后，延长辅助功能免检查时间
                            try:
                                from api.credits import set_auxiliary_skip_check_status
                                from api.config import AUXILIARY_FEATURES_SKIP_CHECK_MINUTES
                                set_auxiliary_skip_check_status(user_id, AUXILIARY_FEATURES_SKIP_CHECK_MINUTES)
                                logger.info(f"🕒 [动态免检查] 随时问验证通过，为用户{user_id}延长{AUXILIARY_FEATURES_SKIP_CHECK_MINUTES}分钟免检查时间")
                            except Exception as extend_error:
                                logger.warning(f"⚠️ [动态免检查] 延长免检查时间失败: {extend_error}")
                        else:
                            # 缓存积分不足，需要进行完整的积分检查
                            logger.warning(f"⚠️ [随时问积分] 后端缓存积分不足阈值，进行完整检查")
                            current_skip_credit_check = False
                    
                    # 如果需要完整积分检查
                    if not current_skip_credit_check:
                        logger.info(f"🔍 [随时问积分] 执行完整积分检查...")
                        # 积分检查（基于音频数据大小估算）
                        estimated_duration = len(audio_data) / 40000  # 粗略估算音频时长
                        multimodal_info = {
                            "audio_duration": estimated_duration,
                            "has_audio": True,
                            "has_image": False
                        }
                        has_credits, temp_user_id, credits_info = check_user_credits_for_operation(
                            auth_token, 'audio_analysis', 'qwen_omni_turbo',
                            content=prompt,
                            multimodal_info=multimodal_info
                        )
                        # 不要覆盖user_id，保持使用正确提取的字符串ID
                        
                        if not has_credits:
                            logger.warning(f"❌ [随时问积分] 完整检查失败 - 积分不足")
                            yield f"data: {json.dumps({'type': 'error', 'error': '积分不足', **credits_info}, ensure_ascii=False)}\n\n"
                            return
                        else:
                            logger.info(f"✅ [随时问积分] 完整检查通过")
                    else:
                        logger.info(f"🚀 [随时问积分] 阈值验证通过，跳过详细检查")
                    
                    # 获取千问API配置
                    api_key = os.getenv('DASHSCOPE_API_KEY')
                    if not api_key:
                        yield f"data: {json.dumps({'type': 'error', 'error': '千问API配置缺失'}, ensure_ascii=False)}\n\n"
                        return
                    
                    logger.info(f"开始本地音频分析，音频格式: {audio_format}，跳过积分检查: {current_skip_credit_check}")
                    
                    # 构建API请求
                    from openai import OpenAI
                    import time
                    
                    client = OpenAI(
                        api_key=api_key,
                        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                        timeout=120.0,
                        max_retries=2
                    )
                    
                    system_prompt = "你是一个专业的英汉口译专家，能够准确理解并翻译音频内容，请先给出音频的transcript，然后进行翻译和解释，并就其中涉及的语法结构、习语和习惯用法、文化背景提供解释和分析。"
                    
                    messages = [
                        {"role": "user", "content": system_prompt},
                        {"role": "assistant", "content": "好的，我记住了你的设定。"},
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "input_audio",
                                    "input_audio": {
                                        "data": f"data:;base64,{audio_data}",
                                        "format": audio_format,
                                    },
                                },
                                {"type": "text", "text": prompt},
                            ],
                        }
                    ]
                    
                    # 流式请求
                    stream = client.chat.completions.create(
                        model="qwen-omni-turbo",
                        messages=messages,
                        modalities=["text"],
                        stream=True,
                        stream_options={"include_usage": True},
                        timeout=60.0
                    )
                    
                    # 发送开始事件
                    yield f"data: {json.dumps({'type': 'start', 'message': '开始分析音频...'}, ensure_ascii=False)}\n\n"
                    
                    actual_usage = None
                    for chunk in stream:
                        # 检查usage信息
                        if hasattr(chunk, 'usage') and chunk.usage:
                            logger.info(f"本地音频分析 API Usage信息: {chunk.usage}")
                            actual_usage = chunk.usage
                        
                        if chunk.choices and chunk.choices[0].delta.content:
                            content = chunk.choices[0].delta.content
                            accumulated_content += content
                            yield f"data: {json.dumps({'type': 'chunk', 'accumulated': accumulated_content}, ensure_ascii=False)}\n\n"
                    
                    # 发送完成事件
                    yield f"data: {json.dumps({'type': 'complete', 'final_content': accumulated_content}, ensure_ascii=False)}\n\n"
                    
                    # 扣减积分
                    try:
                        with app.app_context():
                            if user_id and actual_usage:
                                usage_info = {
                                    'input_tokens': actual_usage.prompt_tokens,
                                    'output_tokens': actual_usage.completion_tokens,
                                    'total_tokens': actual_usage.total_tokens
                                }
                                
                                if hasattr(actual_usage, 'prompt_tokens_details') and actual_usage.prompt_tokens_details:
                                    usage_info['prompt_tokens_details'] = {
                                        'audio_tokens': actual_usage.prompt_tokens_details.audio_tokens or 0,
                                        'text_tokens': actual_usage.prompt_tokens_details.text_tokens or 0,
                                        'cached_tokens': actual_usage.prompt_tokens_details.cached_tokens or 0
                                    }
                                
                                logger.info(f"本地音频分析使用实际usage信息扣减积分: {usage_info}")
                                on_completion(user_id, usage_info)
                            elif user_id:
                                token_count = len(accumulated_content) // 2
                                logger.warning(f"本地音频分析未获取到实际usage信息，使用估算: {token_count}")
                                on_completion(user_id, {'input_tokens': token_count, 'output_tokens': token_count})
                    except Exception as credit_error:
                        logger.error(f"本地音频分析积分扣减失败: {credit_error}")
                        
                except Exception as e:
                    logger.error(f"本地音频分析失败: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"
            
            # 异步循环处理
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                async_gen = async_generate()
                while True:
                    try:
                        chunk = loop.run_until_complete(async_gen.__anext__())
                        yield chunk
                    except StopAsyncIteration:
                        break
            finally:
                loop.close()
        
        return Response(generate(), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive'
        })
    except Exception as e:
        logger.error(f"本地音频分析失败: {e}")
        return jsonify({"error": f"本地音频分析失败: {str(e)}"}), 500

# 音频分析路由已移至 api/audio_routes.py
# 原有的 analyze_audio_stream 函数已迁移

@app.route('/api/ask-question-stream', methods=['POST'])
@create_rate_limiter("content", "smart_qa")
def ask_question_stream():
    """流式智能问答"""
    try:
        data = request.json
        file_id = data.get('file_id')
        question = data.get('question')
        current_time = data.get('current_time')
        snippet_duration = data.get('snippet_duration', 6)
        
        if not file_id or not question:
            return jsonify({"error": "缺少必要参数"}), 400
            
        # 移除积分检查，将其移动到音频处理之后
        from api.credits import check_user_credits_for_operation, deduct_user_credits
        auth_token = request.headers.get('Authorization', '').replace('Bearer ', '')

        def on_completion(user_id, usage_info):
            """AI操作完成后的回调，用于扣减积分"""
            try:
                # 获取音频文件原始名称用于记录
                audio_filename = ''
                try:
                    if hasattr(audio_manager, 'original_filenames') and file_id in audio_manager.original_filenames:
                        audio_filename = audio_manager.original_filenames[file_id]
                    else:
                        # 如果没有原始文件名，使用file_id
                        audio_filename = f"音频文件_{file_id[:8]}"
                except Exception:
                    audio_filename = f"音频文件_{file_id[:8]}" if file_id else "未知音频"
                
                result = deduct_user_credits(
                    user_id, 'smart_qa', 'qwen_omni_turbo',
                    token_usage=usage_info,
                    audio_info=audio_filename
                )
                if result.get('success'):
                    logger.info(f"智能问答积分扣减成功: {result}")
                else:
                    logger.error(f"智能问答积分扣减失败: {result}")
            except Exception as e:
                logger.error(f"智能问答积分扣减异常: {e}")

        def extract_audio_snippet_legacy(audio_path, current_time, duration):
            """提取音频片段（遗留方法，仅作兼容）"""
            try:
                audio = AudioSegment.from_file(audio_path)
                end_ms = int(current_time * 1000)
                start_ms = max(0, end_ms - (duration * 1000))
                if end_ms > len(audio):
                    end_ms = len(audio)
                
                snippet = audio[start_ms:end_ms]
                
                # 保存片段
                snippet_id = str(uuid.uuid4())
                snippet_filename = f"snippet_{snippet_id}.wav"
                snippet_path = os.path.join("temp", snippet_filename)
                snippet.export(snippet_path, format="wav")
                
                return snippet_path
            except Exception as e:
                logger.error(f"提取音频片段失败: {e}")
                return None

        def generate_stream():
            async def async_generate():
                import re
                try:
                    # 检查文件是否存在
                    try:
                        audio_path = await audio_manager.get_audio_path(file_id)
                    except FileNotFoundError:
                        # 兼容模式：从全局字典获取
                        if not hasattr(app, 'audio_files') or file_id not in app.audio_files:
                            yield f"data: {json.dumps({'type': 'error', 'error': '音频文件不存在'}, ensure_ascii=False)}\n\n"
                            return
                        audio_path = app.audio_files[file_id]
                    
                    if not os.path.exists(audio_path):
                        yield f"data: {json.dumps({'type': 'error', 'error': '音频文件已被删除'}, ensure_ascii=False)}\n\n"
                        return
                    
                    # 如果提供了当前时间，使用音频片段
                    if current_time is not None:
                        try:
                            # 使用音频管理器提取音频片段
                            snippet_id = await audio_manager.extract_audio_snippet(file_id, current_time, snippet_duration)
                            snippet_path = await audio_manager.get_audio_path(snippet_id)
                            
                            # 立即发送snippet信息给前端，让前端可以立即加载snippet
                            snippet_url = f"/api/audio-file/{snippet_id}"
                            snippet_data = {
                                'type': 'snippet_ready', 
                                'snippet_info': {
                                    'snippet_url': snippet_url, 
                                    'snippet_file_id': snippet_id
                                }
                            }
                            yield f"data: {json.dumps(snippet_data, ensure_ascii=False)}\n\n"
                            
                        except Exception as e:
                            logger.error(f"音频片段提取失败: {e}")
                            error_msg = "提取音频片段失败"
                            yield f"data: {json.dumps({'type': 'error', 'error': error_msg}, ensure_ascii=False)}\n\n"
                            return
                        
                        # 音频片段提取完成后，进行积分检查
                        multimodal_info = {
                            "audio_duration": snippet_duration,
                            "has_audio": True,
                            "has_image": False
                        }
                        
                        has_credits, user_id, credits_info = check_user_credits_for_operation(
                            auth_token, 'smart_qa', 'qwen_omni_turbo',
                            content=question,
                            multimodal_info=multimodal_info
                        )
                        
                        if not has_credits:
                            yield f"data: {json.dumps({'type': 'error', 'error': '积分不足'}, ensure_ascii=False)}\n\n"
                            return
                    
                    # 获取千问API配置
                    api_key = os.getenv('DASHSCOPE_API_KEY')
                    if not api_key:
                        yield f"data: {json.dumps({'type': 'error', 'error': '千问API配置缺失'}, ensure_ascii=False)}\n\n"
                        return
                    
                    from openai import OpenAI
                    import time
                    
                    client = OpenAI(
                        api_key=api_key,
                        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                        timeout=60.0,
                        max_retries=3
                    )
                    
                    if current_time is not None:
                        # 编码音频片段
                        import base64
                        with open(snippet_path, "rb") as audio_file:
                            base64_audio = base64.b64encode(audio_file.read()).decode("utf-8")
                        
                        # 创建详细的提示词
                        combined_prompt = f"""你是一个专业的英汉口译专家，能够准确理解并翻译音频内容。

这是用户刚才听到的前{snippet_duration}秒音频内容，请按以下步骤进行分析：

1. 首先给出音频的完整transcript（英文原文）
2. 然后提供准确的中文翻译
3. 针对其中的语法结构、习语和习惯用法进行详细解释
4. 分析相关的文化背景和语言特点

请确保提供详细的解释和分析，而不仅仅是简单的转录。"""
                        
                        # 系统消息
                        system_message = {
                            "role": "system", 
                            "content": "你是一个专业的英汉口译专家。对于任何音频内容，你必须提供：1)完整transcript 2)准确翻译 3)语法解释 4)文化背景分析。绝不能只提供简单的转录，必须包含详细的解释和分析。"
                        }
                        
                        messages = [
                            system_message,
                            {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "input_audio",
                                        "input_audio": {
                                            "data": f"data:;base64,{audio_data}",
                                            "format": "mp3",
                                        },
                                    },
                                    {"type": "text", "text": combined_prompt},
                                ],
                            }
                        ]
                        
                        # 注意：音频片段不再需要手动清理，由音频管理器自动管理
                    else:
                        # 基于历史的智能问答改为：只发送当前问题，不使用任何音频上下文
                        # 这样可以最小化token消耗
                        messages = [
                            {
                                "role": "system",
                                "content": "你是一个智能助手，请直接回答用户的问题。"
                            },
                            {
                                "role": "user",
                                "content": question
                            }
                        ]
                    
                    # 增加重试机制的流式请求
                    max_retries = 3
                    retry_delay = 1
                    
                    for attempt in range(max_retries):
                        try:
                            stream = client.chat.completions.create(
                                model="qwen-omni-turbo",
                                messages=messages,
                                modalities=["text"],
                                stream=True,
                                stream_options={"include_usage": True},
                                timeout=60.0
                            )
                            break  # 成功则跳出重试循环
                        except Exception as api_error:
                            logger.warning(f"智能问答API调用失败 (尝试 {attempt + 1}/{max_retries}): {api_error}")
                            if attempt == max_retries - 1:
                                raise api_error
                            time.sleep(retry_delay)
                            retry_delay *= 2
                    
                    # 发送开始事件
                    yield f"data: {json.dumps({'type': 'start', 'message': '正在处理您的问题...'}, ensure_ascii=False)}\n\n"
                    
                    complete_content = ""
                    actual_usage = None  # 存储实际的usage信息
                    for chunk in stream:
                        # 检查并打印usage信息
                        if hasattr(chunk, 'usage') and chunk.usage:
                            logger.info(f"API Usage信息 (智能问答): {chunk.usage}")
                            print(f"API Usage信息 (智能问答): {chunk.usage}")
                            actual_usage = chunk.usage  # 保存实际usage信息
                        
                        if chunk.choices and chunk.choices[0].delta.content:
                            content = chunk.choices[0].delta.content
                            complete_content += content
                            yield f"data: {json.dumps({'type': 'chunk', 'accumulated': complete_content}, ensure_ascii=False)}\n\n"
                    
                    # 智能问答不保存任何会话历史，每次都是独立的请求
                    # 当提供了current_time时：基于音频片段回答
                    # 当没有提供current_time时：纯文本问答，token消耗最小
                    
                    yield f"data: {json.dumps({'type': 'complete', 'final_content': complete_content}, ensure_ascii=False)}\n\n"
                    
                    # 扣减积分 - 使用实际usage信息
                    try:
                        with app.app_context():
                            if user_id and actual_usage:
                                # 转换实际usage信息为我们需要的格式，包括详细的token分解
                                usage_info = {
                                    'input_tokens': actual_usage.prompt_tokens,
                                    'output_tokens': actual_usage.completion_tokens,
                                    'total_tokens': actual_usage.total_tokens
                                }
                                
                                # 如果有详细的token分解信息，也包括进来
                                if hasattr(actual_usage, 'prompt_tokens_details') and actual_usage.prompt_tokens_details:
                                    usage_info['prompt_tokens_details'] = {
                                        'audio_tokens': actual_usage.prompt_tokens_details.audio_tokens or 0,
                                        'text_tokens': actual_usage.prompt_tokens_details.text_tokens or 0,
                                        'cached_tokens': actual_usage.prompt_tokens_details.cached_tokens or 0
                                    }
                                
                                if hasattr(actual_usage, 'completion_tokens_details') and actual_usage.completion_tokens_details:
                                    usage_info['completion_tokens_details'] = {
                                        'text_tokens': actual_usage.completion_tokens_details.text_tokens or 0,
                                        'audio_tokens': actual_usage.completion_tokens_details.audio_tokens or 0
                                    }
                                
                                logger.info(f"使用实际usage信息扣减积分: {usage_info}")
                                on_completion(user_id, usage_info)
                            elif user_id:
                                # 如果没有actual_usage，使用估算（不应该发生）
                                token_count = len(complete_content) // 2
                                logger.warning(f"未获取到实际usage信息，使用估算: input/output={token_count}")
                                on_completion(user_id, {'input_tokens': token_count, 'output_tokens': token_count})
                            else:
                                logger.error("无法扣减积分：未获取到user_id")
                    except Exception as credit_error:
                        logger.error(f"智能问答积分扣减失败: {credit_error}")
                        
                except Exception as e:
                    logger.error(f"智能问答失败: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"
            
            # 参考代码的异步循环处理方式
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                async_gen = async_generate()
                while True:
                    try:
                        chunk = loop.run_until_complete(async_gen.__anext__())
                        yield chunk
                    except StopAsyncIteration:
                        break
            finally:
                loop.close()
        
        return Response(stream_with_context(generate_stream()), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no',  # 禁用nginx缓冲
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        })
    except Exception as e:
        logger.error(f"流式智能问答失败: {e}")
        return jsonify({"error": f"流式智能问答失败: {str(e)}"}), 500

@app.route('/api/continue-conversation-stream', methods=['POST'])
@create_rate_limiter("content", "smart_qa")
def continue_conversation_stream():
    """流式继续对话"""
    try:
        data = request.json
        file_id = data.get('file_id')
        message = data.get('question') or data.get('message')  # 兼容前端发送的question或message字段
        snippet_duration = data.get('snippet_duration', 6)  # 获取音频片段时长
        
        if not file_id or not message:
            return jsonify({"error": "缺少必要参数"}), 400
            
        # 检查积分 - 支持多模态估算
        from api.credits import check_user_credits_for_operation
        auth_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        
        # 连续对话基于音频片段进行，需要多模态积分预估
        # 使用实际的音频片段时长
        multimodal_info = {
            "audio_duration": snippet_duration,  # 使用实际的音频片段时长
            "has_audio": True,
            "has_image": False
        }
        
        has_credits, user_id, credits_info = check_user_credits_for_operation(
            auth_token, 'smart_qa', 'qwen_omni_turbo',
            content=message,
            multimodal_info=multimodal_info
        )
        
        if not has_credits:
            return jsonify({
                "success": False,
                "error": "积分不足",
                **credits_info
            }), 402

        def on_completion(user_id, usage_info):
            """AI操作完成后的回调，用于扣减积分"""
            try:
                from api.credits import deduct_user_credits
                
                # 获取音频文件原始名称用于记录
                audio_filename = ''
                try:
                    if hasattr(audio_manager, 'original_filenames') and file_id in audio_manager.original_filenames:
                        audio_filename = audio_manager.original_filenames[file_id]
                    else:
                        # 如果没有原始文件名，使用file_id
                        audio_filename = f"音频文件_{file_id[:8]}"
                except Exception:
                    audio_filename = f"音频文件_{file_id[:8]}" if file_id else "未知音频"
                
                result = deduct_user_credits(
                    user_id, 'smart_qa', 'qwen_omni_turbo',
                    token_usage=usage_info,
                    audio_info=audio_filename
                )
                if result.get('success'):
                    logger.info(f"继续对话积分扣减成功: {result}")
                else:
                    logger.error(f"继续对话积分扣减失败: {result}")
            except Exception as e:
                logger.error(f"继续对话积分扣减异常: {e}")

        def generate_stream():
            async def async_generate():
                import re
                try:
                    # 连续对话完全独立模式，直接基于当前音频片段进行对话
                    # 不需要检查会话历史，因为UI设计已确保用户先分析音频才能对话
                    
                    # 获取千问API配置
                    api_key = os.getenv('DASHSCOPE_API_KEY')
                    if not api_key:
                        yield f"data: {json.dumps({'type': 'error', 'error': '千问API配置缺失'}, ensure_ascii=False)}\n\n"
                        return
                    
                    from openai import OpenAI
                    import time
                    
                    client = OpenAI(
                        api_key=api_key,
                        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
                        timeout=60.0,
                        max_retries=3
                    )
                    
                    # 连续对话直接使用当前音频文件，不重新提取片段
                    # 检查音频文件是否存在
                    try:
                        audio_path = await audio_manager.get_audio_path(file_id)
                    except FileNotFoundError:
                        if not hasattr(app, 'audio_files') or file_id not in app.audio_files:
                            yield f"data: {json.dumps({'type': 'error', 'error': '音频文件不存在'}, ensure_ascii=False)}\n\n"
                            return
                        audio_path = app.audio_files[file_id]
                    
                    if not os.path.exists(audio_path):
                        yield f"data: {json.dumps({'type': 'error', 'error': '音频文件已被删除'}, ensure_ascii=False)}\n\n"
                        return
                    
                    # 直接使用现有音频文件，不重新提取片段
                    # 编码音频文件
                    import base64
                    with open(audio_path, "rb") as audio_file:
                        base64_audio = base64.b64encode(audio_file.read()).decode("utf-8")
                    
                    # 为连续对话设计的简洁系统提示词
                    system_message = {
                        "role": "system",
                        "content": "你是一个智能助手，基于音频内容回答用户问题。请直接、简洁地回答用户的问题。"
                    }
                    
                    # 构建消息：系统消息 + 音频 + 用户问题
                    messages = [
                        system_message,
                        {
                            "role": "user",
                            "content": [
                                {
                                    "type": "input_audio",
                                    "input_audio": {
                                        "data": f"data:;base64,{base64_audio}",
                                        "format": "mp3",
                                    },
                                },
                                {"type": "text", "text": message},
                            ],
                        }
                    ]
                    
                    # 增加重试机制的流式请求
                    max_retries = 3
                    retry_delay = 1
                    
                    for attempt in range(max_retries):
                        try:
                            stream = client.chat.completions.create(
                                model="qwen-omni-turbo",
                                messages=messages,
                                modalities=["text"],
                                stream=True,
                                stream_options={"include_usage": True},
                                timeout=60.0
                            )
                            break  # 成功则跳出重试循环
                        except Exception as api_error:
                            logger.warning(f"继续对话API调用失败 (尝试 {attempt + 1}/{max_retries}): {api_error}")
                            if attempt == max_retries - 1:
                                raise api_error
                            time.sleep(retry_delay)
                            retry_delay *= 2
                    
                    # 发送开始事件
                    yield f"data: {json.dumps({'type': 'start', 'message': 'AI正在思考...'}, ensure_ascii=False)}\n\n"
                    
                    complete_content = ""
                    actual_usage = None  # 存储实际的usage信息
                    for chunk in stream:
                        # 检查并打印usage信息
                        if hasattr(chunk, 'usage') and chunk.usage:
                            logger.info(f"API Usage信息 (继续对话): {chunk.usage}")
                            print(f"API Usage信息 (继续对话): {chunk.usage}")
                            actual_usage = chunk.usage  # 保存实际usage信息
                        
                        if chunk.choices and chunk.choices[0].delta.content:
                            content = chunk.choices[0].delta.content
                            complete_content += content
                            yield f"data: {json.dumps({'type': 'chunk', 'accumulated': complete_content}, ensure_ascii=False)}\n\n"
                    
                    # 连续对话不保存任何会话历史，每次都是独立的请求
                    # 这样可以确保token消耗最小
                    
                    yield f"data: {json.dumps({'type': 'complete', 'final_content': complete_content}, ensure_ascii=False)}\n\n"
                    
                    # 扣减积分 - 使用实际usage信息
                    try:
                        with app.app_context():
                            if user_id and actual_usage:
                                # 转换实际usage信息为我们需要的格式，包括详细的token分解
                                usage_info = {
                                    'input_tokens': actual_usage.prompt_tokens,
                                    'output_tokens': actual_usage.completion_tokens,
                                    'total_tokens': actual_usage.total_tokens
                                }
                                
                                # 如果有详细的token分解信息，也包括进来
                                if hasattr(actual_usage, 'prompt_tokens_details') and actual_usage.prompt_tokens_details:
                                    usage_info['prompt_tokens_details'] = {
                                        'audio_tokens': actual_usage.prompt_tokens_details.audio_tokens or 0,
                                        'text_tokens': actual_usage.prompt_tokens_details.text_tokens or 0,
                                        'cached_tokens': actual_usage.prompt_tokens_details.cached_tokens or 0
                                    }
                                
                                if hasattr(actual_usage, 'completion_tokens_details') and actual_usage.completion_tokens_details:
                                    usage_info['completion_tokens_details'] = {
                                        'text_tokens': actual_usage.completion_tokens_details.text_tokens or 0,
                                        'audio_tokens': actual_usage.completion_tokens_details.audio_tokens or 0
                                    }
                                
                                logger.info(f"继续对话使用实际usage信息扣减积分: {usage_info}")
                                on_completion(user_id, usage_info)
                            elif user_id:
                                # 如果没有actual_usage，使用估算（不应该发生）
                                token_count = len(complete_content) // 2
                                logger.warning(f"继续对话未获取到实际usage信息，使用估算: input/output={token_count}")
                                on_completion(user_id, {'input_tokens': token_count, 'output_tokens': token_count})
                            else:
                                logger.error("无法扣减积分：未获取到user_id")
                    except Exception as credit_error:
                        logger.error(f"继续对话积分扣减失败: {credit_error}")
                        
                except Exception as e:
                    logger.error(f"继续对话失败: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"
            
            # 参考代码的异步循环处理方式
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                async_gen = async_generate()
                while True:
                    try:
                        chunk = loop.run_until_complete(async_gen.__anext__())
                        yield chunk
                    except StopAsyncIteration:
                        break
            finally:
                loop.close()
        
        return Response(stream_with_context(generate_stream()), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no',  # 禁用nginx缓冲
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        })
    except Exception as e:
        logger.error(f"流式继续对话失败: {e}")
        return jsonify({"error": f"流式继续对话失败: {str(e)}"}), 500

@app.route('/api/generate-timestamps', methods=['POST'])
@create_rate_limiter("content", "generate_timestamps")
def generate_timestamps():
    """从字幕文件生成时间戳"""
    try:
        data = request.json
        subtitle_file_id = data.get('subtitle_file_id')
        
        if not subtitle_file_id:
            return jsonify({"error": "缺少字幕文件ID"}), 400
            
        auth_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        
        def generate_stream():
            async def async_generate():
                import re
                accumulated_content = ""
                user_id = None
                
                try:
                    # 由 check_user_credits_for_operation 内部完成用户身份验证，避免重复调用
                    from api.credits import check_user_credits_for_operation
                    has_credits, user_id, credits_info = check_user_credits_for_operation(
                        auth_token, 'generate_timestamps', 'zhipu_flash',
                        content="Generate timestamps from subtitle",
                        multimodal_info={"has_audio": False, "has_image": False, "text_only": True}
                    )
                    if not has_credits:
                        yield f"data: {json.dumps({'type': 'error', 'error': '积分不足', **credits_info}, ensure_ascii=False)}\n\n"
                        return
                    
                    yield f"data: {json.dumps({'type': 'progress', 'message': '开始生成时间戳...'}, ensure_ascii=False)}\n\n"
                    
                    # 发送测试进度事件，确保SSE连接正常
                    yield f"data: {json.dumps({'type': 'progress', 'message': '开始实时监控AI输出...'}, ensure_ascii=False)}\n\n"
                    
                    # 调用chapter.py生成时间戳
                    import subprocess
                    import tempfile
                    import shutil
                    
                    # 创建临时工作目录
                    with tempfile.TemporaryDirectory() as temp_dir:
                        # 复制字幕文件到临时目录
                        temp_srt_path = os.path.join(temp_dir, 'test.srt')
                        shutil.copy2(subtitle_file_path, temp_srt_path)
                        
                        # 复制chapter.py到临时目录
                        chapter_py_path = os.path.join(temp_dir, 'chapter.py')
                        shutil.copy2('chapter.py', chapter_py_path)
                        
                        # 添加日志：记录临时目录和文件列表
                        logger.info(f"生成时间戳 - 临时工作目录: {temp_dir}, 文件列表: {os.listdir(temp_dir)}")

                        yield f"data: {json.dumps({'type': 'progress', 'message': '正在调用AI生成章节摘要...'}, ensure_ascii=False)}\n\n"
                        
                        # 运行chapter.py
                        try:
                            # 添加日志：记录执行命令和工作目录
                            logger.info(f"生成时间戳 - 执行命令: python chapter.py, cwd={temp_dir}")
                            process = subprocess.Popen(
                                ['python', 'chapter.py'],
                                cwd=temp_dir,
                                stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE,
                                universal_newlines=True,
                                bufsize=1
                            )
                            
                            # 监控进程输出 - 适应智谱API真正流式输出的优化版解析
                            sent_timestamps = set()  # 跟踪已发送的时间戳，避免重复  
                            
                            while True:
                                output = process.stdout.readline()
                                if output == '' and process.poll() is not None:
                                    break
                                    
                                if output:
                                    line = output.strip()
                                    # 添加日志：记录stdout内容
                                    logger.info(f"chapter.py stdout: {line}")
                                    
                                    # 跳过STREAM_CHUNK输出和其他系统消息
                                    if (line.startswith('STREAM_CHUNK:') or 
                                        line.startswith('[') or
                                        'API' in line or
                                        '正在' in line or
                                        '开始接收' in line or
                                        '完整响应:' in line or
                                        line.startswith('完整响应:') or
                                        line.startswith('摘要:')):
                                        continue
                                    
                                    # 检测完整的时间戳行（格式：HH:MM:SS - 标题 或 HH:MM:SS,mmm - 标题）
                                    import re
                                    # 支持两种时间戳格式：HH:MM:SS 和 HH:MM:SS,mmm
                                    timestamp_line_regex = re.compile(r'^(\d{2}):(\d{2}):(\d{2})(?:,\d+)?\s*-\s*(.+)$')
                                    match = timestamp_line_regex.match(line)
                                    
                                    if match:
                                        # 流式输出：只发送时间戳标题，不发送内容（避免STREAM_CHUNK污染）
                                        h, m, s, title = match.groups()
                                        total_seconds = int(h) * 3600 + int(m) * 60 + int(s)
                                        
                                        if total_seconds not in sent_timestamps:
                                            timestamp_data = {
                                                'type': 'timestamp',
                                                'time': total_seconds,
                                                'title': title,
                                                'content': []  # 流式输出时不发送内容，避免污染
                                            }
                                            sse_data = f"data: {json.dumps(timestamp_data, ensure_ascii=False)}\n\n"
                                            yield sse_data
                                            sent_timestamps.add(total_seconds)
                                            logger.info(f"🚀 流式发送时间戳标题: {total_seconds}s - {title}")
                            
                            # 流式输出完成提示
                            yield f"data: {json.dumps({'type': 'progress', 'message': '流式输出完成，准备解析详细内容...'}, ensure_ascii=False)}\n\n"
                            
                            # 检查进程是否成功完成
                            return_code = process.poll()
                            # 添加日志：记录子进程返回码
                            logger.info(f"chapter.py 返回码: {return_code}")
                            if return_code != 0:
                                stderr_output = process.stderr.read()
                                logger.error(f"chapter.py执行失败: {stderr_output}")
                                yield f"data: {json.dumps({'type': 'error', 'error': '生成章节摘要失败'}, ensure_ascii=False)}\n\n"
                                return
                        
                        except Exception as process_error:
                            logger.error(f"执行chapter.py时出错: {process_error}")
                            yield f"data: {json.dumps({'type': 'error', 'error': '生成过程出错'}, ensure_ascii=False)}\n\n"
                            return
                        
                        # 读取生成的chapters.txt文件
                        chapters_file = os.path.join(temp_dir, 'chapters.txt')
                        if not os.path.exists(chapters_file):
                            yield f"data: {json.dumps({'type': 'error', 'error': '未生成章节文件'}, ensure_ascii=False)}\n\n"
                            return
                        
                        yield f"data: {json.dumps({'type': 'progress', 'message': '解析生成的章节内容...'}, ensure_ascii=False)}\n\n"
                        
                        # 读取生成的文本内容
                        with open(chapters_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        # 添加日志：记录chapters.txt内容预览
                        logger.info(f"生成时间戳 - chapters.txt内容预览 (前500字符): {content[:500]}")
                        # 保存完整的chapters.txt到全局temp文件夹，供下载/调试
                        saved_chapters = os.path.join('temp', f"{subtitle_file_id}_chapters.txt")
                        shutil.copy2(chapters_file, saved_chapters)
                        logger.info(f"生成时间戳 - 保存完整章节文件至: {saved_chapters}")
                        # 解析生成的章节内容，匹配每个时间戳块直到下一个时间戳
                        import re
                        # 正则：匹配HH:MM:SS或HH:MM:SS,mmm - 随后所有文本块，直到下一个时间戳行或文件结尾
                        timestamp_regex = re.compile(
                            r'(?P<h>\d{2}):(?P<m>\d{2}):(?P<s>\d{2})(?:,\d+)?\s*-\s*(?P<block>.*?)'  
                            r'(?=(?:\n\d{2}:\d{2}:\d{2}(?:,\d+)?\s*-)|\Z)',
                            re.DOTALL
                        )
                        matches = list(timestamp_regex.finditer(content))
                         
                        if not matches:
                            # 调试：记录完整content
                            logger.error(f"未找到有效的时间戳格式, chapters.txt完整内容:\n{content}")
                            yield f"data: {json.dumps({'type': 'error', 'error': '解析章节失败'}, ensure_ascii=False)}\n\n"
                            return
                        
                        # 发送最终文件解析的时间戳数据（作为备用方案）
                        remaining_count = len([m for m in matches if (int(m.group('h')) * 3600 + int(m.group('m')) * 60 + int(m.group('s'))) not in sent_timestamps])
                        logger.info(f"流式解析已发送 {len(sent_timestamps)} 个，从最终文件补充发送 {remaining_count} 个时间戳")
                        for m in matches:
                            h = m.group('h')
                            mi = m.group('m')
                            s = m.group('s')
                            block = m.group('block')
                            total_seconds = int(h) * 3600 + int(mi) * 60 + int(s)
                            # 将文本块拆分为标题和内容行
                            lines = [line.strip() for line in block.strip().split('\n') if line.strip()]
                            title = lines[0] if lines else ''
                            content_lines = lines[1:]
                            
                            # 只发送之前流式阶段已发送过的时间戳的内容更新
                            if total_seconds in sent_timestamps:
                                timestamp_data = {
                                    'type': 'timestamp_update',  # 特殊类型，告诉前端这是更新
                                    'time': total_seconds,
                                    'title': title,
                                    'content': content_lines  # 发送数组格式
                                }
                                yield f"data: {json.dumps(timestamp_data, ensure_ascii=False)}\n\n"
                                logger.info(f"📝 更新时间戳内容: {total_seconds}s - {title} (内容行数: {len(content_lines)})")
                            else:
                                # 如果流式阶段遗漏了某些时间戳，作为新时间戳发送
                                timestamp_data = {
                                    'type': 'timestamp',
                                    'time': total_seconds,
                                    'title': title,
                                    'content': content_lines
                                }
                                yield f"data: {json.dumps(timestamp_data, ensure_ascii=False)}\n\n"
                                sent_timestamps.add(total_seconds)
                                logger.info(f"📋 补充遗漏时间戳: {total_seconds}s - {title}")
                        # 扣减积分 - 基于实际内容计算
                        from api.credits import deduct_user_credits
                        import tiktoken
                        
                        # 计算实际的token使用量
                        try:
                            # 使用tiktoken计算输入和输出tokens
                            encoding = tiktoken.get_encoding("o200k_base")
                            
                            # 计算输入tokens（字幕内容）
                            with open(temp_srt_path, 'r', encoding='utf-8') as f:
                                subtitle_content = f.read()
                            input_tokens = len(encoding.encode(subtitle_content))
                            
                            # 计算输出tokens（生成的章节内容）
                            output_tokens = len(encoding.encode(content))
                            
                            total_tokens = input_tokens + output_tokens
                            logger.info(f"实际token使用量: 输入={input_tokens}, 输出={output_tokens}, 总计={total_tokens}")
                            
                            usage_info = {
                                'input_tokens': input_tokens,
                                'output_tokens': output_tokens, 
                                'total_tokens': total_tokens
                            }
                        except Exception as token_error:
                            logger.warning(f"Token计算失败，使用估算值: {token_error}")
                            # 备用估算：每个时间戳约1000 tokens
                            estimated_tokens = max(len(matches) * 1000, 2000)  
                            usage_info = {'total_tokens': estimated_tokens}
                        
                        deduct_result = deduct_user_credits(
                            user_id,
                            'generate_timestamps',
                            'zhipu_flash',
                            token_usage=usage_info,
                            audio_info=f'Generated {len(matches)} timestamps, tokens: {usage_info.get("total_tokens", "unknown")}'
                        )
                        
                        if deduct_result['success']:
                            logger.info(f"生成时间戳积分扣减成功: {deduct_result}")
                            # 发送完成消息，包含积分扣减信息
                            completion_message = {
                                'type': 'complete', 
                                'message': f'成功生成 {len(matches)} 个时间戳',
                                'credits_info': {
                                    'credits_deducted': deduct_result.get('credits_deducted', 0),
                                    'remaining_credits': deduct_result.get('remaining_credits', 0),
                                    'tokens_used': usage_info.get('total_tokens', 0)
                                }
                            }
                            yield f"data: {json.dumps(completion_message, ensure_ascii=False)}\n\n"
                        else:
                            logger.error(f"生成时间戳积分扣减失败: {deduct_result}")
                            # 即使积分扣减失败，也要通知前端完成
                            error_msg = deduct_result.get("error", "未知错误")
                            yield f"data: {json.dumps({'type': 'complete', 'message': f'生成完成，但积分扣减失败: {error_msg}'}, ensure_ascii=False)}\n\n"
                        
                except Exception as e:
                    logger.error(f"生成时间戳过程出错: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'error': f'生成失败: {str(e)}'}, ensure_ascii=False)}\n\n"
            
            # 异步循环处理
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                async_gen = async_generate()
                while True:
                    try:
                        chunk = loop.run_until_complete(async_gen.__anext__())
                        yield chunk
                    except StopAsyncIteration:
                        break
            finally:
                loop.close()
        
        return Response(stream_with_context(generate_stream()), mimetype='text/event-stream', headers={
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no',  # 禁用nginx缓冲
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        })
        
    except Exception as e:
        logger.error(f"生成时间戳API失败: {e}")
        return jsonify({"error": f"生成时间戳失败: {str(e)}"}), 500

@app.route('/api/upload-subtitle', methods=['POST'])
@create_rate_limiter("content", "upload_subtitle")
def upload_subtitle():
    """上传字幕文件到服务器"""
    # 检查是否要求跳过积分检查
    skip_credits_check = request.form.get('skip_credits_check', 'false').lower() == 'true'
    
    # 积分检查 - 统一导入
    from api.config import CHECK_CREDITS_UPLOAD_SUBTITLE
    from api.credits import get_user_id_from_request, get_user_credits_from_cache, service_supabase_client
    from api.credits_cache import cache_user_credits
    
    if CHECK_CREDITS_UPLOAD_SUBTITLE and not skip_credits_check:
        user_id = get_user_id_from_request()
        if not user_id:
            return jsonify({"success": False, "error": "需要登录才能使用此功能"}), 401
        current_credits = get_user_credits_from_cache(user_id)
        if current_credits is None:
            credits_resp = service_supabase_client.table("user_credits").select("credits").eq("user_id", user_id).execute()
            data = credits_resp.data or []
            current_credits = data[0].get("credits", 0) if data else 0
            cache_user_credits(user_id, current_credits)
        if current_credits < 1:
            return jsonify({"success": False, "error": "积分不足，请充值", "current_credits": current_credits, "required_credits": 1}), 200
    elif skip_credits_check:
        # 即使前端要求跳过，后端也要验证用户身份和基本积分阈值
        user_id = get_user_id_from_request()
        if not user_id:
            return jsonify({"success": False, "error": "需要登录才能使用此功能"}), 401
        
        # 后端缓存验证（安全措施）
        cached_credits = get_user_credits_from_cache(user_id)
        threshold = 11  # 字幕上传需要1积分 + 10缓冲
        
        # 如果缓存未命中，主动查询数据库
        if cached_credits is None:
            logger.info(f"🔍 [字幕上传积分] 缓存未命中，查询数据库: 用户={user_id}")
            credits_resp = service_supabase_client.table("user_credits").select("credits").eq("user_id", user_id).execute()
            data = credits_resp.data or []
            cached_credits = data[0].get("credits", 0) if data else 0
            cache_user_credits(user_id, cached_credits)
            logger.info(f"🔍 [字幕上传积分] 数据库查询结果: 用户={user_id}, 积分={cached_credits}")
        
        if cached_credits < threshold:
            logger.warning(f"🔍 [字幕上传积分] 后端缓存验证失败: 缓存积分={cached_credits}, 阈值={threshold}")
            return jsonify({"success": False, "error": "积分不足，请充值", "current_credits": cached_credits, "required_credits": 1}), 200
        
        logger.info(f"🚀 [字幕上传积分] 跳过积分检查生效，后端缓存验证通过: 积分={cached_credits}")
    
    try:
        if 'subtitleFile' not in request.files:
            return jsonify({"error": "没有找到字幕文件"}), 400

        file = request.files['subtitleFile']
        file_id = request.form.get('file_id')
        
        if not file_id or file.filename == '':
            return jsonify({"error": "缺少必要参数或文件名"}), 400
        
        # 确保temp目录存在
        temp_dir = "temp"
        os.makedirs(temp_dir, exist_ok=True)
        
        # 直接保存原始文件
        subtitle_path = os.path.join(temp_dir, f'{file_id}.srt')
        file.save(subtitle_path)
        
        logger.info(f"字幕文件保存成功: {file_id}, 路径: {subtitle_path}")
        
        return jsonify({
            "success": True,
            "file_id": file_id,
            "filename": file.filename,
            "message": "字幕文件上传成功"
        })
        
    except Exception as e:
        logger.error(f"字幕文件上传失败: {e}")
        return jsonify({"error": f"字幕文件上传失败: {str(e)}"}), 500

@app.route('/api/audio-stats', methods=['GET'])
def get_audio_stats():
    """获取音频文件统计信息"""
    try:
        stats = audio_manager.get_file_stats()
        return jsonify({
            "success": True,
            "stats": stats,
            "management_rules": {
                "uploaded_files": "运行期间永不清理，用户可以持续使用",
                "recording_files": "智能管理，新录音时自动清理旧录音",
                "snippet_files": "智能管理，新片段时自动清理旧片段",
                "resource_control": f"最多{audio_manager.max_total_files}个文件并存，防止存储滥用",
                "startup_cleanup": "确保每次启动都是干净状态，自动清理temp文件夹所有音频"
            }
        })
    except Exception as e:
        logger.error(f"获取音频统计失败: {e}")
        return jsonify({"error": f"获取音频统计失败: {str(e)}"}), 500

@app.route('/api/cleanup-orphaned', methods=['POST'])
def cleanup_orphaned():
    """手动清理孤立文件"""
    try:
        result = run_async(audio_manager.cleanup_orphaned_files)()
        return jsonify({
            "success": True,
            "message": "孤立文件清理完成",
            "cleaned_files": result.get("cleaned_files", 0)
        })
    except Exception as e:
        logger.error(f"清理孤立文件失败: {e}")
        return jsonify({"error": f"清理孤立文件失败: {str(e)}"}), 500

@app.route('/api/check-resource-limits', methods=['POST'])
def check_resource_limits():
    """手动检查并执行资源限制"""
    try:
        # 获取检查前的状态
        before_stats = audio_manager.get_file_stats()
        
        # 执行资源限制检查
        run_async(audio_manager.check_resource_limits)()
        
        # 获取检查后的状态
        after_stats = audio_manager.get_file_stats()
        
        return jsonify({
            "success": True,
            "before": before_stats,
            "after": after_stats,
            "cleaned": before_stats["total_active_files"] - after_stats["total_active_files"],
            "message": "资源限制检查完成"
        })
    except Exception as e:
        logger.error(f"资源限制检查失败: {e}")
        return jsonify({"error": f"检查失败: {str(e)}"}), 500

# 错误处理
@app.errorhandler(404)
def not_found(e):
    return jsonify({"error": "请求的资源不存在"}), 404

@app.errorhandler(429)
def ratelimit_handler(e):
    return jsonify({"error": "请求过于频繁，请稍后再试"}), 429

# 应用速率限制
def apply_rate_limits():
    """为标记的路由动态添加速率限制"""
    try:
        def make_key_func(cat):
            def key_func():
                return get_user_identifier()
            return key_func

        for endpoint, func in app.view_functions.items():
            if hasattr(func, '_rate_limit_category'):
                category = func._rate_limit_category
                operation = func._rate_limit_operation
                limit_rule = get_limit(category, operation)
                limiter.limit(limit_rule, key_func=make_key_func(category))(func)
                logger.info(f"为路由 {endpoint} 应用了速率限制: {limit_rule} (类别: {category})")
        
        logger.info("已为所有标记的路由动态添加速率限制")
    except Exception as e:
        logger.error(f"添加速率限制时出错: {str(e)}")

# 书签相关的API端点
@app.route('/api/export-bookmarks', methods=['POST'])
@create_rate_limiter("auth", "bookmark_save")
def export_bookmarks():
    """导出书签文件供下载"""
    # 积分检查
    from api.config import CHECK_CREDITS_BOOKMARK_SAVE
    if CHECK_CREDITS_BOOKMARK_SAVE:
        from api.credits import get_user_id_from_request, get_user_credits_from_cache, service_supabase_client
        from api.credits_cache import cache_user_credits
        user_id = get_user_id_from_request()
        if not user_id:
            return jsonify({"success": False, "error": "需要登录才能使用此功能"}), 401
        current_credits = get_user_credits_from_cache(user_id)
        if current_credits is None:
            credits_resp = service_supabase_client.table("user_credits").select("credits").eq("user_id", user_id).execute()
            data = credits_resp.data or []
            current_credits = data[0].get("credits", 0) if data else 0
            cache_user_credits(user_id, current_credits)
        if current_credits < 1:
            return jsonify({"success": False, "error": "积分不足，请充值", "current_credits": current_credits, "required_credits": 1}), 200
    try:
        data = request.get_json()
        filename = data.get('filename')
        bookmarks = data.get('bookmarks', [])
        
        if not filename:
            return jsonify({'success': False, 'error': '文件名不能为空'}), 400
        
        # 生成书签文件内容（纯文本格式）
        content_lines = [f"书签文件 - {filename}"]
        content_lines.append(f"创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content_lines.append(f"书签数量: {len(bookmarks)}")
        content_lines.append("")
        
        for i, bookmark in enumerate(bookmarks, 1):
            start_time = bookmark.get('startTime', 0)
            end_time = bookmark.get('endTime', 0)
            minutes_start = int(start_time // 60)
            seconds_start = int(start_time % 60)
            minutes_end = int(end_time // 60)
            seconds_end = int(end_time % 60)
            
            content_lines.append(f"书签 {i}:")
            content_lines.append(f"  开始时间: {minutes_start:02d}:{seconds_start:02d}")
            content_lines.append(f"  结束时间: {minutes_end:02d}:{seconds_end:02d}")
            content_lines.append(f"  时长: {end_time - start_time:.1f}秒")
            content_lines.append("")
        
        # 添加JSON数据供程序读取
        content_lines.append("=== 程序数据 (请勿修改) ===")
        import json
        bookmark_data = {
            'audio_file': filename,
            'created_at': datetime.utcnow().isoformat(),
            'bookmarks': bookmarks
        }
        content_lines.append(json.dumps(bookmark_data, ensure_ascii=False))
        
        content = '\n'.join(content_lines)
        
        # 生成书签文件名
        base_name = os.path.splitext(filename)[0]
        bookmark_filename = f"{base_name}_bookmark.txt"
        
        return jsonify({
            'success': True,
            'filename': bookmark_filename,
            'content': content
        })
        
    except Exception as e:
        logger.error(f"导出书签文件失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/parse-bookmarks', methods=['POST'])
@create_rate_limiter("auth", "bookmark_load")
def parse_bookmarks():
    """解析上传的书签文件内容"""
    # 积分检查
    from api.config import CHECK_CREDITS_BOOKMARK_LOAD
    if CHECK_CREDITS_BOOKMARK_LOAD:
        from api.credits import get_user_id_from_request, get_user_credits_from_cache, service_supabase_client
        from api.credits_cache import cache_user_credits
        user_id = get_user_id_from_request()
        if not user_id:
            return jsonify({"success": False, "error": "需要登录才能使用此功能"}), 401
        current_credits = get_user_credits_from_cache(user_id)
        if current_credits is None:
            credits_resp = service_supabase_client.table("user_credits").select("credits").eq("user_id", user_id).execute()
            data = credits_resp.data or []
            current_credits = data[0].get("credits", 0) if data else 0
            cache_user_credits(user_id, current_credits)
        if current_credits < 1:
            return jsonify({"success": False, "error": "积分不足，请充值", "current_credits": current_credits, "required_credits": 1}), 200
    try:
        data = request.get_json()
        content = data.get('content', '')
        
        if not content:
            return jsonify({'success': False, 'error': '书签文件内容为空'}), 400
        
        # 尝试从文件末尾提取JSON数据
        lines = content.split('\n')
        json_line = None
        
        # 查找JSON数据行
        for line in reversed(lines):
            line = line.strip()
            if line.startswith('{') and line.endswith('}'):
                json_line = line
                break
        
        if json_line:
            try:
                import json
                bookmark_data = json.loads(json_line)
                logger.info("书签文件解析成功")
                return jsonify({
                    'success': True,
                    'bookmarks': bookmark_data.get('bookmarks', []),
                    'created_at': bookmark_data.get('created_at'),
                    'audio_file': bookmark_data.get('audio_file')
                })
            except json.JSONDecodeError:
                pass
        
        # 如果没有找到JSON数据，返回空书签列表
        logger.info("未找到有效的书签数据，返回空列表")
        return jsonify({
            'success': True,
            'bookmarks': [],
            'created_at': None,
            'audio_file': None
        })
        
    except Exception as e:
        logger.error(f"解析书签文件失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# =================== 播客功能路由 ===================

@app.route('/podcast/search', methods=['GET'])
@create_rate_limiter("content", "podcast_search")
def podcast_search():
    """搜索播客"""
    # 检查是否跳过积分检查
    skip_credits_check = request.args.get('skip_credits_check', 'false').lower() == 'true'
    logger.info(f"[播客搜索] 收到搜索请求，skip_credits_check={skip_credits_check}")
    
    # 积分检查
    from api.config import CHECK_CREDITS_PODCAST_SEARCH
    if CHECK_CREDITS_PODCAST_SEARCH and not skip_credits_check:
        from api.credits import get_user_id_from_request, get_user_credits_from_cache, service_supabase_client
        from api.credits_cache import cache_user_credits
        
        # 添加详细的用户认证日志
        logger.info(f"[播客搜索] 开始积分检查，CHECK_CREDITS_PODCAST_SEARCH={CHECK_CREDITS_PODCAST_SEARCH}")
        
        # 记录请求头信息（脱敏）
        auth_header = request.headers.get('Authorization')
        cookie_token = request.cookies.get('session_token')
        logger.info(f"[播客搜索] 认证信息 - Authorization头: {'存在' if auth_header else '不存在'}, Cookie令牌: {'存在' if cookie_token else '不存在'}")
        
        user_id = get_user_id_from_request()
        logger.info(f"[播客搜索] 获取到的用户ID: {user_id}")
        
        if not user_id:
            logger.warning(f"[播客搜索] 用户认证失败，拒绝访问")
            return jsonify({"success": False, "error": "需要登录才能使用此功能"}), 401
            
        current_credits = get_user_credits_from_cache(user_id)
        logger.info(f"[播客搜索] 用户={user_id} 缓存积分={current_credits}")
        
        if current_credits is None:
            logger.info(f"[播客搜索] 缓存未命中，查询数据库: 用户={user_id}")
            credits_resp = service_supabase_client.table("user_credits").select("credits").eq("user_id", user_id).execute()
            data = credits_resp.data or []
            current_credits = data[0].get("credits", 0) if data else 0
            cache_user_credits(user_id, current_credits)
            logger.info(f"[播客搜索] 数据库查询结果: 用户={user_id} 积分={current_credits}")
        else:
            logger.info(f"[播客搜索] 使用缓存积分: 用户={user_id} 积分={current_credits}")
            
        if current_credits < 1:
            logger.warning(f"[播客搜索] 积分不足: 用户={user_id} 当前积分={current_credits} 需要积分=1")
            return jsonify({"success": False, "error": "积分不足，请充值", "current_credits": current_credits, "required_credits": 1}), 200
        else:
            logger.info(f"[播客搜索] 积分检查通过: 用户={user_id} 积分={current_credits}")
    elif skip_credits_check:
        logger.info(f"[播客搜索] ✅ 跳过积分检查，直接执行搜索功能")
            
    try:
        query = request.args.get('q', '')
        if not query:
            return jsonify({'error': '请提供搜索关键词'}), 400
        
        # 调用iTunes Search API
        from urllib.parse import quote
        url = f'https://itunes.apple.com/search?term={quote(query)}&media=podcast&limit=20'
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        return jsonify(data)
    except requests.RequestException as e:
        return jsonify({'error': f'搜索请求失败: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500

@app.route('/podcast/episodes', methods=['GET'])
@create_rate_limiter("content", "podcast_episodes")
def podcast_episodes():
    """获取播客节目列表"""
    try:
        feed_url = request.args.get('feedUrl', '')
        page = int(request.args.get('page', 0))  # 分页参数
        per_page = int(request.args.get('per_page', 20))  # 每页数量
        
        logger.info(f"===== 播客节目API调用 =====")
        logger.info(f"feed_url: {feed_url}")
        logger.info(f"page: {page}, per_page: {per_page}")
        
        if not feed_url:
            return jsonify({'error': '请提供播客feed URL'}), 400
        
        # 解析RSS feed获取节目列表
        import feedparser
        
        # 检查是否可以使用dateutil进行日期排序
        if not DATEUTIL_AVAILABLE:
            # 降级处理，不进行日期排序
            feed = feedparser.parse(feed_url)
            episodes = []
            for entry in feed.entries[:20]:
                episode = {
                    'title': entry.get('title', '未知标题'),
                    'published': entry.get('published', '未知日期'),
                    'description': entry.get('summary', ''),
                    'duration': entry.get('itunes_duration', ''),
                    'audio_url': ''
                }
                if hasattr(entry, 'enclosures') and entry.enclosures:
                    episode['audio_url'] = entry.enclosures[0].get('href', '')
                elif hasattr(entry, 'links'):
                    for link in entry.links:
                        if link.get('type', '').startswith('audio/'):
                            episode['audio_url'] = link.get('href', '')
                            break
                episodes.append(episode)
            logger.warning("dateutil不可用，使用降级模式，未进行日期排序")
            return jsonify(episodes)
        
        import datetime
        
        feed = feedparser.parse(feed_url)
        logger.info(f"RSS解析完成，总节目数: {len(feed.entries)}")
        
        episodes = []
        
        # 获取所有节目并添加日期解析
        for i, entry in enumerate(feed.entries):
            episode = {
                'title': entry.get('title', '未知标题'),
                'published': entry.get('published', '未知日期'),
                'description': entry.get('summary', ''),
                'duration': entry.get('itunes_duration', ''),
                'audio_url': '',
                'parsed_date': None
            }
            
            # 解析发布日期用于排序
            try:
                if episode['published'] and episode['published'] != '未知日期':
                    episode['parsed_date'] = date_parser.parse(episode['published'])
                    if i < 5:  # 只记录前5个的日期解析结果
                        logger.info(f"节目 {i+1}: {episode['title'][:30]}... 发布日期: {episode['published']} -> {episode['parsed_date']}")
                else:
                    # 如果没有发布日期，使用一个很早的日期作为默认值
                    episode['parsed_date'] = datetime.datetime(1970, 1, 1)
                    if i < 5:
                        logger.info(f"节目 {i+1}: {episode['title'][:30]}... 无发布日期，使用默认日期")
            except Exception as parse_error:
                logger.warning(f"解析日期失败 '{episode['published']}': {parse_error}")
                episode['parsed_date'] = datetime.datetime(1970, 1, 1)
            
            # 获取音频链接
            if hasattr(entry, 'enclosures') and entry.enclosures:
                episode['audio_url'] = entry.enclosures[0].get('href', '')
            elif hasattr(entry, 'links'):
                for link in entry.links:
                    if link.get('type', '').startswith('audio/'):
                        episode['audio_url'] = link.get('href', '')
                        break
            
            episodes.append(episode)
        
        logger.info(f"节目列表构建完成，共 {len(episodes)} 期")
        
        # 按发布日期降序排序（最新的在前）
        episodes_before_sort = [ep['title'][:20] + f"({ep['parsed_date'].strftime('%Y-%m-%d') if ep['parsed_date'] and ep['parsed_date'].year > 1970 else 'no-date'})" for ep in episodes[:5]]
        logger.info(f"排序前前5期: {episodes_before_sort}")
        
        episodes.sort(key=lambda x: x['parsed_date'], reverse=True)
        
        episodes_after_sort = [ep['title'][:20] + f"({ep['parsed_date'].strftime('%Y-%m-%d') if ep['parsed_date'] and ep['parsed_date'].year > 1970 else 'no-date'})" for ep in episodes[:5]]
        logger.info(f"排序后前5期: {episodes_after_sort}")
        
        # 移除解析日期字段（前端不需要）
        for episode in episodes:
            del episode['parsed_date']
        
        # 分页处理
        start_index = page * per_page
        end_index = start_index + per_page
        paginated_episodes = episodes[start_index:end_index]
        
        logger.info(f"分页处理: page={page}, per_page={per_page}, start={start_index}, end={end_index}")
        logger.info(f"返回节目数: {len(paginated_episodes)}, 总数: {len(episodes)}, has_more: {end_index < len(episodes)}")
        
        result = {
            'episodes': paginated_episodes,
            'total': len(episodes),
            'page': page,
            'per_page': per_page,
            'has_more': end_index < len(episodes)
        }
        
        logger.info(f"===== API调用完成 =====")
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f'获取节目列表失败: {e}')
        import traceback
        logger.error(f'错误详情: {traceback.format_exc()}')
        return jsonify({'error': f'获取节目列表失败: {str(e)}'}), 500

@app.route('/podcast/proxy_download', methods=['GET'])
@create_rate_limiter("content", "podcast_download")
def podcast_proxy_download():
    """代理下载播客音频"""
    try:
        audio_url = request.args.get('audio_url', '')
        if not audio_url:
            return jsonify({'error': '请提供音频URL'}), 400
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'audio/*,*/*;q=0.9',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'identity',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        max_retries = 3
        retry_delay = 2  # in seconds
        last_exception = None

        for attempt in range(max_retries):
            try:
                logger.info(f"尝试下载播客音频 (第 {attempt + 1}/{max_retries} 次): {audio_url}")
                # 流式下载音频文件
                audio_response = requests.get(audio_url, stream=True, headers=headers, timeout=10)
                audio_response.raise_for_status()
                
                # 获取文件信息
                content_type = audio_response.headers.get('content-type', 'audio/mpeg')
                content_length = audio_response.headers.get('content-length', '')
                
                # 设置响应头
                def generate():
                    try:
                        for chunk in audio_response.iter_content(chunk_size=8192):
                            if chunk:
                                yield chunk
                    except requests.exceptions.ChunkedEncodingError as e:
                        logger.error(f"流式传输时发生错误: {e}")
                
                response = Response(generate(), content_type=content_type)
                
                # 设置CORS头
                response.headers['Access-Control-Allow-Origin'] = '*'
                response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
                response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
                
                if content_length:
                    response.headers['Content-Length'] = content_length
                
                logger.info(f"播客音频下载成功，开始流式传输")
                return response
            
            except (requests.exceptions.ConnectTimeout, requests.exceptions.ReadTimeout, requests.exceptions.ConnectionError) as e:
                logger.warning(f"第 {attempt + 1} 次下载尝试失败，网络错误: {e}")
                last_exception = e
                if attempt < max_retries - 1:
                    logger.info(f"在 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                else:
                    logger.error("已达到最大重试次数")
                    
            except requests.exceptions.RequestException as e:
                logger.warning(f"第 {attempt + 1} 次下载尝试失败，请求错误: {e}")
                last_exception = e
                if attempt < max_retries - 1:
                    logger.info(f"在 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                else:
                    logger.error("已达到最大重试次数")
                    
            except Exception as e:
                # 捕获所有其他异常，防止程序崩溃
                logger.error(f"第 {attempt + 1} 次下载尝试失败，未预期错误: {e}")
                logger.error(f"错误详情: {traceback.format_exc()}")
                last_exception = e
                if attempt < max_retries - 1:
                    logger.info(f"在 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                else:
                    logger.error("已达到最大重试次数")
        
        # 所有重试都失败后
        logger.error(f"播客下载完全失败，最后错误: {last_exception}")
        return jsonify({'error': f'代理下载失败: {str(last_exception)}'}), 500

    except Exception as e:
        # 最外层异常捕获，防止整个函数崩溃
        logger.error(f"播客代理下载函数发生严重错误: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return jsonify({'error': f'播客下载服务异常: {str(e)}'}), 500

# =================== 播客功能路由结束 ===================

# 应用速率限制
apply_rate_limits()

@app.route('/api/credits/get-fast', methods=['GET'])
@create_rate_limiter("auth", "credit_check")
def get_credits_fast():
    """快速获取积分（仅从后端缓存，用于阈值检查）"""
    try:
        # 获取认证令牌
        from api.credits import get_user_credits_from_cache
        from api.auth import verify_user_auth
        
        auth_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        
        if not auth_token:
            return jsonify({"success": False, "error": "缺少认证令牌"}), 401
        
        try:
            # 验证用户身份
            user_id = verify_user_auth(auth_token)
            if not user_id:
                return jsonify({"success": False, "error": "认证失败"}), 401
            
            # 从后端缓存获取积分（快速）
            cached_credits = get_user_credits_from_cache(user_id)
            
            if cached_credits is not None:
                logger.info(f"快速积分检查: 用户={user_id}, 后端缓存积分={cached_credits}")
                return jsonify({
                    "success": True,
                    "credits": cached_credits,
                    "source": "backend_cache",
                    "user_id": user_id
                })
            else:
                # 缓存不存在，需要查询数据库
                logger.info(f"快速积分检查: 用户={user_id}, 后端缓存不存在")
                return jsonify({
                    "success": False,
                    "error": "缓存不存在，需要完整查询",
                    "reason": "cache_miss"
                })
                
        except Exception as auth_error:
            logger.error(f"快速积分检查认证失败: {auth_error}")
            return jsonify({
                "success": False,
                "error": f"认证失败: {str(auth_error)}"
            }), 401
            
    except Exception as e:
        logger.error(f"快速积分检查API异常: {e}")
        return jsonify({
            "success": False,
            "error": f"快速积分检查失败: {str(e)}"
        }), 500

@app.route('/api/check-credits-for-qa', methods=['POST'])
@create_rate_limiter("auth", "credit_check")
def check_credits_for_qa():
    """为智能问答立即检查积分"""
    try:
        data = request.json
        operation = data.get('operation', 'smart_qa')
        snippet_duration = data.get('snippet_duration', 6)
        
        # 获取认证令牌
        from api.credits import check_user_credits_for_operation
        auth_token = request.headers.get('Authorization', '').replace('Bearer ', '')
        
        if not auth_token:
            return jsonify({"success": False, "error": "缺少认证令牌"}), 401
        
        try:
            # 基于音频片段时长估算积分需求
            estimated_duration = float(snippet_duration)
            multimodal_info = {
                "audio_duration": estimated_duration,
                "has_audio": True,
                "has_image": False
            }
            
            logger.info(f"立即积分检查: 操作={operation}, 音频时长={estimated_duration}秒")
            
            # 执行积分检查
            has_credits, user_id, credits_info = check_user_credits_for_operation(
                auth_token, operation, 'qwen_omni_turbo',
                content="智能问答积分检查",
                multimodal_info=multimodal_info
            )
            
            if has_credits:
                logger.info(f"积分检查通过: 用户={user_id}, 当前积分={credits_info.get('current_credits', '未知')}")
                return jsonify({
                    "success": True,
                    "user_id": user_id,
                    **credits_info
                })
            else:
                logger.warning(f"积分不足: 用户={user_id}, 需要积分={credits_info.get('required_credits', '未知')}, 当前积分={credits_info.get('current_credits', '未知')}")
                return jsonify({
                    "success": False,
                    "error": "积分不足，无法进行智能问答",
                    **credits_info
                })
                
        except Exception as credit_error:
            logger.error(f"积分检查异常: {credit_error}")
            return jsonify({
                "success": False,
                "error": f"积分检查失败: {str(credit_error)}"
            }), 500
            
    except Exception as e:
        logger.error(f"积分检查API异常: {e}")
        return jsonify({
            "success": False,
            "error": f"积分检查失败: {str(e)}"
        }), 500

# 字幕问答API端点
@app.route('/api/subtitle-chat-stream', methods=['POST'])
def subtitle_chat_stream():
    """
    字幕问答流式响应API
    基于上下文（上一句+当前句+下一句）进行翻译和语法分析
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据无效"}), 400
        
        # 获取请求参数
        current_subtitle = data.get('current_subtitle', '')
        previous_subtitle = data.get('previous_subtitle', '')
        next_subtitle = data.get('next_subtitle', '')
        user_question = data.get('user_question', '')
        model_id = data.get('model', 'glm-4-flash')
        temperature = data.get('temperature', 0.3)
        
        if not current_subtitle:
            return jsonify({"error": "缺少当前字幕内容"}), 400
        
        # 获取用户信息 - 简化版本，参考问答参考的实现
        auth_header = request.headers.get('Authorization')
        user_id = None
        if auth_header and auth_header.startswith('Bearer '):
            try:
                token = auth_header.split(' ')[1]
                # 使用supabase验证token并获取真实用户ID
                from api.credits import supabase_client
                user_response = supabase_client.auth.get_user(token)
                if user_response.user:
                    user_id = user_response.user.id
                    logger.info(f"字幕问答验证用户成功: {user_id}")
                else:
                    logger.warning(f"字幕问答用户验证失败")
                    return jsonify({'error': '需要登录才能使用此功能'}), 401
            except Exception as e:
                logger.error(f"字幕问答获取用户ID失败: {str(e)}")
                return jsonify({'error': '用户认证失败'}), 401
        else:
            return jsonify({'error': '需要登录才能使用此功能'}), 401
        
        # 构建上下文
        context = []
        if previous_subtitle:
            context.append(f"上一句: {previous_subtitle}")
        context.append(f"当前句: {current_subtitle}")
        if next_subtitle:
            context.append(f"下一句: {next_subtitle}")
        
        context_text = '\n'.join(context)
        
        # 构建系统提示 - 专门针对字幕理解
        if user_question:
            # 用户追问模式
            system_message = f"""你是一个专业的语言学习助手。用户正在学习音频内容，需要你帮助理解字幕内容。

上下文信息：
{context_text}

请基于上述上下文回答用户的问题。注意：
1. 回答要准确、简洁
2. 如果涉及语法，请简要说明
3. 如果有习语或固定搭配，请解释含义
4. 使用中文回答"""
            
            prompt = user_question
        else:
            # 默认分析模式
            system_message = f"""你是一个专业的语言学习助手。用户正在学习音频内容，请帮助分析当前字幕句子。

上下文信息：
{context_text}

请对"当前句"进行详细分析，包括：
1. 中文翻译
2. 语法结构分析
3. 习语解释

请用中文回答，格式清晰易懂。"""
            
            prompt = f"请分析这个句子：{current_subtitle}"
        
        # 安全修改：在后端进行积分检查，使用实际的prompt内容
        full_prompt_content = system_message + '\n\n用户问题：' + prompt
        
        # 检查用户是否有足够的积分进行此操作
        from api.credits import check_user_credits_for_operation
        has_credits, user_id, credits_info = check_user_credits_for_operation(
            auth_header.replace('Bearer ', ''), 
            'subtitle_chat', 
            model_id,
            content=full_prompt_content,
            system_message=system_message
        )
        
        # 获取预估积分数
        estimated_credits = credits_info.get('required_credits')
        
        if not has_credits:
            # 积分不足，返回错误消息
            logger.warning(f"用户积分不足，操作类型: subtitle_chat, 模型: {model_id}, 用户ID: {user_id if user_id else 'unknown'}")
            return jsonify({
                'error': '积分不足，请先充值',
                'credits_info': credits_info
            }), 403  # 使用403 Forbidden
        
        # 定义积分扣减回调函数
        def on_completion(usage_info):
            """处理生成完成时的积分扣减"""
            try:
                # 导入积分管理模块
                from api.credits import deduct_user_credits
                
                # 执行积分扣减
                result = deduct_user_credits(
                    user_id=user_id,
                    operation='subtitle_chat',
                    model=model_id,
                    token_usage=usage_info
                )
                
                if result.get('success'):
                    credits_deducted = result.get('credits_deducted', 1)
                    logger.info(f"字幕问答积分扣减成功: 用户 {user_id}, 扣减 {credits_deducted} 积分")
                else:
                    logger.warning(f"字幕问答积分扣减失败: 用户 {user_id}, 错误: {result.get('error', '未知错误')}")
                
                return result
                    
            except Exception as e:
                logger.error(f"字幕问答处理积分扣减时出错: {str(e)}")
                return {"success": False, "error": str(e)}
        
        # 创建流式响应 - 使用zhipu_api，手动处理积分扣减
        try:
            # 导入zhipu API
            from zhipu_api import create_streaming_response
            import re, json
            
            # 调用智谱API获取Response对象
            api_response = create_streaming_response(
                prompt=prompt,
                system_message=system_message,
                frontend_model_id=model_id,
                estimated_credits=estimated_credits
            )
            
            # ---------------------------------------------
            # 包装器：在转发流式内容的同时解析隐藏的token信息
            # ---------------------------------------------
            def response_wrapper():
                # 用于保存解析到的token使用信息（dict）
                parsed_token_usage = None
                thinking_pattern = re.compile(r"<!--\s*HIDDEN_TOKEN_DATA:\s*(\{.*?\})\s*-->")
                
                # Flask Response.response 是一个可迭代的byte块
                for raw_chunk in api_response.response:
                    try:
                        # 向前端透传原始chunk
                        yield raw_chunk
                        
                        # 尝试解析token信息（仅解析一次即可）
                        if parsed_token_usage is None:
                            # 将byte转换为str
                            if isinstance(raw_chunk, bytes):
                                chunk_str = raw_chunk.decode('utf-8', errors='ignore')
                            else:
                                chunk_str = str(raw_chunk)
                            
                            match = thinking_pattern.search(chunk_str)
                            if match:
                                json_str = match.group(1)
                                try:
                                    parsed_token_usage = json.loads(json_str)
                                    logger.info(f"字幕问答流中捕获到token使用信息: {parsed_token_usage}")
                                except json.JSONDecodeError as je:
                                    logger.warning(f"解析token使用JSON失败: {je}; 原始: {json_str[:100]}")
                    except Exception as stream_err:
                        logger.error(f"字幕问答流式转发错误: {stream_err}")
                        yield f"错误: {str(stream_err)}".encode('utf-8')
                        break
                
                # -----------------------------
                # 流结束 -> 进行积分扣减
                # -----------------------------
                try:
                    from api.credits import deduct_user_credits
                    token_usage_for_deduct = parsed_token_usage if parsed_token_usage else estimated_credits
                    result = deduct_user_credits(
                        user_id=user_id,
                        operation='subtitle_chat',
                        model=model_id,
                        token_usage=token_usage_for_deduct
                    )
                    if result.get('success'):
                        logger.info(f"字幕问答积分扣减成功: {result}")
                    else:
                        logger.warning(f"字幕问答积分扣减失败: {result}")
                except Exception as deduct_err:
                    logger.error(f"字幕问答积分扣减异常: {deduct_err}")
            
            # 返回新的流式Response
            return Response(stream_with_context(response_wrapper()), content_type='text/plain; charset=utf-8')
            
        except Exception as e:
            logger.error(f"调用模型API失败: {str(e)}")
            return Response("生成回答时发生错误，请稍后重试。", content_type='text/plain; charset=utf-8')
        
    except Exception as e:
        logger.error(f"字幕问答处理失败: {e}")
        return jsonify({"error": "字幕问答处理失败，请重试"}), 500

# 语法分析API端点
@app.route('/api/grammar-analysis-stream', methods=['POST'])
def grammar_analysis_stream():
    """
    英语语法结构分析流式响应API
    基于智谱模型进行语法结构分析和可视化
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "请求数据无效"}), 400
        
        # 获取请求参数
        sentence = data.get('sentence', '')
        system_message = data.get('system_message', '')
        user_prompt = data.get('user_prompt', '')
        analysis_type = data.get('analysis_type', 'comprehensive')
        model_id = data.get('model', 'zhipu_flash')
        temperature = data.get('temperature', 0.3)
        
        if not sentence:
            return jsonify({"error": "缺少待分析的句子"}), 400
        
        # 获取用户信息 - 使用与字幕问答相同的验证方式
        auth_header = request.headers.get('Authorization')
        user_id = None
        if auth_header and auth_header.startswith('Bearer '):
            try:
                token = auth_header.split(' ')[1]
                # 使用supabase验证token并获取真实用户ID
                from api.credits import supabase_client
                user_response = supabase_client.auth.get_user(token)
                if user_response.user:
                    user_id = user_response.user.id
                    logger.info(f"语法分析验证用户成功: {user_id}")
                else:
                    logger.warning(f"语法分析用户验证失败")
                    return jsonify({'error': '需要登录才能使用此功能'}), 401
            except Exception as e:
                logger.error(f"语法分析获取用户ID失败: {str(e)}")
                return jsonify({'error': '用户认证失败'}), 401
        else:
            return jsonify({'error': '需要登录才能使用此功能'}), 401
        
        # 简洁的系统角色定义
        if not system_message:
            system_message = """你是一个专业的英语语法分析专家。你需要对给定的英语句子进行准确、结构化的语法分析，用于教学和可视化展示。请严格按照用户要求的格式输出，使用中文回答。"""
        
        # 层级化输出的prompt，便于前端树形解析
        if not user_prompt:
            user_prompt = f"""请对以下英语句子进行语法结构分层分析，以类似JSON的层级结构输出。

句子：{sentence}

**关键要求 - 必须遵守**：
✅ 主语、谓语、宾语、表语、状语等主干成分必须并列在顶层（不缩进）
❌ 禁止将主干成分缩进到其他成分之下
✅ 修饰成分（定语从句、介词短语等）缩进2个空格
✅ 从句内部成分再缩进2个空格

**正确示例**（注意主干成分并列）：
主语: "There's a reason"
谓语: "have survived"
宾语: "his meditations"
  修饰短语: "about a life worth living"
状语: "nearly 2,000 years"

**错误示例**（禁止这样做）：
主语: "There's a reason"
  谓语: "have survived"  ← 错误！谓语不能缩进
  宾语: "his meditations"  ← 错误！宾语不能缩进

**输出格式**：
- 使用中文标签，英文内容用双引号包围
- 不要输出其他说明，只输出结构化分析

请开始分析："""

        # 合并prompt用于积分计算
        full_prompt_content = system_message + '\n\n' + user_prompt
        
        # 检查用户是否有足够的积分进行此操作
        from api.credits import check_user_credits_for_operation
        has_credits, user_id, credits_info = check_user_credits_for_operation(
            auth_header.replace('Bearer ', ''), 
            'grammar_analysis', 
            model_id,
            content=full_prompt_content,
            system_message=system_message
        )
        
        # 获取预估积分数
        estimated_credits = credits_info.get('required_credits', 2)
        
        if not has_credits:
            # 积分不足，返回错误消息
            logger.warning(f"用户积分不足，操作类型: grammar_analysis, 模型: {model_id}, 用户ID: {user_id if user_id else 'unknown'}")
            return jsonify({
                'error': '积分不足，请先充值',
                'credits_info': credits_info
            }), 403
        
        # 创建流式响应 - 使用智谱API
        try:
            # 导入zhipu API
            from zhipu_api import create_streaming_response
            import re, json
            
            # 调用智谱API获取Response对象
            api_response = create_streaming_response(
                prompt=user_prompt,
                system_message=system_message,
                frontend_model_id=model_id,
                estimated_credits=estimated_credits
            )
            
            # 包装器：在转发流式内容的同时解析隐藏的token信息
            def response_wrapper():
                # 用于保存解析到的token使用信息（dict）
                parsed_token_usage = None
                thinking_pattern = re.compile(r"<!--\s*HIDDEN_TOKEN_DATA:\s*(\{.*?\})\s*-->")
                
                # Flask Response.response 是一个可迭代的byte块
                for raw_chunk in api_response.response:
                    try:
                        # 将byte转换为str
                        if isinstance(raw_chunk, bytes):
                            chunk_str = raw_chunk.decode('utf-8', errors='ignore')
                        else:
                            chunk_str = str(raw_chunk)
                        
                        # 尝试解析token信息（仅解析一次即可）
                        if parsed_token_usage is None:
                            match = thinking_pattern.search(chunk_str)
                            if match:
                                json_str = match.group(1)
                                try:
                                    parsed_token_usage = json.loads(json_str)
                                    logger.info(f"语法分析流中捕获到token使用信息: {parsed_token_usage}")
                                except json.JSONDecodeError as je:
                                    logger.warning(f"解析token使用JSON失败: {je}; 原始: {json_str[:100]}")
                        
                        # 过滤掉HIDDEN_TOKEN_DATA，只向前端透传有用内容
                        filtered_chunk_str = thinking_pattern.sub('', chunk_str)
                        
                        # 只有当过滤后的内容不为空时才向前端发送
                        if filtered_chunk_str.strip():
                            if isinstance(raw_chunk, bytes):
                                yield filtered_chunk_str.encode('utf-8')
                            else:
                                yield filtered_chunk_str
                        
                    except Exception as stream_err:
                        logger.error(f"语法分析流式转发错误: {stream_err}")
                        yield f"错误: {str(stream_err)}".encode('utf-8')
                        break
                
                # 流结束 -> 进行积分扣减
                try:
                    from api.credits import deduct_user_credits
                    token_usage_for_deduct = parsed_token_usage if parsed_token_usage else estimated_credits
                    result = deduct_user_credits(
                        user_id=user_id,
                        operation='grammar_analysis',
                        model=model_id,
                        token_usage=token_usage_for_deduct
                    )
                    
                    if result.get('success'):
                        credits_deducted = result.get('credits_deducted', estimated_credits)
                        logger.info(f"语法分析积分扣减成功: 用户 {user_id}, 扣减 {credits_deducted} 积分")
                    else:
                        logger.warning(f"语法分析积分扣减失败: 用户 {user_id}, 错误: {result.get('error', '未知错误')}")
                except Exception as deduct_err:
                    logger.error(f"语法分析积分扣减异常: {deduct_err}")
            
            # 返回新的流式Response
            return Response(stream_with_context(response_wrapper()), content_type='text/plain; charset=utf-8')
            
        except Exception as e:
            logger.error(f"调用智谱API失败: {str(e)}")
            return Response("语法分析时发生错误，请稍后重试。", content_type='text/plain; charset=utf-8')
        
    except Exception as e:
        logger.error(f"语法分析处理失败: {e}")
        return jsonify({"error": "语法分析处理失败，请重试"}), 500

@app.route('/api/health', methods=['GET'])
def api_health():
    """API健康检查端点"""
    try:
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        })
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return jsonify({
            'status': 'error',
            'error': str(e)
        }), 500

@app.route('/api/auxiliary-config', methods=['GET'])
def get_auxiliary_config():
    """获取辅助功能配置"""
    try:
        from api.config import AUXILIARY_FEATURES_SKIP_CHECK_MINUTES, FORCE_CREDITS_REFRESH_ON_LOGIN
        
        return jsonify({
            'success': True,
            'config': {
                'skip_check_minutes': AUXILIARY_FEATURES_SKIP_CHECK_MINUTES,
                'force_credits_refresh_on_login': FORCE_CREDITS_REFRESH_ON_LOGIN
            }
        })
    except Exception as e:
        logger.error(f"获取辅助功能配置失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/auxiliary-skip-status', methods=['GET'])
def get_auxiliary_skip_status():
    """获取用户辅助功能免检查状态"""
    try:
        from api.auth import get_user_from_token
        from api.credits import get_auxiliary_skip_check_status
        
        # 从请求头获取认证令牌
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'success': False, 'error': '未提供认证令牌'}), 401
        
        token = auth_header.split(' ')[1]
        user_data = get_user_from_token(token)
        
        if not user_data:
            return jsonify({'success': False, 'error': '用户认证失败'}), 401
        
        user_id = user_data['id']
        skip_status = get_auxiliary_skip_check_status(user_id)
        
        return jsonify({
            'success': True,
            'status': skip_status
        })
        
    except Exception as e:
        logger.error(f"获取辅助功能免检查状态失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/analyze', methods=['GET', 'POST'])
def analyze_sentence():
    """分析英文句子的语法结构，使用AI模型进行句子成分分析，返回流式响应"""
    try:
        # 处理GET请求
        if request.method == 'GET':
            sentence = request.args.get('sentence', '').strip()
        else:
            # 处理POST请求
            data = request.get_json()
            sentence = data.get('sentence', '').strip()
        
        if not sentence:
            return jsonify({'error': '请输入一个英文句子'}), 400
        
        logger.info(f"句子成分分析请求: {sentence}")
        
        # 简洁的系统角色定义
        system_prompt = """你是一个专业的英语语法分析专家。你需要对给定的英语句子进行准确、结构化的语法分析，用于教学和可视化展示。请严格按照用户要求的格式输出，使用中文回答。"""

        # 详细的用户指令和格式要求
        user_prompt = f"""请对以下英语句子进行详细的语法结构分析：

**待分析句子**：{sentence}

**输出格式要求**：
请严格按照以下Markdown格式输出：

# {sentence}

**句子类型**: [单句/并列句/复合句]

按以下格式分析句子成分（如果是复合句，请按分句顺序分析）：

## 第一分句（如果是复合句）
### 主语: [主语文本] - [语法作用和含义]
### 谓语: [谓语文本] - [动词类型] - [含义]
### 宾语: [宾语文本] - [含义]（如果有）
### 表语: [表语文本] - [含义]（如果是主系表结构）
### 状语: [状语文本] - [状语类型] - [含义]（如果有）

## 第二分句（如果有）
### 连接词: [连接词] - [作用说明]
### 主语: [主语文本] - [语法作用和含义]
### 谓语: [谓语文本] - [动词类型] - [含义]
### 宾语: [宾语文本] - [含义]（如果有）
### 表语: [表语文本] - [含义]（如果是主系表结构）
### 状语: [状语文本] - [状语类型] - [含义]（如果有）

**分析原则**：
1. **准确识别句子类型**：单句（主谓宾、主系表、主谓）或复合句（并列句、复从句）
2. **复合句处理**：如果是并列句或复合句，按句子顺序分别分析每个分句的成分，用"第一分句"、"第二分句"等标明
3. **系动词标注**：对于系动词，在"语法作用"中明确标注为"系动词"
4. **状语准确分类**：只有真正的状语（时间、地点、方式、原因等）才标为状语，连词、介词短语要准确分类
5. **成分归属明确**：每个词汇只能属于一个句子成分，不能重复或交叉
6. 每个成分说明控制在15字以内
7. 用中文回答，禁止英文
8. **严格要求：只输出实际存在的句子成分**，如果某个成分不存在，请完全跳过该部分
9. 确保格式正确，所有标题前必须有空格（如`### 语法作用:`）

**示例**：
"Senior is a staff writer"应该分析为：
- 主语部分：Senior（句子的主体）
- 谓语部分：is（系动词，连接主语和表语）
- 表语部分：a staff writer（说明主语的身份）

请开始分析："""
        
        # 使用项目现有的zhipu_api模块创建流式响应
        from zhipu_api import create_streaming_response
        
        # 创建流式响应 - 修正参数传递
        return create_streaming_response(
            prompt=user_prompt,
            system_message=system_prompt,
            frontend_model_id="zhipu_flash",
            estimated_credits=None
        )
        
    except Exception as e:
        logger.error(f"分析句子时发生错误: {str(e)}")
        return jsonify({'error': f'分析失败: {str(e)}'}), 500

# 启动应用
if __name__ == '__main__':
    logger.info(f"启动Flask应用，监听端口: {PORT}")
    logger.info("调试模式: 关闭")
    app.run(host='0.0.0.0', port=PORT, debug=False)