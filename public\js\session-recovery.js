/**
 * Session恢复脚本 - 处理URL中有token但Session未建立的情况
 */
(function() {
    'use strict';
    
    // ============== 可配置开关 ==============
    // 是否显示右上角的Session恢复状态提示气泡
    const SHOW_STATUS_TOAST = false;
    
    console.log('🔄 [Session Recovery] 启动Session恢复脚本...');
    
    /**
     * 从URL中提取认证参数
     */
    function extractAuthParamsFromURL() {
        const url = new URL(window.location.href);
        const params = new URLSearchParams(url.hash.substring(1)); // 移除#号
        
        return {
            access_token: params.get('access_token') || url.searchParams.get('access_token'),
            refresh_token: params.get('refresh_token') || url.searchParams.get('refresh_token'),
            expires_in: params.get('expires_in') || url.searchParams.get('expires_in'),
            token_type: params.get('token_type') || url.searchParams.get('token_type'),
            expires_at: params.get('expires_at') || url.searchParams.get('expires_at')
        };
    }
    
    /**
     * 手动设置Supabase Session
     */
    async function setSupabaseSession(authParams) {
        console.log('🔄 [Session Recovery] 正在手动设置Supabase Session...');
        
        try {
            if (!window.supabaseClient) {
                throw new Error('Supabase客户端未初始化');
            }
            
            const { access_token, refresh_token, expires_in } = authParams;
            
            if (!access_token || !refresh_token) {
                throw new Error('缺少必要的认证参数');
            }
            
            // 计算过期时间
            const expiresAt = Math.floor(Date.now() / 1000) + parseInt(expires_in || '3600');
            
            const { data, error } = await window.supabaseClient.auth.setSession({
                access_token,
                refresh_token
            });
            
            if (error) {
                console.log('❌ [Session Recovery] 设置Session失败:', error.message);
                return false;
            }
            
            // 与后端同步，设置cookie
            await fetch('/api/auth/set-session', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    token: access_token,
                    userId: data.session.user.id,
                    expires: Math.floor(Date.now() / 1000) + parseInt(expires_in || '3600')
                }),
                credentials: 'include'
            });
            
            if (data.session) {
                console.log('✅ [Session Recovery] Session设置成功');
                console.log('用户:', data.session.user?.email);
                
                // 保存到localStorage
                localStorage.setItem('authToken', access_token);
                localStorage.setItem('refreshToken', refresh_token);
                localStorage.setItem('userData', JSON.stringify(data.session.user));
                localStorage.setItem('isAuthenticated', 'true');
                localStorage.setItem('userEmail', data.session.user?.email || '');
                localStorage.setItem('userId', data.session.user?.id || '');
                
                return true;
            }
            
            return false;
        } catch (e) {
            console.log('❌ [Session Recovery] 设置Session时出错:', e.message);
            return false;
        }
    }
    
    /**
     * 与后端同步Session
     */
    async function syncWithBackend() {
        console.log('🔄 [Session Recovery] 正在与后端同步Session...');
        
        try {
            const { data: { session }, error } = await window.supabaseClient.auth.getSession();
            
            if (error || !session) {
                throw new Error('无法获取Supabase Session');
            }
            
            // 使用后端期望的字段同步 Session
            const response = await fetch('/api/auth/set-session', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                credentials: 'include',
                body: JSON.stringify({
                    token: session.access_token,
                    userId: session.user.id,
                    // 使用 Supabase Session 对象中的 expires_at 或默认 1 小时
                    expires: session.expires_at || (Math.floor(Date.now() / 1000) + (session.expires_in || 3600))
                })
            });
            
            if (response.ok) {
                console.log('✅ [Session Recovery] 后端同步成功');
                return true;
            } else {
                const errorData = await response.json();
                console.log('❌ [Session Recovery] 后端同步失败:', errorData);
                return false;
            }
        } catch (e) {
            console.log('❌ [Session Recovery] 后端同步出错:', e.message);
            return false;
        }
    }
    
    /**
     * 清理URL参数
     */
    function cleanupURL() {
        console.log('🧹 [Session Recovery] 清理URL参数...');
        
        const url = new URL(window.location.href);
        const cleanURL = `${url.origin}${url.pathname}`;
        
        // 不使用pushState，避免影响浏览器历史
        if (window.location.href !== cleanURL) {
            window.history.replaceState({}, document.title, cleanURL);
        }
    }
    
    /**
     * 显示恢复状态
     */
    function showRecoveryStatus(message, isSuccess = false) {
        console.log(`🔄 [Session Recovery] ${message}`);

        if (!SHOW_STATUS_TOAST) {
            return; // 关闭提示气泡，仅在控制台输出
        }

        // 创建状态提示
        const statusDiv = document.createElement('div');
        statusDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            background: ${isSuccess ? '#4CAF50' : '#ff9800'};
            color: white;
            border-radius: 8px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 300px;
        `;
        statusDiv.textContent = message;
        
        document.body.appendChild(statusDiv);
        
        // 自动移除
        setTimeout(() => {
            if (statusDiv.parentNode) {
                statusDiv.parentNode.removeChild(statusDiv);
            }
        }, isSuccess ? 3000 : 5000);
    }
    
    /**
     * 主恢复流程
     */
    async function recoverSession() {
        try {
            showRecoveryStatus('正在分析认证状态...');
            
            // 1. 检查URL中的认证参数
            const authParams = extractAuthParamsFromURL();
            console.log('🔄 [Session Recovery] URL认证参数:', authParams);
            
            if (!authParams.access_token) {
                console.log('🔄 [Session Recovery] URL中没有access_token，跳过恢复');
                return;
            }
            
            // 2. 检查Supabase Session状态
            const { data: { session } } = await window.supabaseClient.auth.getSession();
            
            if (session) {
                console.log('✅ [Session Recovery] Supabase Session已存在，无需恢复');
                showRecoveryStatus('认证状态正常', true);
                cleanupURL();
                return;
            }
            
            // 3. 手动设置Session
            showRecoveryStatus('正在恢复Session...');
            const sessionSet = await setSupabaseSession(authParams);
            
            if (!sessionSet) {
                showRecoveryStatus('Session恢复失败', false);
                return;
            }
            
            // 4. 与后端同步
            showRecoveryStatus('正在与后端同步...');
            const backendSynced = await syncWithBackend();
            
            if (backendSynced) {
                showRecoveryStatus('认证恢复成功！', true);
                
                // 5. 清理URL
                cleanupURL();
                
                // 6. 已注释掉自动重载，避免页面抖动
                // if (window.location.reload) {
                //     setTimeout(() => {
                //         window.location.reload();
                //     }, 1000);
                // }
            } else {
                showRecoveryStatus('后端同步失败', false);
            }
            
        } catch (e) {
            console.log('❌ [Session Recovery] 恢复过程出错:', e.message);
            showRecoveryStatus('恢复过程出错: ' + e.message, false);
        }
    }
    
    /**
     * 等待Supabase客户端初始化后执行
     */
    function waitForSupabaseAndRecover() {
        if (window.supabaseClient) {
            recoverSession();
        } else {
            console.log('🔄 [Session Recovery] 等待Supabase客户端初始化...');
            setTimeout(waitForSupabaseAndRecover, 500);
        }
    }
    
    // 页面加载时执行恢复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', waitForSupabaseAndRecover);
    } else {
        waitForSupabaseAndRecover();
    }
    
    // 导出恢复函数供手动调用
    window.recoverSession = recoverSession;
    
})(); 