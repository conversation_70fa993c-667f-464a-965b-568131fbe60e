@echo off
echo ============================================================
echo 部署500用户规模的统一Rate Limit配置到Fly.io
echo ============================================================
echo.

echo 正在设置用户规模环境变量...
fly secrets set USER_SCALE=500

echo.
echo 正在设置API速率限制环境变量...
echo.

fly secrets set ZHIPU_API_RATE_LIMIT=15
fly secrets set GEMINI_API_RATE_LIMIT=15  
fly secrets set DEEPSEEK_API_RATE_LIMIT=15
fly secrets set QWEN_API_RATE_LIMIT=15
fly secrets set GROK_API_RATE_LIMIT=15
fly secrets set CLAUDE_API_RATE_LIMIT=15
fly secrets set OPENROUTER_API_RATE_LIMIT=15

echo.
echo ============================================================
echo 统一Rate Limit配置部署完成！
echo ============================================================
echo.
echo 配置详情:
echo - 适用规模: 500用户
echo - 预期月AI操作: 68,000次
echo - 用户层限制: 已通过统一配置文件管理
echo - API层限制: 15次/秒 (含50%缓冲)
echo.
echo 新特性:
echo - 环境变量优先：如设置了环境变量，将优先使用
echo - 配置文件兜底：如未设置环境变量，自动使用配置文件中的值
echo - 动态规模切换：可通过 USER_SCALE 环境变量切换用户规模
echo.
echo 请检查设置是否成功:
echo   fly secrets list
echo.
echo 升级到其他规模:
echo   1000用户: fly secrets set USER_SCALE=1000
echo   2000用户: fly secrets set USER_SCALE=2000
echo.
pause 