/**
 * API客户端模块 - 提供统一的API请求处理，包括令牌验证、刷新和错误处理
 */
(function() {
    'use strict';

    // 内部状态
    let isInitialized = false;
    
    /**
     * 初始化API客户端
     */
    function init() {
        if (isInitialized) return;
        console.log('初始化API客户端...');
        isInitialized = true;
    }
    
    /**
     * 获取有效令牌
     * @returns {Promise<string>} 有效的令牌
     */
    async function getValidToken() {
        // 优先使用TokenRefreshHelper获取有效令牌
        if (window.TokenRefreshHelper && typeof window.TokenRefreshHelper.getValidAuthToken === 'function') {
            try {
                const token = await window.TokenRefreshHelper.getValidAuthToken();
                if (token) return token;
            } catch (error) {
                console.error('通过TokenRefreshHelper获取令牌失败:', error);
            }
        }
        
        // 备选方案：直接从localStorage获取
        const token = localStorage.getItem('authToken');
        if (!token || token === 'null' || token === 'undefined') {
            console.warn('无法获取有效令牌');
            return null;
        }
        
        return token;
    }
    
    /**
     * 发起API请求，自动处理令牌和错误
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<Response>} 响应对象
     */
    async function request(url, options = {}) {
        // 确保初始化
        if (!isInitialized) init();
        
        // 准备请求选项
        const requestOptions = {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            credentials: 'include' // 包含cookies
        };
        
        // 如果没有明确设置不自动添加认证头，则添加
        if (options.noAuth !== true) {
            try {
                const token = await getValidToken();
                if (token) {
                    requestOptions.headers['Authorization'] = `Bearer ${token}`;
                }
            } catch (error) {
                console.error('获取认证令牌失败:', error);
            }
        }
        
        // 清理特殊选项
        delete requestOptions.noAuth;
        
        try {
            // 发起请求
            const response = await fetch(url, requestOptions);
            
            // 处理401/403错误（认证/授权问题）
            if (response.status === 401 || response.status === 403) {
                console.warn(`API请求返回${response.status}状态码，可能是认证问题`);
                
                // 尝试刷新令牌并重试
                if (window.TokenRefreshHelper && typeof window.TokenRefreshHelper.refreshAuthToken === 'function') {
                    try {
                        console.log('尝试刷新令牌后重试请求');
                        const newToken = await window.TokenRefreshHelper.refreshAuthToken();
                        
                        if (newToken) {
                            // 使用新令牌重试请求
                            requestOptions.headers['Authorization'] = `Bearer ${newToken}`;
                            return fetch(url, requestOptions);
                        }
                    } catch (refreshError) {
                        console.error('刷新令牌失败:', refreshError);
                    }
                }
                
                // 如果无法刷新令牌，可能需要重新登录
                if (window.TokenRefreshHelper && typeof window.TokenRefreshHelper.recoverFromSession === 'function') {
                    try {
                        const sessionResult = await window.TokenRefreshHelper.recoverFromSession();
                        if (sessionResult.success) {
                            // 使用会话恢复的令牌重试请求
                            requestOptions.headers['Authorization'] = `Bearer ${sessionResult.token}`;
                            return fetch(url, requestOptions);
                        }
                    } catch (sessionError) {
                        console.error('从会话恢复失败:', sessionError);
                    }
                }
            }
            
            return response;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }
    
    /**
     * 发起GET请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<Response>} 响应对象
     */
    function get(url, options = {}) {
        return request(url, {
            method: 'GET',
            ...options
        });
    }
    
    /**
     * 发起POST请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise<Response>} 响应对象
     */
    function post(url, data = {}, options = {}) {
        return request(url, {
            method: 'POST',
            body: JSON.stringify(data),
            ...options
        });
    }
    
    /**
     * 发起PUT请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise<Response>} 响应对象
     */
    function put(url, data = {}, options = {}) {
        return request(url, {
            method: 'PUT',
            body: JSON.stringify(data),
            ...options
        });
    }
    
    /**
     * 发起DELETE请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<Response>} 响应对象
     */
    function del(url, options = {}) {
        return request(url, {
            method: 'DELETE',
            ...options
        });
    }

    // 初始化客户端
    init();
    
    // 暴露API
    window.ApiClient = {
        init,
        request,
        get,
        post,
        put,
        delete: del,
        getValidToken
    };
})(); 