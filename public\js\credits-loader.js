// 如果未加载 uiUtils，则创建简单占位实现，避免报错
if (typeof window.uiUtils === 'undefined') {
    window.uiUtils = {
        showLoadingOverlay: (msg) => console.log('[uiUtils stub] showLoadingOverlay:', msg),
        hideLoadingOverlay: () => console.log('[uiUtils stub] hideLoadingOverlay'),
        showErrorAlert: (msg) => alert(msg)
    };
}

// 积分加载器 - 页面加载时自动执行
(function() {
  'use strict';
  
  console.log('积分加载器正在初始化...');
  
  // 监听积分预加载事件
  document.addEventListener('credits-preloaded', function(event) {
    console.log('⚡ [Credits Loader] 收到积分预加载事件:', event.detail);
    
    // 立即更新显示
    if (window.CreditsManager && typeof window.CreditsManager.updateCreditsDisplay === 'function') {
      window.CreditsManager.credits = event.detail.credits;
      window.CreditsManager.updateCreditsDisplay();
      console.log(`⚡ [Credits Loader] 积分已快速更新为 ${event.detail.credits}`);
    }
    
    // 更新页面上的积分显示元素
    updateCreditsElements(event.detail.credits);
  });
  
  // 监听全局积分更新事件（保持兼容性）
  document.addEventListener('credits-updated', function(event) {
    console.log('积分加载器收到全局积分更新:', event.detail.credits);
    updateCreditsElements(event.detail.credits);
  });
  
  // 更新页面上的积分显示元素
  function updateCreditsElements(credits) {
    // 更新所有积分显示元素
    const creditsElements = document.querySelectorAll('.credits-display, .user-credits, #userCredits');
    creditsElements.forEach(element => {
      if (element) {
        element.textContent = credits;
        element.style.opacity = '1';
        // 移除加载状态
        element.classList.remove('credits-loading');
      }
    });
  }
  
  /**
   * 从后端加载初始积分
   */
  async function loadInitialCredits() {
    console.log('[Credits Loader] 正在从后端加载积分...');
    try {
      // 统一获取最新access_token
      let authToken = null;
      if (window.unifiedAuthManager && typeof window.unifiedAuthManager.getAccessToken === 'function') {
        authToken = window.unifiedAuthManager.getAccessToken();
      }
      if (!authToken) {
        authToken = localStorage.getItem('authToken');
      }

      const resp = await fetch('/api/credits/get-fast', {
        credentials: 'include',
        headers: authToken ? { 'Authorization': `Bearer ${authToken}` } : {}
      });
      if (!resp.ok) {
        throw new Error('API 响应状态非 200');
      }
      const data = await resp.json();
      if (data.success) {
        const credits = data.credits ?? 0;
        console.log(`[Credits Loader] 后端返回积分: ${credits}`);
        updateCreditsElements(credits);
        // 派发事件供全局监听
        document.dispatchEvent(new CustomEvent('credits-updated', { detail: { credits } }));
      } else {
        throw new Error(data.error || 'unknown error');
      }
    } catch (e) {
      console.error('[Credits Loader] 获取积分失败:', e);
      throw e;
    }
  }
  
  // 检查并加载用户积分
  async function checkAndLoadCredits(isInitialLoad = false) {
    const initialLoad = isInitialLoad;
    console.log(`[Credits Loader] 检查积分，是否首次加载: ${isInitialLoad}`);

    // 等待统一认证管理器准备就绪
    await window.unifiedAuthManager.waitForReady();
    console.log('[Credits Loader] 认证管理器已就绪，继续执行');

    const isAuthenticated = window.unifiedAuthManager.isAuthenticated();
    console.log(`[Credits Loader] 当前认证状态: ${isAuthenticated}`);

    if (isAuthenticated) {
        // 用户已登录
        console.log('[Credits Loader] 用户已认证，加载积分');
        uiUtils.showLoadingOverlay('Loading user data...');
        try {
            await loadInitialCredits();
        } catch (error) {
            console.error('[Credits Loader] 加载初始积分失败:', error);
            // 首次加载失败时仅尝试刷新，不弹窗；非首次加载才弹窗
            try {
                console.log('[Credits Loader] 尝试刷新认证状态并重试加载积分');
                await window.unifiedAuthManager.refreshAuthState();
                await loadInitialCredits();
                console.log('[Credits Loader] 重试后积分加载成功');
            } catch (retryError) {
                console.error('[Credits Loader] 重试后仍无法加载积分:', retryError);
                if (!initialLoad) {
                    uiUtils.showErrorAlert('Failed to load user data. Please try refreshing.');
                }
            }
        } finally {
            uiUtils.hideLoadingOverlay();
        }
    } else {
        // 用户未登录
        console.log('[Credits Loader] 用户未认证，跳转到登录页');
        
        // 只有在不是OAuth回调的情况下才重定向，避免中断登录流程
        const url = new URL(window.location.href);
        const hasAuthTokens = url.hash.includes('access_token');
        
        if (!hasAuthTokens) {
            window.location.href = 'index.html';
        } else {
            console.log('[Credits Loader] 检测到OAuth回调，暂停重定向，等待认证完成');
        }
    }
  }
  
  // 页面加载时的初始化逻辑
  function initializeCreditsLoader() {
    console.log('[Credits Loader] 积分加载器正在初始化...');
    
    // 使用新的检查和加载函数
    checkAndLoadCredits(true);
  }
  
  // 注释掉自动在页面加载时执行初始化的逻辑，由菜单点击时手动触发
  // document.addEventListener('DOMContentLoaded', () => {
  //   // 确保在其他脚本之后执行，特别是 unified-auth-manager
  //   setTimeout(initializeCreditsLoader, 100);
  // });
  
})();