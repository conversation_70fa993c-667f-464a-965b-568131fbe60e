问题描述：
登录进入main.html后，播放本地音频时，点随时问按钮，前端提示：智能问答失败，请重试

浏览器日志

已创建共享 Supabase 客户端实例，重定向URL: http://127.0.0.1:8080/main.html, 流程类型: pkce
unified-auth-manager.js:38 [Unified Auth] 统一认证管理器初始化
unified-auth-manager.js:717 [Unified Auth] 统一认证管理器脚本已加载
credits-loader.js:14 积分加载器正在初始化...
api-client.js:15 初始化API客户端...
credit-history.js:111 删除了已存在的积分历史模态框
credit-history.js:111 删除了已存在的积分历史模态框
payment-packages.js:5 payment-packages.js 开始加载
payment-packages.js:302 payment-packages.js 加载完成，函数已导出到 window.paymentPackages
bookmark-force-test.js:2 🔧 书签标记强制测试脚本已加载
bookmark-force-test.js:81 🔧 书签测试函数已准备：
bookmark-force-test.js:82 🔧 - forceShowBookmarkTest() : 强制显示彩色测试标记
bookmark-force-test.js:83 🔧 - checkBookmarks() : 检查书签数量
bookmark-force-test.js:84 🔧 - forceRefreshBookmarks() : 强制刷新书签标记
extreme-bookmark-test.js:2 🔥 极端书签显示测试脚本已加载
extreme-bookmark-test.js:146 🔥 极端测试函数已准备：
extreme-bookmark-test.js:147 🔥 - extremeBookmarkTest() : 极端显示测试
extreme-bookmark-test.js:148 🔥 - checkDOMHierarchy() : 检查DOM层级
quick-bookmark-fix-test.js:2 ✅ 书签修复验证脚本已加载
quick-bookmark-fix-test.js:85 ✅ 快速验证函数已准备：
quick-bookmark-fix-test.js:86 ✅ - quickBookmarkFixTest() : 验证修复效果
quick-bookmark-fix-test.js:87 ✅ - checkCurrentBookmarks() : 检查当前书签状态
main.html:2093 全局错误保护机制已启用
session-recovery.js?v=1.0&t=20250628:11 🔄 [Session Recovery] 启动Session恢复脚本...
manual-fix.js?v=1.0&t=20250628:156 🛠️ 手动修复工具已加载！输入 manualFix.help() 查看使用说明
resource-loader.js:566 开始加载库，模式: 仅本地
resource-loader.js:398 库 marked 已通过其他方式加载
resource-loader.js:398 库 mermaid 已通过其他方式加载
resource-loader.js:398 库 d3 已通过其他方式加载
resource-loader.js:398 库 supabase 已通过其他方式加载
resource-loader.js:303 所有必要库加载完成
resource-loader.js:630 marked库已初始化
resource-loader.js:653 mermaid库已初始化
resource-loader.js:597 所有必要库加载成功
unified-auth-manager.js:164 [Unified Auth] 认证状态已更新: Object
logout.js:3 登出脚本已加载
logout.js:10 找到登出按钮，正在添加点击事件
credit-history.js:1334 检测到localStorage中的认证状态，准备初始化积分历史管理器
credit-history.js:411 通过统一认证管理器获得用户ID: 5fde16f5-d2a3-473c-b98a-bdddfdcf05fd
credit-history.js:412 积分历史记录管理器初始化成功，用户ID: 5fde16f5-d2a3-473c-b98a-bdddfdcf05fd
credit-batch-manager.js:776 检测到localStorage中的认证状态，准备初始化积分批次管理器
credit-batch-manager.js:368 积分批次管理器初始化成功，用户ID: 5fde16f5-d2a3-473c-b98a-bdddfdcf05fd
subtitle-manager.js:1085 字幕管理器已初始化
main.html:1604 等待OAuth回调处理...
main.html:1606 OAuth回调处理完成，继续页面初始化。
main.html:1609 📋 开始页面统一初始化
main.html:1621 ⏳ 等待认证状态确定...
main.html:1624 📋 认证状态检查结果: Object
main.html:1637 ✅ 用户已登录，继续初始化页面
main.html:1651 ✅ 页面基础初始化完成
main.html:1655 💰 开始异步初始化积分系统
credits.js:787 积分管理器开始初始化过程...
credits.js:793 初始化积分管理器...
credits.js:822 用户登录状态: 已登录
credits.js:1507 获取有效令牌...
credits.js:1566 令牌状态检查: 过期时间=2025/7/23 14:51:56, 当前时间=2025/7/23 13:52:02, 剩余=3593秒
credits.js:1589 发送令牌验证请求，令牌长度: 893
main.html:303 菜单交互初始化完成
main.html:1717 积分更新: 0
ui-language.js:188 [UI Language] translatePage called but language data is not ready or is currently loading. Aborting.
translatePage @ ui-language.js:188Understand this warning
audio-app.js?v=20250625-1145:3407 AudioApp额外功能初始化完成
audio-app.js?v=20250625-1145:3408 当前上传指示器模式: simple
main.html:1687 🎵 音频应用初始化完成
main.html:1861 音频按钮事件绑定完成
main.html:2047 菜单按钮事件绑定完成
session-recovery.js?v=1.0&t=20250628:152 🔄 [Session Recovery] 正在分析认证状态...
session-recovery.js?v=1.0&t=20250628:194 🔄 [Session Recovery] URL认证参数: Object
session-recovery.js?v=1.0&t=20250628:197 🔄 [Session Recovery] URL中没有access_token，跳过恢复
ui-language.js:188 [UI Language] translatePage called but language data is not ready or is currently loading. Aborting.
translatePage @ ui-language.js:188Understand this warning
timestamp-manager.js:16 时间戳管理器初始化完成
timestamp-manager.js:900 Timestamp manager created and loaded to global object
podcast-rss-config-loader.js:19 🔄 正在加载播客RSS修复配置...
bookmark-manager.js:620 智能拖拽事件监听器已设置
bookmark-manager.js:18 书签管理器初始化完成
bookmark-manager.js:940 全局书签管理器已创建
ui-language.js:151 [UI Language] Language data for 'zh' loaded successfully. Applying translation.
ui-language.js:194 🛡️ [UI Language] 页面翻译保护已启用
ui-language.js:245 手动翻译输入框placeholder: 请输入您的问题...
ui-language.js:253 手动翻译播客搜索框placeholder: 搜索播客节目...
ui-language.js:261 手动翻译字幕搜索框placeholder: 搜索字幕
ui-language.js:183 [UI Language] translatePage已执行过，跳过重复调用
ui-language.js:83 Page translated to: zh
podcast-manager.js:1226 播客管理器已初始化，支持国际化
main.html:604 全局DOM变化监听器已启动
main.html:1 Unchecked runtime.lastError: Could not establish connection. Receiving end does not exist.Understand this error
ui-language.js:194 🛡️ [UI Language] 页面翻译保护已启用
ui-language.js:245 手动翻译输入框placeholder: 请输入您的问题...
ui-language.js:253 手动翻译播客搜索框placeholder: 搜索播客节目...
ui-language.js:261 手动翻译字幕搜索框placeholder: 搜索字幕
74818.js:1 [Violation] Permissions policy violation: unload is not allowed in this document.
e.linster @ 74818.js:1Understand this error
podcast-rss-config-loader.js:53 🔧 已启用修复: McKinsey Inside the Strategy Room (mckinsey)
podcast-rss-config-loader.js:30 ✅ 播客RSS修复配置加载完成
podcast-rss-config-loader.js:31 📊 配置版本: 1.0.0
podcast-rss-config-loader.js:32 📅 更新时间: 2025-01-26
podcast-rss-config-loader.js:33 🔧 可用修复数量: 1
podcast-manager.js:357 ✅ 默认播客配置加载完成，共 7 个播客
podcast-manager.js:375 加载合并播客列表，共 9 个播客 (收藏 2, 默认 7)
podcast-manager.js:1166 播客使用独立播放器，无需设置主播放器监听
podcast-manager.js:39 播客管理器初始化完成
images/podcast-covers/1721313249.jpg:1 
            
            
           Failed to load resource: the server responded with a status of 404 (NOT FOUND)Understand this error
images/podcast-covers/647826736.jpg:1 
            
            
           Failed to load resource: the server responded with a status of 404 (NOT FOUND)Understand this error
main.html:392 强制执行页面翻译
ui-language.js:183 [UI Language] translatePage已执行过，跳过重复调用
main.html:404 找到 1 个上传按钮, 18 个AI标签
credits.js:787 积分管理器开始初始化过程...
credits.js:793 初始化积分管理器...
credits.js:822 用户登录状态: 已登录
credits.js:1507 获取有效令牌...
credits.js:1566 令牌状态检查: 过期时间=2025/7/23 14:51:56, 当前时间=2025/7/23 13:52:02, 剩余=3593秒
credits.js:1589 发送令牌验证请求，令牌长度: 893
ui-language.js:268 🛡️ [UI Language] 页面翻译保护已移除
ui-language.js:268 🛡️ [UI Language] 页面翻译保护已移除
podcast-rss-config-loader.js:93 ===== RSS URL修复检查 =====
podcast-rss-config-loader.js:94 原始URL: https://feeds.npr.org/510308/podcast.xml
podcast-rss-config-loader.js:95 播客名称: Hidden Brain
podcast-rss-config-loader.js:121 ℹ️ 无需修复，使用原始URL
podcast-manager.js:550 ===== 前端加载节目列表 =====
podcast-manager.js:551 播客: Hidden Brain
podcast-manager.js:552 Feed URL: https://feeds.npr.org/510308/podcast.xml
podcast-manager.js:553 页码: 0, 每页: 3
podcast-manager.js:559 API调用URL: /podcast/episodes?feedUrl=https%3A%2F%2Ffeeds.npr.org%2F510308%2Fpodcast.xml&page=0&per_page=3
ui-language.js:159 🛡️ [UI Language] 语言切换保护已移除
credits.js:846 初始化：使用缓存积分，避免重复请求: 8738
credits.js:1446 更新UI积分显示: 8738
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
main.html:1717 积分更新: 8738
credits.js:3128 🕒 [动态免检查] 设置60分钟辅助功能免检查，到期时间: 2025/7/23 14:52:05
credits.js:852 🚀 [初始化] 积分8738 > 0，设置60分钟免检查时间
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:1446 更新UI积分显示: 8738
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:954 [Credits] OAuth管理器事件监听器已设置
credits.js:913 ✅ 积分管理器初始化完成，状态: Object
main.html:1658 💰 积分系统初始化完成
credits.js:3413 🚀 [动态缓存] 启动动态缓存时间管理机制...
credits.js:3323 📋 [辅助配置] 已加载后端配置: Object
credits.js:3417 🚀 [动态缓存] 配置加载完成: Object
credits.js:3421 🚀 [动态缓存] 当前积分状态: 8738
credits.js:3109 🕒 [动态免检查] 已存在有效免检查时间(剩余60分钟)，跳过重复设置
credits.js:3427 🚀 [动态缓存] 设置60分钟免检查时间
credits.js:3395 🕒 [自动检查] 已设置60分钟后自动检查积分
credits.js:3431 🚀 [动态缓存] 自动定期检查已设置
credits.js:3433 ✅ [动态缓存] 动态缓存管理已启动，免检查时间: 60分钟
credits.js:858 ✅ [初始化] 动态缓存管理已启动
credits.js:846 初始化：使用缓存积分，避免重复请求: 8738
credits.js:1446 更新UI积分显示: 8738
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:3109 🕒 [动态免检查] 已存在有效免检查时间(剩余60分钟)，跳过重复设置
credits.js:852 🚀 [初始化] 积分8738 > 0，设置60分钟免检查时间
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:1446 更新UI积分显示: 8738
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:954 [Credits] OAuth管理器事件监听器已设置
credits.js:913 ✅ 积分管理器初始化完成，状态: Object
podcast-manager.js:564 API响应状态: 200
podcast-manager.js:565 API响应数据: Object
podcast-manager.js:574 ✅ 检测到新API格式
podcast-manager.js:575 总节目数: 566, 当前页: 0, 每页: 3, 还有更多: true
podcast-manager.js:576 当前页节目数: 3
podcast-manager.js:583 前3个节目: Array(3)
podcast-manager.js:599 准备渲染节目列表...
podcast-manager.js:648 渲染节目: 当前页=0, 新增3期, 总计3期
podcast-manager.js:687 添加"加载更多"按钮，剩余563集
podcast-manager.js:601 ===== 节目列表加载完成 =====
podcast-manager.js:1153 [SUCCESS] 下载完成
main.html:1841 音频按钮被点击
audio-app.js?v=20250625-1145:588 🔍 文件上传检查: Object
audio-app.js?v=20250625-1145:604 ✅ 文件大小检查通过: 45.95MB <= 100MB
audio-app.js?v=20250625-1145:617 🚀 开始本地文件处理: You_20_The_Wisdom_of_Stoicism.mp3
audio-app.js?v=20250625-1145:623 📁 创建本地播放URL成功: blob:http://127.0.0.1:8080/1200124b-18fc-432e-a7f2-6d5bf1564c31
audio-app.js?v=20250625-1145:624 🔑 生成文件ID: local_1753249992650_gje46u1oi
audio-app.js?v=20250625-1145:635 🎵 音频播放器加载完成，文件引用已存储
timestamp-manager.js:130 Timestamp upload button enabled
bookmark-manager.js:446 📁 如果您有 You_20_The_Wisdom_of_Stoicism_bookmark.txt 书签文件，请将其拖拽到页面来加载书签
audio-app.js?v=20250625-1145:648 ✅ 本地音频文件加载完成: You_20_The_Wisdom_of_Stoicism.mp3 (45.95MB)
bookmark-manager.js:200 📍 更新书签标记 - 播放器类型: uploaded
bookmark-manager.js:201 📍 进度条元素: <div class=​"progress-bar" id=​"uploadedProgressBar">​…​</div>​
bookmark-manager.js:202 📍 播放器元素: <audio id=​"uploadedPlayer" controls preload=​"metadata" data-timestamp-sync-bound=​"true" src=​"blob:​http:​/​/​127.0.0.1:​8080/​1200124b-18fc-432e-a7f2-6d5bf1564c31">​</audio>​media
bookmark-manager.js:203 📍 播放器时长: 3011.36975
bookmark-manager.js:204 📍 当前书签数量: 0 Array(1)
bookmark-manager.js:225 📍 清除现有书签标记数量: 0
bookmark-manager.js:229 📍 开始添加 0 个书签标记
bookmark-manager.js:250 📍 书签标记更新完成
bookmark-manager.js:200 📍 更新书签标记 - 播放器类型: uploaded
bookmark-manager.js:201 📍 进度条元素: <div class=​"progress-bar" id=​"uploadedProgressBar">​…​</div>​
bookmark-manager.js:202 📍 播放器元素: <audio id=​"uploadedPlayer" controls preload=​"metadata" data-timestamp-sync-bound=​"true" src=​"blob:​http:​/​/​127.0.0.1:​8080/​1200124b-18fc-432e-a7f2-6d5bf1564c31">​</audio>​media
bookmark-manager.js:203 📍 播放器时长: 3011.36975
bookmark-manager.js:204 📍 当前书签数量: 0 Array(1)
bookmark-manager.js:225 📍 清除现有书签标记数量: 0
bookmark-manager.js:229 📍 开始添加 0 个书签标记
bookmark-manager.js:250 📍 书签标记更新完成
credits.js:2524 准备缓存积分数据: 8738
credits.js:2541 积分已缓存到本地 (8738)
credits.js:2524 准备缓存积分数据: 8738
credits.js:2541 积分已缓存到本地 (8738)
main.html:1641 � 认证状态变化: SIGNED_IN true
main.html:392 强制执行页面翻译
ui-language.js:183 [UI Language] translatePage已执行过，跳过重复调用
main.html:404 找到 1 个上传按钮, 17 个AI标签
unified-auth-manager.js:315 [Unified Auth] 登录后强制刷新积分
credits.js:3253 🔄 [登录积分同步] 开始强制刷新积分...
credits.js:1446 更新UI积分显示: 8738
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:3262 🔄 [登录积分同步] 使用登录页预加载积分: 8738
credits.js:3282 🔄 [登录积分同步] 当前积分: 8738
credits.js:3109 🕒 [动态免检查] 已存在有效免检查时间(剩余56分钟)，跳过重复设置
credits.js:3287 ✅ [登录积分同步] 积分2907 > 0，设置60分钟辅助功能免检查
credits.js:3294 ✅ [登录积分同步] 动态缓存管理已存在，跳过启动
credits.js:3301 ✅ [登录积分同步] 完成，耗时: 1ms
main.html:1914 菜单已显示
main.html:1917 📊 菜单点击：从后端缓存获取积分
main.html:1955 ✅ 菜单积分获取成功: 8738
credits.js:2524 准备缓存积分数据: 8738
credits.js:2541 积分已缓存到本地 (8738)
credits.js:3638 智能积分检查: 缓存积分=8738, 需要积分=1, 操作=smart_qa
credits.js:3642 缓存积分充足，跳过后端检查
audio-app.js?v=20250625-1145:1416 🔍 [音频片段分析] 关键参数:
audio-app.js?v=20250625-1145:1417   ▶️ 当前播放时间: 330.300s (05:30)
audio-app.js?v=20250625-1145:1418   ⏱️ 用户设置时长: 6s (用于回放)
audio-app.js?v=20250625-1145:1419   ➕ VBR容错缓冲: 1s (实际提取7s)
audio-app.js?v=20250625-1145:1420   -> VBR补偿偏移: 2s (提取窗口后移)
audio-app.js?v=20250625-1145:1423 🚀 [并行操作1] 开始即时回放...
audio-app.js?v=20250625-1145:1424   ▶️ 即时回放范围: 324.300s - 330.300s (6s)
audio-app.js?v=20250625-1145:3512 🔍 [即时回放] 时间范围验证:
audio-app.js?v=20250625-1145:3513   ▶️ 原始时间: 330.300s (05:30)
audio-app.js?v=20250625-1145:3514   ▶️ 回退时长: 6s
audio-app.js?v=20250625-1145:3515   📊 回放范围: 324.300s - 330.300s
audio-app.js?v=20250625-1145:3516   📄 对应文件名: 片段_324s-330s.mp3
audio-app.js?v=20250625-1145:3517   🎵 音频总时长: 3011.4s
audio-app.js?v=20250625-1145:3545 ⏰ [即时回放] 设置 6s 计时器，将在 1753250334155 触发
bookmark-manager.js:200 📍 更新书签标记 - 播放器类型: uploaded
bookmark-manager.js:201 📍 进度条元素: <div class=​"progress-bar" id=​"uploadedProgressBar">​…​</div>​
bookmark-manager.js:202 📍 播放器元素: <audio id=​"uploadedPlayer" controls preload=​"metadata" data-timestamp-sync-bound=​"true" src=​"blob:​http:​/​/​127.0.0.1:​8080/​1200124b-18fc-432e-a7f2-6d5bf1564c31">​</audio>​media
bookmark-manager.js:203 📍 播放器时长: 3011.36975
bookmark-manager.js:204 📍 当前书签数量: 1 Array(1)
bookmark-manager.js:225 📍 清除现有书签标记数量: 0
bookmark-manager.js:229 📍 开始添加 1 个书签标记
bookmark-manager.js:238 📍 书签 1: 时间=324.299527s, 位置=10.8%, 标签=05:24
bookmark-manager.js:247 📍 书签标记已添加到进度条
bookmark-manager.js:250 📍 书签标记更新完成
bookmark-manager.js:184 添加书签: Object
audio-app.js?v=20250625-1145:1444 🚀 开始三个并行操作...
audio-app.js?v=20250625-1145:1506 ⚡ [积分检查] 开始后端积分验证...
audio-app.js?v=20250625-1145:1551 🚀 [积分检查] 尝试快速后端缓存验证...
audio-app.js?v=20250625-1145:1451 🚀 [并行操作3] 开始音频片段生成...
audio-app.js?v=20250625-1145:1625 ⚡ [音频处理] 开始音频片段生成...
audio-app.js?v=20250625-1145:1632 🔍 [音频处理] 时间范围验证:
audio-app.js?v=20250625-1145:1633   ▶️ 原始时间点: 330.300s
audio-app.js?v=20250625-1145:1634   -> VBR补偿偏移: 2s
audio-app.js?v=20250625-1145:1635   = 有效时间点: 332.300s
audio-app.js?v=20250625-1145:1636   ⏱️ 提取时长: 7s
audio-app.js?v=20250625-1145:1637   🎯 意图内容范围: 323.300s - 330.300s
audio-app.js?v=20250625-1145:1638   🚀 实际请求范围: 325.300s - 332.300s
audio-app.js?v=20250625-1145:1644 📁 [音频处理] 本地文件模式，开始提取音频片段...
audio-app.js?v=20250625-1145:2011 🔍 [音频提取] 时间范围验证:
audio-app.js?v=20250625-1145:2012   ▶️ (有效)输入currentTime: 332.300s
audio-app.js?v=20250625-1145:2013   ▶️ (有效)输入snippetDuration: 7s
audio-app.js?v=20250625-1145:2014   📊 提取范围: 325.300s - 332.300s
audio-app.js?v=20250625-1145:2021 🔄 大文件检测(46.0MB)，尝试分块处理...
audio-app.js?v=20250625-1145:2090 🚀 启用分块处理模式，文件大小: 46.0MB
audio-app.js?v=20250625-1145:2096 🔍 [分块处理] 时间范围验证:
audio-app.js?v=20250625-1145:2097   ▶️ 输入currentTime: 332.300s
audio-app.js?v=20250625-1145:2098   ▶️ 输入snippetDuration: 7s
audio-app.js?v=20250625-1145:2099   📊 目标范围: 325.300s - 332.300s
audio-app.js?v=20250625-1145:2106 📊 音频信息: 时长=3011.4s, 文件大小=46.0MB, 比特率=128.0kbps
audio-app.js?v=20250625-1145:2117 📊 分块计算:
audio-app.js?v=20250625-1145:2118   ⏱️ 缓冲时间范围: 323.3s - 334.3s
audio-app.js?v=20250625-1145:2119   💾 字节范围: 5172817 - 5348819 (0.2MB)
audio-app.js?v=20250625-1145:1455 🚀 [并行操作2] 等待积分检查结果...
audio-app.js?v=20250625-1145:3531 🔄 [即时回放] 播放进度: 324.3s (进度0.0s/6s, 剩余6.0s)
audio-app.js?v=20250625-1145:3521 ✅ [即时回放] 开始播放成功，从 324.300s 开始
bookmark-manager.js:200 📍 更新书签标记 - 播放器类型: uploaded
bookmark-manager.js:201 📍 进度条元素: <div class=​"progress-bar" id=​"uploadedProgressBar">​…​</div>​
bookmark-manager.js:202 📍 播放器元素: <audio id=​"uploadedPlayer" controls preload=​"metadata" data-timestamp-sync-bound=​"true" src=​"blob:​http:​/​/​127.0.0.1:​8080/​1200124b-18fc-432e-a7f2-6d5bf1564c31">​</audio>​media
bookmark-manager.js:203 📍 播放器时长: 3011.36975
bookmark-manager.js:204 📍 当前书签数量: 1 Array(1)
bookmark-manager.js:225 📍 清除现有书签标记数量: 1
bookmark-manager.js:229 📍 开始添加 1 个书签标记
bookmark-manager.js:238 📍 书签 1: 时间=324.299527s, 位置=10.8%, 标签=05:24
bookmark-manager.js:247 📍 书签标记已添加到进度条
bookmark-manager.js:250 📍 书签标记更新完成
audio-app.js?v=20250625-1145:2139 🎯 分块中的目标片段:
audio-app.js?v=20250625-1145:2140   📊 分块时长: 10.998s
audio-app.js?v=20250625-1145:2141   📊 分块中位置: 2.000s - 9.000s
audio-app.js?v=20250625-1145:2142   📊 全局时间对应: 325.300s - 332.300s
audio-app.js?v=20250625-1145:2143   🔍 分块偏移验证: 323.300s + 2.000s = 325.300s
audio-app.js?v=20250625-1145:2153 🔍 [分块处理] 采样点验证:
audio-app.js?v=20250625-1145:2154   📊 采样率: 48000Hz
audio-app.js?v=20250625-1145:2155   📊 采样点范围: 96000 - 432000 (336000个采样点)
audio-app.js?v=20250625-1145:2156   📊 分块中时间: 2.000s - 9.000s
audio-app.js?v=20250625-1145:2161   🌍 全局时间验证: 325.300s - 332.300s
audio-app.js?v=20250625-1145:2162   ⚖️ 时间偏差: 开始0.000s, 结束0.000s
audio-app.js?v=20250625-1145:2183 ⚡ 分块处理完成: 总耗时=194.20ms, 读取=6.40ms, 解码=70.70ms, 转换=51.50ms, 片段大小=1344044字节
audio-app.js?v=20250625-1145:2184 ✅ [分块处理] 最终验证: 提取了全局时间 325.300s - 332.300s 的音频片段
audio-app.js?v=20250625-1145:1658 ✅ [音频处理] 生成文件信息:
audio-app.js?v=20250625-1145:1659   📄 文件名: 片段_323s-330s.mp3
audio-app.js?v=20250625-1145:1660   💾 文件大小: 1344044字节
audio-app.js?v=20250625-1145:2965 音频已加载到snippet播放器，等待手动播放: 片段_323s-330s.mp3
audio-app.js?v=20250625-1145:1669 ⚡ [音频处理] 本地文件处理完成，耗时: 208.3ms
audio-app.js?v=20250625-1145:3531 🔄 [即时回放] 播放进度: 325.3s (进度1.0s/6s, 剩余5.0s)
audio-app.js?v=20250625-1145:3531 🔄 [即时回放] 播放进度: 326.4s (进度2.1s/6s, 剩余3.9s)
audio-app.js?v=20250625-1145:1587 💰 [积分检查] 后端缓存验证 - 操作:随时问, 积分:8738, 需要:4, 阈值:14
audio-app.js?v=20250625-1145:1591 🚀 [积分检查] 随时问后端缓存积分充足(8738>14)，跳过详细检查
credits.js:2524 准备缓存积分数据: 8738
credits.js:2541 积分已缓存到本地 (8738)
credits.js:1446 更新UI积分显示: 8738
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
audio-app.js?v=20250625-1145:1514 🚀 [积分检查] 后端缓存验证通过，耗时: 4486.4ms
audio-app.js?v=20250625-1145:1465 ✅ [并行操作2] 积分检查通过
audio-app.js?v=20250625-1145:1468 🚀 [并行操作3] 等待音频处理完成...
audio-app.js?v=20250625-1145:1471 ✅ [并行操作3] 音频处理完成，开始AI分析...
audio-app.js?v=20250625-1145:1472   📄 生成文件名: 片段_323s-330s.mp3
audio-app.js?v=20250625-1145:1721 ⚡ [AI分析] 使用预处理的音频数据，跳过重复处理...
audio-app.js?v=20250625-1145:1730 🚀 [AI分析] 辅助功能免检查生效，剩余54分钟，跳过积分检查
api/analyze-local-audio-stream:1 
            
            
           Failed to load resource: the server responded with a status of 501 (NOT IMPLEMENTED)Understand this error
audio-app.js?v=20250625-1145:1801 优化的本地音频分析失败: Error: HTTP error! status: 501
    at AudioApp.streamQuestionWithLocalSnippetOptimized (audio-app.js?v=20250625-1145:1754:23)
    at async AudioApp.streamQuestionWithProcessedAudio (audio-app.js?v=20250625-1145:1711:13)
    at async AudioApp.askQuestionAtCurrentTime (audio-app.js?v=20250625-1145:1475:13)
streamQuestionWithLocalSnippetOptimized @ audio-app.js?v=20250625-1145:1801Understand this error
audio-app.js?v=20250625-1145:1485 智能问答失败: Error: HTTP error! status: 501
    at AudioApp.streamQuestionWithLocalSnippetOptimized (audio-app.js?v=20250625-1145:1754:23)
    at async AudioApp.streamQuestionWithProcessedAudio (audio-app.js?v=20250625-1145:1711:13)
    at async AudioApp.askQuestionAtCurrentTime (audio-app.js?v=20250625-1145:1475:13)
askQuestionAtCurrentTime @ audio-app.js?v=20250625-1145:1485Understand this error
credits.js:1947 🔄 [积分同步] API调用完成，开始积分同步
credits.js:1948 🔄 [积分同步] 当前前端缓存积分: 8738
credits.js:1968 🚀 [积分同步] 强制执行API调用后积分同步（跳过防重复检查）
credits.js:1974 🌐 [积分同步] 从服务器获取最新积分（跳过缓存）
credits.js:1023 跳过积分缓存，强制从服务器获取最新积分
credits.js:1119 从服务器获取最新积分数据...
credits.js:1507 获取有效令牌...
credits.js:1566 令牌状态检查: 过期时间=2025/7/23 14:51:56, 当前时间=2025/7/23 13:58:52, 剩余=3183秒
credits.js:1589 发送令牌验证请求，令牌长度: 893
audio-app.js?v=20250625-1145:3548 ⏰ [即时回放] 计时器触发:
audio-app.js?v=20250625-1145:3549   ▶️ 当前播放时间: 330.241s
audio-app.js?v=20250625-1145:3550   📊 实际播放时长: 5.942s / 6s
audio-app.js?v=20250625-1145:3551   📍 目标结束时间: 330.300s
audio-app.js?v=20250625-1145:3555 ✅ [即时回放] 播放完成，已在 6s 后自动暂停
audio-app.js?v=20250625-1145:3556   📊 最终播放范围: 324.300s - 330.241s
audio-app.js?v=20250625-1145:3567 🧹 [即时回放] 清理完成
credits.js:1125 尝试使用原始端点: /api/credits/get
credits.js:1126 认证令牌长度: 893
credits.js:1164 服务器返回积分: 8738
credits.js:2524 准备缓存积分数据: 8738
credits.js:2541 积分已缓存到本地 (8738)
credits.js:1446 更新UI积分显示: 8738
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:3109 🕒 [动态免检查] 已存在有效免检查时间(剩余54分钟)，跳过重复设置
credits.js:241 🚀 [积分获取完成] 积分8738 > 0，设置60分钟免检查时间
credits.js:1976 📊 [积分同步] 从服务器获取到积分: 8738
credits.js:1979 🎨 [积分同步] 更新前端积分显示
credits.js:1446 更新UI积分显示: 8738
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:1983 📡 [积分同步] 触发积分更新事件
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:1989 🔄 [积分同步] 强制更新所有积分显示元素
credits.js:2033 开始强制更新所有积分显示元素，目标积分: 8738
credits.js:2037 找到 1 个普通积分显示元素
credits.js:2045 更新积分元素 1: 8738 → 8738
credits.js:2056 更新菜单积分显示: 8738 → 8738
credits.js:2068 更新直接查找的积分元素: 8738 → 8738
credits.js:2071 ✅ 已强制更新所有积分显示元素为: 8738
credits.js:1993 💾 [积分同步] 清除并重新设置前端缓存
credits.js:2524 准备缓存积分数据: 8738
credits.js:2541 积分已缓存到本地 (8738)
credits.js:2005 ✅ [积分同步] API调用后积分同步完成，当前积分: 8738
credits.js:3638 智能积分检查: 缓存积分=8738, 需要积分=1, 操作=smart_qa
credits.js:3642 缓存积分充足，跳过后端检查
audio-app.js?v=20250625-1145:1416 🔍 [音频片段分析] 关键参数:
audio-app.js?v=20250625-1145:1417   ▶️ 当前播放时间: 329.686s (05:29)
audio-app.js?v=20250625-1145:1418   ⏱️ 用户设置时长: 6s (用于回放)
audio-app.js?v=20250625-1145:1419   ➕ VBR容错缓冲: 1s (实际提取7s)
audio-app.js?v=20250625-1145:1420   -> VBR补偿偏移: 2s (提取窗口后移)
audio-app.js?v=20250625-1145:1423 🚀 [并行操作1] 开始即时回放...
audio-app.js?v=20250625-1145:1424   ▶️ 即时回放范围: 323.686s - 329.686s (6s)
audio-app.js?v=20250625-1145:3498 [即时回放] 清除之前的计时器
audio-app.js?v=20250625-1145:3512 🔍 [即时回放] 时间范围验证:
audio-app.js?v=20250625-1145:3513   ▶️ 原始时间: 329.686s (05:29)
audio-app.js?v=20250625-1145:3514   ▶️ 回退时长: 6s
audio-app.js?v=20250625-1145:3515   📊 回放范围: 323.686s - 329.686s
audio-app.js?v=20250625-1145:3516   📄 对应文件名: 片段_323s-329s.mp3
audio-app.js?v=20250625-1145:3517   🎵 音频总时长: 3011.4s
audio-app.js?v=20250625-1145:3545 ⏰ [即时回放] 设置 6s 计时器，将在 1753250365626 触发
audio-app.js?v=20250625-1145:1444 🚀 开始三个并行操作...
audio-app.js?v=20250625-1145:1506 ⚡ [积分检查] 开始后端积分验证...
audio-app.js?v=20250625-1145:1551 🚀 [积分检查] 尝试快速后端缓存验证...
audio-app.js?v=20250625-1145:1451 🚀 [并行操作3] 开始音频片段生成...
audio-app.js?v=20250625-1145:1625 ⚡ [音频处理] 开始音频片段生成...
audio-app.js?v=20250625-1145:1632 🔍 [音频处理] 时间范围验证:
audio-app.js?v=20250625-1145:1633   ▶️ 原始时间点: 329.686s
audio-app.js?v=20250625-1145:1634   -> VBR补偿偏移: 2s
audio-app.js?v=20250625-1145:1635   = 有效时间点: 331.686s
audio-app.js?v=20250625-1145:1636   ⏱️ 提取时长: 7s
audio-app.js?v=20250625-1145:1637   🎯 意图内容范围: 322.686s - 329.686s
audio-app.js?v=20250625-1145:1638   🚀 实际请求范围: 324.686s - 331.686s
audio-app.js?v=20250625-1145:1644 📁 [音频处理] 本地文件模式，开始提取音频片段...
audio-app.js?v=20250625-1145:2011 🔍 [音频提取] 时间范围验证:
audio-app.js?v=20250625-1145:2012   ▶️ (有效)输入currentTime: 331.686s
audio-app.js?v=20250625-1145:2013   ▶️ (有效)输入snippetDuration: 7s
audio-app.js?v=20250625-1145:2014   📊 提取范围: 324.686s - 331.686s
audio-app.js?v=20250625-1145:2021 🔄 大文件检测(46.0MB)，尝试分块处理...
audio-app.js?v=20250625-1145:2090 🚀 启用分块处理模式，文件大小: 46.0MB
audio-app.js?v=20250625-1145:2096 🔍 [分块处理] 时间范围验证:
audio-app.js?v=20250625-1145:2097   ▶️ 输入currentTime: 331.686s
audio-app.js?v=20250625-1145:2098   ▶️ 输入snippetDuration: 7s
audio-app.js?v=20250625-1145:2099   📊 目标范围: 324.686s - 331.686s
audio-app.js?v=20250625-1145:2106 📊 音频信息: 时长=3011.4s, 文件大小=46.0MB, 比特率=128.0kbps
audio-app.js?v=20250625-1145:2117 📊 分块计算:
audio-app.js?v=20250625-1145:2118   ⏱️ 缓冲时间范围: 322.7s - 333.7s
audio-app.js?v=20250625-1145:2119   💾 字节范围: 5163008 - 5339010 (0.2MB)
audio-app.js?v=20250625-1145:1455 🚀 [并行操作2] 等待积分检查结果...
audio-app.js?v=20250625-1145:3531 🔄 [即时回放] 播放进度: 323.7s (进度0.0s/6s, 剩余6.0s)
audio-app.js?v=20250625-1145:3521 ✅ [即时回放] 开始播放成功，从 323.686s 开始
audio-app.js?v=20250625-1145:2139 🎯 分块中的目标片段:
audio-app.js?v=20250625-1145:2140   📊 分块时长: 10.998s
audio-app.js?v=20250625-1145:2141   📊 分块中位置: 2.000s - 9.000s
audio-app.js?v=20250625-1145:2142   📊 全局时间对应: 324.686s - 331.686s
audio-app.js?v=20250625-1145:2143   🔍 分块偏移验证: 322.686s + 2.000s = 324.686s
audio-app.js?v=20250625-1145:2153 🔍 [分块处理] 采样点验证:
audio-app.js?v=20250625-1145:2154   📊 采样率: 48000Hz
audio-app.js?v=20250625-1145:2155   📊 采样点范围: 96000 - 432000 (336000个采样点)
audio-app.js?v=20250625-1145:2156   📊 分块中时间: 2.000s - 9.000s
audio-app.js?v=20250625-1145:2161   🌍 全局时间验证: 324.686s - 331.686s
audio-app.js?v=20250625-1145:2162   ⚖️ 时间偏差: 开始0.000s, 结束0.000s
audio-app.js?v=20250625-1145:2183 ⚡ 分块处理完成: 总耗时=179.50ms, 读取=5.30ms, 解码=66.90ms, 转换=49.40ms, 片段大小=1344044字节
audio-app.js?v=20250625-1145:2184 ✅ [分块处理] 最终验证: 提取了全局时间 324.686s - 331.686s 的音频片段
audio-app.js?v=20250625-1145:1658 ✅ [音频处理] 生成文件信息:
audio-app.js?v=20250625-1145:1659   📄 文件名: 片段_322s-329s.mp3
audio-app.js?v=20250625-1145:1660   💾 文件大小: 1344044字节
audio-app.js?v=20250625-1145:2965 音频已加载到snippet播放器，等待手动播放: 片段_322s-329s.mp3
audio-app.js?v=20250625-1145:1669 ⚡ [音频处理] 本地文件处理完成，耗时: 193.1ms
audio-app.js?v=20250625-1145:3531 🔄 [即时回放] 播放进度: 325.7s (进度2.1s/6s, 剩余3.9s)
audio-app.js?v=20250625-1145:1587 💰 [积分检查] 后端缓存验证 - 操作:随时问, 积分:8738, 需要:4, 阈值:14
audio-app.js?v=20250625-1145:1591 🚀 [积分检查] 随时问后端缓存积分充足(8738>14)，跳过详细检查
credits.js:2524 准备缓存积分数据: 8738
credits.js:2541 积分已缓存到本地 (8738)
credits.js:1446 更新UI积分显示: 8738
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
audio-app.js?v=20250625-1145:1514 🚀 [积分检查] 后端缓存验证通过，耗时: 4424.7ms
audio-app.js?v=20250625-1145:1465 ✅ [并行操作2] 积分检查通过
audio-app.js?v=20250625-1145:1468 🚀 [并行操作3] 等待音频处理完成...
audio-app.js?v=20250625-1145:1471 ✅ [并行操作3] 音频处理完成，开始AI分析...
audio-app.js?v=20250625-1145:1472   📄 生成文件名: 片段_322s-329s.mp3
audio-app.js?v=20250625-1145:1721 ⚡ [AI分析] 使用预处理的音频数据，跳过重复处理...
audio-app.js?v=20250625-1145:1730 🚀 [AI分析] 辅助功能免检查生效，剩余53分钟，跳过积分检查
api/analyze-local-audio-stream:1 
            
            
           Failed to load resource: the server responded with a status of 501 (NOT IMPLEMENTED)Understand this error
audio-app.js?v=20250625-1145:1801 优化的本地音频分析失败: Error: HTTP error! status: 501
    at AudioApp.streamQuestionWithLocalSnippetOptimized (audio-app.js?v=20250625-1145:1754:23)
    at async AudioApp.streamQuestionWithProcessedAudio (audio-app.js?v=20250625-1145:1711:13)
    at async AudioApp.askQuestionAtCurrentTime (audio-app.js?v=20250625-1145:1475:13)
streamQuestionWithLocalSnippetOptimized @ audio-app.js?v=20250625-1145:1801Understand this error
audio-app.js?v=20250625-1145:1485 智能问答失败: Error: HTTP error! status: 501
    at AudioApp.streamQuestionWithLocalSnippetOptimized (audio-app.js?v=20250625-1145:1754:23)
    at async AudioApp.streamQuestionWithProcessedAudio (audio-app.js?v=20250625-1145:1711:13)
    at async AudioApp.askQuestionAtCurrentTime (audio-app.js?v=20250625-1145:1475:13)
askQuestionAtCurrentTime @ audio-app.js?v=20250625-1145:1485Understand this error
credits.js:1947 🔄 [积分同步] API调用完成，开始积分同步
credits.js:1948 🔄 [积分同步] 当前前端缓存积分: 8738
credits.js:1968 🚀 [积分同步] 强制执行API调用后积分同步（跳过防重复检查）
credits.js:1974 🌐 [积分同步] 从服务器获取最新积分（跳过缓存）
credits.js:1023 跳过积分缓存，强制从服务器获取最新积分
credits.js:1119 从服务器获取最新积分数据...
credits.js:1507 获取有效令牌...
credits.js:1566 令牌状态检查: 过期时间=2025/7/23 14:51:56, 当前时间=2025/7/23 13:59:24, 剩余=3151秒
credits.js:1589 发送令牌验证请求，令牌长度: 893
audio-app.js?v=20250625-1145:3548 ⏰ [即时回放] 计时器触发:
audio-app.js?v=20250625-1145:3549   ▶️ 当前播放时间: 329.639s
audio-app.js?v=20250625-1145:3550   📊 实际播放时长: 5.953s / 6s
audio-app.js?v=20250625-1145:3551   📍 目标结束时间: 329.686s
audio-app.js?v=20250625-1145:3555 ✅ [即时回放] 播放完成，已在 6s 后自动暂停
audio-app.js?v=20250625-1145:3556   📊 最终播放范围: 323.686s - 329.639s
audio-app.js?v=20250625-1145:3567 🧹 [即时回放] 清理完成
credits.js:1125 尝试使用原始端点: /api/credits/get
credits.js:1126 认证令牌长度: 893
credits.js:1164 服务器返回积分: 8738
credits.js:2524 准备缓存积分数据: 8738
credits.js:2541 积分已缓存到本地 (8738)
credits.js:1446 更新UI积分显示: 8738
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:3109 🕒 [动态免检查] 已存在有效免检查时间(剩余53分钟)，跳过重复设置
credits.js:241 🚀 [积分获取完成] 积分8738 > 0，设置60分钟免检查时间
credits.js:1976 📊 [积分同步] 从服务器获取到积分: 8738
credits.js:1979 🎨 [积分同步] 更新前端积分显示
credits.js:1446 更新UI积分显示: 8738
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:1983 📡 [积分同步] 触发积分更新事件
credits-loader.js:33 积分加载器收到全局积分更新: 8738
main.html:1752 收到积分更新事件: Object
credits.js:1989 🔄 [积分同步] 强制更新所有积分显示元素
credits.js:2033 开始强制更新所有积分显示元素，目标积分: 8738
credits.js:2037 找到 1 个普通积分显示元素
credits.js:2045 更新积分元素 1: 8738 → 8738
credits.js:2056 更新菜单积分显示: 8738 → 8738
credits.js:2068 更新直接查找的积分元素: 8738 → 8738
credits.js:2071 ✅ 已强制更新所有积分显示元素为: 8738
credits.js:1993 💾 [积分同步] 清除并重新设置前端缓存
credits.js:2524 准备缓存积分数据: 8738
credits.js:2541 积分已缓存到本地 (8738)
credits.js:2005 ✅ [积分同步] API调用后积分同步完成，当前积分: 8738

后端日志

正在启动Python后端服务器...
2025-07-23 13:51:42,713 - INFO - 播客功能：dateutil已成功加载，支持日期排序
2025-07-23 13:51:42,713 - INFO - 直接实现音频功能，不依赖audio文件夹
2025-07-23 13:51:42,717 - INFO - 已从 C:\Users\<USER>\tool\web_音频理解\0717-1740-重构app脚本\.env 加载环境 变量
2025-07-23 13:51:42,718 - INFO - 开始应用核心初始化...
2025-07-23 13:51:42,724 - INFO - 从文件 .flask_secret_key 读取SECRET_KEY成功
2025-07-23 13:51:42,729 - INFO - 初始化Creem API客户端: https://test-api.creem.io/v1
2025-07-23 13:51:42,729 - INFO - 初始化Creem API客户端: https://test-api.creem.io/v1
2025-07-23 13:51:43,052 - INFO - Supabase客户端初始化成功: https://ewlpneznhlwxuapaibiu.supabase.co
2025-07-23 13:51:43,068 - INFO - 服务角色Supabase客户端初始化成功
2025-07-23 13:51:43,068 - INFO - 已为支付路由添加速率限制
2025-07-23 13:51:43,069 - INFO - 支付模块已注册
2025-07-23 13:51:43,088 - INFO - Credits模块: Supabase客户端初始化成功: https://ewlpneznhlwxuapaibiu.supabase.co
2025-07-23 13:51:43,104 - INFO - Credits模块: 服务角色Supabase客户端初始化成功
2025-07-23 13:51:43,104 - INFO - Credits模块: 已为积分路由添加速率限制
2025-07-23 13:51:43,104 - INFO - 积分模块已注册
2025-07-23 13:51:43,105 - INFO - 积分模块已注册
2025-07-23 13:51:43,109 - INFO - 为认证路由添加速率限制: 登录=15 per minute, 100 per hour, 400 per day, 密码重置=5 per minute, 20 per hour, 40 per day
2025-07-23 13:51:43,110 - INFO - 音频管理器初始化完成 - 资源控制：最多3个文件并存
2025-07-23 13:51:43,111 - INFO - 🔄 执行启动时临时文件清理...
2025-07-23 13:51:43,113 - INFO - ✅ 启动清理完成，已清理 2 个临时文件，确保每次启动都是干净状态
2025-07-23 13:51:43,113 - INFO - 音频管理器已初始化，启动清理完成
2025-07-23 13:51:43,116 - INFO - 音频路由已注册
2025-07-23 13:51:43,117 - INFO - 应用核心初始化完成，防重复初始化标志已设置
2025-07-23 13:51:43,123 - INFO - 为路由 audio.upload_audio 应用了速率限制: 15 per minute, 100 per hour, 500 per day (类别: content)
2025-07-23 13:51:43,123 - INFO - 为路由 audio.analyze_audio_stream 应用了速率限制: 20 per minute, 120 per hour, 600 per day (类别: content)
2025-07-23 13:51:43,123 - INFO - 为路由 audio.analyze_local_audio_stream 应用了速率限制: 20 per minute, 120 per hour, 600 per day (类别: content)
2025-07-23 13:51:43,124 - INFO - 为路由 analyze_local_audio_stream 应用了速率限制: 20 per minute, 120 per hour, 600 per day (类别: content)
2025-07-23 13:51:43,125 - INFO - 为路由 ask_question_stream 应用了速率限制: 25 per minute, 150 per hour, 800 per day (类别: content)
2025-07-23 13:51:43,125 - INFO - 为路由 continue_conversation_stream 应用了速率限制: 25 per minute, 150 per hour, 800 per day (类别: content)
2025-07-23 13:51:43,127 - INFO - 为路由 generate_timestamps 应用了速率限制: 50 per minute, 500 per hour, 2000 per day (类别: content)
2025-07-23 13:51:43,128 - INFO - 为路由 upload_subtitle 应用了速率限制: 50 per minute, 500 per hour, 2000 per day (类别: content)
2025-07-23 13:51:43,128 - INFO - 为路由 export_bookmarks 应用了速率限制: 50 per minute, 500 per hour, 2000 per day (类别: auth)
2025-07-23 13:51:43,129 - INFO - 为路由 parse_bookmarks 应用了速率限制: 50 per minute, 500 per hour, 2000 per day (类别: auth)
2025-07-23 13:51:43,129 - INFO - 为路由 podcast_search 应用了速率限制: 10 per minute, 60 per hour, 300 per day (类 别: content)
2025-07-23 13:51:43,129 - INFO - 为路由 podcast_episodes 应用了速率限制: 15 per minute, 80 per hour, 400 per day ( 类别: content)
2025-07-23 13:51:43,131 - INFO - 为路由 podcast_proxy_download 应用了速率限制: 5 per minute, 30 per hour, 100 per day (类别: content)
2025-07-23 13:51:43,131 - INFO - 已为所有标记的路由动态添加速率限制
2025-07-23 13:51:43,134 - INFO - 启动Flask应用，监听端口: 8080
2025-07-23 13:51:43,134 - INFO - 调试模式: 关闭
 * Serving Flask app 'app'
 * Debug mode: off
2025-07-23 13:51:43,155 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://************:8080
2025-07-23 13:51:43,155 - INFO - [33mPress CTRL+C to quit[0m
2025-07-23 13:51:46,933 - INFO - 尝试加载index.html文件
2025-07-23 13:51:47,103 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET / HTTP/1.1" 200 -
2025-07-23 13:51:47,161 - INFO - 动态生成env.js配置 - Supabase URL: https://ewlpneznhlwxuapaibiu.s...
2025-07-23 13:51:47,163 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET /env.js HTTP/1.1" 200 -
2025-07-23 13:51:47,169 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET /css/landing.css HTTP/1.1" 200 -
2025-07-23 13:51:47,170 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET /css/grammar-styles.css HTTP/1.1" 200 -
2025-07-23 13:51:47,174 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET /js/ui-language.js HTTP/1.1" 200 -
2025-07-23 13:51:47,179 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET /js/resource-loader.js HTTP/1.1" 200 -
2025-07-23 13:51:47,183 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET /js/app-config.js HTTP/1.1" 200 -
2025-07-23 13:51:47,183 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET /js/env-config.js HTTP/1.1" 200 -
2025-07-23 13:51:47,187 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET /js/lib/supabase.min.js HTTP/1.1" 200 -
2025-07-23 13:51:47,195 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET /js/supabase-client.js HTTP/1.1" 200 -
2025-07-23 13:51:47,197 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET /js/grammar-analyzer.js HTTP/1.1" 200 -
2025-07-23 13:51:47,499 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:47] "GET /lang/zh.json HTTP/1.1" 200 -
2025-07-23 13:51:49,044 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:49] "GET /log.html HTTP/1.1" 200 -
2025-07-23 13:51:49,114 - INFO - 动态生成env.js配置 - Supabase URL: https://ewlpneznhlwxuapaibiu.s...
2025-07-23 13:51:49,117 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:49] "[36mGET /js/env-config.js HTTP/1.1[0m" 304 -
2025-07-23 13:51:49,117 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:49] "GET /env.js HTTP/1.1" 200 -
2025-07-23 13:51:49,123 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:49] "GET /css/log.css HTTP/1.1" 200 -
2025-07-23 13:51:49,126 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:49] "GET /css/mobile-styles.css HTTP/1.1" 200 -
2025-07-23 13:51:49,130 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:49] "GET /js/auth.js HTTP/1.1" 200 -
2025-07-23 13:51:49,136 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:49] "[36mGET /js/ui-language.js HTTP/1.1[0m" 304 -
2025-07-23 13:51:49,211 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:49] "[36mGET /lang/zh.json HTTP/1.1[0m" 304 -
2025-07-23 13:51:49,215 - INFO - 127.0.0.1 - - [23/Jul/2025 13:51:49] "[36mGET /js/lib/supabase.min.js HTTP/1.1[0m" 304 -
2025-07-23 13:51:58,005 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:51:58,892 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:51:59,737 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:51:59,739 - INFO - [快速积分API] 快速获取用户积分请求: 用户ID=5fde16f5-d2a3-473c-b98a-bdddfdcf05fd
2025-07-23 13:51:59,739 - INFO - [快速积分API] 缓存未命中，查询数据库并同步缓存: 用户ID=5fde16f5-d2a3-473c-b98a-bdddfdcf05fd
2025-07-23 13:52:01,450 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/rest/v1/user_credits?select=credits%2Cupdated_at&user_id=eq.5fde16f5-d2a3-473c-b98a-bdddfdcf05fd "HTTP/2 200 OK"
2025-07-23 13:52:01,456 - INFO - [快速积分API] 数据库查询成功并已缓存: 用户=5fde16f5-d2a3-473c-b98a-bdddfdcf05fd,  积分=8738
2025-07-23 13:52:01,457 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /api/credits/get-fast HTTP/1.1" 200 -
2025-07-23 13:52:01,485 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /main.html HTTP/1.1" 200 -
2025-07-23 13:52:01,547 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/lib/marked.min.js HTTP/1.1" 200 -
2025-07-23 13:52:01,555 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/lib/mermaid.min.js HTTP/1.1" 200 -
2025-07-23 13:52:01,556 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "[36mGET /js/resource-loader.js HTTP/1.1[0m" 304 -
2025-07-23 13:52:01,556 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/lib/d3.v7.min.js HTTP/1.1" 200 -
2025-07-23 13:52:01,556 - INFO - 动态生成env.js配置 - Supabase URL: https://ewlpneznhlwxuapaibiu.s...
2025-07-23 13:52:01,557 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /env.js HTTP/1.1" 200 -
2025-07-23 13:52:01,568 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /css/credits.css HTTP/1.1" 200 -
2025-07-23 13:52:01,581 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /css/credit-history.css HTTP/1.1" 200 -
2025-07-23 13:52:01,581 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /css/payment.css HTTP/1.1" 200 -
2025-07-23 13:52:01,585 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /css/timestamp-styles.css HTTP/1.1" 200 -
2025-07-23 13:52:01,606 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "[36mGET /css/grammar-styles.css HTTP/1.1[0m" 304 -
2025-07-23 13:52:01,606 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /css/audio-styles.css HTTP/1.1" 200 -
2025-07-23 13:52:01,641 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "[36mGET /js/ui-language.js HTTP/1.1[0m" 304 -
2025-07-23 13:52:01,647 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/language-settings.js HTTP/1.1" 200 -
2025-07-23 13:52:01,650 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "[36mGET /js/lib/supabase.min.js HTTP/1.1[0m" 304 -
2025-07-23 13:52:01,667 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "[36mGET /js/supabase-client.js HTTP/1.1[0m" 304 -
2025-07-23 13:52:01,669 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/google-auth.js HTTP/1.1" 200 -
2025-07-23 13:52:01,672 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/credits-loader.js HTTP/1.1" 200 -
2025-07-23 13:52:01,685 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/unified-auth-manager.js HTTP/1.1" 200 -
2025-07-23 13:52:01,689 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/logout.js HTTP/1.1" 200 -
2025-07-23 13:52:01,690 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/api-client.js HTTP/1.1" 200 -
2025-07-23 13:52:01,696 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/credits.js HTTP/1.1" 200 -
2025-07-23 13:52:01,701 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/credit-history.js HTTP/1.1" 200 -
2025-07-23 13:52:01,707 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/credit-batch-manager.js HTTP/1.1" 200 -
2025-07-23 13:52:01,715 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/token-error-handler.js HTTP/1.1" 200 -
2025-07-23 13:52:01,718 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/payment-packages.js HTTP/1.1" 200 -
2025-07-23 13:52:01,721 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/payment.js HTTP/1.1" 200 -
2025-07-23 13:52:01,724 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "[36mGET /js/grammar-analyzer.js HTTP/1.1[0m" 304 -
2025-07-23 13:52:01,730 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/audio-app.js?v=20250625-1145 HTTP/1.1" 200 -
2025-07-23 13:52:01,732 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/subtitle-manager.js HTTP/1.1" 200 -
2025-07-23 13:52:01,735 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/subtitle-chat.js HTTP/1.1" 200 -
2025-07-23 13:52:01,746 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/bookmark-force-test.js HTTP/1.1" 200 -
2025-07-23 13:52:01,748 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/extreme-bookmark-test.js HTTP/1.1" 200 -
2025-07-23 13:52:01,750 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/quick-bookmark-fix-test.js HTTP/1.1" 200 -
2025-07-23 13:52:01,757 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/session-recovery.js?v=1.0&t=20250628 HTTP/1.1" 200 -
2025-07-23 13:52:01,758 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/manual-fix.js?v=1.0&t=20250628 HTTP/1.1" 200 -
2025-07-23 13:52:01,760 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/timestamp-manager.js HTTP/1.1" 200 -
2025-07-23 13:52:01,770 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/podcast-rss-config-loader.js HTTP/1.1" 200 -
2025-07-23 13:52:01,772 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/podcast-manager.js HTTP/1.1" 200 -
2025-07-23 13:52:01,773 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:01] "GET /js/bookmark-manager.js HTTP/1.1" 200 -
2025-07-23 13:52:02,015 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "[36mGET /lang/zh.json HTTP/1.1[0m" 304 -
2025-07-23 13:52:02,154 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "GET /config/podcast-rss-fixes.json?v=1753249922139 HTTP/1.1" 200 -
2025-07-23 13:52:02,160 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "GET /config/default-podcasts.json?v=1753249922146 HTTP/1.1" 200 -
2025-07-23 13:52:02,353 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "[33mGET /images/podcast-covers/1721313249.jpg HTTP/1.1[0m" 404 -
2025-07-23 13:52:02,354 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "[33mGET /images/podcast-covers/647826736.jpg HTTP/1.1[0m" 404 -
2025-07-23 13:52:02,358 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "GET /images/podcast-covers/1568797958.jpg HTTP/1.1" 200 -
2025-07-23 13:52:02,362 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "GET /images/podcast-covers/1028908750.jpg HTTP/1.1" 200 -
2025-07-23 13:52:02,388 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "GET /images/podcast-covers/1528594034.jpg HTTP/1.1" 200 -
2025-07-23 13:52:02,388 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "GET /images/podcast-covers/1545953110.jpg HTTP/1.1" 200 -
2025-07-23 13:52:02,399 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "GET /images/podcast-covers/1150124817.jpg HTTP/1.1" 200 -
2025-07-23 13:52:02,400 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "GET /images/podcast-covers/1683199352.jpg HTTP/1.1" 200 -
2025-07-23 13:52:02,400 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:02] "GET /images/podcast-covers/1683199129.jpg HTTP/1.1" 200 -
2025-07-23 13:52:02,626 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:52:03,191 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:52:04,051 - INFO - 成功加载Supabase配置: URL=https://ewlpneznhlwx..., KEY类型=服务密钥
2025-07-23 13:52:04,052 - INFO - ===== 播客节目API调用 =====
2025-07-23 13:52:04,054 - INFO - feed_url: https://feeds.npr.org/510308/podcast.xml
2025-07-23 13:52:04,054 - INFO - page: 0, per_page: 3
2025-07-23 13:52:05,153 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:05] "GET /api/auth/verify HTTP/1.1" 200 -
2025-07-23 13:52:05,263 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:05] "GET /api/auxiliary-config HTTP/1.1" 200 -
2025-07-23 13:52:06,057 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:52:06,568 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:52:08,122 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:08] "GET /api/auth/verify HTTP/1.1" 200 -
2025-07-23 13:52:08,516 - INFO - RSS解析完成，总节目数: 566
2025-07-23 13:52:08,516 - INFO - 节目 1: You 2.0: The Wisdom of Stoicis... 发布日期: Mon, 21 Jul 2025 18:33:00 +0000 -> 2025-07-21 18:33:00+00:00
2025-07-23 13:52:08,518 - INFO - 节目 2: You 2.0: The Path to Contentme... 发布日期: Mon, 14 Jul 2025 19:00:00 +0000 -> 2025-07-14 19:00:00+00:00
2025-07-23 13:52:08,518 - INFO - 节目 3: You 2.0: The Passion Pill... 发布日期: Mon, 7 Jul 2025 19:00:00 +0000 -> 2025-07-07 19:00:00+00:00
2025-07-23 13:52:08,519 - INFO - 节目 4: You 2.0: What Is Your Life For... 发布日期: Mon, 30 Jun 2025 19:00:00 +0000 -> 2025-06-30 19:00:00+00:00
2025-07-23 13:52:08,520 - INFO - 节目 5: Win Hearts, Then Minds + Your ... 发布日期: Mon, 23 Jun 2025 19:00:00 +0000 -> 2025-06-23 19:00:00+00:00
2025-07-23 13:52:08,598 - INFO - 节目列表构建完成，共 566 期
2025-07-23 13:52:08,598 - INFO - 排序前前5期: ['You 2.0: The Wisdom (2025-07-21)', 'You 2.0: The Path to(2025-07-14)', 'You 2.0: The Passion(2025-07-07)', 'You 2.0: What Is You(2025-06-30)', 'Win Hearts, Then Min(2025-06-23)']
2025-07-23 13:52:08,600 - INFO - 排序后前5期: ['You 2.0: The Wisdom (2025-07-21)', 'You 2.0: The Path to(2025-07-14)', 'You 2.0: The Passion(2025-07-07)', 'You 2.0: What Is You(2025-06-30)', 'Win Hearts, Then Min(2025-06-23)']
2025-07-23 13:52:08,600 - INFO - 分页处理: page=0, per_page=3, start=0, end=3
2025-07-23 13:52:08,601 - INFO - 返回节目数: 3, 总数: 566, has_more: True
2025-07-23 13:52:08,602 - INFO - ===== API调用完成 =====
2025-07-23 13:52:08,605 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:08] "GET /podcast/episodes?feedUrl=https://feeds.npr.org/510308/podcast.xml&page=0&per_page=3 HTTP/1.1" 200 -
2025-07-23 13:52:12,616 - INFO - 127.0.0.1 - - [23/Jul/2025 13:52:12] "GET /favicon.ico HTTP/1.1" 200 -
2025-07-23 13:52:58,857 - INFO - 尝试下载播客音频 (第 1/3 次): https://dts.podtrac.com/redirect.mp3/tracking.swap.fm/track/0bDcdoop59bdTYSfajQW/stitcher.simplecastaudio.com/df179a36-a022-41e3-bf7c-b7a4efc6f51e/episodes/c89ac6e0-7a61-40eb-a9a3-5cef684509ea/audio/128/default.mp3?aid=rss_feed&awCollectionId=df179a36-a022-41e3-bf7c-b7a4efc6f51e&awEpisodeId=c89ac6e0-7a61-40eb-a9a3-5cef684509ea&feed=kwWc0lhf
2025-07-23 13:53:01,975 - INFO - 播客音频下载成功，开始流式传输
2025-07-23 13:53:01,976 - INFO - 127.0.0.1 - - [23/Jul/2025 13:53:01] "GET /podcast/proxy_download?audio_url=https://dts.podtrac.com/redirect.mp3/tracking.swap.fm/track/0bDcdoop59bdTYSfajQW/stitcher.simplecastaudio.com/df179a36-a022-41e3-bf7c-b7a4efc6f51e/episodes/c89ac6e0-7a61-40eb-a9a3-5cef684509ea/audio/128/default.mp3?aid%3Drss_feed%26awCollectionId%3Ddf179a36-a022-41e3-bf7c-b7a4efc6f51e%26awEpisodeId%3Dc89ac6e0-7a61-40eb-a9a3-5cef684509ea%26feed%3DkwWc0lhf HTTP/1.1" 200 -
2025-07-23 13:57:26,983 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:57:27,904 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:57:30,667 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:57:30,669 - INFO - [快速积分API] 快速获取用户积分请求: 用户ID=5fde16f5-d2a3-473c-b98a-bdddfdcf05fd
2025-07-23 13:57:30,669 - INFO - [快速积分API] 从缓存返回用户积分: 8738
2025-07-23 13:57:30,672 - INFO - 127.0.0.1 - - [23/Jul/2025 13:57:30] "GET /api/credits/get-fast HTTP/1.1" 200 -
2025-07-23 13:58:49,926 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:58:50,847 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:58:52,634 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:58:52,635 - INFO - [快速积分API] 快速获取用户积分请求: 用户ID=5fde16f5-d2a3-473c-b98a-bdddfdcf05fd
2025-07-23 13:58:52,638 - INFO - [快速积分API] 从缓存返回用户积分: 8738
2025-07-23 13:58:52,639 - INFO - 127.0.0.1 - - [23/Jul/2025 13:58:52] "GET /api/credits/get-fast HTTP/1.1" 200 -
2025-07-23 13:58:52,669 - INFO - 127.0.0.1 - - [23/Jul/2025 13:58:52] "[35m[1mPOST /api/analyze-local-audio-stream HTTP/1.1[0m" 501 -
2025-07-23 13:58:53,539 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:58:54,433 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:58:56,207 - INFO - 127.0.0.1 - - [23/Jul/2025 13:58:56] "GET /api/auth/verify HTTP/1.1" 200 -
2025-07-23 13:58:57,113 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:58:57,115 - INFO - [积分API] 获取用户积分请求: 用户ID=5fde16f5-d2a3-473c-b98a-bdddfdcf05fd
2025-07-23 13:58:57,116 - INFO - [积分API] 从缓存返回用户积分: 8738
2025-07-23 13:58:57,117 - INFO - 127.0.0.1 - - [23/Jul/2025 13:58:57] "GET /api/credits/get HTTP/1.1" 200 -
2025-07-23 13:59:22,079 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:59:22,907 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:59:24,046 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:59:24,047 - INFO - [快速积分API] 快速获取用户积分请求: 用户ID=5fde16f5-d2a3-473c-b98a-bdddfdcf05fd
2025-07-23 13:59:24,047 - INFO - [快速积分API] 从缓存返回用户积分: 8738
2025-07-23 13:59:24,048 - INFO - 127.0.0.1 - - [23/Jul/2025 13:59:24] "GET /api/credits/get-fast HTTP/1.1" 200 -
2025-07-23 13:59:24,077 - INFO - 127.0.0.1 - - [23/Jul/2025 13:59:24] "[35m[1mPOST /api/analyze-local-audio-stream HTTP/1.1[0m" 501 -
2025-07-23 13:59:24,984 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:59:25,869 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:59:27,613 - INFO - 127.0.0.1 - - [23/Jul/2025 13:59:27] "GET /api/auth/verify HTTP/1.1" 200 -
2025-07-23 13:59:28,463 - INFO - HTTP Request: GET https://ewlpneznhlwxuapaibiu.supabase.co/auth/v1/user "HTTP/2 200 OK"
2025-07-23 13:59:29,495 - INFO - [积分API] 获取用户积分请求: 用户ID=5fde16f5-d2a3-473c-b98a-bdddfdcf05fd
2025-07-23 13:59:29,495 - INFO - [积分API] 从缓存返回用户积分: 8738
2025-07-23 13:59:29,497 - INFO - 127.0.0.1 - - [23/Jul/2025 13:59:29] "GET /api/credits/get HTTP/1.1" 200 -
2025-07-23 14:00:00,942 - INFO - 127.0.0.1 - - [23/Jul/2025 14:00:00] "[36mGET /css/audio-styles.css HTTP/1.1[0m" 304 -
2025-07-23 14:00:00,957 - INFO - 127.0.0.1 - - [23/Jul/2025 14:00:00] "[36mGET /css/credits.css HTTP/1.1[0m" 304 -
2025-07-23 14:00:00,968 - INFO - 127.0.0.1 - - [23/Jul/2025 14:00:00] "[36mGET /css/credit-history.css HTTP/1.1[0m" 304 -
2025-07-23 14:00:00,990 - INFO - 127.0.0.1 - - [23/Jul/2025 14:00:00] "[36mGET /css/payment.css HTTP/1.1[0m" 304 -
2025-07-23 14:00:01,024 - INFO - 127.0.0.1 - - [23/Jul/2025 14:00:01] "[36mGET /css/timestamp-styles.css HTTP/1.1[0m" 304 -
2025-07-23 14:00:01,028 - INFO - 127.0.0.1 - - [23/Jul/2025 14:00:01] "[36mGET /css/grammar-styles.css HTTP/1.1[0m" 304 -
-------------------------------------------------
现在让我查看主要的app.py文件中该路由的实现：
我发现路由存在但可能实现不完整。让我查看完整的函数实现：
Tool call timed out after 10s (read file)
让我使用grep搜索来查看这个函数的结束：
让我检查音频路由模块的实现：
找到问题根源了！
在 api/audio_routes.py 第 461 行，analyze_local_audio_stream 函数直接返回了 501 错误：

现在让我检查app.py中是否有完整的实现：
让我查看app.py中这个函数周围更多的代码：
现在让我检查app.py的610行，看看analyze_local_audio_stream的结束：
现在我明白了问题！在app.py中有一个完整的 analyze_local_audio_stream 实现，但是在audio_routes.py中又有一个返回501错误的同名路由。这导致了路由冲突。
让我查看app.py是否注册了audio_routes蓝图：
让我查看 init_audio_routes 函数：
现在我明白了问题的根源：
app.py 中有一个完整的 analyze_local_audio_stream 实现（第371行）
audio_routes.py 中也有一个同名路由（第457行），但只返回501错误
音频路由蓝图在app.py中被注册，路径前缀是 /api
这导致蓝图中的路由 /api/analyze-local-audio-stream 覆盖了app.py中的同名路由
解决方案：删除audio_routes.py中返回501错误的路由实现

修改audio_router.py

@audio_bp.route('/analyze-local-audio-stream', methods=['POST'])
@create_rate_limiter("content", "audio_analysis")
# ... existing code ...
-def analyze_local_audio_stream():
-    """分析本地音频文件（不需要上传）- 暂时重定向到原有实现"""
-    try:
-        # 暂时返回错误，提示使用原有的路由
-        return jsonify({"error": "本地音频分析功能暂时不可用，请使用上传音频功能"}), 501
-        
-    except Exception as e:
-        logger.error(f"本地音频分析失败: {e}")
-        return jsonify({"error": f"本地音频分析失败: {str(e)}"}), 500
+# NOTE: 禁用重复的 /analyze-local-audio-stream 路由，避免覆盖 app.py 中的完整实现
+# def analyze_local_audio_stream_disabled():
+#     """已禁用。请使用 app.py 中的 analyze_local_audio_stream"""
+#     return jsonify({"error": "此端点已禁用，请使用 /api/analyze-local-audio-stream"}), 410

Ran tool
已完成修复：

修改逻辑：
1. `api/audio_routes.py`  
   - 注释掉重复的 `/api/analyze-local-audio-stream` 路由实现，避免覆盖 `app.py` 中的完整实现。  

最小化修改后，当前请求将被正确路由到 `app.py` 中的实现，不再返回 501，智能问答功能即可正常使用。请重新启动后端或热重载后测试随时问按钮。
