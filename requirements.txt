# Requirements file
flask==2.3.3
flask-cors==4.0.0
python-dotenv==1.0.0
requests==2.31.0
beautifulsoup4==4.12.2
lxml==4.9.3

tiktoken==0.9.0 
# mailchecker removed, using RapidAPI Mailcheck service instead
Flask-Limiter==3.5.0
supabase==1.0.3  # Supabase Python 客户端库
openai==1.63.2  # 更新到与本地环境相同的版本
python-dateutil==2.8.2  # 用于日期处理
uuid==1.30  # 用于生成UUID
cryptography==41.0.4  # 用于加密处理
markupsafe==2.1.3  # 与Flask兼容的版本
gunicorn>=20.1.0
pydub==0.25.1  # 用于音频处理
feedparser==6.0.10  # 用于解析播客RSS feed
