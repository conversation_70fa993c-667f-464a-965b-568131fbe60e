<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reset Password | AI eBook Reader</title>
  <link rel="stylesheet" href="css/log.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- 动态预加载检测到的语言文件 -->
  <script>
    // 创建一个预加载链接，只加载检测到的语言文件
    const detectedLang = localStorage.getItem('uiLanguage') || 
      (navigator.language && navigator.language.toLowerCase().startsWith('zh') ? 'zh' : 'en');
    const preloadLink = document.createElement('link');
    preloadLink.rel = 'preload';
    preloadLink.href = `lang/${detectedLang}.json`;
    preloadLink.as = 'fetch';
    preloadLink.crossOrigin = 'anonymous';
    document.head.appendChild(preloadLink);
  </script>
  
  <!-- 语言初始化脚本 - 放在头部优先检测语言 -->
  <script>
    // 从localStorage获取保存的语言设置，或从浏览器语言检测
    const storedLang = localStorage.getItem('uiLanguage');
    const browserLang = navigator.language || navigator.userLanguage;
    
    // 确定要使用的语言
    const supportedLanguages = ['zh', 'en'];
    let initialLang;
    
    // 优先使用存储的语言偏好，否则基于浏览器语言检测
    if (storedLang && supportedLanguages.includes(storedLang)) {
      initialLang = storedLang;
      console.log(`使用存储的语言偏好: ${initialLang}`);
    } else if (browserLang.toLowerCase().startsWith('zh')) {
      initialLang = 'zh';
      console.log(`使用浏览器设置的中文`);
    } else {
      initialLang = 'en';
      console.log(`使用英文作为默认语言`);
    }
    
    // 根据检测到的语言设置html lang属性
    document.documentElement.lang = initialLang === 'en' ? 'en' : 'zh-CN';
    
    // 存储检测到的语言供ui-language.js使用
    window.initialDetectedLang = initialLang;
  </script>
  
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
  <script src="/env.js"></script>
  <script src="js/ui-language.js"></script>
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background-color: #f5f7fa;
      padding: 20px;
    }
    .auth-container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 8px 30px rgba(0,0,0,0.08);
      width: 100%;
      max-width: 400px;
      padding: 30px;
    }
    .auth-header {
      margin-bottom: 20px;
      text-align: center;
    }
    .auth-header h2 {
      margin-bottom: 10px;
    }
    .input-group {
      margin-bottom: 15px;
    }
    input {
      width: 100%;
      padding: 12px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      font-size: 1rem;
    }
    button {
      width: 100%;
      padding: 12px;
      background-color: #4361ee;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 1rem;
      font-weight: 500;
      margin-top: 20px;
    }
    button:hover {
      background-color: #3a56d4;
    }
    .message {
      margin-top: 15px;
      padding: 10px;
      border-radius: 8px;
      text-align: center;
    }
    .message.success {
      background-color: #e7f7e7;
      color: #2e7d32;
    }
    .message.error {
      background-color: #feeeee;
      color: #c62828;
    }
  </style>
</head>
<body>
  <div class="auth-container">
    <div class="auth-header">
      <h2 id="reset-title" data-i18n="log.auth.reset_title">Reset Password</h2>
      <p id="reset-subtitle" data-i18n="log.auth.subtitle">Enter your new password below</p>
    </div>
    
    <form id="newPasswordForm">
      <div class="input-group">
        <input type="password" id="newPassword" data-i18n-placeholder="log.auth.password_placeholder" placeholder="New Password" required>
      </div>
      <div class="input-group">
        <input type="password" id="confirmNewPassword" data-i18n-placeholder="log.auth.confirm_password" placeholder="Confirm New Password" required>
      </div>
      <button type="submit" id="resetPasswordButton" data-i18n="log.auth.reset_button">Reset Password</button>
    </form>
    <div class="message" id="resetPasswordMessage" style="display: none;"></div>
  </div>
  
  <script>
    // 国际化文本刷新函数
    function updateI18nTexts() {
      document.getElementById('reset-title').textContent = UILanguage.getText('log.auth.reset_title', 'Reset Password');
      document.getElementById('reset-subtitle').textContent = UILanguage.getText('log.auth.subtitle', 'Enter your new password below');
      document.getElementById('newPassword').placeholder = UILanguage.getText('log.auth.password_placeholder', 'New Password');
      document.getElementById('confirmNewPassword').placeholder = UILanguage.getText('log.auth.confirm_password', 'Confirm New Password');
      document.getElementById('resetPasswordButton').textContent = UILanguage.getText('log.auth.reset_button', 'Reset Password');
    }

    // 页面加载时设置文本
    document.addEventListener('DOMContentLoaded', function() {
      updateI18nTexts();
      // 监听语言切换事件
      document.addEventListener('ui-language-changed', updateI18nTexts);

      // 初始化 Supabase 客户端 - 从动态配置获取，无硬编码
      const SUPABASE_URL = window._env_?.SUPABASE_URL || 
        (window.ENV && window.ENV.SUPABASE && window.ENV.SUPABASE.URL);
      const SUPABASE_ANON_KEY = window._env_?.SUPABASE_ANON_KEY || 
        (window.ENV && window.ENV.SUPABASE && window.ENV.SUPABASE.ANON_KEY);
        
      // 验证配置是否存在
      if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
        console.error('Supabase配置缺失，请检查.env文件');
        alert('系统配置错误，请联系管理员');
        return;
      }
      
      const supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    
      // 获取新密码表单
      const newPasswordForm = document.getElementById('newPasswordForm');
      const resetMessage = document.getElementById('resetPasswordMessage');
      const resetButton = document.getElementById('resetPasswordButton');
      
      // 显示错误消息的辅助函数
      function showErrorMessage(errorObj) {
        console.error('重置密码错误:', errorObj);
        
        // 处理常见错误类型
        let errorMessage = '';
        
        // 特殊处理相同密码错误
        if (errorObj && errorObj.message && (
            errorObj.message.includes('same password') || 
            errorObj.message.includes('new password should be different')
        )) {
          // 相同密码错误，直接使用翻译好的错误信息
          resetMessage.textContent = UILanguage.getText('log.auth.same_password_error', '新密码与旧密码相同，请设置不同的密码');
          resetMessage.className = 'message error';
          resetButton.disabled = false;
          resetButton.textContent = UILanguage.getText('log.auth.reset_button', 'Reset Password');
          return;
        }
        
        // 处理其他错误情况
        try {
          if (errorObj && typeof errorObj === 'object') {
            if (errorObj.message) {
              errorMessage = errorObj.message;
            } else if (errorObj.error_description) {
              errorMessage = errorObj.error_description;
            } else if (errorObj.error) {
              errorMessage = errorObj.error;
            } else {
              // 避免显示[object Object]
              const errorStr = JSON.stringify(errorObj);
              errorMessage = errorStr === '{}' ? 
                UILanguage.getText('log.auth.unknown_error', 'Unknown error') : 
                errorStr;
            }
          } else if (errorObj) {
            errorMessage = String(errorObj);
          } else {
            errorMessage = UILanguage.getText('log.auth.unknown_error', 'Unknown error');
          }
        } catch (e) {
          errorMessage = UILanguage.getText('log.auth.unknown_error', 'Unknown error');
          console.error('Error parsing error object:', e);
        }
        
        // 记录详细错误信息到控制台
        console.log('错误详情:', errorObj);
        console.log('处理后的错误消息:', errorMessage);
        
        // 显示翻译好的前缀 + 错误信息
        resetMessage.innerHTML = UILanguage.getText('log.auth.reset_failed_prefix', 'Password reset failed: ') 
          + '<span class="error-message">' + errorMessage + '</span>'
          + '<br><small>' + UILanguage.getText('log.auth.debug_error_info', 'Detailed error info logged to console') + '</small>';
        resetMessage.className = 'message error';
        resetButton.disabled = false;
        resetButton.textContent = UILanguage.getText('log.auth.reset_button', 'Reset Password');
      }
      
      // 从URL解析token
      function parseTokenFromUrl() {
        // 检查URL的hash部分 (#access_token=...)
        const hash = window.location.hash.substring(1);
        if (hash) {
          const params = new URLSearchParams(hash);
          const accessToken = params.get('access_token');
          if (accessToken) {
            console.log('Found token in hash fragment');
            return accessToken;
          }
        }
        
        // 检查URL查询参数 (?type=recovery&token=...)
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');
        if (token) {
          console.log('Found token in query parameters');
          return token;
        }
        
        return null;
      }
      
      // 尝试从会话中获取令牌
      async function checkSession() {
        try {
          const { data, error } = await supabaseClient.auth.getSession();
          if (error) throw error;
          if (data?.session?.access_token) {
            console.log('Found active session');
            return data.session.access_token;
          }
          return null;
        } catch (error) {
          console.error('Error checking session:', error);
          return null;
        }
      }
      
      newPasswordForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // 获取新密码
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmNewPassword').value;
        
        // 显示消息并禁用按钮
        resetMessage.style.display = 'block';
        resetMessage.textContent = UILanguage.getText('log.auth.processing', 'Processing your request...');
        resetMessage.className = 'message';
        resetButton.disabled = true;
        resetButton.textContent = UILanguage.getText('main.generating', 'Processing...');
        
        // 检查密码匹配
        if (newPassword !== confirmPassword) {
          resetMessage.textContent = UILanguage.getText('log.auth.password_mismatch', 'Passwords do not match, please re-enter');
          resetMessage.className = 'message error';
          resetButton.disabled = false;
          resetButton.textContent = UILanguage.getText('log.auth.reset_button', 'Reset Password');
          return;
        }
        
        try {
          // 先检查URL中的token
          let token = parseTokenFromUrl();
          
          // 如果URL中没有token，检查是否有活跃会话
          if (!token) {
            token = await checkSession();
          }
          
          // 如果仍然没有找到token，报错
          if (!token) {
            resetMessage.textContent = UILanguage.getText('log.auth.invalid_reset_link', 'Invalid reset link, please request a new one.');
            resetMessage.className = 'message error';
            resetButton.disabled = false;
            resetButton.textContent = UILanguage.getText('log.auth.reset_button', 'Reset Password');
            return;
          }
          
          // 尝试通过不同的方法重置密码
          let success = false;
          
          // 方法1：使用updateUser方法 (如果已经有会话)
          try {
            const { data, error } = await supabaseClient.auth.updateUser({ 
              password: newPassword 
            });
            
            if (error) throw error;
            success = true;
          } catch (err) {
            // 特别检查是否是"相同密码"错误
            if (err?.message && (
                err.message.includes('same password') || 
                err.message.includes('new password should be different')
            )) {
              showErrorMessage(err);
              return;
            }
            
            console.log('方法1失败:', err?.message || err);
            // 继续尝试其他方法
          }
          
          // 如果方法1失败，尝试方法2：显式设置会话
          if (!success) {
            // 解析URL hash中的refresh_token
            const hashParams = new URLSearchParams(window.location.hash.substring(1));
            const refreshToken = hashParams.get('refresh_token');
            console.log('Method2 tokens:', { access_token: token, refresh_token: refreshToken });
            try {
              // 确保我们有refresh_token，这是关键
              if (!refreshToken) {
                console.error('缺少refresh_token，无法建立完整会话');
                throw new Error('Missing refresh token');
              }
              
              // 使用后端API处理密码重置
              const response = await fetch('/api/auth/reset-password', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  password: newPassword,
                  access_token: token,
                  refresh_token: refreshToken
                })
              });
              
              const result = await response.json();
              console.log('后端密码重置响应:', result);
              
              if (!response.ok || !result.success) {
                throw new Error(result.message || '密码重置请求失败');
              }
              
              // 现在使用返回的令牌设置会话
              const { data: sessionData, error: setSessionError } = await supabaseClient.auth.setSession({ 
                access_token: result.tokens.access_token, 
                refresh_token: result.tokens.refresh_token 
              });
              
              console.log('setSessionData:', sessionData, 'setSessionError:', setSessionError);
              
              if (setSessionError) throw setSessionError;
              
              // 确认会话已经设置成功
              const { data: sessionCheck } = await supabaseClient.auth.getSession();
              console.log('Session check after setSession:', sessionCheck);
              
              if (!sessionCheck?.session) {
                throw new Error('Failed to establish session');
              }
              
              // 现在有了有效会话，更新密码
              const { data: updateData, error: updateError } = await supabaseClient.auth.updateUser({ 
                password: newPassword 
              });
              
              console.log('updateData:', updateData, 'updateError:', updateError);
              
              if (updateError) throw updateError;
              success = true;
            } catch (err) {
              // 特别检查是否是"相同密码"错误
              if (err?.message && (
                  err.message.includes('same password') || 
                  err.message.includes('new password should be different')
              )) {
                showErrorMessage(err);
                return;
              }
              console.log('方法2失败:', err?.message || err);
            }
          }
          
          // 如果仍然失败，显示错误信息
          if (!success) {
            resetMessage.textContent = UILanguage.getText('log.auth.reset_failed', 'Unable to reset password, please return to login and request a new reset link.');
            resetMessage.className = 'message error';
            resetButton.disabled = false;
            resetButton.textContent = UILanguage.getText('log.auth.reset_button', 'Reset Password');
            return;
          }

          // 重要：密码更新成功后，更新会话信息
          try {
            // 重新获取并更新用户会话信息
            const { data } = await supabaseClient.auth.getSession();
            if (data && data.session) {
              localStorage.setItem('authToken', data.session.access_token);
              localStorage.setItem('userData', JSON.stringify(data.session.user));
              console.log('密码重置后会话信息已更新');
            } else {
              console.log('无法获取会话信息，但密码已重置成功');
            }
          } catch (refreshError) {
            console.error('更新会话信息失败:', refreshError);
            // 即使无法更新会话，密码重置仍然成功
          }
          
          // 显示成功消息
          resetMessage.textContent = UILanguage.getText('log.auth.reset_success', 'Password reset successful! Redirecting to main page in 3 seconds...');
          resetMessage.className = 'message success';
          
          // 3秒后跳转到主页面
          setTimeout(() => {
            resetMessage.textContent = UILanguage.getText('log.auth.redirecting', 'Redirecting...');
            window.location.href = '/main.html';
          }, 3000);
          
        } catch (error) {
          // 使用统一的错误显示函数
          showErrorMessage(error);
        }
      });
    });
  </script>
</body>
</html>