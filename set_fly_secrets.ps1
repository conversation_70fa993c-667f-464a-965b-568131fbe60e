# 设置fly.io的环境变量

# 确保已登录fly.io
Write-Host "确保您已经登录到fly.io (flyctl auth login)" -ForegroundColor Yellow

# 设置生产环境变量
Write-Host "正在设置fly.io环境变量..." -ForegroundColor Green

# 基本环境配置
Write-Host "设置基本环境配置..." -ForegroundColor Cyan
flyctl secrets set ENVIRONMENT=production
flyctl secrets set APP_URL=https://reader-app.fly.dev
flyctl secrets set PORT=8080
flyctl secrets set DEBUG=false

# 支付相关配置
Write-Host "`n注意: 请手动设置敏感的API密钥:" -ForegroundColor Yellow
Write-Host "flyctl secrets set CREEM_API_KEY=creem_3Ju3rcuSjKuGE16Gb4eIlm" -ForegroundColor Magenta
Write-Host "flyctl secrets set CREEM_WEBHOOK_SECRET=creem_3Ju3rcuSjKuGE16Gb4eIlm" -ForegroundColor Magenta

Write-Host "`n环境变量设置完成。" -ForegroundColor Green
