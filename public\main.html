<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AudioPilot - AI音频学习助手</title>
    
    <!-- 网站图标 -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">

    <!-- 简化的页面状态管理 -->
    <script>
        // 页面初始化状态标志
        window.pageInitialized = false;
        
        // 监听页面卸载事件
        window.addEventListener('beforeunload', function() {
            console.log('页面即将卸载');
            window.pageInitialized = false;
        });
    </script>

    <!-- 添加资源加载策略控制 -->
    <script>
        // 强制使用本地资源
        localStorage.setItem('useLocalLibs', 'true');
        localStorage.setItem('localFirst', 'true');
        
        // 语言初始化脚本部分保持不变
        const storedLang = localStorage.getItem('uiLanguage');
        const browserLang = navigator.language || navigator.userLanguage;
        
        // 确定要使用的语言
        const supportedLanguages = ['zh', 'en'];
        let initialLang;
        
        // 优先使用存储的语言偏好，否则基于浏览器语言检测
        if (storedLang && supportedLanguages.includes(storedLang)) {
          initialLang = storedLang;
        } else if (browserLang.toLowerCase().startsWith('zh')) {
          initialLang = 'zh';
        } else {
          initialLang = 'en';
        }
        
        // 根据检测到的语言设置html lang属性
        document.documentElement.lang = initialLang === 'en' ? 'en' : 'zh-CN';
        
        // 根据检测到的语言立即设置页面标题
        if (initialLang === 'en') {
                      document.title = 'AudioPilot - AI Audio Learning Assistant';
        } else {
                      document.title = 'AudioPilot - AI音频学习助手';
        }
        
        // 存储检测到的语言供ui-language.js使用
        window.initialDetectedLang = initialLang;
        
        // 国际化处理：避免视觉闪烁
        // - 中文用户：页面使用中文作为默认，直接显示，无需隐藏
        // - 英文用户：页面先隐藏，预设英文文本后再显示，避免中英文切换闪烁
        if (initialLang === 'en') {
            document.documentElement.style.visibility = 'hidden';
            console.log('英文环境：隐藏页面等待翻译完成');
            
            // 当DOM准备好后，预设英文文本
            document.addEventListener('DOMContentLoaded', function() {
                // 预设关键元素的英文文本，避免闪烁
                const presetElements = [
                    { selector: '[data-i18n="heading"]', text: 'AI Audio Copilot' },
                    { selector: '[data-i18n="audio.menu"]', text: 'Menu' },
                    { selector: '[data-i18n="menu.credits"]', text: 'Credits' },
                    { selector: '[data-i18n="menu.credit_expiry"]', text: 'Credit Validity' },
                    { selector: '[data-i18n="menu.recharge"]', text: 'Recharge' },
                    { selector: '[data-i18n="ui.menu.settings"]', text: 'Settings' },
                    { selector: '[data-i18n="menu.logout"]', text: 'Logout' },
                    { selector: '[data-i18n="audio.audio_input"]', text: 'Audio Input' },
                    { selector: '[data-i18n="audio.upload_audio"]', text: 'Choose Audio' },
                    { selector: '[data-i18n="audio.start_recording"]', text: 'Start Recording' },
                    { selector: '[data-i18n="audio.uploaded_playback"]', text: 'Playback' },
                    { selector: '[data-i18n="audio.no_file"]', text: 'No File' },
                    { selector: '[data-i18n="audio.current_audio_playback"]', text: 'Current Audio Playback' },
                    { selector: '[data-i18n="audio.no_recording"]', text: 'No Recording' },
                    { selector: '[data-i18n="audio.smart_qa"]', text: 'Smart Q&A' },
                    { selector: '[data-i18n="audio.subtitles"]', text: 'Subtitles' },
                    { selector: '[data-i18n="audio.ask_current_position"]', text: 'Ask Anytime' },
                    { selector: '[data-i18n="audio.send"]', text: 'Send' },
                    { selector: '[data-i18n="audio.processing"]', text: 'Processing...' },
                    { selector: '[data-i18n="credits.title"]', text: 'Credits Information' },
                    { selector: '[data-i18n="credits.current"]', text: 'Current Credits' },
                    { selector: '[data-i18n="credits.view_history"]', text: 'View Usage History' },
                    { selector: '[data-i18n="credits.usage_history"]', text: 'Credits Usage History' },
                    { selector: '[data-i18n="audio.generate_timestamp"]', text: 'Generate Timestamp' },
                    { selector: '[data-i18n="audio.upload_timestamp"]', text: 'Upload Timestamp' },
                    { selector: '[data-i18n="timestamp.title"]', text: 'Time Stamps' },
                    { selector: '[data-i18n="timestamp.no_file"]', text: 'Please upload a timestamp file first' }
                ];
                
                presetElements.forEach(item => {
                    const elements = document.querySelectorAll(item.selector);
                    elements.forEach(el => {
                        el.textContent = item.text;
                    });
                });
                
                console.log('英文环境：预设关键元素文本完成');
                
                // 预设完成后，先初始化关键的菜单事件和音频应用，再显示页面
                setTimeout(() => {
                    // 提前初始化菜单交互，确保页面显示时事件已绑定
                    if (typeof initMenuInteractions === 'function') {
                        initMenuInteractions();
                        console.log('英文环境：提前初始化菜单事件');
                    }
                    
                    // 提前初始化音频应用，确保页面显示时音频控件事件已绑定
                    if (window.AudioApp && !window.audioApp) {
                        try {
                            window.audioApp = new AudioApp();
                            console.log('英文环境：提前初始化音频应用');
                        } catch (error) {
                            console.warn('英文环境：提前初始化音频应用失败，将在认证完成后重试:', error);
                        }
                    }
                    
                    if (document.documentElement.style.visibility === 'hidden') {
                        document.documentElement.style.visibility = 'visible';
                        console.log('英文环境：预设完成，显示页面');
                    }
                }, 50);
            });
        }
        
        // 超时保护：无论如何都要在1秒内显示页面
        setTimeout(() => {
            if (document.documentElement.style.visibility === 'hidden') {
                document.documentElement.style.visibility = 'visible';
                console.log('超时保护：强制显示页面');
            }
        }, 1000);
        
        // 仅在英文环境添加临时事件处理器，防止音频按钮在AudioApp未就绪时无响应
        if (initialLang === 'en') {
            document.addEventListener('DOMContentLoaded', function() {
                console.log('英文环境：确保音频应用早期可用');
                
                // 仅进行必要的预初始化，不添加临时事件处理器
                // 统一由后续的音频应用初始化流程处理
            });
        }
        
        // 资源加载失败处理函数
        window.handleResourceError = function(scriptElement, resourceName, localPath) {
            console.warn(`${resourceName} 加载失败，使用本地版本: ${localPath}`);
            scriptElement.src = localPath;
            return false;
        };
        
        // 初始化菜单交互 - 提前定义以支持英文环境的提前初始化
        function initMenuInteractions() {
            const userMenuBtn = document.getElementById('userMenuBtn');
            const userDropdown = document.getElementById('userDropdown');
            const creditsLink = document.getElementById('creditsLink');
            const rechargeLink = document.getElementById('rechargeLink');
            const settingsLink = document.getElementById('settingsLink');
            const logoutLink = document.getElementById('logoutLink');
            
            // 标记已初始化，避免重复初始化
            if (userMenuBtn) {
                userMenuBtn.setAttribute('data-menu-initialized', 'true');
            }

            // 积分链接点击事件
            if (creditsLink) {
                creditsLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 隐藏菜单
                    if (userDropdown) {
                        userDropdown.style.opacity = '0';
                        userDropdown.style.visibility = 'hidden';
                        userDropdown.style.transform = 'translateY(-10px)';
                    }
                    
                    // 显示积分使用记录
                    if (window.CreditHistoryManager && typeof window.CreditHistoryManager.showModal === 'function') {
                        window.CreditHistoryManager.showModal();
                    } else {
                        console.log('积分使用记录功能暂未初始化，请稍后再试');
                        alert('积分使用记录功能正在加载中，请稍后再试');
                    }
                });
            }

            // 积分有效期链接点击事件
            const creditExpiryLink = document.getElementById('creditExpiryLink');
            if (creditExpiryLink) {
                creditExpiryLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 隐藏菜单
                    if (userDropdown) {
                        userDropdown.style.opacity = '0';
                        userDropdown.style.visibility = 'hidden';
                        userDropdown.style.transform = 'translateY(-10px)';
                    }
                    
                    // 显示积分批次详情
                    if (window.CreditBatchManager && typeof window.CreditBatchManager.showModal === 'function') {
                        window.CreditBatchManager.showModal();
                    } else {
                        console.log('积分有效期功能暂未初始化，请稍后再试');
                        alert('积分有效期功能正在加载中，请稍后再试');
                    }
                });
            }

            // 充值链接点击事件
            if (rechargeLink) {
                rechargeLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 隐藏菜单
                    if (userDropdown) {
                        userDropdown.style.opacity = '0';
                        userDropdown.style.visibility = 'hidden';
                        userDropdown.style.transform = 'translateY(-10px)';
                    }
                    
                    // 显示支付模态框
                    if (window.PaymentPackages && typeof window.PaymentPackages.showModal === 'function') {
                        window.PaymentPackages.showModal();
                    } else {
                        console.warn('支付包管理器未初始化');
                    }
                });
            }

            // 设置链接点击事件
            if (settingsLink) {
                settingsLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 隐藏菜单
                    if (userDropdown) {
                        userDropdown.style.opacity = '0';
                        userDropdown.style.visibility = 'hidden';
                        userDropdown.style.transform = 'translateY(-10px)';
                    }
                    
                    // 显示设置模态框
                    showSettingsModal();
                });
            }

            // 退出登录链接点击事件
            if (logoutLink) {
                logoutLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 隐藏菜单
                    if (userDropdown) {
                        userDropdown.style.opacity = '0';
                        userDropdown.style.visibility = 'hidden';
                        userDropdown.style.transform = 'translateY(-10px)';
                    }
                    
                    // 触发退出登录
                    if (window.logout && typeof window.logout === 'function') {
                        window.logout();
                    } else {
                        // 直接跳转到首页
                        window.location.href = '/';
                    }
                });
            }
            
            // 点击页面其他地方隐藏菜单 - 增强用户体验
            document.addEventListener('click', function(e) {
                // 检查菜单是否可见
                const isMenuVisible = userDropdown && userDropdown.style.opacity === '1' && userDropdown.style.visibility === 'visible';
                
                if (isMenuVisible) {
                    // 检查点击的是否是菜单按钮或菜单内容
                    const currentMenuBtn = document.getElementById('userMenuBtn');
                    const isClickOnMenu = currentMenuBtn && (currentMenuBtn.contains(e.target) || userDropdown.contains(e.target));
                    
                    if (!isClickOnMenu) {
                        // 点击了菜单外的区域，隐藏菜单
                        userDropdown.style.opacity = '0';
                        userDropdown.style.visibility = 'hidden';
                        userDropdown.style.transform = 'translateY(-10px)';
                        console.log('点击页面其他区域，关闭菜单');
                    }
                }
            });
            
            console.log('菜单交互初始化完成');
            
            // 强制更新积分显示
            if (typeof forceUpdateCreditsDisplay === 'function') {
                forceUpdateCreditsDisplay();
            }
        }
    </script>

    <!-- 预加载支付服务域名，提高DNS解析速度 -->
    <link rel="preconnect" href="https://creem.io" crossorigin>
    <link rel="dns-prefetch" href="https://creem.io">
    <link rel="preconnect" href="https://test-api.creem.io" crossorigin>
    <link rel="dns-prefetch" href="https://test-api.creem.io">

    <!-- 直接使用本地资源替换外部依赖库 -->
    <script src="js/lib/marked.min.js"></script>
    <script src="js/lib/mermaid.min.js"></script>
    <script src="js/lib/d3.v7.min.js"></script>

    <!-- 引入resource-loader以提供降级功能 -->
    <script src="js/resource-loader.js"></script>

    <!-- 样式文件 -->
    <link rel="stylesheet" href="css/audio-styles.css">
    <link rel="stylesheet" href="css/credits.css">
    <link rel="stylesheet" href="css/credit-history.css">
    <link rel="stylesheet" href="css/payment.css">
    <!-- 添加时间戳样式 -->
    <link rel="stylesheet" href="css/timestamp-styles.css">
    <!-- 语法分析样式 -->
    <link rel="stylesheet" href="css/grammar-styles.css">
    <!-- 字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- 强制覆盖播客按钮样式 -->
    <style>
        /* 强制移除所有播客按钮的点击高亮和边框 */
        .episode-rewind-btn,
        .episode-forward-btn,
        .episode-play-btn,
        .episode-download-btn {
            outline: none !important;
            -webkit-tap-highlight-color: transparent !important;
            -webkit-touch-callout: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
            user-select: none !important;
            border: none !important;
            box-shadow: none !important;
            transform: none !important;
        }
        
        /* 强制移除hover和focus状态的样式 */
        .episode-rewind-btn:focus,
        .episode-forward-btn:focus,
        .episode-play-btn:focus,
        .episode-download-btn:focus,
        .episode-rewind-btn:active,
        .episode-forward-btn:active,
        .episode-play-btn:active,
        .episode-download-btn:active {
            outline: none !important;
            border: none !important;
            box-shadow: none !important;
            -webkit-tap-highlight-color: transparent !important;
        }
        
        /* 确保播客滚动条样式生效 - 使用ID选择器 */
        #podcastEpisodesList::-webkit-scrollbar {
            width: 5px !important;
        }
        
        #podcastEpisodesList::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2) !important;
            border-radius: 2px !important;
        }
    </style>

    
    <!-- 引入UI语言系统 -->
    <script src="js/ui-language.js"></script>
    
    <!-- 确保UI语言系统初始化 -->
    <script>
        // 强制翻译函数 - 添加防递归保护
        function forceTranslateAll() {
            if (window.UILanguage && !isProcessingLanguageEvent) {
                console.log('强制执行页面翻译');
                isProcessingLanguageEvent = true;
                window.UILanguage.translatePage();
                // 释放锁定标志
                setTimeout(() => {
                    isProcessingLanguageEvent = false;
                }, 50);
                
                // 检查特定的关键元素是否已翻译
                const uploadSpans = document.querySelectorAll('[data-i18n="audio.upload_audio"]');
                const aiTabs = document.querySelectorAll('[data-i18n^="audio."]');
                
                console.log(`找到 ${uploadSpans.length} 个上传按钮, ${aiTabs.length} 个AI标签`);
                
                // 如果元素仍显示中文，手动翻译
                uploadSpans.forEach(span => {
                    if ((span.textContent === '上传音频文件' || span.textContent === '上传') && window.UILanguage.getCurrentLanguage() === 'en') {
                        span.textContent = 'Upload Audio File';
                        console.log('手动翻译上传按钮:', span.textContent);
                    }
                });
                
                // 增强的AI标签翻译逻辑
                if (window.UILanguage.getCurrentLanguage() === 'en') {
                    // 直接通过ID查找AI标签按钮
                    const summaryBtn = document.getElementById('summaryTabBtn');
                    const mindmapBtn = document.getElementById('mindmapTabBtn');
                    const knowledgeBtn = document.getElementById('knowledgegraphTabBtn');
                    
                    if (summaryBtn && (summaryBtn.textContent === 'AI音频分析' || summaryBtn.textContent.includes('音频分析'))) {
                        summaryBtn.textContent = 'AI Audio Analysis';
                        console.log('手动翻译AI音频分析按钮');
                    }
                    
                    if (mindmapBtn && (mindmapBtn.textContent === 'AI脑图' || mindmapBtn.textContent.includes('脑图'))) {
                        mindmapBtn.textContent = 'AI Brain Map';
                        console.log('手动翻译AI脑图按钮');
                    }
                    
                    if (knowledgeBtn && (knowledgeBtn.textContent === 'AI知识图谱' || knowledgeBtn.textContent.includes('知识图谱'))) {
                        knowledgeBtn.textContent = 'AI Knowledge Graph';
                        console.log('手动翻译AI知识图谱按钮');
                    }
                }
                
                // 通用的data-i18n翻译作为备用
                aiTabs.forEach(tab => {
                    const key = tab.getAttribute('data-i18n');
                    if (window.UILanguage.getCurrentLanguage() === 'en') {
                        if (key === 'audio.ai_analysis' && (tab.textContent === 'AI音频分析' || tab.textContent.includes('音频分析'))) {
                            tab.textContent = 'AI Audio Analysis';
                            console.log('备用翻译AI音频分析标签');
                        } else if (key === 'audio.smart_qa' && (tab.textContent === '智能问答' || tab.textContent.includes('智能问答'))) {
                            tab.textContent = 'Smart Q&A';
                            console.log('备用翻译智能问答标签');
                        }
                    }
                });
                
                // 强制翻译输入框placeholder
                const questionInput = document.getElementById('questionInput');
                if (questionInput && window.UILanguage.getCurrentLanguage() === 'en') {
                    const placeholderKey = questionInput.getAttribute('data-i18n-placeholder');
                    if (placeholderKey && window.UILanguage.getText) {
                        const translation = window.UILanguage.getText(placeholderKey);
                        if (translation && translation !== placeholderKey) {
                            questionInput.setAttribute('placeholder', translation);
                            console.log('手动翻译输入框placeholder:', translation);
                        }
                    }
                }
                
                // 翻译完成后显示页面（解决闪烁问题）
                if (window.UILanguage.getCurrentLanguage() === 'en' && 
                    document.documentElement.style.visibility === 'hidden') {
                    document.documentElement.style.visibility = 'visible';
                    console.log('英文翻译完成，显示页面');
                }
            }
        }
        
        // 在页面完全加载后确保翻译执行
        window.addEventListener('load', function() {
            if (!isProcessingLanguageEvent) {
                setTimeout(() => {
                    if (!isProcessingLanguageEvent) {
                        forceTranslateAll();
                    }
                }, 50);
            }
        });
        
        // 防止无限递归的标志
        let isProcessingLanguageEvent = false;
        
        // 监听UI语言加载事件
        document.addEventListener('ui-language-loaded', function() {
            if (!isProcessingLanguageEvent) {
                isProcessingLanguageEvent = true;
                
                // 确保页面在语言加载后立即显示
                if (document.documentElement.style.visibility === 'hidden') {
                    document.documentElement.style.visibility = 'visible';
                    console.log('UI语言加载完成，显示页面');
                }
                
                // 更新上传按钮的data-after-text属性
                const uploadButtons = document.querySelectorAll('.mobile-upload-btn');
                uploadButtons.forEach(btn => {
                    btn.setAttribute('data-after-text', window.UILanguage.getCurrentLanguage() === 'en' ? 'Upload' : '上传');
                });
                
                // 释放标志
                setTimeout(() => {
                    isProcessingLanguageEvent = false;
                }, 50);
            }
        });
        
        // 监听UI语言变化事件 - 修复无限递归问题
        document.addEventListener('ui-language-changed', function() {
            if (!isProcessingLanguageEvent) {
                isProcessingLanguageEvent = true;
                
                // 更新上传按钮的data-after-text属性
                const uploadButtons = document.querySelectorAll('.mobile-upload-btn');
                uploadButtons.forEach(btn => {
                    btn.setAttribute('data-after-text', window.UILanguage.getCurrentLanguage() === 'en' ? 'Upload' : '上传');
                });
                
                // 释放标志
                setTimeout(() => {
                    isProcessingLanguageEvent = false;
                }, 50);
            }
        });
        
        // 在页面可见性变化时重新翻译（用于处理移动端切换等情况）
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden && !isProcessingLanguageEvent) {
                // 使用延迟避免重复触发
                setTimeout(() => {
                    if (!isProcessingLanguageEvent) {
                        forceTranslateAll();
                    }
                }, 100);
            }
        });
        
        // 监听DOM变化，如果关键元素被重新设置，立即翻译
        let domMutationPending = false;
        let lastTranslationTime = 0; // 添加翻译时间记录，减少日志频率
        const globalObserver = new MutationObserver(function(mutations) {
            // 如果已经有一个待处理的翻译操作，则跳过
            if (domMutationPending || isProcessingLanguageEvent) return;
            
            let shouldTranslate = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const target = mutation.target;
                    // 监听AI标签按钮变化
                    if (target.id === 'summaryTabBtn' || target.id === 'mindmapTabBtn' || 
                        target.id === 'knowledgegraphTabBtn' || 
                        (target.classList && target.classList.contains('ai-tab-button'))) {
                        shouldTranslate = true;
                    }
                    // 监听上传按钮变化（包括移动端）
                    if (target.classList && (target.classList.contains('mobile-upload-btn') || 
                        target.classList.contains('upload-label'))) {
                        shouldTranslate = true;
                    }
                    // 监听data-i18n元素变化
                    if (target.getAttribute && target.getAttribute('data-i18n')) {
                        shouldTranslate = true;
                    }
                }
            });
            
            if (shouldTranslate && window.UILanguage && window.UILanguage.getCurrentLanguage() === 'en') {
                // 减少日志频率，只在首次或间隔较长时输出
                if (!domMutationPending && Date.now() - lastTranslationTime > 5000) {
                    console.log('执行页面翻译');
                    lastTranslationTime = Date.now();
                }
                domMutationPending = true;
                
                // 使用延迟来防止短时间内重复执行
                setTimeout(() => {
                    if (window.UILanguage && !isProcessingLanguageEvent) {
                        isProcessingLanguageEvent = true;
                        window.UILanguage.translatePage();
                        setTimeout(() => {
                            isProcessingLanguageEvent = false;
                        }, 50);
                    }
                    domMutationPending = false;
                }, 100);
            }
        });
        
        // 启动观察器，监听整个页面的关键变化
        setTimeout(() => {
            // 监听整个body，确保捕获所有元素变化
            const body = document.body;
            if (body) {
                globalObserver.observe(body, {
                    childList: true,
                    subtree: true,
                    characterData: true,
                    attributes: true,
                    attributeFilter: ['data-i18n']
                });
                console.log('全局DOM变化监听器已启动');
            }
        }, 100);
        

    </script>
    
    <!-- 引入AI语言设置系统 -->
    <script src="js/language-settings.js"></script>
    
    <!-- API费用显示样式 -->
    <style>
        /* 增加粘贴内容的特定样式 */
        .pasted-content {
            max-width: 100%;
            width: 100%;
            word-break: break-word;
            overflow-wrap: break-word;
            white-space: pre-wrap;
            overflow: auto;
            max-height: calc(99vh - 165px); /* 调整高度以显示更多内容 */
            font-size: 14px;
            line-height: 1.5;
            padding: 12px; /* 增加内边距，四边都有 */
            margin: 5px auto 24px auto; /* 增加下方边距到24px */
            border-radius: 6px;
            background-color: #fafafa;
            border: 1px solid #eee;
            position: relative; /* 为内部元素提供定位上下文 */
            top: 0; /* 确保容器从顶部开始 */
            box-sizing: border-box; /* 确保内边距不会增加容器宽度 */
            display: block; /* 确保它是块级元素 */
        }
        
        /* 在容器后添加额外底部间距 */
        .pasted-content::after {
            content: "";
            display: block;
            height: 16px; /* 额外底部空间 */
            width: 100%;
            clear: both;
        }
        
        /* 粘贴内容中的段落样式 */
        .pasted-content p {
            margin: 0 0 0.4em 0;
            padding: 0;
        }
        
        /* 空行样式 */
        .pasted-content p.empty-line {
            margin-bottom: 0.2em;
            height: 0.5em;
        }
        

        .api-cost-info {
            margin-top: 15px;
            padding: 10px;
            background-color: #f5f5f5;
            border-left: 3px solid #4caf50;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
        .api-cost-info h3 {
            margin-top: 0;
            margin-bottom: 8px;
            font-size: 14px;
            color: #4caf50;
        }
        .api-cost-info ul {
            margin: 0;
            padding-left: 20px;
        }        

        /* 通用布局修复 */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        /* 主容器布局 - 增强版 */
        .container {
            display: flex;
            height: 100vh;
            width: calc(100% - 20px);
            margin: 10px;
            gap: 13px;
            box-sizing: border-box;
            overflow: hidden; /* 防止内容溢出导致页面出现滚动条 */
            position: relative; /* 为绝对定位提供参考 */
            min-height: 100vh; /* 确保容器至少填满整个视口 */
            flex-direction: column;
        }
        
        /* 列通用样式 - 增强版 */
        .column {
            box-sizing: border-box;
            padding: 15px;
            overflow-y: auto;
            overflow-x: hidden; /* 防止水平溢出 */
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-height: 100%; /* 确保不超过父容器高度 */
            position: relative; /* 为内部元素提供定位上下文 */
            contain: layout; /* 限制布局影响范围 */
            isolation: isolate; /* 创建新的层叠上下文 */
        }
        
        /* 列宽度分配 - 增强版 */
        #chapters {
            width: 20%;
            flex: 0 0 20%;
            min-width: 0; /* 防止内容撑开容器 */
            max-width: 20%; /* 强制最大宽度 */
        }
        
        #content {
            width: 45%;
            flex: 0 0 45%;
            min-width: 0; /* 防止内容撑开容器 */
            max-width: 45%; /* 强制最大宽度 */
            word-wrap: break-word; /* 确保长单词换行 */
            overflow-wrap: break-word; /* 现代浏览器支持 */
            hyphens: auto; /* 允许单词断字符 */
        }
        
        #aiContent {
            width: 35%;
            flex: 0 0 35%;
            min-width: 0; /* 防止内容撑开容器 */
            max-width: 35%; /* 强制最大宽度 */
            display: flex;
            flex-direction: column;
        }

        /* 加载覆盖层样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 9998;
        }
        

        
        /* 模态对话框样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .modal-content {
            background-color: #fff;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }
        
        .modal-header h3 {
            margin: 0;
            color: #2196F3;
        }
        
        .modal-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            padding: 15px 20px;
            border-top: 1px solid #eee;
            gap: 10px;
        }
        
        .close-button {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .close-button:hover {
            color: #333;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group input,
        .input-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .input-group textarea {
            min-height: 200px;
            resize: vertical;
        }
        
        .confirm-button {
            padding: 8px 16px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .confirm-button:hover {
            background-color: #0b7dda;
        }
        
        .cancel-button {
            padding: 8px 16px;
            background-color: #f1f1f1;
            color: #333;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .cancel-button:hover {
            background-color: #e5e5e5;
        }

        /* AI知识图谱样式 */
        .knowledgegraph-container {
            width: 100%;
            height: 400px;
            overflow: hidden;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            margin-bottom: 15px;
            position: relative;
        }
        
        .knowledge-graph-svg {
            width: 100%;
            height: 100%;
        }
        
        .graph-node {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .graph-node:hover {
            filter: brightness(1.1);
        }
        
        .graph-edge {
            stroke-opacity: 0.6;
            transition: stroke-opacity 0.2s ease;
        }
        
        .graph-edge:hover {
            stroke-opacity: 1;
        }
        
        .graph-label {
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 12px;
            pointer-events: none;
            user-select: none;
        }
        
        .relationship-label {
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 10px;
            fill: #555;
            pointer-events: none;
            user-select: none;
        }
        
        .graph-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            flex-direction: column;
            gap: 5px;
            z-index: 100;
            background: rgba(255,255,255,0.8);
            padding: 5px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        
        .graph-control-button {
            padding: 5px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: pointer;
            font-size: 16px;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .graph-control-button:hover {
            background-color: #f0f0f0;
        }
        
        .graph-tooltip {
            position: absolute;
            background-color: white;
            padding: 5px 10px;
            border-radius: 4px;
            box-shadow: 0 1px 5px rgba(0,0,0,0.2);
            pointer-events: none;
            z-index: 1000;
            font-size: 12px;
            max-width: 200px;
            visibility: hidden;
        }
        
        /* 设置模态框样式 */
        .settings-group {
            margin-bottom: 20px;
        }
        
        .settings-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        
        .slider-container {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }
        
        .slider-container input[type="range"] {
            flex: 1;
            height: 6px;
            background: #ddd;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }
        
        .slider-container input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            background: #2196F3;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .slider-container input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            background: #2196F3;
            border-radius: 50%;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .slider-value {
            display: flex;
            align-items: center;
            gap: 2px;
            font-weight: 600;
            color: #2196F3;
            min-width: 40px;
        }
        
        .setting-description {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        
        /* 设置模态框标题栏高度调整 */
        #settingsModal .modal-header {
            padding: 15px 20px;
        }
        
        #settingsModal .modal-header h3 {
            margin: 0;
            font-size: 18px;
        }
        
        /* HTML内联样式已移除，统一由外部CSS文件管理滚动条样式 */

        /* 积分加载动画样式 */
        .credits-loading {
            opacity: 0.6;
            animation: creditsLoading 1.5s ease-in-out infinite alternate;
        }
        
        @keyframes creditsLoading {
            0% { opacity: 0.4; }
            100% { opacity: 0.8; }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 头部 - 横贯左右 -->
        <header class="header">
            <h1 style="font-size: 28px;"><i class="fas fa-headphones"></i> <span data-i18n="heading">AI Audio Copilot</span></h1>
            <!-- 用户菜单 -->
            <div class="user-menu">
                        <button class="menu-btn" id="userMenuBtn">
                            <i class="fas fa-user-circle"></i>
                            <span data-i18n="audio.menu">菜单</span>
                        </button>
                        <div class="dropdown-menu" id="userDropdown">
                            <a href="#" id="creditsLink">
                                <i class="fas fa-coins"></i>
                                <span data-i18n="menu.credits">积分</span>
                                <span id="userCredits" class="credits-amount"></span>
                            </a>
                            <a href="#" id="creditExpiryLink">
                                <i class="fas fa-calendar-alt"></i>
                                <span data-i18n="menu.credit_expiry">积分有效期</span>
                            </a>
                            <a href="#" id="rechargeLink">
                                <i class="fas fa-credit-card"></i>
                                <span data-i18n="menu.recharge">充值</span>
                            </a>
                            <a href="#" id="settingsLink">
                                <i class="fas fa-cog"></i>
                                <span data-i18n="ui.menu.settings">设置</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="/" id="logoutLink">
                                <i class="fas fa-sign-out-alt"></i>
                                <span data-i18n="menu.logout">退出</span>
                            </a>
                        </div>
                    </div>
                </header>

        <!-- 主体内容区域 -->
        <div class="main-wrapper">
            <!-- 左侧边栏 -->
            <aside class="sidebar" id="sidebar">
                <!-- Tab 切换导航 -->
                <div class="sidebar-tabs">
                    <div class="tab-nav">
                        <button class="tab-btn active" data-tab="podcast" style="font-size: 0.75rem;">
                            <i class="fas fa-podcast"></i>
                            <span data-i18n="sidebar.podcast">播客</span>
                        </button>
                        <button class="tab-btn" data-tab="timestamp" style="font-size: 0.75rem;">
                            <i class="fas fa-clock"></i>
                            <span data-i18n="sidebar.timestamp">时间戳</span>
                        </button>
                    </div>
                </div>

                <!-- 播客内容 -->
                <div class="tab-content" id="podcastTab">
                    <!-- 播客搜索区域 -->
                    <div class="podcast-search-section">
                        <div class="search-box">
                            <input type="text" id="podcastSearchBox" placeholder="搜索播客节目..." data-i18n-placeholder="podcast.search_placeholder">
                            <button id="podcastSearchBtn" class="search-btn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="search-loading hidden" id="podcastSearchLoading">
                            <div class="loading-spinner"></div>
                            <span data-i18n="podcast.searching">搜索中...</span>
                        </div>
                    </div>

                    <!-- 收藏的播客列表 -->
                    <div class="podcast-favorites">
                        <h4><i class="fas fa-heart"></i> <span data-i18n="podcast.favorites">收藏的播客</span></h4>
                        <div class="podcast-favorites-list" id="podcastFavoritesList">
                            <div class="no-favorites" data-i18n="podcast.no_favorites">暂无收藏的播客</div>
                        </div>
                    </div>

                    <!-- 播客节目列表 -->
                    <div class="podcast-episodes">
                        <h4><i class="fas fa-list"></i> <span data-i18n="podcast.episodes">播客节目</span></h4>
                        <!-- 节目列表容器 -->
                        <div id="podcastEpisodesList" class="episodes-list-container">
                            <!-- 初始状态提示 -->
                            <div class="no-episodes" data-i18n="podcast.select_podcast">请选择播客查看节目列表</div>
                        </div>
                        <!-- "加载更多"按钮的独立容器 -->
                        <div id="loadMoreContainer" class="load-more-container"></div>
                    </div>
                </div>

                <!-- 时间戳内容 -->
                <div class="tab-content hidden" id="timestampTab">
                <!-- 时间戳按钮组 -->
                <div class="timestamp-upload-section">
                    <input type="file" id="timestampFileInput" accept=".txt" style="display: none;">
                    <div class="timestamp-buttons">
                        <button id="generateTimestampBtn" class="btn btn-primary timestamp-btn" disabled>
                            <span data-i18n="audio.generate_timestamp">生成时间戳</span>
                        </button>
                        <button id="uploadTimestampBtn" class="btn btn-secondary timestamp-btn" disabled>
                            <span data-i18n="audio.upload_timestamp">上传时间戳</span>
                        </button>
                    </div>
                </div>
                
                <!-- 时间戳导航 -->
                <div class="timestamp-section">
                    <h4><i class="fas fa-clock"></i> <span data-i18n="timestamp.title">Time Stamps</span></h4>
                    <div class="timestamp-list" id="timestampList">
                        <div class="no-timestamps" data-i18n="timestamp.no_file">Please upload a timestamp file first</div>
                    </div>
                </div>
                
                <!-- 章节摘要 -->
                <div class="chapter-content">
                    <h4><i class="fas fa-book"></i> <span data-i18n="chapter.content_title">Chapter Summary</span></h4>
                    <div class="chapter-text" id="chapterText">
                        <div class="no-chapter" data-i18n="chapter.select_timestamp">Please select a timestamp to view the corresponding chapter content</div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 右侧主要内容区域 -->
            <div class="container">

                <!-- 主要功能区域 -->
                <main class="main-content">
                    <!-- 拖拽提示 -->
                    <div id="dropHint" class="drop-hint hidden">
                        <div class="drop-hint-content">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>拖拽音频文件或书签文件到此处</p>
                            <small>支持同时拖拽音频文件和对应的书签文件</small>
                        </div>
                    </div>
                    
                    <!-- 音频上传和录制区域 -->
                    <section class="audio-upload-section">
                        <div class="card">
                            <h2><i class="fas fa-upload"></i> <span data-i18n="audio.audio_input">音频输入</span></h2>
                                                         <div class="upload-controls">
                                 <input type="file" id="audioFileInput" accept=".mp3,.wav,.mp4" style="display: none;">
                                 
                                 <button id="uploadBtn" class="btn btn-primary">
                                     <i class="fas fa-file-upload"></i> <span data-i18n="audio.upload_audio">选择音频</span>
                                 </button>
                                 
                                 <button id="recordBtn" class="btn btn-secondary">
                                     <i class="fas fa-microphone"></i> <span data-i18n="audio.start_recording">开始录音</span>
                                 </button>
                             </div>
                            <div id="recordingStatus" class="recording-status hidden">
                                <div class="recording-indicator">
                                    <i class="fas fa-circle recording-dot"></i>
                                    <span id="recordingDuration">00:00</span>
                                </div>
                            </div>
                            <!-- 文件上传进度条 -->
                            <div id="uploadProgress" class="upload-progress hidden" style="display: none;">
                                <div class="upload-progress-label">
                                    <span data-i18n="audio.uploading">上传中</span>
                                    <span id="uploadProgressPercent">0%</span>
                                </div>
                                <div class="upload-progress-bar">
                                    <div id="uploadProgressFill" class="upload-progress-fill"></div>
                                </div>
                            </div>
                            
                            <!-- 简化的上传状态提示（备用方案） -->
                            <div id="uploadStatus" class="upload-status hidden" style="display: none;">
                                <div class="upload-status-content">
                                    <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                    <span id="uploadStatusText" data-i18n="audio.uploading">上传中</span>
                                    <div class="upload-loading-dots">
                                        <div class="dot"></div>
                                        <div class="dot"></div>
                                        <div class="dot"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 上传文件播放器 -->
                    <section class="audio-player-section">
                        <div class="card">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h2 style="margin-bottom: 0;"><i class="fas fa-file-audio"></i> <span data-i18n="audio.uploaded_playback">播放</span></h2>
                                
                                <!-- 播放速度控制器 -->
                                <div class="speed-selector">
                                    <div class="speed-display" id="uploadedSpeedDisplay"><span data-i18n="audio.speed">Speed</span>: 1.0x</div>
                                    <div class="speed-options" id="uploadedSpeedOptions">
                                        <div class="speed-option" data-speed="0.5">0.5x</div>
                                        <div class="speed-option" data-speed="0.6">0.6x</div>
                                        <div class="speed-option" data-speed="0.7">0.7x</div>
                                        <div class="speed-option" data-speed="0.8">0.8x</div>
                                        <div class="speed-option" data-speed="0.9">0.9x</div>
                                        <div class="speed-option active" data-speed="1.0">1.0x</div>
                                        <div class="speed-option" data-speed="1.1">1.1x</div>
                                        <div class="speed-option" data-speed="1.2">1.2x</div>
                                        <div class="speed-option" data-speed="1.3">1.3x</div>
                                        <div class="speed-option" data-speed="1.4">1.4x</div>
                                        <div class="speed-option" data-speed="1.5">1.5x</div>
                                        <div class="speed-option" data-speed="1.6">1.6x</div>
                                        <div class="speed-option" data-speed="1.7">1.7x</div>
                                        <div class="speed-option" data-speed="1.8">1.8x</div>
                                        <div class="speed-option" data-speed="1.9">1.9x</div>
                                        <div class="speed-option" data-speed="2.0">2.0x</div>
                                        <div class="speed-option" data-speed="2.1">2.1x</div>
                                        <div class="speed-option" data-speed="2.2">2.2x</div>
                                        <div class="speed-option" data-speed="2.3">2.3x</div>
                                        <div class="speed-option" data-speed="2.4">2.4x</div>
                                        <div class="speed-option" data-speed="2.5">2.5x</div>
                                        <div class="speed-option" data-speed="2.6">2.6x</div>
                                        <div class="speed-option" data-speed="2.7">2.7x</div>
                                        <div class="speed-option" data-speed="2.8">2.8x</div>
                                        <div class="speed-option" data-speed="2.9">2.9x</div>
                                        <div class="speed-option" data-speed="3.0">3.0x</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div id="uploadedPlayerContainer" class="player-container hidden">
                                <div class="audio-info">
                                    <span id="uploadedFileName" data-i18n="audio.no_file">无文件</span>
                                    <span id="uploadedDuration">00:00</span>
                            </div>
                                <audio id="uploadedPlayer" controls preload="metadata"></audio>
                                <div class="player-controls">
                                    <button id="uploadedRewindBtn" class="control-btn">
                                        <i class="fas fa-backward"></i> -5s
                            </button>
                                    <button id="uploadedPlayPauseBtn" class="control-btn play-btn">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button id="uploadedFastForwardBtn" class="control-btn">
                                        <i class="fas fa-forward"></i> +10s
                            </button>
                                    <button id="uploadedBookmarkBtn" class="control-btn bookmark-btn" title="添加书签">
                                        <i class="fas fa-bookmark"></i>
                                    </button>
                                    <button id="uploadedBookmarkPlayBtn" class="control-btn bookmark-play-btn" title="播放书签" disabled>
                                        <i class="fas fa-play-circle"></i>
                                    </button>
                                    <button id="uploadedLoadBookmarkBtn" class="control-btn load-bookmark-btn" data-i18n-title="audio.load_bookmark">
                                        <i class="fas fa-folder-open"></i>
                                    </button>
                                    <button id="uploadedExportBookmarkBtn" class="control-btn export-bookmark-btn" title="导出书签">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <input type="file" id="uploadedBookmarkFileInput" accept=".txt" style="display: none;">
                            </div>
                                <div class="progress-container">
                                    <span id="uploadedCurrentTime">00:00</span>
                                    <div class="progress-bar" id="uploadedProgressBar">
                                        <div id="uploadedProgressFill" class="progress-fill"></div>
                                        <div id="uploadedProgressHandle" class="progress-handle"></div>
                            </div>
                                    <span id="uploadedTotalTime">00:00</span>
                        </div>
                        </div>
                    </section>

                    <!-- 录音文件播放器 -->
                    <section class="recording-player-section">
                        <div class="card">
                            <h2><i class="fas fa-microphone"></i> <span data-i18n="audio.current_audio_playback">当前音频播放</span></h2>
                            <div id="recordingPlayerContainer" class="player-container hidden">
                                <div class="audio-info">
                                    <span id="recordingFileName" data-i18n="audio.no_recording">无录音</span>
                                    <span id="recordingDuration">00:00</span>
                        </div>
                                <audio id="recordingPlayer" controls preload="metadata"></audio>
                                <div class="player-controls">
                                    <button id="recordingRewindBtn" class="control-btn">
                                        <i class="fas fa-backward"></i> -5s
                                    </button>
                                    <button id="recordingPlayPauseBtn" class="control-btn play-btn">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button id="recordingFastForwardBtn" class="control-btn">
                                        <i class="fas fa-forward"></i> +10s
                                    </button>
                            </div>
                                <div class="progress-container">
                                    <span id="recordingCurrentTime">00:00</span>
                                    <div class="progress-bar" id="recordingProgressBar">
                                        <div id="recordingProgressFill" class="progress-fill"></div>
                                        <div id="recordingProgressHandle" class="progress-handle"></div>
                            </div>
                                    <span id="recordingTotalTime">00:00</span>
                        </div>
                    </div>
                </section>

                <!-- 智能问答区域 - 统一处理AI分析和问答 -->
                <section class="qa-section">
                    <div class="card">
                        <!-- Tab导航和控制按钮 -->
                        <div class="qa-header">
                            <div class="qa-tabs">
                                <button id="qaTabBtn" class="qa-tab-btn active" data-i18n="audio.smart_qa">智能问答</button>
                                <button id="subtitleTabBtn" class="qa-tab-btn" data-i18n="audio.subtitles">字幕</button>
                            </div>
                            <div class="qa-controls">
                                <!-- 添加字幕搜索输入框 -->
                                <div class="search-box" style="margin-right: 8px;">
                                    <input type="text" id="subtitleSearchBox" placeholder="" data-i18n-placeholder="subtitle.search_placeholder" />
                                    <button id="subtitleSearchBtn" class="search-btn">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                                <button id="questionBtn" class="btn btn-info" disabled>
                                    <i class="fas fa-question-circle"></i> <span data-i18n="audio.ask_current_position">随时问</span>
                                </button>
                                <button id="exportQABtn" class="btn btn-secondary" disabled title="导出问答记录">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 智能问答内容 -->
                        <div id="qaTabContent" class="qa-tab-content active">
                            <!-- AI结果显示区域 -->
                            <div class="chat-messages-container" id="chatMessagesContainer">
                                <div class="chat-messages" id="chatMessages">

                                </div>
                            </div>
                            
                            <!-- 独立的输入区域 -->
                            <div class="chat-input-container">
                                <input type="text" id="questionInput" data-i18n-placeholder="audio.enter_question" placeholder="" class="chat-input" disabled>
                                <button id="submitQuestionBtn" class="btn btn-primary" disabled>
                                    <i class="fas fa-paper-plane"></i> <span data-i18n="audio.send">发送</span>
                                </button>
                            </div>
                        </div>
                        
                        <!-- 字幕内容 -->
                        <div id="subtitleTabContent" class="qa-tab-content">
                            <!-- 字幕上传区域 -->
                            <div id="subtitleUploadArea" class="subtitle-upload-area">
                                <div class="subtitle-upload-zone" id="subtitleDropZone">
                                    <i class="fas fa-file-upload"></i>
                                    <p data-i18n="subtitle.drop_srt">拖拽SRT字幕文件到这里</p>
                                    <small data-i18n="subtitle.or_click">或点击选择文件</small>
                                    <input type="file" id="subtitleFileInput" accept=".srt" style="display: none;">
                                </div>
                            </div>
                            
                            <!-- 字幕显示区域 -->
                            <div id="subtitleDisplayArea" class="subtitle-display-area hidden">
                                <div id="subtitleContainer" class="subtitle-container">
                                    <!-- 字幕内容将在这里显示 -->
                                </div>
                                <button id="clearSubtitleBtn" class="clear-subtitle-btn">
                                    <i class="fas fa-times"></i> <span data-i18n="subtitle.clear">清除</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
            </main>

            <!-- 状态提示区域 -->
            <div id="statusToast" class="toast hidden">
                <div class="toast-content">
                    <span id="toastMessage"></span>
                    <button id="toastClose" class="toast-close">&times;</button>
                </div>
            </div>
            
            <!-- 拖拽提示区域 -->
            <div id="dropHint" class="drop-hint hidden">
                <div class="drop-hint-content">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>将书签文件拖拽到这里来加载</p>
                    <small>支持 *_bookmark.txt 格式</small>
                </div>
            </div>

            <!-- 加载遮罩 -->
            <div id="loadingOverlay" class="loading-overlay hidden">
                <div class="loading-content">
                    <div class="spinner-large"></div>
                    <p data-i18n="audio.processing">处理中，请稍候...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 积分查看模态框 -->
    <div id="creditsModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 data-i18n="credits.title">积分信息</h3>
                <button class="modal-close" id="closeCreditsModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="credits-info">
                    <div class="current-credits">
                        <span data-i18n="credits.current">当前积分</span>: <span id="currentCredits">0</span>
                    </div>
                    <button id="viewHistoryBtn" class="btn btn-secondary">
                        <i class="fas fa-history"></i> <span data-i18n="credits.view_history">查看使用记录</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 积分使用记录模态框 -->
    <div id="creditHistoryModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 data-i18n="credits.usage_history">积分使用记录</h3>
                <button class="modal-close" id="closeCreditHistoryModal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="creditHistoryContent">
                    <div class="loading">
                        <div class="spinner"></div>
                        <span data-i18n="loading">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 设置模态框 -->
    <div id="settingsModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 data-i18n="settings.title">设置</h3>
                <button class="modal-close" id="closeSettingsModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="settings-group">
                    <label for="snippetDurationSlider" data-i18n="settings.snippet_duration">音频片段时长:</label>
                    <div class="slider-container">
                        <input type="range" id="snippetDurationSlider" min="3" max="10" value="6" step="1">
                        <div class="slider-value">
                            <span id="snippetDurationValue">6</span><span data-i18n="settings.seconds">秒</span>
                        </div>
                    </div>
                    <div class="setting-description" data-i18n="settings.snippet_description">
                        设置"随时问"功能生成的音频片段时长（3-10秒）
                    </div>
                </div>
            </div>
        </div>
    </div>
        
    <!-- 支付模态框 -->
    <div id="paymentModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 data-i18n="payment.title">积分充值</h3>
                <button class="modal-close" id="closePaymentModal">&times;</button>
            </div>
            <div class="modal-body">
                <div id="paymentContent">
                    <!-- 支付内容将由JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 播客搜索结果模态框 -->
    <div id="podcastSearchModal" class="search-modal">
        <div class="search-modal-content">
            <div class="search-modal-header">
                <h3 data-i18n="podcast.search_results">搜索结果</h3>
                <button class="search-modal-close" id="podcastModalCloseBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="search-modal-body">
                <div class="search-loading hidden" id="modalSearchLoading">
                    <div class="loading-spinner"></div>
                    <span data-i18n="podcast.searching">搜索中...</span>
                </div>
                <div id="searchResultsList" class="search-results-list">
                    <!-- 搜索结果将动态填充 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 环境配置 -->
    <script src="/env.js"></script>
    
    <!-- 核心认证和数据库客户端 -->
    <script src="js/lib/supabase.min.js"></script>
    <script src="js/supabase-client.js"></script>
    
    <!-- 统一认证管理器，管理核心认证状态 -->
    <script src="js/unified-auth-manager.js"></script>
    
    <!-- 新的Google OAuth处理器 -->
    <script src="js/google-auth.js"></script>
    
    <!-- 积分和支付相关 -->
    <script src="js/credits-loader.js"></script>
    <script src="js/logout.js"></script>
    <script src="js/api-client.js"></script>
    <script src="js/credits.js"></script>
    <script src="js/credit-history.js"></script>
    <script src="js/credit-batch-manager.js"></script>
    <script src="js/token-error-handler.js"></script>
    <script src="js/payment-packages.js"></script>
    <script src="js/payment.js"></script>
    
    <!-- 语法分析器模块 -->
    <script src="js/grammar-analyzer.js"></script>
    
    <!-- 音频理解应用的主要JS（需要复制并修改）-->
    <script src="js/audio-app.js?v=20250625-1145"></script>
    
    <!-- 字幕管理器 -->
    <script src="js/subtitle-manager.js"></script>
    
    <!-- 字幕问答管理器 -->
    <script src="js/subtitle-chat.js"></script>
    
    <!-- 书签标记强制测试脚本 -->
    <script src="js/bookmark-force-test.js"></script>
    <script src="js/extreme-bookmark-test.js"></script>
    <script src="js/quick-bookmark-fix-test.js"></script>
    
    <!-- OAuth调试工具 -->

    <!-- 初始化脚本 -->
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            // 首先，等待新的OAuth回调处理器完成工作
            if (window.oAuthCallbackPromise) {
                console.log('等待OAuth回调处理...');
                await window.oAuthCallbackPromise;
                console.log('OAuth回调处理完成，继续页面初始化。');
            }

            console.log('📋 开始页面统一初始化');
            
            try {
                // 等待统一认证管理器初始化完成
                const authManager = window.unifiedAuthManager;
                if (!authManager) {
                    console.error('❌ 统一认证管理器未找到');
                    window.location.href = '/index.html';
                    return;
                }

                // 等待认证状态确定
                console.log('⏳ 等待认证状态确定...');
                const authState = await authManager.waitForAuthState(3000);
                
                console.log('📋 认证状态检查结果:', {
                    isAuthenticated: authState.isAuthenticated,
                    hasUser: !!authState.user,
                    lastUpdate: authState.lastUpdate
                });

                // 检查是否已认证
                if (!authState.isAuthenticated) {
                    console.log('🚨 用户未登录，重定向到登录页');
                    window.location.href = '/index.html';
                    return;
                }

                console.log('✅ 用户已登录，继续初始化页面');

                // 监听认证状态变化
                authManager.addListener((event, session, authState) => {
                    console.log('� 认证状态变化:', event, authState.isAuthenticated);
                    
                    if (!authState.isAuthenticated) {
                        console.log('🚨 检测到用户登出，重定向到登录页');
                        window.location.href = '/index.html';
                    }
                });

                // 标记页面初始化完成（提前设置，不等待积分系统）
                window.pageInitialized = true;
                console.log('✅ 页面基础初始化完成');

                // 异步初始化积分系统（不阻塞用户交互）
                if (window.CreditsManager) {
                    console.log('💰 开始异步初始化积分系统');
                    window.CreditsManager.init()
                        .then(() => {
                            console.log('💰 积分系统初始化完成');
                            // 初始化完成后，立即显示从登录页缓存的积分
                            if (typeof forceUpdateCreditsDisplay === 'function') {
                                forceUpdateCreditsDisplay();
                            }
                        })
                        .catch(error => {
                            console.error('💰 积分系统初始化失败:', error);
                            // 即使失败也尝试显示缓存的积分
                            if (typeof forceUpdateCreditsDisplay === 'function') {
                                forceUpdateCreditsDisplay();
                            }
                        });
                } else {
                    console.warn('💰 CreditsManager 未找到，跳过积分系统初始化');
                }

                // 初始化UI组件
                initSettingsModal();
                initMenuInteractions();
                
                // 初始化UI语言
                if (window.UILanguage) {
                    window.UILanguage.translatePage();
                }

                // 初始化音频应用
                if (window.AudioApp && !window.audioApp) {
                    window.audioApp = new AudioApp();
                    console.log('🎵 音频应用初始化完成');
                } else if (window.audioApp) {
                    console.log('🎵 音频应用已存在，跳过初始化');
                }

                // 初始化事件处理
                initAudioButtonEvents();
                initMenuButtonEvents();

            } catch (error) {
                console.error('❌ 页面初始化失败:', error);
                window.location.href = '/index.html';
            }
        });

        // 强制更新积分显示函数
        let lastCreditsValue = null; // 缓存上次的积分值，避免重复日志
        function forceUpdateCreditsDisplay() {
            const userCreditsElement = document.getElementById('userCredits');
            if (!userCreditsElement) {
                return; // 减少日志输出
            }

            let creditsValue = 0;
            
            // 尝试多种方式获取积分值
            if (window.CreditsManager && typeof window.CreditsManager.getCredits === 'function') {
                creditsValue = window.CreditsManager.getCredits();
                // 只在积分值变化时输出日志
                if (creditsValue !== lastCreditsValue) {
                    console.log('积分更新:', creditsValue);
                    lastCreditsValue = creditsValue;
                }
            } else {
                // 从localStorage获取缓存的积分
                try {
                    const cachedCredits = localStorage.getItem('cachedCredits');
                    if (cachedCredits && cachedCredits !== 'null') {
                        const parsed = JSON.parse(cachedCredits);
                        creditsValue = parsed.credits || 0;
                    }
                } catch (e) {
                    // 减少警告日志频率
                    if (Date.now() % 10000 < 2000) { // 每10秒只输出一次警告
                        console.warn('解析缓存积分失败:', e);
                    }
                }
            }

            // 确保积分值是数字
            creditsValue = parseInt(creditsValue) || 0;
            
            // 只在需要更新时才操作DOM
            if (userCreditsElement.textContent !== creditsValue.toString()) {
                userCreditsElement.textContent = creditsValue;
                
                // 确保元素可见
                userCreditsElement.style.display = 'inline-block';
                userCreditsElement.style.opacity = '1';
                userCreditsElement.style.visibility = 'visible';
            }
        }
        
        // 监听积分更新事件，确保实时同步
        document.addEventListener('credits-updated', function(event) {
            console.log('收到积分更新事件:', event.detail);
            forceUpdateCreditsDisplay();
        });

        // 设置模态框相关函数
        function showSettingsModal() {
            const settingsModal = document.getElementById('settingsModal');
            if (settingsModal) {
                // 从localStorage读取当前设置
                const savedDuration = localStorage.getItem('snippetDuration') || '6';
                const slider = document.getElementById('snippetDurationSlider');
                const valueDisplay = document.getElementById('snippetDurationValue');
                
                if (slider && valueDisplay) {
                    slider.value = savedDuration;
                    valueDisplay.textContent = savedDuration;
                }
                
                settingsModal.classList.remove('hidden');
            }
        }
        
        function hideSettingsModal() {
            const settingsModal = document.getElementById('settingsModal');
            if (settingsModal) {
                settingsModal.classList.add('hidden');
            }
        }
        
        // 设置模态框事件初始化
        function initSettingsModal() {
            const settingsModal = document.getElementById('settingsModal');
            const closeSettingsModal = document.getElementById('closeSettingsModal');
            const snippetDurationSlider = document.getElementById('snippetDurationSlider');
            const snippetDurationValue = document.getElementById('snippetDurationValue');
            
            // 关闭按钮
            if (closeSettingsModal) {
                closeSettingsModal.addEventListener('click', hideSettingsModal);
            }
            
            // 点击遮罩关闭
            if (settingsModal) {
                settingsModal.addEventListener('click', function(e) {
                    if (e.target === settingsModal) {
                        hideSettingsModal();
                    }
                });
            }
            
            // 滑块值变化
            if (snippetDurationSlider && snippetDurationValue) {
                snippetDurationSlider.addEventListener('input', function() {
                    const value = this.value;
                    snippetDurationValue.textContent = value;
                    // 实时保存到localStorage
                    localStorage.setItem('snippetDuration', value);
                });
            }
        }

        // 全局音频按钮事件处理机制
        function initAudioButtonEvents() {
            const uploadBtn = document.getElementById('uploadBtn');
            const audioFileInput = document.getElementById('audioFileInput');
            
            if (!uploadBtn || !audioFileInput) {
                console.warn('音频按钮或文件输入元素不存在');
                return;
            }
            
            // 避免重复绑定
            if (uploadBtn.hasAttribute('data-audio-event-bound')) {
                return;
            }
            
            // 清除可能存在的旧事件
            uploadBtn.onclick = null;
            const newUploadBtn = uploadBtn.cloneNode(true);
            uploadBtn.parentNode.replaceChild(newUploadBtn, uploadBtn);
            
            // 重新获取替换后的按钮
            const finalUploadBtn = document.getElementById('uploadBtn');
            
            // 添加新的事件监听器
            finalUploadBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('音频按钮被点击');
                
                // 确保AudioApp已初始化
                if (!window.audioApp) {
                    if (window.AudioApp) {
                        try {
                            window.audioApp = new AudioApp();
                            console.log('点击时初始化AudioApp成功');
                        } catch (error) {
                            console.error('点击时初始化AudioApp失败:', error);
                        }
                    }
                }
                
                // 触发文件选择
                audioFileInput.click();
            });
            
            // 标记已绑定事件
            finalUploadBtn.setAttribute('data-audio-event-bound', 'true');
            console.log('音频按钮事件绑定完成');
        }
        
        // 全局菜单按钮事件处理机制  
        function initMenuButtonEvents() {
            const userMenuBtn = document.getElementById('userMenuBtn');
            const userDropdown = document.getElementById('userDropdown');
            
            if (!userMenuBtn || !userDropdown) {
                return;
            }
            
            // 避免重复绑定
            if (userMenuBtn.hasAttribute('data-menu-event-bound')) {
                return;
            }
            
            // 清除可能存在的旧事件
            const newMenuBtn = userMenuBtn.cloneNode(true);
            userMenuBtn.parentNode.replaceChild(newMenuBtn, userMenuBtn);
            
            const finalMenuBtn = document.getElementById('userMenuBtn');
            
            // 添加防抖机制的点击事件
            let clickTimeout;
            finalMenuBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // 清除之前的延时
                if (clickTimeout) {
                    clearTimeout(clickTimeout);
                }
                
                // 防抖处理
                clickTimeout = setTimeout(() => {
                    const dropdown = document.getElementById('userDropdown');
                    if (!dropdown) return;
                    
                    const isVisible = dropdown.style.opacity === '1' && dropdown.style.visibility === 'visible';
                    
                    if (isVisible) {
                        // 隐藏菜单
                        dropdown.style.opacity = '0';
                        dropdown.style.visibility = 'hidden';
                        dropdown.style.transform = 'translateY(-10px)';
                        console.log('菜单已隐藏');
                    } else {
                        // 显示菜单
                        dropdown.style.display = 'block';
                        dropdown.style.opacity = '1';
                        dropdown.style.visibility = 'visible';
                        dropdown.style.transform = 'translateY(0)';
                        console.log('菜单已显示');
                        
                        // 直接从后端缓存获取最新积分（最简单快速的方法）
                        console.log('📊 菜单点击：从后端缓存获取积分');
                        
                        // 立即显示加载状态
                        const userCreditsElement = document.getElementById('userCredits');
                        if (userCreditsElement) {
                            const originalText = userCreditsElement.textContent;
                            userCreditsElement.innerHTML = '<div class="loading-spinner"></div>';
                            userCreditsElement.style.opacity = '0.8';
                            
                            // 获取当前token
                            const authToken = localStorage.getItem('authToken');
                            if (!authToken) {
                                // 没有token，直接跳转登录页
                                console.log('没有token，跳转登录页');
                                window.location.href = '/index.html';
                                return;
                            }
                            
                            // 直接调用后端缓存API
                            fetch('/api/credits/get-fast', {
                                method: 'GET',
                                headers: {
                                    'Authorization': `Bearer ${authToken}`,
                                    'Content-Type': 'application/json'
                                }
                            })
                            .then(response => {
                                if (response.status === 401) {
                                    // token失效，清理并跳转
                                    console.log('Token失效，清理数据并跳转登录页');
                                    localStorage.clear();
                                    window.location.href = '/index.html';
                                    return null;
                                }
                                return response.json();
                            })
                            .then(data => {
                                if (data && data.success) {
                                    console.log('✅ 菜单积分获取成功:', data.credits);
                                    userCreditsElement.style.opacity = '1';
                                    userCreditsElement.innerHTML = data.credits;
                                    
                                    // 更新本地缓存
                                    if (window.CreditsManager) {
                                        window.CreditsManager.credits = data.credits;
                                        window.CreditsManager.cacheCredits(data.credits);
                                    }
                                } else if (data && !data.success) {
                                    console.warn('⚠️ 快速积分API失败，尝试完整API:', data.error);
                                    // 快速API失败，降级到完整API
                                    fetch('/api/credits/get', {
                                        method: 'GET',
                                        headers: {
                                            'Authorization': `Bearer ${authToken}`,
                                            'Content-Type': 'application/json'
                                        }
                                    })
                                    .then(response => response.json())
                                    .then(fallbackData => {
                                        if (fallbackData && fallbackData.success) {
                                            console.log('✅ 完整积分API获取成功:', fallbackData.credits);
                                            userCreditsElement.style.opacity = '1';
                                            userCreditsElement.innerHTML = fallbackData.credits;
                                            
                                            // 更新本地缓存
                                            if (window.CreditsManager) {
                                                window.CreditsManager.credits = fallbackData.credits;
                                                window.CreditsManager.cacheCredits(fallbackData.credits);
                                            }
                                        } else {
                                            throw new Error(fallbackData.error || '完整API获取积分失败');
                                        }
                                    })
                                    .catch(fallbackError => {
                                        console.error('❌ 完整积分API也失败:', fallbackError);
                                        // 最终降级到缓存显示
                                        userCreditsElement.style.opacity = '1';
                                        if (typeof forceUpdateCreditsDisplay === 'function') {
                                            forceUpdateCreditsDisplay();
                                        } else {
                                            userCreditsElement.innerHTML = originalText || '--';
                                        }
                                    });
                                }
                                // data为null时（401情况）不做任何操作
                            })
                            .catch(error => {
                                console.error('❌ 菜单积分获取失败:', error);
                                // 网络错误等，降级到完整API
                                fetch('/api/credits/get', {
                                    method: 'GET',
                                    headers: {
                                        'Authorization': `Bearer ${authToken}`,
                                        'Content-Type': 'application/json'
                                    }
                                })
                                .then(response => response.json())
                                .then(fallbackData => {
                                    if (fallbackData && fallbackData.success) {
                                        console.log('✅ 网络错误降级：完整积分API获取成功:', fallbackData.credits);
                                        userCreditsElement.style.opacity = '1';
                                        userCreditsElement.innerHTML = fallbackData.credits;
                                        
                                        // 更新本地缓存
                                        if (window.CreditsManager) {
                                            window.CreditsManager.credits = fallbackData.credits;
                                            window.CreditsManager.cacheCredits(fallbackData.credits);
                                        }
                                    } else {
                                        throw new Error('最终降级也失败');
                                    }
                                })
                                .catch(finalError => {
                                    console.error('❌ 所有积分获取方式都失败:', finalError);
                                    // 最终显示缓存的积分
                                    userCreditsElement.style.opacity = '1';
                                    if (typeof forceUpdateCreditsDisplay === 'function') {
                                        forceUpdateCreditsDisplay();
                                    } else {
                                        userCreditsElement.innerHTML = originalText || '--';
                                    }
                                });
                            });
                        }
                    }
                }, 50); // 50ms防抖
            });
            
            // 标记已绑定事件
            finalMenuBtn.setAttribute('data-menu-event-bound', 'true');
            console.log('菜单按钮事件绑定完成');
        }

        // 全局错误处理：防止Object.entries等方法的undefined错误
        window.addEventListener('error', function(event) {
            // 检查是否是Object.entries相关的错误
            if (event.error && event.error.message && 
                (event.error.message.includes('Cannot convert undefined or null to object') ||
                 event.error.message.includes('Object.entries'))) {
                
                console.warn('检测到Object.entries错误，已忽略:', event.error.message);
                event.preventDefault(); // 阻止错误继续传播
                return false;
            }
        });
        
        // 增强Object.entries方法，添加安全检查
        const originalObjectEntries = Object.entries;
        Object.entries = function(obj) {
            if (obj === null || obj === undefined) {
                // 静默处理，避免日志噪音
                return [];
            }
            return originalObjectEntries.call(this, obj);
        };
        
        // 增强Object.keys方法，添加安全检查
        const originalObjectKeys = Object.keys;
        Object.keys = function(obj) {
            if (obj === null || obj === undefined) {
                // 静默处理，避免日志噪音
                return [];
            }
            return originalObjectKeys.call(this, obj);
        };
        
        // 增强Object.values方法，添加安全检查
        const originalObjectValues = Object.values;
        Object.values = function(obj) {
            if (obj === null || obj === undefined) {
                // 静默处理，避免日志噪音
                return [];
            }
            return originalObjectValues.call(this, obj);
        };
        
        console.log('全局错误保护机制已启用');
    </script>

    <!-- Session恢复脚本 - 优先处理URL中的token -->
    <script src="js/session-recovery.js?v=1.0&t=20250628"></script>
    
    <!-- 手动修复工具 - 用户可在控制台直接调用 -->
    <script src="js/manual-fix.js?v=1.0&t=20250628"></script>
    
    <!-- 引入时间戳功能JavaScript -->
    <script src="js/timestamp-manager.js"></script>
    
    <!-- 播客RSS配置加载器 -->
    <script src="js/podcast-rss-config-loader.js"></script>
    
    <!-- 播客管理器 -->
    <script src="js/podcast-manager.js"></script>
    
    <!-- 引入书签管理器JavaScript -->
    <script src="js/bookmark-manager.js"></script>
</body>
</html>