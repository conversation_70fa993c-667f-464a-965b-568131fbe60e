-- ========================================
-- 验证所有必需的表是否存在
-- ========================================

-- 1. 检查表是否存在
SELECT 
    table_name,
    table_schema,
    CASE 
        WHEN table_name IS NOT NULL THEN '✅ 存在'
        ELSE '❌ 不存在'
    END as status
FROM information_schema.tables
WHERE table_schema = 'public' 
    AND table_name IN (
        'user_credits', 
        'credit_operations', 
        'payment_orders', 
        'security_logs', 
        'user_profiles'
    )
ORDER BY table_name;

-- 2. 检查RLS是否启用
SELECT 
    schemaname,
    tablename,
    rowsecurity,
    CASE 
        WHEN rowsecurity THEN '🔒 RLS 已启用'
        ELSE '⚠️ RLS 未启用'
    END as security_status
FROM pg_tables 
WHERE schemaname = 'public' 
    AND tablename IN (
        'user_credits', 
        'credit_operations', 
        'payment_orders', 
        'security_logs', 
        'user_profiles'
    )
ORDER BY tablename;

-- 3. 检查RLS策略数量
SELECT 
    schemaname,
    tablename,
    COUNT(*) as policy_count
FROM pg_policies 
WHERE schemaname = 'public' 
    AND tablename IN (
        'user_credits', 
        'credit_operations', 
        'payment_orders', 
        'security_logs', 
        'user_profiles'
    )
GROUP BY schemaname, tablename
ORDER BY tablename;

-- 4. 检查重要索引
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
    AND tablename IN (
        'user_credits', 
        'credit_operations', 
        'payment_orders', 
        'security_logs', 
        'user_profiles'
    )
ORDER BY tablename, indexname;

-- 5. 检查触发器
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_statement
FROM information_schema.triggers 
WHERE event_object_schema = 'public'
    AND event_object_table IN (
        'user_credits', 
        'credit_operations', 
        'payment_orders', 
        'security_logs', 
        'user_profiles'
    )
ORDER BY event_object_table, trigger_name; 