import os
import logging
import time
import json
import random
import requests
import threading
import queue
from collections import defaultdict
from flask import Response, stream_with_context, request
from dotenv import load_dotenv
from werkzeug.exceptions import ClientDisconnected
from api.models_config import MODEL_CONFIG
import uuid
import re
from datetime import datetime
from urllib.parse import quote
from collections import deque

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建线程局部存储对象，用于缓存状态信息
thread_local = threading.local()

# 创建队列和队列处理线程
request_queue = deque()
queue_processor_thread = None
queue_processor_running = False

# API状态监控
api_rate_limit = {
    "remaining": 100,  # 默认值
    "limit": 100,      # 默认值
    "reset": int(time.time()) + 3600,  # 1小时后重置
    "last_update": 0   # 上次更新时间
}

# -------------- API 密钥管理系统 --------------

# API密钥使用计数器
api_key_usage = defaultdict(list)
api_key_usage_lock = threading.Lock()

def get_rate_limit():
    """获取API速率限制配置"""
    try:
        from api.rate_limit_config import get_api_rate_limit
        return get_api_rate_limit('ZHIPU')
    except ImportError:
        # 回退到环境变量读取
        return int(os.environ.get('ZHIPU_API_RATE_LIMIT', 10))

def get_api_key():
    """
    智能选择API密钥，基于负载均衡
    
    返回:
        选择的API密钥，如果所有密钥都已超限，返回使用量最少的密钥
    """
    # 尝试获取多个API密钥
    multi_keys = os.environ.get('ZHIPU_API_KEYS', '')
    if not multi_keys:
        # 回退到单个密钥
        return os.environ.get('ZHIPU_API_KEY')
    
    keys = [key.strip() for key in multi_keys.split(',') if key.strip()]
    if not keys:
        return os.environ.get('ZHIPU_API_KEY')
    
    # 定义每个密钥的速率限制
    rate_limit = get_rate_limit()
    
    # 当前时间
    current_time = time.time()
    
    with api_key_usage_lock:
        # 清理过期的使用记录（1秒窗口）
        for key in list(api_key_usage.keys()):
            api_key_usage[key] = [t for t in api_key_usage[key] if current_time - t < 1.0]
        
        # 检查是否有未达到速率限制的密钥
        available_keys = []
        for key in keys:
            usage_count = len(api_key_usage.get(key, []))
            if usage_count < rate_limit:
                available_keys.append((key, usage_count))
        
        selected_key = None
        if available_keys:
            # 选择使用量最少的可用密钥
            selected_key, _ = min(available_keys, key=lambda x: x[1])
        else:
            # 如果所有密钥都超过限制，选择请求最少的一个
            usage_counts = [(key, len(api_key_usage.get(key, []))) for key in keys]
            selected_key, _ = min(usage_counts, key=lambda x: x[1])
            logger.warning(f"所有API密钥已达到速率限制({rate_limit}/秒)，选择负载最小的密钥")
        
        # 记录所选密钥的使用情况
        api_key_usage[selected_key].append(current_time)
        logger.debug(f"选择API密钥: {selected_key[:8]}**** (当前活跃请求: {len(api_key_usage[selected_key])})")
        
        return selected_key

# -------------- 请求队列处理系统 --------------

def queue_processor():
    """
    请求队列处理线程：不断从队列中获取请求并处理
    """
    global queue_processor_running
    
    logger.info("启动API请求队列处理线程")
    
    while queue_processor_running:
        try:
            # 检查队列是否为空
            if not request_queue:
                # 队列为空，暂停一下
                time.sleep(1)
                continue
                
            # 从队列中获取请求
            request_data = request_queue.popleft()
            
            # 处理请求
            process_queued_request(request_data)
            
        except IndexError:
            # 队列为空，继续循环
            time.sleep(1)
        except Exception as e:
            logger.error(f"处理队列请求时出错: {str(e)}")
            time.sleep(1)
    
    logger.info("API请求队列处理线程已停止")

def process_queued_request(request_data):
    """
    处理来自队列的请求
    
    参数:
        request_data: 包含请求信息的字典
    """
    prompt = request_data['prompt']
    system_message = request_data['system_message']
    model_name = request_data.get('model_name', 'glm-4-flash')  # 默认为glm-4-flash
    response_queue = request_data['response_queue']
    
    try:
        # 为队列中的请求获取API密钥
        api_key = get_api_key()
        
        if not api_key:
            response_queue.put("错误: 未找到有效的API密钥")
            response_queue.put(None)  # 结束信号
            return
        
        logger.info(f"处理队列请求，使用API密钥: {api_key[:8]}**** 和模型: {model_name}")
        
        # 智谱API端点
        api_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        
        # 请求头
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 请求数据
        data = {
            "model": model_name,  # 使用传入的模型名称
            "messages": [
                {"role": "system", "content": system_message},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "stream": True,
            "max_tokens": 8000
        }
        
        # 发送API请求并处理流式响应
        with requests.post(
                api_url,
                headers=headers,
                json=data,
                stream=True,
                timeout=(5, 15)
            ) as response:
            
            if response.status_code != 200:
                error_msg = f"API请求失败，状态码: {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f", 详细错误: {error_detail}"
                except:
                    error_msg += f", 响应内容: {response.text}"
                
                logger.error(error_msg)
                
                # 检查是否是认证相关错误，如果是则跳过积分扣减
                is_auth_error = (
                    response.status_code == 401 or
                    response.status_code == 403 or
                    "unauthorized" in response.text.lower() or
                    "authentication" in response.text.lower() or
                    "invalid api key" in response.text.lower() or
                    "api key" in response.text.lower()
                )
                
                if is_auth_error:
                    logger.info("检测到智谱API认证错误，跳过积分扣减")
                    response_queue.put("<!-- CREDITS_SKIP: API_ERROR -->\n")
                
                response_queue.put(error_msg)
                response_queue.put(None)  # 结束信号
                return
            
            # 处理流式响应
            for chunk in response.iter_lines():
                if chunk:
                    chunk_str = chunk.decode('utf-8')
                    
                    # 跳过"data: "前缀
                    if chunk_str.startswith('data: '):
                        chunk_str = chunk_str[6:]
                    
                    # 跳过心跳消息
                    if chunk_str == '[DONE]':
                        continue
                    
                    try:
                        chunk_data = json.loads(chunk_str)
                        if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                            delta = chunk_data['choices'][0].get('delta', {})
                            content = delta.get('content', '')
                            if content:
                                response_queue.put(content)
                    except json.JSONDecodeError:
                        continue
    
    except Exception as e:
        logger.error(f"处理队列请求时发生错误: {str(e)}")
        response_queue.put(f"处理请求时出错: {str(e)}")
    
    finally:
        # 发送结束信号
        response_queue.put(None)

def ensure_queue_processor_running():
    """确保队列处理线程正在运行"""
    global queue_processor_thread, queue_processor_running
    
    with threading.Lock():
        if queue_processor_thread is None or not queue_processor_thread.is_alive():
            queue_processor_running = True
            queue_processor_thread = threading.Thread(target=queue_processor, daemon=True)
            queue_processor_thread.start()
            logger.info("启动了新的队列处理线程")

def stop_queue_processor():
    """停止队列处理线程"""
    global queue_processor_running
    queue_processor_running = False
    
    if queue_processor_thread and queue_processor_thread.is_alive():
        queue_processor_thread.join(timeout=2)
        logger.info("队列处理线程已停止")

# -------------- 流式响应处理 --------------

def format_token_usage(token_usage, is_partial=False, source="未知来源", model_name="glm-4-flash", frontend_model_id=None):
    """
    格式化token使用情况信息

    参数:
        token_usage: 包含token使用情况的字典
        is_partial: 是否是部分统计（流被中断）
        source: 数据来源（如 "API" 或 "估算"）
        model_name: API返回的原始模型名称
        frontend_model_id: 前端传入的模型ID (如 zhipu_flash)

    返回：
        格式化后的token使用情况字符串 (用于前端)
    """
    # 创建token使用数据JSON - 直接使用前端传入的模型ID
    token_usage_data = {
        'input_tokens': token_usage['prompt_tokens'], 
        'output_tokens': token_usage['completion_tokens'], 
        'total_tokens': token_usage['total_tokens'],  
        'model': frontend_model_id,  # 直接使用传入的前端模型ID
        'source': source
    }

    # 生成用于日志的详细信息
    partial_notice = "（部分，流已中断）" if is_partial else ""
    source_notice = f"（数据来源: {source}）"
    log_info = (
        f"\n\n==== Token使用情况{partial_notice} {source_notice} ====\n"
        f"模型: {model_name}\n"
        f"前端模型ID: {frontend_model_id}\n"
        f"输入tokens: {token_usage['prompt_tokens']}\n"
        f"输出tokens: {token_usage['completion_tokens']}\n"
        f"总tokens: {token_usage['total_tokens']}\n"
    )
    logger.info(log_info)
    usage_info = f"<!-- HIDDEN_TOKEN_DATA: {json.dumps(token_usage_data)} -->"
    return usage_info

def format_error_message(error_message, error_type="general_error"):
    """
    将错误信息格式化为HTML注释，隐藏技术细节
    
    参数:
        error_message: 原始错误信息
        error_type: 错误类型，用于前端识别（如 "api_error"）
        
    返回:
        格式化后的HTML注释字符串
    """
    error_data = {
        'error': error_message,
        'error_type': error_type
    }
    return f"<!-- HIDDEN_ERROR_DATA: {json.dumps(error_data)} -->"

def create_streaming_response(prompt, system_message, frontend_model_id=None, estimated_credits=None):
    """
    创建智谱GLM-4流式响应
    
    参数:
        prompt: 用户输入提示词
        system_message: 系统提示词
        frontend_model_id: 前端使用的模型ID（例如zhipu_flash等）
        estimated_credits: 预估所需积分数（用于中断时积分扣减）
        
    返回:
        Flask Response对象，生成流式响应
    """
    # 创建一个唯一的请求ID用于日志追踪
    request_id = f"{time.time()}-{str(uuid.uuid4())[:8]}"
    
    # 定义生成器函数，用于流式输出
    def generate():
        # 从中心化配置获取API模型名称
        from api.models_config import get_model_by_id
        if frontend_model_id:
            model_config = get_model_by_id(frontend_model_id)
            api_model_name = model_config.get("api_model_name", "glm-4-flash")
        else:
            api_model_name = "glm-4-flash"
        
        # 记录开始处理的消息 - 使用请求ID便于追踪
        logger.info(f"请求 [{request_id}] - 使用配置的API模型名称: {api_model_name}")
        logger.info(f"请求 [{request_id}] - 根据前端模型ID [{frontend_model_id}] 确定API模型: {api_model_name}")
        
        # 根据是否有预估积分输出积分信息
        if estimated_credits:
            logger.info(f"智谱API 预计扣减积分: {estimated_credits}")
        
        # 使用线程局部存储来缓存tiktoken加载状态，避免重复初始化
        if not hasattr(thread_local, 'tiktoken_loaded'):
            thread_local.tiktoken_loaded = False
        
        # 使用字典缓存tokenizer，避免重复加载
        if not hasattr(thread_local, 'tokenizer_cache'):
            thread_local.tokenizer_cache = {}
        
        # 方便地估算token数量，使用缓存版本避免重复初始化
        def estimate_tokens(text):
            # 简单估算，不精确但足够应急使用
            if not text:
                return 0
                
            # 尝试使用tiktoken（更准确）
            try:
                # 仅在首次调用时初始化tiktoken
                if not thread_local.tiktoken_loaded:
                    try:
                        import tiktoken
                        # 缓存编码器
                        if 'cl100k_base' not in thread_local.tokenizer_cache:
                            thread_local.tokenizer_cache['cl100k_base'] = tiktoken.get_encoding("cl100k_base")
                        thread_local.tiktoken_loaded = True
                        logger.info("tiktoken库已加载，使用cl100k_base编码器")
                    except ImportError:
                        logger.warning("未找到tiktoken库，将使用简单字符估算")
                        thread_local.tiktoken_loaded = False
                
                # 如果已成功加载tiktoken，使用它进行编码
                if thread_local.tiktoken_loaded and 'cl100k_base' in thread_local.tokenizer_cache:
                    encoding = thread_local.tokenizer_cache['cl100k_base']
                    return len(encoding.encode(text))
            except Exception as e:
                logger.warning(f"使用tiktoken估算tokens失败: {str(e)}")
            
            # 回退到简单估算
            if isinstance(text, str):
                # 中文约为1.5倍字符数（粗略估计）
                chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
                other_chars = len(text) - chinese_chars
                return int(chinese_chars * 1.5 + other_chars * 0.25)
            else:
                return 0
        
        # 当前API使用情况
        rate_limit = get_rate_limit()
        current_time = time.time()
        
        # 初始化token使用情况
        token_usage = {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
        
        # 创建一个列表来存储生成的输出tokens（用于估算输出token数）
        generated_output = []
        
        # 检查是否需要排队
        need_queue = False
        
        with api_key_usage_lock:
            # 获取可用的API密钥列表
            multi_keys = os.environ.get('ZHIPU_API_KEYS', '')
            if multi_keys:
                keys = [key.strip() for key in multi_keys.split(',') if key.strip()]
                
                # 清理过期的使用记录
                for key in list(api_key_usage.keys()):
                    api_key_usage[key] = [t for t in api_key_usage[key] if current_time - t < 1.0]
                
                # 检查是否有任何密钥未达到速率限制
                all_keys_at_limit = True
                for key in keys:
                    if len(api_key_usage.get(key, [])) < rate_limit:
                        all_keys_at_limit = False
                        break
                
                need_queue = all_keys_at_limit
        
        # 如果所有密钥都已达到限制，使用队列处理
        if need_queue:
            logger.info("所有API密钥都已达到速率限制，请求加入队列")
            yield "请求正在排队等待处理，将很快开始处理...\n"
            
            # 创建响应队列
            response_queue = queue.Queue()
            
            # 将请求放入队列
            request_data = {
                'prompt': prompt,
                'system_message': system_message,
                'model_name': api_model_name,  # 使用动态确定的API模型名称
                'frontend_model_id': frontend_model_id,  # 传递前端模型ID
                'response_queue': response_queue,
                'timestamp': time.time()
            }
            
            # 确保队列处理线程正在运行
            ensure_queue_processor_running()
            
            # 添加请求到队列
            request_queue.append(request_data)
            
            # 从响应队列获取结果并传递给客户端
            while True:
                try:
                    # 超时设置防止无限等待
                    result = response_queue.get(timeout=60)
                    if result is None:  # 结束信号
                        break
                    yield result
                except queue.Empty:
                    error_msg = "\n等待响应超时，请检查API服务状态...\n"
                    logger.error(error_msg)
                    yield format_error_message(error_msg)
                    break
                    
            return
        
        # 记录流是否已经取消和token使用情况是否已报告
        stream_canceled = False
        token_usage_reported = False
        
        # 正常流程：直接处理请求，不通过队列
        api_key = get_api_key()
        if not api_key:
            error_msg = "错误: 未找到有效的API密钥，请配置ZHIPU_API_KEY或ZHIPU_API_KEYS环境变量"
            logger.error(error_msg)
            yield format_error_message(error_msg)
            return
        
        logger.info(f"使用API密钥: {api_key[:8]}****(已隐藏部分内容) 和模型: {api_model_name}")
        
        # 智谱API端点
        api_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        
        # 请求头
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        
        # 请求数据
        data = {
            "model": api_model_name,  # 使用动态确定的API模型名称
            "messages": [
                {"role": "system", "content": system_message},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "stream": True,
            "max_tokens": 8000
        }
        
        try:
            logger.info(f"正在发送请求到智谱API (模型: {api_model_name})...")
            
            # 估算输入tokens（用于在取消情况下的估算）
            estimated_prompt_tokens = estimate_tokens(prompt) + estimate_tokens(system_message)
            token_usage['prompt_tokens'] = estimated_prompt_tokens
            
            # 发送流式请求
            response = requests.post(
                api_url,
                headers=headers,
                json=data,
                stream=True,
                timeout=(5, 15)
            )
            
            # 检查请求是否成功
            if response.status_code != 200:
                error_msg = f"API请求失败，状态码: {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f", 详细错误: {error_detail}"
                except:
                    error_msg += f", 响应内容: {response.text}"
                
                logger.error(error_msg)
                
                # 检查是否是认证相关错误，如果是则跳过积分扣减
                is_auth_error = (
                    response.status_code == 401 or
                    response.status_code == 403 or
                    "unauthorized" in response.text.lower() or
                    "authentication" in response.text.lower() or
                    "invalid api key" in response.text.lower() or
                    "api key" in response.text.lower()
                )
                
                if is_auth_error:
                    logger.info("检测到智谱API认证错误，跳过积分扣减")
                    yield "<!-- CREDITS_SKIP: API_ERROR -->\n"
                    yield format_error_message(error_msg, "api_error")
                else:
                    yield format_error_message(error_msg)
                return

            # 流式处理代码
            logger.info("API初始响应成功，尝试建立数据流...")
            
            # 设置标志来跟踪是否成功接收到实际数据
            data_stream_established = False
            
            # 流式处理的主要代码块
            try:
                for chunk in response.iter_lines():
                    if chunk:
                        if not data_stream_established:
                            data_stream_established = True
                            logger.info("API连接成功，数据流已建立")
                        
                        chunk_str = chunk.decode('utf-8')
                        logger.debug(f"收到数据块: {chunk_str}")
    
                        # 跳过"data: "前缀
                        if chunk_str.startswith('data: '):
                            chunk_str = chunk_str[6:]
    
                        # 跳过心跳消息
                        if chunk_str == '[DONE]':
                            logger.debug("收到结束标记")
                            continue
    
                        try:
                            chunk_data = json.loads(chunk_str)
                            
                            # 提取usage信息（通常在最后一个块）
                            if 'usage' in chunk_data:
                                # 更新实际token使用情况
                                token_usage = chunk_data['usage']
                            
                            if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                delta = chunk_data['choices'][0].get('delta', {})
                                content = delta.get('content', '')
                                if content:
                                    # 保存生成的内容用于估算token数
                                    generated_output.append(content)
                                    yield content

                        except json.JSONDecodeError as e:
                            logger.error(f"JSON解析错误: {e}, 原始数据: {chunk_str}")
                            continue
                
                # 流式输出完成后，处理token使用情况报告
                if token_usage["total_tokens"] > 0:
                    usage_info = format_token_usage(token_usage, is_partial=False, source="API", 
                                                   model_name=api_model_name, frontend_model_id=frontend_model_id)
                    logger.info(usage_info)
                    yield usage_info
                    token_usage_reported = True
                else:
                    # 估算已生成的输出tokens
                    generated_text = ''.join(generated_output)
                    estimated_output_tokens = estimate_tokens(generated_text)
                    
                    # 更新token使用情况估算
                    token_usage['completion_tokens'] = estimated_output_tokens
                    token_usage['total_tokens'] = token_usage['prompt_tokens'] + token_usage['completion_tokens']
                    
                    # 输出估算的token使用情况
                    usage_info = format_token_usage(token_usage, is_partial=True, source="估算", 
                                                   model_name=api_model_name, frontend_model_id=frontend_model_id)
                    logger.info(usage_info)
                    yield usage_info
                    token_usage_reported = True
                
            except ClientDisconnected:
                # 客户端断开连接，设置标志但不yield，记录到日志
                stream_canceled = True
                logger.warning("检测到客户端断开连接，流已取消")
            
            except GeneratorExit:
                stream_canceled = True
                logger.warning(f"请求 [{request_id}] - 检测到客户端断开连接(GeneratorExit)")
                
                # 在GeneratorExit时立即处理积分扣减，不使用yield
                if data_stream_established and not token_usage_reported and estimated_credits is not None and estimated_credits > 0:
                    try:
                        logger.info(f"GeneratorExit时使用预估积分扣减: {estimated_credits}")
                        # 生成token使用信息，使用预估积分
                        formatted_usage = format_token_usage(
                            {"input_tokens": estimated_credits, "output_tokens": 0}, 
                            is_partial=True, 
                            source="GeneratorExit预估",
                            model_name=api_model_name,
                            frontend_model_id=frontend_model_id
                        )
                        # 不能yield，直接记录到日志
                        logger.info(formatted_usage)
                        token_usage_reported = True
                    except Exception as e:
                        logger.error(f"GeneratorExit时积分扣减失败: {str(e)}")
                
                # 重要：重新抛出GeneratorExit以避免Flask框架错误
                raise
            
            # 检查是否真正建立了数据流（仅在未取消的情况下验证）
            if not data_stream_established and not stream_canceled:
                error_msg = "API响应成功但未接收到任何数据，可能存在连接问题"
                logger.error(error_msg)
                yield format_error_message(error_msg)
                
            logger.info("数据流接收完成")
        
        except requests.exceptions.ConnectionError as e:
            logger.error(f"连接错误: {str(e)}")
            if not stream_canceled:
                error_msg = f"\n\n连接错误: 无法与API服务器建立稳定连接，请检查网络。详情: {str(e)}"
                # 添加连接中断但流已开始的标记
                if data_stream_established:
                    error_msg_with_flag = f"{error_msg} <!-- CONNECTION_INTERRUPTED_AFTER_STREAM -->"
                    yield format_error_message(error_msg_with_flag)
                else:
                    yield format_error_message(error_msg)
        except requests.exceptions.Timeout as e:
            logger.error(f"请求超时: {str(e)}")
            if not stream_canceled:
                error_msg = f"\n\n请求超时: API响应时间过长，请稍后重试。详情: {str(e)}"
                yield format_error_message(error_msg)
        except requests.exceptions.RequestException as e:
            logger.error(f"请求异常: {str(e)}")
            if not stream_canceled:
                error_msg = f"\n\n请求失败: {str(e)}"
                yield format_error_message(error_msg)
        except Exception as e:
            logger.error(f"处理响应时发生错误: {str(e)}")
            if not stream_canceled:
                error_msg = f"\n\n处理错误: {str(e)}"
                yield format_error_message(error_msg)
        
        finally:
            # 简化finally块处理，避免yield操作导致Flask错误
            # 如果流被取消且未报告token使用情况，只记录到日志（积分扣减已在GeneratorExit处理中完成）
            if stream_canceled and not token_usage_reported:
                logger.info("流已取消，token使用情况已在异常处理中记录")
                
            # 只在流处理正常结束且未报告token时报告token使用情况
            elif not token_usage_reported and not stream_canceled:
                generated_text = ''.join(generated_output)
                estimated_output_tokens = estimate_tokens(generated_text)
                
                token_usage['completion_tokens'] = estimated_output_tokens
                token_usage['total_tokens'] = token_usage['prompt_tokens'] + token_usage['completion_tokens']
                
                usage_info = format_token_usage(token_usage, is_partial=False, source="估算", 
                                              model_name=api_model_name, frontend_model_id=frontend_model_id)
                logger.info(usage_info)
                
                # 只在没有取消的情况下才yield
                if not stream_canceled:
                    yield usage_info
    
    # 使用stream_with_context确保流式响应正确关闭
    return Response(stream_with_context(generate()), content_type='text/plain; charset=utf-8')

# -------------- 服务初始化和关闭 --------------

# 全局变量跟踪初始化状态
api_service_initialized = False

def init_api_service():
    """初始化API服务"""
    global api_service_initialized
    
    # 检查是否已经初始化
    if api_service_initialized:
        logger.info("API服务已初始化，跳过重复初始化")
        return
    
    # 确保队列处理线程启动
    ensure_queue_processor_running()
    logger.info("API服务初始化完成")
    
    # 更新初始化状态
    api_service_initialized = True

def shutdown_api_service():
    """关闭API服务"""
    global api_service_initialized
    
    # 只有在已初始化的情况下才执行关闭操作
    if api_service_initialized:
        stop_queue_processor()
        logger.info("API服务已关闭")
        api_service_initialized = False