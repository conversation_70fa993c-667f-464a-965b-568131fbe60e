from flask import Blueprint, request, jsonify, current_app
import logging
import traceback
from datetime import datetime, timezone, timedelta
import supabase
from api.models_config import MODEL_CONFIG, FEATURE_TOKEN_ESTIMATES, CREDIT_COST_RATE, calculate_cost, get_model_by_id
from api.rate_limit_config import get_limit  # 导入集中配置的速率限制
import json
import math
from werkzeug.exceptions import RequestEntityTooLarge
from werkzeug.datastructures import FileStorage
import importlib.util
import os
from api.auth import verify_token
from api.payment import supabase_client, service_supabase_client
from api.token_utils import estimate_tokens
from api.credits_cache import get_cached_credits, cache_user_credits  # 添加缓存导入
from api.config import AUXILIARY_FEATURES_SKIP_CHECK_MINUTES

def get_user_credits_from_cache(user_id):
    """
    从后端缓存获取用户积分（安全的快速查询）
    
    Args:
        user_id: 用户ID
        
    Returns:
        int or None: 积分数量，如果缓存不存在返回None
    """
    try:
        return get_cached_credits(user_id)
    except Exception as e:
        logger.error(f"从缓存获取积分失败: {e}")
        return None

def force_refresh_user_credits_from_db(user_id):
    """
    强制从数据库刷新用户积分并更新缓存（用于登录时同步）
    
    Args:
        user_id: 用户ID
        
    Returns:
        int or None: 刷新后的积分数量
    """
    try:
        from api.credits_cache import cache_user_credits
        
        logger.info(f"🔄 [强制积分刷新] 开始从数据库刷新用户{user_id}积分")
        
        # 直接查询数据库，不使用缓存
        credits_response = service_supabase_client.table('user_credits').select('credits', 'updated_at').eq('user_id', user_id).execute()
        
        if credits_response.data and len(credits_response.data) > 0:
            credits = credits_response.data[0].get('credits', 0)
            updated_at = credits_response.data[0].get('updated_at')
            
            # 强制更新缓存
            cache_user_credits(user_id, credits)
            
            logger.info(f"✅ [强制积分刷新] 用户{user_id}积分已从数据库刷新: {credits}, 更新时间: {updated_at}")
            return credits
        else:
            logger.warning(f"⚠️ [强制积分刷新] 用户{user_id}在数据库中没有积分记录")
            # 缓存0积分，避免重复查询
            cache_user_credits(user_id, 0)
            return 0
            
    except Exception as e:
        logger.error(f"❌ [强制积分刷新] 刷新用户{user_id}积分失败: {e}")
        return None

def get_auxiliary_skip_check_status(user_id):
    """
    获取用户辅助功能免检查状态
    
    Args:
        user_id: 用户ID
        
    Returns:
        dict: {
            'can_skip': bool,  # 是否可以跳过检查
            'expires_at': timestamp,  # 免检查到期时间
            'remaining_minutes': int  # 剩余免检查分钟数
        }
    """
    try:
        from api.credits_cache import get_cached_credits
        
        # 检查缓存积分
        cached_credits = get_cached_credits(user_id)
        
        if cached_credits is None or cached_credits <= 0:
            return {
                'can_skip': False,
                'expires_at': None,
                'remaining_minutes': 0,
                'reason': 'no_credits'
            }
        
        # 检查是否有免检查记录
        skip_key = f"aux_skip_check:{user_id}"
        # 这里需要实现免检查时间的存储，可以使用Redis或者简单的内存缓存
        # 暂时返回基于积分的判断
        
        return {
            'can_skip': cached_credits > 0,
            'expires_at': None,  # 待实现
            'remaining_minutes': AUXILIARY_FEATURES_SKIP_CHECK_MINUTES,
            'reason': 'sufficient_credits'
        }
        
    except Exception as e:
        logger.error(f"获取辅助功能免检查状态失败: {e}")
        return {
            'can_skip': False,
            'expires_at': None,
            'remaining_minutes': 0,
            'reason': 'error'
        }

def set_auxiliary_skip_check_status(user_id, skip_duration_minutes=None):
    """
    设置用户辅助功能免检查状态
    
    Args:
        user_id: 用户ID
        skip_duration_minutes: 免检查持续分钟数，默认使用配置值
        
    Returns:
        bool: 设置是否成功
    """
    try:
        from api.config import AUXILIARY_FEATURES_SKIP_CHECK_MINUTES
        from datetime import datetime, timedelta
        
        if skip_duration_minutes is None:
            skip_duration_minutes = AUXILIARY_FEATURES_SKIP_CHECK_MINUTES
        
        # 计算到期时间
        expires_at = datetime.utcnow() + timedelta(minutes=skip_duration_minutes)
        
        # 这里需要实现免检查时间的存储
        # 可以使用Redis、数据库或内存缓存
        skip_key = f"aux_skip_check:{user_id}"
        
        logger.info(f"🕒 [动态免检查] 用户{user_id}设置{skip_duration_minutes}分钟免检查，到期时间: {expires_at}")
        
        # 暂时返回成功，待实现具体存储逻辑
        return True
        
    except Exception as e:
        logger.error(f"设置辅助功能免检查状态失败: {e}")
        return False

# 添加提示词模板缓存机制，避免重复导入app.py
_prompt_templates_cache = {}
_prompt_templates_loaded = False
_credits_initialized = False

def get_user_id_from_request():
    """
    从请求中获取用户ID的辅助函数
    支持从Authorization头中解析JWT获取用户ID
    """
    try:
        # 优先从Authorization头获取JWT
        auth_header = request.headers.get('Authorization')
        token = None
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.replace('Bearer ', '')
        else:
            # 尝试从HttpOnly Cookie获取session_token
            token = request.cookies.get('session_token')
            if token:
                logger.debug("从cookie获取到session_token 作为JWT")
            else:
                logger.warning("获取用户ID失败: 授权头和cookie均缺失")
                return None
        
        auth_token = token
        
        # 使用JWT获取用户信息
        if not supabase_client:
            logger.error("获取用户ID失败: Supabase客户端未初始化")
            return None
        
        user_response = supabase_client.auth.get_user(auth_token)
        user = user_response.user
        
        if not user:
            logger.warning("获取用户ID失败: 无效的JWT令牌")
            return None
        
        return user.id
        
    except Exception as e:
        error_msg = str(e)
        
        # 检查是否是令牌失效相关的错误
        if any(msg in error_msg for msg in [
            'Session from session_id claim in JWT does not exist',
            'invalid_token',
            'token_expired',
            'JWT expired',
            'Invalid token'
        ]):
            logger.warning(f"令牌已失效或过期: {error_msg}")
            return None
        else:
            logger.error(f"获取用户ID时出错: {error_msg}")
            return None

def get_prompt_template(operation):
    """获取提示词模板，使用缓存避免重复导入app.py"""
    global _prompt_templates_cache, _prompt_templates_loaded
    
    if not _prompt_templates_loaded:
        logger.info("一次性加载所有提示词模板到缓存")
        try:
            # 尝试直接从app模块导入，但通过安全的方式
            import sys
            
            # 优先从已加载的模块中获取
            if 'app' in sys.modules:
                app_module = sys.modules['app']
                logger.debug("从已加载的app模块获取提示词模板")
                
                # 提取所有提示词模板
                _prompt_templates_cache = {
                    'summary': getattr(app_module, 'summary_prompt', None),
                    'mindmap': getattr(app_module, 'mindmap_prompt', None),
                    'knowledgegraph': getattr(app_module, 'knowledgegraph_prompt', None),
                    'generate_timestamps': getattr(app_module, 'generate_timestamps_prompt', None)
                }
                
                _prompt_templates_loaded = True
                logger.info("已从已加载的app模块获取提示词模板")
            else:
                # 如果app模块没有加载，使用默认模板，避免重新执行app.py
                logger.warning("app模块未加载，使用默认提示词模板")
                _prompt_templates_cache = {
                    'summary': "请为以下内容生成摘要:\n\n{content}",
                    'mindmap': "请为以下内容生成思维导图:\n\n{content}",
                    'knowledgegraph': "请为以下内容生成知识图谱:\n\n{content}",
                    'generate_timestamps': "请为以下字幕生成时间戳:\n\n{content}"
                }
                _prompt_templates_loaded = True
                
        except Exception as e:
            logger.error(f"加载提示词模板失败: {str(e)}")
            # 提供默认的fallback模板
            _prompt_templates_cache = {
                'summary': "请为以下内容生成摘要:\n\n{content}",
                'mindmap': "请为以下内容生成思维导图:\n\n{content}",
                'knowledgegraph': "请为以下内容生成知识图谱:\n\n{content}",
                    'generate_timestamps': "请为以下字幕生成时间戳:\n\n{content}"
            }
            _prompt_templates_loaded = True
            logger.warning("使用默认fallback提示词模板")
    
    return _prompt_templates_cache.get(operation)

# 设置日志记录
logger = logging.getLogger(__name__)

# 创建积分管理蓝图
credits_bp = Blueprint('credits', __name__)

# 初始化Supabase客户端
supabase_client = None
service_supabase_client = None

# 定义API路由常量
API_ROUTES = {
    'check': '/api/credits/check',
    'deduct': '/api/credits/deduct',
    'get': '/api/credits/get',
    'add': '/api/credits/add',
    'history': '/api/credits/history',
    'calculate': '/api/credits/calculate'
}

# 添加安全的错误处理函数
def safe_error_response(exception, error_message="处理积分请求时发生错误", status_code=500):
    """
    安全的错误响应，避免暴露敏感信息
    """
    logger.error(f"{error_message}: {str(exception)}")
    logger.error(f"异常详情: {traceback.format_exc()}")
    return jsonify({
        "success": False,
        "error": error_message
    }), status_code

def init_credits_module(app, limiter):
    """初始化积分模块"""
    global supabase_client, service_supabase_client, _credits_initialized
    
    # 防止重复初始化
    if _credits_initialized:
        logger.info("Credits模块: 已初始化，跳过重复初始化")
        return
    
    # 初始化普通Supabase客户端
    supabase_url = app.config.get('SUPABASE_URL')
    supabase_key = app.config.get('SUPABASE_ANON_KEY')
    
    if supabase_url and supabase_key:
        try:
            # 创建普通Supabase客户端
            supabase_client = supabase.create_client(supabase_url, supabase_key)
            logger.info(f"Credits模块: Supabase客户端初始化成功: {supabase_url}")
            
            # 初始化服务角色Supabase客户端
            service_key = app.config.get('SUPABASE_SERVICE_ROLE_KEY_AUDIO') 
            if service_key:
                service_supabase_client = supabase.create_client(supabase_url, service_key)
                logger.info("Credits模块: 服务角色Supabase客户端初始化成功")
            else:
                logger.warning("Credits模块: 服务角色密钥缺失，将无法执行需要高权限的操作")
        except Exception as e:
            logger.error(f"Credits模块: 初始化Supabase客户端失败: {str(e)}")
    else:
        logger.warning("Credits模块: 缺少Supabase配置，数据库功能将不可用")

    # 应用速率限制
    try:
        limiter.limit(get_limit("credits", "get_credits"))(get_user_credits)
        limiter.limit(get_limit("credits", "check_credits"))(check_credits)  # 新增检查积分的速率限制
        limiter.limit(get_limit("credits", "deduct_credits"))(deduct_credits)
        limiter.limit(get_limit("credits", "add_credits"))(add_credits)
        limiter.limit(get_limit("credits", "credits_history"))(get_credits_history)
        logger.info("Credits模块: 已为积分路由添加速率限制")
    except Exception as e:
        logger.error(f"Credits模块: 添加速率限制时出错: {str(e)}")
    
    # 标记积分模块已初始化
    _credits_initialized = True
    logger.info("积分模块已注册")

def check_user_credits_for_operation(auth_token, operation, model_id, content=None, system_message=None, multimodal_info=None):
    """
    检查用户是否有足够的积分进行指定操作 - 优化版：支持缓存和多模态模型
    
    参数:
        auth_token: 用户的认证令牌
        operation: 操作类型，如 'summary', 'mindmap', 'audio_analysis', 'smart_qa'
        model_id: 模型ID，如 'zhipu_flash', 'qwen_omni_turbo'
        content: 操作的内容文本，用于估算token数量
        system_message: 系统提示文本，用于估算token数量
        multimodal_info: 多模态信息字典，包含：
            - audio_duration: 音频时长（秒）
            - audio_file_size: 音频文件大小（字节）
            - has_audio: 是否包含音频
            - has_image: 是否包含图片
        
    返回:
        tuple: (has_enough_credits, user_id, credits_info)
            - has_enough_credits: 布尔值，表示是否有足够积分
            - user_id: 用户ID，如验证失败则为None
            - credits_info: 包含当前积分和所需积分的字典
    """
    try:
        # 导入缓存模块
        from api.credits_cache import get_cached_credits, cache_user_credits
        
        if not supabase_client:
            logger.error("普通Supabase客户端未初始化，无法检查积分")
            return False, None, {"error": "服务暂时不可用"}
        
        if not service_supabase_client:
            logger.warning("服务角色Supabase客户端未初始化，尝试使用普通客户端")
            # 可以继续执行，但功能可能受限
        
        # 使用JWT获取用户信息
        try:
            user_response = supabase_client.auth.get_user(auth_token)
            
            # 更宽松的响应检查
            user = None
            if user_response and hasattr(user_response, 'user'):
                user = user_response.user
            elif user_response and hasattr(user_response, 'data'):
                # 某些版本的Supabase客户端返回.data
                user = getattr(user_response.data, 'user', None)
            
            if not user or not hasattr(user, 'id'):
                logger.warning(f"无法验证用户JWT - 响应类型: {type(user_response)}, 用户对象: {user}")
                return False, None, {"error": "无效的用户令牌"}
            
            user_id = user.id
            logger.info(f"用户身份验证成功: {user_id}")
        except Exception as auth_error:
            logger.error(f"验证JWT出错: {str(auth_error)}")
            return False, None, {"error": "验证用户身份失败"}
        
        # 计算所需积分
        import math
        
        # 如果是EPUB提取操作，需要5积分
        if operation == 'epub_extract':
            required_credits = 5  # 修改为需要5积分
            logger.info(f"EPUB提取操作需要{required_credits}积分，用户ID={user_id}")
        
        # 如果是字幕问答操作，需要1积分
        elif operation == 'subtitle_chat':
            required_credits = 1
            logger.info(f"字幕问答操作需要{required_credits}积分，用户ID={user_id}")
            
        # 如果是语法分析操作，需要2积分
        elif operation == 'grammar_analysis':
            required_credits = 2
            logger.info(f"语法分析操作需要{required_credits}积分，用户ID={user_id}")
            
        else:
            # 如果提供了内容，使用token_utils进行更精确的估算
            if content:
                from api.token_utils import estimate_tokens
                
                # 获取模型配置
                model_config = get_model_by_id(model_id)
                
                # 检查是否为多模态模型
                if model_config.get("multimodal", False) and multimodal_info:
                    # 使用多模态成本计算
                    audio_duration = multimodal_info.get("audio_duration", 0)
                    text_content = content or ""
                    
                    logger.info(f"多模态模型积分检查: {model_id}, 操作={operation}, 音频时长={audio_duration}秒")
                    
                    from api.models_config import calculate_multimodal_cost
                    cost_info = calculate_multimodal_cost(model_id, operation, audio_duration, text_content)
                    
                    usd_cost = cost_info["total_cost"]
                    logger.info(f"多模态成本详情: {cost_info}")
                    logger.info(f"多模态总成本=${usd_cost:.6f}")
                    
                    # 转换为积分
                    required_credits = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))
                    logger.info(f"积分计算: 最大值(1, 向上取整(${usd_cost:.6f} / ${CREDIT_COST_RATE:.6f})) = {required_credits}积分")
                    
                else:
                    # 传统单模态模型处理
                    # 记录模型信息 - 使用科学计数法或更高精度显示
                    input_price = model_config['pricing']['input']
                    output_price = model_config['pricing']['output']
                    logger.info(f"模型信息: {model_id}, 输入价格={input_price:.2e} (约{input_price*1_000_000:.2f}元/百万tokens), 输出价格={output_price:.2e} (约{output_price*1_000_000:.2f}元/百万tokens)")
                    
                    # 检查是否为问答操作类型
                    chat_operations = ['graph_node_chat', 'node_chat', 'text_chat']
                    if operation in chat_operations:
                        # 对于问答操作，直接使用提供的内容进行token估算
                        logger.info(f"检测到问答操作: {operation}")
                        input_tokens = estimate_tokens(content)
                        logger.info(f"问答内容token估算: {input_tokens}个tokens")
                        
                        total_input_tokens = input_tokens
                        
                        # 问答操作的输出token估算（一般比较简短）
                        if operation == 'graph_node_chat':
                            output_tokens = int(input_tokens * 0.3)  # 知识图谱问答可能输出较多
                            logger.info(f"知识图谱问答输出token估算: {input_tokens} * 0.3 = {output_tokens}个tokens")
                        elif operation == 'node_chat':
                            output_tokens = int(input_tokens * 0.25)  # 脑图节点问答
                            logger.info(f"脑图节点问答输出token估算: {input_tokens} * 0.25 = {output_tokens}个tokens")
                        elif operation == 'text_chat':
                            output_tokens = int(input_tokens * 0.2)  # 文本问答
                            logger.info(f"文本问答输出token估算: {input_tokens} * 0.2 = {output_tokens}个tokens")
                        else:
                            output_tokens = int(input_tokens * 0.2)  # 默认问答输出估算
                            logger.info(f"其他问答操作输出token估算: {input_tokens} * 0.2 = {output_tokens}个tokens")
                    else:
                        # 对于非问答操作，使用原有的提示词模板逻辑
                        # 获取特定操作类型的提示词模板
                        prompt_template = ""
                        language = "中文"  # 
                        
                        # 使用缓存的提示词模板，避免重复导入app.py
                        prompt_template = get_prompt_template(operation)
                        if prompt_template:
                            logger.info(f"已从缓存获取{operation}提示词模板")
                        else:
                            logger.warning(f"无法获取{operation}提示词模板，将使用默认估算")
                        
                        # 根据操作类型估算提示词token数
                        formatted_prompt = ""
                        if prompt_template:
                            # 使用实际提示词模板替换占位符
                            formatted_prompt = prompt_template.replace('{content}', content).replace('{language}', language)
                            # 估算提示词token数量
                            prompt_tokens = estimate_tokens(formatted_prompt)
                            logger.info(f"基于实际提示词模板的输入token估算: {prompt_tokens}个tokens")
                            
                            input_tokens = prompt_tokens  # 使用真实提示词token数
                        else:
                            # 如果无法获取提示词模板，则直接估算内容token数
                            input_tokens = estimate_tokens(content)
                            logger.info(f"内容token估算: {input_tokens}个tokens")
                        
                        total_input_tokens = input_tokens
                    
                        if system_message:
                            system_tokens = estimate_tokens(system_message)
                            logger.info(f"系统提示token估算: {system_tokens}个tokens")
                            total_input_tokens += system_tokens
                        
                        # 估算输出token数 - 直接根据输入比例计算
                        if operation == 'summary':
                            output_tokens = int(input_tokens * 0.15)
                            logger.info(f"摘要操作输出token估算: {input_tokens} * 0.15 = {output_tokens}个tokens")
                        elif operation == 'mindmap':
                            output_tokens = int(input_tokens * 0.1)
                            logger.info(f"思维导图操作输出token估算: {input_tokens} * 0.1 = {output_tokens}个tokens")
                        elif operation == 'knowledgegraph':
                            output_tokens = int(input_tokens * 0.9)
                            logger.info(f"知识图谱操作输出token估算: {input_tokens} * 0.9 = {output_tokens}个tokens")
                        else:
                            output_tokens = int(input_tokens / 3)
                            logger.info(f"其他操作输出token估算: {input_tokens} / 5 = {output_tokens}个tokens")
                    
                    logger.info(f"总输入token数: {total_input_tokens}个tokens")
                    
                    # 传统单模态成本计算
                    input_cost = total_input_tokens * input_price
                    output_cost = output_tokens * output_price
                    usd_cost = input_cost + output_cost
                    
                    logger.info(f"成本计算: 输入成本=${input_cost:.6f} ({total_input_tokens} * ${input_price:.6f}), 输出成本=${output_cost:.6f} ({output_tokens} * ${output_price:.6f})")
                    logger.info(f"总美元成本=${usd_cost:.6f}")
                    
                    # 转换为积分
                    required_credits = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))
                    logger.info(f"积分计算: 最大值(1, 向上取整(${usd_cost:.6f} / ${CREDIT_COST_RATE:.6f})) = {required_credits}积分")
            else:
                # 如果没有提供内容，使用默认估算
                logger.info(f"未提供内容，使用默认估算计算积分")
                # 计算美元成本
                usd_cost = calculate_cost(model_id, operation)
                logger.info(f"默认估算美元成本=${usd_cost:.6f}")
                
                # 转换为积分
                required_credits = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))
                logger.info(f"积分计算: 最大值(1, 向上取整(${usd_cost:.6f} / ${CREDIT_COST_RATE:.6f})) = {required_credits}积分")
        
        # 如果不需要积分，直接返回成功
        if required_credits <= 0:
            return True, user_id, {
                "current_credits": 0,
                "required_credits": 0,
                "message": "此操作不需要积分"
            }
        
        # 优先尝试从缓存获取积分
        cached_credits = get_cached_credits(user_id)
        current_credits = None
        
        if cached_credits is not None:
            logger.info(f"从缓存获取用户积分: 用户={user_id}, 缓存积分={cached_credits}, 需要积分={required_credits}")
            current_credits = cached_credits
            
            # 如果缓存积分明显充足（有余量），直接通过
            if current_credits >= (required_credits + 5):
                logger.info(f"缓存积分充足，跳过数据库查询: 当前={current_credits}, 需要={required_credits}")
                return True, user_id, {
                    "current_credits": current_credits,
                    "required_credits": required_credits
                }
            
            # 如果缓存积分明显不足，也直接返回
            if current_credits < required_credits:
                logger.warning(f"缓存积分不足: 当前={current_credits}, 需要={required_credits}")
                return False, user_id, {
                    "current_credits": current_credits,
                    "required_credits": required_credits,
                    "message": "积分不足"
                }
        
        # 缓存不存在或积分接近边界时，查询数据库
        logger.info(f"缓存不存在或积分接近边界，查询数据库确认: 用户={user_id}")
        
        if service_supabase_client:
            credits_response = service_supabase_client.table('user_credits').select('credits, updated_at').eq('user_id', user_id).execute()
        else:
            logger.warning("使用普通客户端查询积分")
            credits_response = supabase_client.table('user_credits').select('credits, updated_at').eq('user_id', user_id).execute()
        
        # 用户有积分记录
        if credits_response.data and len(credits_response.data) > 0:
            current_credits = credits_response.data[0].get('credits', 0)
            
            # 更新缓存
            cache_user_credits(user_id, current_credits)
            logger.info(f"已更新用户积分缓存: 用户={user_id}, 积分={current_credits}")
            
            logger.info(f"检查用户积分: 当前={current_credits}, 需要={required_credits}, 操作={operation}, 模型={model_id}")
            
            # 检查积分是否足够
            if current_credits >= required_credits:
                return True, user_id, {
                    "current_credits": current_credits,
                    "required_credits": required_credits
                }
            else:
                logger.warning(f"用户 {user_id} 积分不足: 当前={current_credits}, 需要={required_credits}")
                return False, user_id, {
                    "current_credits": current_credits,
                    "required_credits": required_credits,
                    "message": "积分不足"
                }
        
        # 用户没有积分记录，但触发器应该已经创建了，可能是查询时机问题
        # 进行重新查询，确保数据一致性
        logger.warning(f"用户 {user_id} 没有积分记录，重新查询确认")
        
        try:
            # 等待1秒后重新查询，避免时序问题
            import time
            time.sleep(1)
            
            if service_supabase_client:
                credits_recheck = service_supabase_client.table('user_credits').select('credits, updated_at').eq('user_id', user_id).execute()
            else:
                credits_recheck = supabase_client.table('user_credits').select('credits, updated_at').eq('user_id', user_id).execute()
            
            if credits_recheck.data and len(credits_recheck.data) > 0:
                current_credits = credits_recheck.data[0].get('credits', 0)
                logger.info(f"重新查询成功，用户 {user_id} 当前积分: {current_credits}")
                
                # 检查积分是否足够
                if current_credits >= required_credits:
                    return True, user_id, {
                        "current_credits": current_credits,
                        "required_credits": required_credits
                    }
                else:
                    logger.warning(f"用户 {user_id} 积分不足: 当前={current_credits}, 需要={required_credits}")
                    return False, user_id, {
                        "current_credits": current_credits,
                        "required_credits": required_credits,
                        "message": "积分不足"
                    }
        except Exception as recheck_error:
            logger.error(f"重新查询积分失败: {str(recheck_error)}")
        
        # 如果重新查询还是没有，说明确实有问题，返回明确的错误
        logger.error(f"用户 {user_id} 积分记录确实不存在，可能是数据库触发器未正确工作")
        return False, user_id, {
            "current_credits": 0,
            "required_credits": required_credits,
            "message": "积分记录异常，请刷新页面重试或联系客服",
            "error_code": "CREDITS_RECORD_MISSING"
        }
    
    except Exception as e:
        logger.exception(f"检查用户积分时出错: {str(e)}")
        return False, None, {"error": "检查积分时出错"}

@credits_bp.route('/api/credits/get', methods=['GET'])
@credits_bp.route('/api/credits/get-user-credits', methods=['GET'])  # 添加备用端点
def get_user_credits():
    """
    获取用户积分
    优先从缓存获取，缓存未命中时查询数据库并更新缓存
    """
    try:
        # 从请求头获取用户ID
        user_id = get_user_id_from_request()
        if not user_id:
            logger.error("[积分API] 无法获取用户ID")
            return jsonify({
                "success": False,
                "error": "用户未认证"
            }), 401
        
        logger.info(f"[积分API] 获取用户积分请求: 用户ID={user_id}")
        
        # 优先从缓存获取积分
        cached_credits = get_cached_credits(user_id)
        if cached_credits is not None:
            logger.info(f"[积分API] 从缓存返回用户积分: {cached_credits}")
            return jsonify({
                "success": True,
                "credits": cached_credits,
                "from_cache": True
            })
        
        logger.info(f"[积分API] 缓存未命中，查询数据库: 用户ID={user_id}")
        
        # 缓存未命中，查询数据库
        credits_response = service_supabase_client.table('user_credits').select('credits', 'updated_at').eq('user_id', user_id).execute()
        
        if not credits_response.data:
            logger.warning(f"[积分API] 用户 {user_id} 没有积分记录，初始化积分")
            # 用户没有积分记录，创建默认积分记录
            now = datetime.utcnow().isoformat()
            default_credits = 100  # 默认积分
            
            # 插入新的积分记录
            insert_response = service_supabase_client.table('user_credits').insert({
                'user_id': user_id,
                'credits': default_credits,
                'created_at': now,
                'updated_at': now
            }).execute()
            
            if insert_response.data:
                # 缓存新创建的积分
                cache_user_credits(user_id, default_credits)
                logger.info(f"[积分API] 用户积分初始化成功: {default_credits}")
                
                return jsonify({
                    "success": True,
                    "credits": default_credits,
                    "updated_at": now,
                    "from_cache": False,
                    "initialized": True
                })
            else:
                logger.error(f"[积分API] 初始化用户积分失败")
                return jsonify({
                    "success": False,
                    "error": "初始化积分失败"
                }), 500
        
        # 用户有积分记录
        if credits_response.data and len(credits_response.data) > 0:
            credits = credits_response.data[0].get('credits', 0)
            updated_at = credits_response.data[0].get('updated_at')
            
            # 尝试获取积分详情（包括到期信息）
            try:
                detail_response = service_supabase_client.rpc('get_user_credits_detail', {'p_user_id': user_id}).execute()
                
                if detail_response.data and len(detail_response.data) > 0:
                    detail = detail_response.data[0]
                    earliest_expiry = detail.get('earliest_expiry')
                    batch_count = detail.get('batch_count', 0)
                    
                    # 将查询结果缓存起来
                    cache_user_credits(user_id, credits)
                    logger.info(f"[积分API] 已缓存用户积分: 用户={user_id}, 积分={credits}, 到期时间={earliest_expiry}")
                    
                    return jsonify({
                        "success": True,
                        "credits": credits,
                        "updated_at": updated_at,
                        "earliest_expiry": earliest_expiry,
                        "batch_count": batch_count,
                        "from_cache": False,
                        "system_version": "new"
                    })
            except Exception as detail_error:
                logger.warning(f"[积分API] 获取积分详情失败，使用传统方式: {str(detail_error)}")
            
            # 回退到传统方式
            cache_user_credits(user_id, credits)
            logger.info(f"[积分API] 已缓存用户积分: 用户={user_id}, 积分={credits}")
            
            logger.info(f"[积分API] 返回用户积分: {credits}, 更新时间: {updated_at}")
            return jsonify({
                "success": True,
                "credits": credits,
                "updated_at": updated_at,
                "from_cache": False,
                "system_version": "legacy"
            })
            
    except Exception as query_error:
        logger.error(f"[积分API] 查询用户积分时出错: {str(query_error)}")
        logger.error(f"[积分API] 查询异常详情: {traceback.format_exc()}")
        return jsonify({
            "success": False,
            "error": "查询积分时出错"
        }), 500

@credits_bp.route('/api/credits/get-fast', methods=['GET'])
def get_user_credits_fast():
    """
    快速获取用户积分
    优先从缓存获取，缓存未命中时自动查询数据库并同步缓存
    用于菜单点击等需要快速响应的场景
    """
    try:
        # 从请求头获取用户ID
        user_id = get_user_id_from_request()
        if not user_id:
            logger.error("[快速积分API] 无法获取用户ID")
            return jsonify({
                "success": False,
                "error": "用户未认证"
            }), 401
        
        logger.info(f"[快速积分API] 快速获取用户积分请求: 用户ID={user_id}")
        
        # 优先从缓存获取积分
        cached_credits = get_cached_credits(user_id)
        if cached_credits is not None:
            logger.info(f"[快速积分API] 从缓存返回用户积分: {cached_credits}")
            return jsonify({
                "success": True,
                "credits": cached_credits,
                "from_cache": True,
                "fast_mode": True
            })
        
        # 缓存未命中，立即查询数据库并同步缓存
        logger.info(f"[快速积分API] 缓存未命中，查询数据库并同步缓存: 用户ID={user_id}")
        
        try:
            # 查询数据库获取积分
            credits_response = service_supabase_client.table('user_credits').select('credits', 'updated_at').eq('user_id', user_id).execute()
            
            if credits_response.data and len(credits_response.data) > 0:
                credits = credits_response.data[0].get('credits', 0)
                updated_at = credits_response.data[0].get('updated_at')
                
                # 立即同步到缓存
                cache_user_credits(user_id, credits)
                logger.info(f"[快速积分API] 数据库查询成功并已缓存: 用户={user_id}, 积分={credits}")
                
                return jsonify({
                    "success": True,
                    "credits": credits,
                    "updated_at": updated_at,
                    "from_cache": False,
                    "fast_mode": True,
                    "cache_synced": True
                })
            else:
                # 用户没有积分记录，返回0并缓存
                logger.warning(f"[快速积分API] 用户 {user_id} 没有积分记录，返回0")
                cache_user_credits(user_id, 0)
                return jsonify({
                    "success": True,
                    "credits": 0,
                    "from_cache": False,
                    "fast_mode": True,
                    "cache_synced": True,
                    "message": "用户积分记录不存在，已创建缓存"
                })
                
        except Exception as db_error:
            logger.error(f"[快速积分API] 数据库查询失败: {str(db_error)}")
            # 数据库查询失败，返回错误但不是404
            return jsonify({
                "success": False,
                "error": "数据库查询失败",
                "fast_mode": True,
                "suggest_retry": True
            }), 500
            
    except Exception as query_error:
        logger.error(f"[快速积分API] 快速查询用户积分时出错: {str(query_error)}")
        return jsonify({
            "success": False,
            "error": "查询积分时出错"
        }), 500

@credits_bp.route('/api/credits/check', methods=['POST'])
def check_credits():
    """检查用户积分是否足够（不扣减）"""
    try:
        # 添加详细的初始化状态日志
        logger.info(f"积分检查开始 - supabase_client状态: {'已初始化' if supabase_client else '未初始化'}")
        logger.info(f"积分检查开始 - service_supabase_client状态: {'已初始化' if service_supabase_client else '未初始化'}")
        
        data = request.json
        operation = data.get('operation')
        if not operation:
            return jsonify({"success": False, "error": "缺少必要参数: operation"}), 400
        model = data.get('model', 'zhipu_api')
        token_usage = data.get('token_usage')
        content = data.get('content')  # 新增：获取内容参数
        
        # 从Authorization头获取JWT
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            logger.warning("检查积分请求缺少有效的Authorization头")
            return jsonify({
                "success": False,
                "error": "未授权的请求"
            }), 401
        
        auth_token = auth_header.replace('Bearer ', '')
        
        # 如果提供了content参数，使用更精确的积分估算（已弃用，问答现在使用统一的安全模式）
        if content:
            logger.warning(f"检测到传递content参数的积分检查，这可能存在安全风险。操作={operation}, 模型={model}")
            # 忽略content参数，继续使用简单的积分检查
        
        # 如果没有content参数，使用原有的简单积分检查逻辑
        logger.info(f"积分检查: 使用简单估算 - 操作={operation}, 模型={model}")
        
        # 计算所需积分逻辑与deduct_credits相同
        if isinstance(token_usage, (int, float)):
            credits_to_deduct = int(token_usage)
        else:
            token_usage = token_usage or {}
            import math
            # 如果有实际 token_usage dict 数据
            if isinstance(token_usage, dict) and (token_usage.get('input_tokens') is not None or token_usage.get('output_tokens') is not None):
                cfg = MODEL_CONFIG.get(model, {})
                pricing = cfg.get('pricing', {})
                inp = token_usage.get('input_tokens', 0)
                out = token_usage.get('output_tokens', 0)
                usd_cost = inp * pricing.get('input', 0) + out * pricing.get('output', 0)
                credits_to_deduct = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))
            else:
                # 无 token_usage 或不支持类型，使用预估
                usd_cost = calculate_cost(model, operation)
                credits_to_deduct = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))
        
        # 确保至少扣 1 分
        credits_to_deduct = max(1, int(credits_to_deduct))
        logger.info(f"计算得出需要积分: {credits_to_deduct}")
        
        # 使用JWT获取用户信息
        try:
            user_response = supabase_client.auth.get_user(auth_token)
            user = user_response.user
            
            if not user:
                logger.warning("无法验证用户JWT")
                return jsonify({
                    "success": False,
                    "error": "无效的用户令牌"
                }), 401
            
            user_id = user.id
            logger.info(f"用户身份验证成功: {user_id}")
        except Exception as auth_error:
            logger.error(f"验证JWT出错: {str(auth_error)}")
            return jsonify({
                "success": False,
                "error": "验证用户身份失败"
            }), 401
        
        # 查询用户当前积分
        try:
            logger.info(f"开始查询用户 {user_id} 的积分")
            
            # 选择可用的Supabase客户端
            if service_supabase_client:
                logger.info("使用服务角色客户端查询积分")
                credits_response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
            else:
                logger.warning("服务角色客户端未初始化，使用普通客户端查询积分")
                credits_response = supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
            
            # 用户没有积分记录
            if not credits_response.data:
                logger.warning(f"用户 {user_id} 没有积分记录")
                return jsonify({
                    "success": False,
                    "error": "用户积分记录不存在"
                }), 404
            
            current_credits = credits_response.data[0].get('credits', 0)
            logger.info(f"用户当前积分: {current_credits}")
            
            # 检查积分是否足够
            if current_credits < credits_to_deduct:
                logger.warning(f"用户 {user_id} 积分不足: 当前={current_credits}, 需要={credits_to_deduct}")
                return jsonify({
                    "success": False,
                    "error": "积分不足",
                    "current_credits": current_credits,
                    "required_credits": credits_to_deduct
                }), 400
            
            # 积分充足，返回成功
            logger.info(f"积分检查通过: 用户={user_id}, 操作={operation}, 当前积分={current_credits}, 需要={credits_to_deduct}")
            return jsonify({
                "success": True,
                "credits": current_credits,
                "current_credits": current_credits,
                "required_credits": credits_to_deduct,
                "message": "积分充足"
            })
            
        except Exception as query_error:
            logger.error(f"查询用户积分失败: {str(query_error)}")
            logger.error(f"查询错误详情: {traceback.format_exc()}")
            return safe_error_response(query_error, "检查积分时出错")
        
    except Exception as e:
        logger.error(f"积分检查总体异常: {str(e)}")
        logger.error(f"异常详情: {traceback.format_exc()}")
        return safe_error_response(e)

@credits_bp.route(API_ROUTES['deduct'], methods=['POST'])
def deduct_credits():
    """扣除用户积分"""
    try:
        data = request.json
        operation = data.get('operation')
        if not operation:
            return jsonify({"success": False, "error": "缺少必要参数: operation"}), 400
        model = data.get('model', 'zhipu_api')
        token_usage = data.get('token_usage')
        
        # 支持客户端直接传入数字 credits
        if isinstance(token_usage, (int, float)):
            credits_to_deduct = int(token_usage)
        else:
            token_usage = token_usage or {}
            import math
            # 如果有实际 token_usage dict 数据
            if isinstance(token_usage, dict) and (token_usage.get('input_tokens') is not None or token_usage.get('output_tokens') is not None):
                cfg = MODEL_CONFIG.get(model, {})
                pricing = cfg.get('pricing', {})
                inp = token_usage.get('input_tokens', 0)
                out = token_usage.get('output_tokens', 0)
                usd_cost = inp * pricing.get('input', 0) + out * pricing.get('output', 0)
                credits_to_deduct = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))
            else:
                # 无 token_usage 或不支持类型，使用预估
                usd_cost = calculate_cost(model, operation)
                credits_to_deduct = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))

        # 确保至少扣 1 分
        credits_to_deduct = max(1, int(credits_to_deduct))
        
        # 提取音频信息，根据操作类型决定显示内容
        raw_audio_info = data.get('book_name', '')
        chapter_title = data.get('chapter_title', '')  # 保持向后兼容
        
        # 根据操作类型处理音频信息显示
        if operation == 'audio_analysis':
            # 录音分析显示"录音片段"
            audio_info = '录音片段'
        elif operation == 'smart_qa' and raw_audio_info:
            # Smart Q&A显示音频文件名称
            audio_info = raw_audio_info
        else:
            # 其他情况保持原有逻辑
            audio_info = raw_audio_info
        
        # 为了保持数据库兼容性，仍使用book_name字段名，但存储音频信息
        book_name = audio_info
        
        # 从Authorization头获取JWT
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            logger.warning("扣除积分请求缺少有效的Authorization头")
            return jsonify({
                "success": False,
                "error": "未授权的请求"
            }), 401
        
        auth_token = auth_header.replace('Bearer ', '')
        
        # 使用JWT获取用户信息
        try:
            user_response = supabase_client.auth.get_user(auth_token)
            user = user_response.user
            
            if not user:
                logger.warning("无法验证用户JWT")
                return jsonify({
                    "success": False,
                    "error": "无效的用户令牌"
                }), 401
            
            user_id = user.id
        except Exception as auth_error:
            logger.error(f"验证JWT出错: {str(auth_error)}")
            return jsonify({
                "success": False,
                "error": "验证用户身份失败"
            }), 401
        
        # 如果服务角色客户端不可用，无法继续
        if not service_supabase_client:
            logger.error("服务角色客户端未初始化，无法扣除积分")
            return jsonify({
                "success": False,
                "error": "服务暂时不可用"
            }), 503
        
        # 查询用户当前积分
        try:
            credits_response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
            
            # 用户没有积分记录
            if not credits_response.data:
                logger.warning(f"用户 {user_id} 没有积分记录")
                return jsonify({
                    "success": False,
                    "error": "用户积分记录不存在"
                }), 404
            
            current_credits = credits_response.data[0].get('credits', 0)
            logger.info(f"用户当前积分: {current_credits}")
            
            # 检查积分是否足够
            if current_credits < credits_to_deduct:
                logger.warning(f"用户 {user_id} 积分不足: 当前={current_credits}, 需要={credits_to_deduct}")
                return jsonify({
                    "success": False,
                    "error": "积分不足",
                    "current_credits": current_credits,
                    "required_credits": credits_to_deduct
                }), 402
            
            # 扣除积分
            new_credits = current_credits - credits_to_deduct
            now = datetime.utcnow().isoformat() + 'Z'  # 'Z' 表示UTC时区
            
            # 更新积分
            update_response = service_supabase_client.table('user_credits').update({
                'credits': new_credits,
                'updated_at': now
            }).eq('user_id', user_id).execute()
            
            if not update_response.data:
                logger.error(f"更新用户 {user_id} 积分失败")
                return jsonify({
                    "success": False,
                    "error": "更新积分失败"
                }), 500
            
                        # 记录积分操作
            try:
                # 获取模型的显示名称
                model_display_name = model
                if model in MODEL_CONFIG:
                    model_config = MODEL_CONFIG[model]
                    model_display_name = model_config.get('i18n_prefix', model_config.get('name', model))
                    if not model_display_name.startswith("main.models."):
                        model_display_name = f"main.models.{model}"
                
                # 基础记录字段
                operation_record = {
                    'user_id': user_id,
                    'operation_type': operation,
                    'model': model_display_name,
                    'credits_amount': -credits_to_deduct,
                    'operation_time': now,
                    'current_credits': new_credits
                }
                
                # 尝试先检查表结构是否支持扩展字段
                try:
                    test_record = operation_record.copy()
                    if book_name:
                        test_record['book_name'] = book_name
                    if chapter_title:
                        test_record['chapter_title'] = chapter_title
                    
                    # 尝试插入完整记录
                    service_supabase_client.table('credit_operations').insert(test_record).execute()
                    logger.info(f"记录积分操作成功: 用户={user_id}, 操作={operation}, 扣除={credits_to_deduct}")
                    
                except Exception as schema_error:
                    # 如果字段不存在导致错误，则尝试只插入基础字段
                    if 'chapter_title' in str(schema_error) or 'book_name' in str(schema_error):
                        logger.warning(f"数据库表不支持扩展字段，使用基础字段插入: {str(schema_error)}")
                        service_supabase_client.table('credit_operations').insert(operation_record).execute()
                        logger.info(f"记录积分操作成功（基础字段）: 用户={user_id}, 操作={operation}, 扣除={credits_to_deduct}")
                    else:
                        # 如果是其他错误，重新抛出
                        raise schema_error
                        
            except Exception as record_error:
                logger.warning(f"记录积分操作失败，但积分已扣除: {str(record_error)}")
            
            return jsonify({
                "success": True,
                "credits": new_credits,
                "deducted": credits_to_deduct
            })
            
        except Exception as query_error:
            logger.error(f"查询或更新用户积分失败: {str(query_error)}")
            return safe_error_response(query_error, "处理积分扣除时出错")
        
    except Exception as e:
        return safe_error_response(e)

@credits_bp.route('/api/credits/add', methods=['POST'])
def add_credits():
    """添加用户积分（仅限管理员）"""
    try:
        # 获取请求数据
        data = request.json
        
        # 验证必要参数
        required_fields = ['userId', 'credits', 'adminKey']
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            return jsonify({
                "success": False,
                "error": f"缺少必要参数: {', '.join(missing_fields)}"
            }), 400
        
        user_id = data.get('userId')
        credits_to_add = data.get('credits')
        admin_key = data.get('adminKey')
        reason = data.get('reason', 'admin_add')
        
        # 验证管理员密钥
        expected_admin_key = current_app.config.get('ADMIN_API_KEY')
        if not expected_admin_key or admin_key != expected_admin_key:
            logger.warning(f"尝试使用无效的管理员密钥添加积分: {admin_key}")
            return jsonify({
                "success": False,
                "error": "无效的管理员密钥"
            }), 403
        
        # 验证积分值为正数
        if not isinstance(credits_to_add, (int, float)) or credits_to_add <= 0:
            return jsonify({
                "success": False,
                "error": "积分值必须为正数"
            }), 400
        
        # 如果服务角色客户端不可用，无法继续
        if not service_supabase_client:
            logger.error("服务角色客户端未初始化，无法添加积分")
            return jsonify({
                "success": False,
                "error": "服务暂时不可用"
            }), 503
        
        # 查询用户当前积分
        try:
            credits_response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
            
            now = datetime.utcnow().isoformat() + 'Z'  # 统一使用UTC时间格式
            
            # 用户没有积分记录，创建新记录
            if not credits_response.data:
                new_credits = credits_to_add
                
                service_supabase_client.table('user_credits').insert({
                    'user_id': user_id,
                    'credits': new_credits,
                    'created_at': now,
                    'updated_at': now
                }).execute()
                
                logger.info(f"为用户 {user_id} 创建了新的积分记录: {new_credits}")
            else:
                # 更新现有积分
                current_credits = credits_response.data[0].get('credits', 0)
                new_credits = current_credits + credits_to_add
                
                service_supabase_client.table('user_credits').update({
                    'credits': new_credits,
                    'updated_at': now
                }).eq('user_id', user_id).execute()
                
                logger.info(f"更新用户 {user_id} 积分: {current_credits} -> {new_credits}")
            
            # 记录积分操作
            try:
                operation_record = {
                    'user_id': user_id,
                    'operation_type': 'admin_add',
                    'credits_amount': credits_to_add,  # 正数表示添加
                    'operation_time': now,
                    'current_credits': new_credits,
                    'description': reason
                }
                
                service_supabase_client.table('credit_operations').insert(operation_record).execute()
                logger.info(f"记录积分操作成功: 用户={user_id}, 操作=admin_add, 添加={credits_to_add}")
            except Exception as record_error:
                # 记录错误但不影响主流程
                logger.warning(f"记录积分操作失败，但积分已添加: {str(record_error)}")
            
            return jsonify({
                "success": True,
                "credits": new_credits,
                "added": credits_to_add
            })
            
        except Exception as query_error:
            logger.error(f"查询或更新用户积分失败: {str(query_error)}")
            return safe_error_response(query_error, "处理积分添加时出错")
        
    except Exception as e:
        return safe_error_response(e)

@credits_bp.route('/api/credits/history', methods=['GET'])
def get_credits_history():
    """获取用户积分历史"""
    try:
        # 从Authorization头获取JWT
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            logger.warning("获取积分历史请求缺少有效的Authorization头")
            return jsonify({
                "success": False,
                "error": "未授权的请求"
            }), 401
        
        auth_token = auth_header.replace('Bearer ', '')
        
        # 使用JWT获取用户信息
        try:
            user_response = supabase_client.auth.get_user(auth_token)
            user = user_response.user
            
            if not user:
                logger.warning("无法验证用户JWT")
                return jsonify({
                    "success": False,
                    "error": "无效的用户令牌"
                }), 401
            
            user_id = user.id
        except Exception as auth_error:
            logger.error(f"验证JWT出错: {str(auth_error)}")
            return jsonify({
                "success": False,
                "error": "验证用户身份失败"
            }), 401
        
        # 获取分页参数
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('pageSize', 10))
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 查询用户积分历史
        try:
            # 首先检查服务角色客户端是否可用
            if not service_supabase_client:
                logger.error("服务角色客户端未初始化，无法查询积分历史")
                return jsonify({
                    "success": False,
                    "error": "服务暂时不可用"
                }), 503
            
            # 优化：一次查询，避免重复请求
            logger.info(f"用户 {user_id} 积分历史第{page}页查询开始: 页面大小={page_size}")
            
            # 使用服务角色客户端查询积分历史
            history_response = service_supabase_client.table('credit_operations') \
                .select('*') \
                .eq('user_id', user_id) \
                .order('operation_time', desc=True) \
                .range(offset, offset + page_size - 1) \
                .execute()
            
            # 优化：只在需要时查询总记录数（如第一页且返回数据等于页面大小，或明确需要翻页时）
            total_count = 0
            actual_returned = len(history_response.data) if history_response.data else 0
            
            if page == 1:
                # 第一页：如果返回的记录数等于页面大小，说明可能还有更多记录
                if actual_returned == page_size:
                    # 只查询是否还有下一页，而不查询精确总数
                    next_page_check = service_supabase_client.table('credit_operations') \
                        .select('id') \
                        .eq('user_id', user_id) \
                        .range(page_size, page_size) \
                        .execute()
                    
                    has_next_page = len(next_page_check.data) > 0 if next_page_check.data else False
                    total_count = page_size + (1 if has_next_page else 0)  # 估算总数
                    logger.info(f"用户 {user_id} 积分历史第一页查询完成: 返回{actual_returned}条，{'有' if has_next_page else '无'}下一页")
                else:
                    # 第一页返回的记录数小于页面大小，说明总数就是返回的数量
                    total_count = actual_returned
                    logger.info(f"用户 {user_id} 积分历史查询完成: 总计{total_count}条记录")
            else:
                # 非第一页或需要精确分页信息时才查询总数
                count_response = service_supabase_client.table('credit_operations') \
                    .select('*', count='exact') \
                    .eq('user_id', user_id) \
                    .execute()
                
                total_count = count_response.count if hasattr(count_response, 'count') else 0
                logger.info(f"用户 {user_id} 积分历史第{page}页查询完成: 本页{actual_returned}条，总计{total_count}条记录")
            
            # 新用户有积分但没有历史记录是正常的，不需要创建初始化记录
            # 直接返回空的历史记录列表
            
            return jsonify({
                "success": True,
                "history": history_response.data or [],
                "pagination": {
                    "page": page,
                    "pageSize": page_size,
                    "totalCount": total_count,
                    "totalPages": max(1, (total_count + page_size - 1) // page_size)
                }
            })
            
        except Exception as query_error:
            logger.error(f"查询用户积分历史失败: {str(query_error)}")
            return safe_error_response(query_error, "获取积分历史时出错")
        
    except Exception as e:
        return safe_error_response(e)

@credits_bp.route('/api/credits/model-pricing', methods=['GET'])
def get_model_pricing():
    """获取模型价格和积分兑换率"""
    try:
        # 动态返回模型定价和估算，来源于模型配置中心
        models_data = {
            model_id: {"name": cfg.get('name',''), "pricing": cfg.get('pricing',{})}
            for model_id, cfg in MODEL_CONFIG.items()
        }
        return jsonify({
            "success": True,
            "models": models_data,
            "credit_cost_rate": CREDIT_COST_RATE,
            "feature_token_estimates": FEATURE_TOKEN_ESTIMATES
        })
        
    except Exception as e:
        return safe_error_response(e)

@credits_bp.route('/api/credits/calculate', methods=['GET'])
def calculate_required_credits_endpoint():
    """计算指定操作和模型所需的积分"""
    try:
        operation = request.args.get('operation')
        model = request.args.get('model')
        # 使用模型配置中心的 calculate_cost 计算预计成本，再转换为积分
        if operation == 'epub_extract':
            required = 0
        else:
            usd_cost = calculate_cost(model, operation)
            import math
            required = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))
        return jsonify({'success': True, 'requiredCredits': required})
    except Exception as e:
        logger.exception('计算所需积分失败')
        return jsonify({'success': False, 'error': '计算所需积分失败'}), 500

@credits_bp.route('/api/credits/calculate-from-usage', methods=['POST'])
def calculate_from_token_usage():
    """根据token使用量计算所需积分"""
    try:
        data = request.json
        token_usage = data.get('token_usage', {})
        model = data.get('model', 'zhipu_api')
        
        if not token_usage or not isinstance(token_usage, dict):
            return jsonify({"success": False, "error": "缺少有效的token_usage参数"}), 400
        
        # 计算操作需要的积分
        cfg = MODEL_CONFIG.get(model, {})
        pricing = cfg.get('pricing', {})
        
        inp = token_usage.get('input_tokens', 0)
        out = token_usage.get('output_tokens', 0)
        
        import math
        usd_cost = inp * pricing.get('input', 0) + out * pricing.get('output', 0)
        credits_required = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))
        
        return jsonify({
            "success": True,
            "credits_required": credits_required,
            "model": model,
            "token_usage": token_usage
        })
    except Exception as e:
        return safe_error_response(e, "计算所需积分时发生错误")

@credits_bp.route('/api/credits/sync-local-deductions', methods=['POST'])
def sync_local_deductions():
    """同步本地积分扣减记录到服务器"""
    try:
        data = request.json
        records = data.get('records', [])
        
        if not records or not isinstance(records, list):
            return jsonify({"success": False, "error": "没有有效的积分记录需要同步"}), 400
        
        # 从Authorization头获取JWT
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            logger.warning("同步积分请求缺少有效的Authorization头")
            return jsonify({
                "success": False,
                "error": "未授权的请求"
            }), 401
        
        auth_token = auth_header.replace('Bearer ', '')
        
        # 使用JWT获取用户信息
        try:
            user_response = supabase_client.auth.get_user(auth_token)
            user = user_response.user
            
            if not user:
                logger.warning("无法验证用户JWT")
                return jsonify({
                    "success": False,
                    "error": "无效的用户令牌"
                }), 401
            
            user_id = user.id
        except Exception as auth_error:
            logger.error(f"验证JWT出错: {str(auth_error)}")
            return jsonify({
                "success": False,
                "error": "验证用户身份失败"
            }), 401
        
        # 如果服务角色客户端不可用，无法继续
        if not service_supabase_client:
            logger.error("服务角色客户端未初始化，无法同步积分")
            return jsonify({
                "success": False,
                "error": "服务暂时不可用"
            }), 503
        
        # 查询用户当前积分
        try:
            credits_response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
            
            # 用户没有积分记录
            if not credits_response.data:
                logger.warning(f"用户 {user_id} 没有积分记录")
                return jsonify({
                    "success": False,
                    "error": "用户积分记录不存在"
                }), 404
            
            current_credits = credits_response.data[0].get('credits', 0)
            
            # 计算需要扣减的总积分
            total_credits_to_deduct = 0
            for record in records:
                credits_deducted = record.get('credits_deducted', 0)
                total_credits_to_deduct += credits_deducted
            
            # 允许积分为负值，不检查积分是否足够
            if current_credits < total_credits_to_deduct:
                logger.warning(f"用户 {user_id} 积分不足，但仍继续扣减: 当前={current_credits}, 需要={total_credits_to_deduct}")
            
            # 扣除积分
            new_credits = current_credits - total_credits_to_deduct
            now = datetime.utcnow().isoformat() + 'Z'  # 统一使用UTC时间格式
            
            # 更新积分
            update_response = service_supabase_client.table('user_credits').update({
                'credits': new_credits,
                'updated_at': now
            }).eq('user_id', user_id).execute()
            
            if not update_response.data:
                logger.error(f"更新用户 {user_id} 积分失败")
                return jsonify({
                    "success": False,
                    "error": "更新积分失败"
                }), 500
            
                        # 记录积分操作
            try:
                # 获取模型的显示名称
                model_display_name = model
                if model in MODEL_CONFIG:
                    model_config = MODEL_CONFIG[model]
                    model_display_name = model_config.get('i18n_prefix', model_config.get('name', model))
                    if not model_display_name.startswith("main.models."):
                        model_display_name = f"main.models.{model}"
                
                # 基础记录字段
                operation_record = {
                    'user_id': user_id,
                    'operation_type': operation,
                    'model': model_display_name,
                    'credits_amount': -credits_to_deduct,
                    'operation_time': now,
                    'current_credits': new_credits
                }
                
                # 尝试插入扩展字段，如果失败则使用基础字段
                try:
                    if book_name or chapter_title:
                        extended_record = operation_record.copy()
                        if book_name:
                            extended_record['book_name'] = book_name
                        if chapter_title:
                            extended_record['chapter_title'] = chapter_title
                        service_supabase_client.table('credit_operations').insert(extended_record).execute()
                    else:
                        service_supabase_client.table('credit_operations').insert(operation_record).execute()
                except Exception as schema_error:
                    # 如果扩展字段失败，使用基础字段
                    if 'chapter_title' in str(schema_error) or 'book_name' in str(schema_error):
                        service_supabase_client.table('credit_operations').insert(operation_record).execute()
                    else:
                        raise schema_error
            except Exception as record_error:
                logger.warning(f"记录积分操作失败，但积分已扣除: {str(record_error)}")
            
            return jsonify({
                "success": True,
                "credits": new_credits,
                "synced_records": len(records),
                "total_deducted": total_credits_to_deduct
            })
        except Exception as sync_error:
            logger.error(f"同步本地积分记录失败: {str(sync_error)}")
            return safe_error_response(sync_error, "同步积分记录失败")
    except Exception as e:
        return safe_error_response(e, "处理积分同步请求失败")

# 添加内部扣减函数，供其他后端模块直接调用(在现有函数前添加)

def deduct_user_credits(user_id, operation, model, token_usage=None, audio_info='', chapter_title=''):
    """
    扣除用户积分的核心函数
    同时更新数据库和后端缓存
    注意：audio_info会被存储到数据库的book_name字段中以保持兼容性
    """
    logger.info(f"开始扣除用户积分: 用户={user_id}, 操作={operation}, 模型={model}")
    
    # 处理音频信息显示逻辑
    if operation == 'audio_analysis':
        # 录音分析显示"录音片段"
        display_audio_info = '录音片段'
    elif operation == 'smart_qa' and audio_info:
        # Smart Q&A显示音频文件名称
        display_audio_info = audio_info
    else:
        # 其他情况保持原有逻辑
        display_audio_info = audio_info
    
    # 为了保持数据库兼容性，仍使用book_name字段名，但存储音频信息
    book_name = display_audio_info
    
    try:
        # 导入缓存函数（防止循环导入）
        from api.credits_cache import get_cached_credits, cache_user_credits
        
        # 从缓存或数据库获取当前积分
        cached_credits = get_cached_credits(user_id)
        if cached_credits is not None:
            current_credits = cached_credits
            logger.info(f"从缓存获取当前积分: {current_credits}")
        else:
            # 缓存未命中，查询数据库
            credits_response = service_supabase_client.table('user_credits').select('credits').eq('user_id', user_id).execute()
            if not credits_response.data:
                logger.error(f"用户 {user_id} 积分记录不存在")
                return {"success": False, "error": "积分记录不存在"}
            
            current_credits = credits_response.data[0]['credits']
            logger.info(f"从数据库获取当前积分: {current_credits}")
            # 更新缓存
            cache_user_credits(user_id, current_credits)
        
        # 计算需要扣除的积分
        if isinstance(token_usage, (int, float)):
            credits_to_deduct = int(token_usage)
        else:
            token_usage = token_usage or {}
            import math
            
            if isinstance(token_usage, dict) and (token_usage.get('input_tokens') is not None or token_usage.get('output_tokens') is not None):
                # 优先使用MODEL_CONFIG中的多模态模型配置
                model_config = MODEL_CONFIG.get(model)
                
                if model_config and model_config.get('multimodal'):
                    # 多模态模型 - 使用详细的token分解信息计算成本
                    pricing = model_config.get('pricing', {})
                    
                    # 获取详细的token分解信息
                    prompt_details = token_usage.get('prompt_tokens_details', {})
                    completion_details = token_usage.get('completion_tokens_details', {})
                    
                    # 分别计算不同类型token的成本
                    audio_input_tokens = prompt_details.get('audio_tokens', 0)
                    text_input_tokens = prompt_details.get('text_tokens', 0)
                    text_output_tokens = completion_details.get('text_tokens', 0)
                    audio_output_tokens = completion_details.get('audio_tokens', 0)
                    
                    # 根据token类型分别计费（使用正确的键名）
                    audio_input_cost = audio_input_tokens * pricing.get('input_audio', 0)
                    text_input_cost = text_input_tokens * pricing.get('input_text', 0)
                    text_output_cost = text_output_tokens * pricing.get('output_text', 0)
                    audio_output_cost = audio_output_tokens * pricing.get('output_audio', 0)
                    
                    total_cost = audio_input_cost + text_input_cost + text_output_cost + audio_output_cost
                    
                    logger.info(f"多模态积分详细计算:")
                    logger.info(f"  音频输入tokens: {audio_input_tokens} × ${pricing.get('input_audio', 0):.6f} = ${audio_input_cost:.6f}")
                    logger.info(f"  文本输入tokens: {text_input_tokens} × ${pricing.get('input_text', 0):.6f} = ${text_input_cost:.6f}")
                    logger.info(f"  文本输出tokens: {text_output_tokens} × ${pricing.get('output_text', 0):.6f} = ${text_output_cost:.6f}")
                    logger.info(f"  音频输出tokens: {audio_output_tokens} × ${pricing.get('output_audio', 0):.6f} = ${audio_output_cost:.6f}")
                    logger.info(f"  总成本: ${total_cost:.6f}")
                    
                    usd_cost = total_cost
                else:
                    # 传统模型 - 使用MODEL_CONFIG
                    cfg = MODEL_CONFIG.get(model, {})
                    pricing = cfg.get('pricing', {})
                    inp = token_usage.get('input_tokens', 0)
                    out = token_usage.get('output_tokens', 0)
                    usd_cost = inp * pricing.get('input', 0) + out * pricing.get('output', 0)
                    logger.info(f"传统模型积分计算: 输入tokens={inp}, 输出tokens={out}, 成本=${usd_cost:.6f}")
                
                credits_to_deduct = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))
                logger.info(f"积分扣减计算: ${usd_cost:.6f} / ${CREDIT_COST_RATE:.6f} = {credits_to_deduct}积分")
            else:
                usd_cost = calculate_cost(model, operation)
                credits_to_deduct = max(1, math.ceil(usd_cost / CREDIT_COST_RATE))
                logger.info(f"使用预估成本扣减积分: ${usd_cost:.6f} = {credits_to_deduct}积分")
        
        credits_to_deduct = max(1, int(credits_to_deduct))
        
        # 检查积分是否足够
        if current_credits < credits_to_deduct:
            logger.warning(f"积分不足: 当前={current_credits}, 需要={credits_to_deduct}")
            return {
                "success": False,
                "error": "积分不足",
                "current_credits": current_credits,
                "required_credits": credits_to_deduct
            }
        
        # 计算扣减后的积分
        new_credits = current_credits - credits_to_deduct
        
        # 使用FIFO积分扣减系统
        try:
            # 调用新的FIFO扣减函数
            fifo_response = service_supabase_client.rpc('deduct_credits_fifo', {
                'p_user_id': user_id,
                'p_credits_to_deduct': credits_to_deduct
            }).execute()
            
            if fifo_response.data and len(fifo_response.data) > 0:
                result = fifo_response.data[0]
                if result.get('success'):
                    new_credits = result.get('remaining_credits')
                    affected_batches = result.get('affected_batches')
                    logger.info(f"FIFO积分扣减成功: 用户={user_id}, 扣减={credits_to_deduct}, 剩余={new_credits}")
                    logger.info(f"影响的积分批次: {affected_batches}")
                else:
                    logger.error(f"积分不足，扣减失败: 用户={user_id}, 当前积分={current_credits}, 需要积分={credits_to_deduct}")
                    return {
                        "success": False,
                        "error": "积分不足",
                        "current_credits": current_credits,
                        "required_credits": credits_to_deduct
                    }
            else:
                logger.error(f"FIFO扣减函数返回空结果: 用户={user_id}")
                return {"success": False, "error": "积分扣减函数执行失败"}
        
        except Exception as fifo_error:
            logger.error(f"FIFO积分扣减失败: {str(fifo_error)}")
            return {"success": False, "error": f"积分扣减失败: {str(fifo_error)}"}
        
        # 立即更新后端缓存
        cache_user_credits(user_id, new_credits)
        logger.info(f"积分扣减成功，已更新缓存: 用户={user_id}, 扣减={credits_to_deduct}, 剩余={new_credits}")
        
        # 记录积分操作历史
        try:
            from datetime import datetime
            now = datetime.utcnow().isoformat() + 'Z'  # 统一使用UTC时间格式
            
            operation_record = {
                'user_id': user_id,
                'operation_type': operation,
                'model': model,
                'credits_amount': -credits_to_deduct,
                'operation_time': now,
                'current_credits': new_credits
            }
            
            # 尝试插入扩展字段
            if book_name or chapter_title:
                extended_record = operation_record.copy()
                if book_name:
                    extended_record['book_name'] = book_name
                if chapter_title:
                    extended_record['chapter_title'] = chapter_title
                service_supabase_client.table('credit_operations').insert(extended_record).execute()
            else:
                service_supabase_client.table('credit_operations').insert(operation_record).execute()
                
            logger.info(f"积分操作记录成功: 用户={user_id}, 操作={operation}")
        except Exception as record_error:
            logger.warning(f"记录积分操作失败，但积分已扣除: {str(record_error)}")
        
        return {
            "success": True,
            "credits_deducted": credits_to_deduct,
            "remaining_credits": new_credits,
            "operation": operation,
            "model": model
        }
        
    except Exception as e:
        logger.error(f"扣除积分时发生错误: {str(e)}")
        logger.error(f"扣除积分异常详情: {traceback.format_exc()}")
        return {"success": False, "error": "扣除积分时发生错误"}

@credits_bp.route('/api/credits/batches', methods=['GET'])
def get_credit_batches():
    """
    获取用户积分批次详情
    返回用户所有有效的积分批次信息，包括剩余积分和到期时间
    """
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({"error": "需要提供认证令牌"}), 401
        
        auth_token = auth_header.split(' ')[1]
        
        # 验证用户身份
        try:
            user_response = supabase_client.auth.get_user(auth_token)
            
            if not user_response or not hasattr(user_response, 'user') or not user_response.user:
                return jsonify({"error": "无效的用户令牌"}), 401
            
            user_id = user_response.user.id
            logger.info(f"获取积分批次详情请求: 用户={user_id}")
            
        except Exception as auth_error:
            logger.error(f"验证JWT出错: {str(auth_error)}")
            return jsonify({"error": "验证用户身份失败"}), 401
        
        # 调用数据库函数获取积分批次详情
        try:
            batch_response = service_supabase_client.rpc('get_user_credit_batches', {
                'p_user_id': user_id
            }).execute()
            
            batches = batch_response.data or []
            logger.info(f"获取到 {len(batches)} 个积分批次")
            
            # 获取积分总览
            summary_response = service_supabase_client.rpc('get_user_credits_summary', {
                'p_user_id': user_id
            }).execute()
            
            summary = summary_response.data[0] if summary_response.data else {
                'total_credits': 0,
                'active_batches': 0,
                'credits_expiring_soon': 0,
                'earliest_expiry_date': None
            }
            
            # 格式化批次数据
            formatted_batches = []
            for batch in batches:
                formatted_batches.append({
                    'batch_id': batch['batch_id'],
                    'credits_amount': batch['credits_amount'],
                    'remaining_credits': batch['remaining_credits'],
                    'source_type': batch['source_type'],
                    'source_description': {
                        'purchase': '积分购买',
                        'signup_bonus': '注册赠送',
                        'admin_add': '管理员添加',
                        'recharge': '积分充值'
                    }.get(batch['source_type'], batch['source_type']),
                    'created_at': batch['created_at'],
                    'expires_at': batch['expires_at'],
                    'days_until_expiry': batch['days_until_expiry'],
                    'is_expiring_soon': batch['days_until_expiry'] <= 7,
                    'order_id': batch.get('order_id'),
                    'description': batch.get('description')
                })
            
            return jsonify({
                "success": True,
                "batches": formatted_batches,
                "summary": {
                    'total_credits': summary['total_credits'],
                    'active_batches': summary['active_batches'],
                    'credits_expiring_soon': summary['credits_expiring_soon'],
                    'earliest_expiry_date': summary['earliest_expiry_date']
                }
            }), 200
            
        except Exception as db_error:
            logger.error(f"获取积分批次详情失败: {str(db_error)}")
            return jsonify({"error": "获取积分批次信息失败"}), 500
            
    except Exception as e:
        logger.error(f"获取积分批次详情出错: {str(e)}")
        return jsonify({"error": "服务器内部错误"}), 500