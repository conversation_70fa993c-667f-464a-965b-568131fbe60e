/**
 * 模型选择器下拉菜单功能
 * 独立的模块，避免与其他代码冲突
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const modelSelector = document.querySelector('.model-selector');
    const currentModel = document.getElementById('currentModel');
    const modelDropdown = document.getElementById('modelDropdown');
    
    if (!modelSelector || !currentModel || !modelDropdown) {
        console.error('模型选择器元素未找到');
        return;
    }
    
    // 点击当前模型显示下拉菜单
    currentModel.addEventListener('click', function(e) {
        // 切换下拉菜单的显示状态
        if (modelDropdown.style.display === 'block') {
            modelDropdown.style.display = 'none';
        } else {
            modelDropdown.style.display = 'block';
        }
        e.stopPropagation(); // 阻止事件冒泡
    });
    
    // 点击下拉菜单内部时阻止事件冒泡
    modelDropdown.addEventListener('click', function(e) {
        e.stopPropagation();
    });
    
    // 点击页面其他区域关闭下拉菜单
    document.addEventListener('click', function() {
        modelDropdown.style.display = 'none';
    });
    
    // 确保CSS类不会干扰显示
    // 移除可能导致冲突的CSS类
    modelDropdown.classList.remove('active');
    
    console.log('模型选择器初始化完成');
});
