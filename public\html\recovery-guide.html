<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源加载问题修复指南 - BookSum</title>
    <link rel="stylesheet" href="../css/recovery.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #3367d6;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            color: #4285f4;
            margin-top: 30px;
        }
        .solution {
            background-color: #f8f9fa;
            border-left: 4px solid #4285f4;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .solution h3 {
            margin-top: 0;
            color: #4285f4;
        }
        .step {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }
        .step:before {
            content: "•";
            color: #4285f4;
            position: absolute;
            left: 0;
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .tip {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #4285f4;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
            margin-top: 10px;
        }
        .button:hover {
            background-color: #3367d6;
        }
    </style>
</head>
<body>
    <h1>BookSum 资源加载问题修复指南</h1>
    
    <p>您正在查看此页面可能是因为BookSum应用在加载资源时遇到了问题。这通常由于网络连接不稳定、CDN服务不可用或浏览器缓存问题导致。</p>
    
    <div class="warning">
        <h3>常见症状</h3>
        <ul>
            <li>页面加载缓慢或不完整</li>
            <li>功能按钮点击无反应</li>
            <li>控制台出现JavaScript错误</li>
            <li>图谱、脑图或摘要无法生成</li>
            <li>显示"无法加载资源"错误提示</li>
        </ul>
    </div>
    
    <h2>快速解决方案</h2>
    
    <div class="solution">
        <h3>解决方案 1: 刷新页面</h3>
        <div class="step">点击浏览器的刷新按钮或按 F5 键刷新页面</div>
        <div class="step">这是最简单的解决方法，可以解决临时网络问题</div>
        <a href="javascript:window.location.reload()" class="button">立即刷新页面</a>
    </div>
    
    <div class="solution">
        <h3>解决方案 2: 清除浏览器缓存</h3>
        <div class="step">在大多数浏览器中，按 Ctrl+Shift+Delete (Windows) 或 Command+Shift+Delete (Mac)</div>
        <div class="step">选择"缓存"或"缓存的图片和文件"，然后点击"清除数据"</div>
        <div class="step">清除缓存后重新加载页面</div>
        <a href="javascript:clearCacheAndReload()" class="button">清除缓存并刷新</a>
    </div>
    
    <div class="solution">
        <h3>解决方案 3: 使用本地资源</h3>
        <div class="step">点击下方按钮启用本地资源模式</div>
        <div class="step">系统将优先使用本地服务器上的资源文件，而不是CDN</div>
        <div class="step">这可以解决CDN服务不可用的问题</div>
        <a href="javascript:useLocalResources()" class="button">使用本地资源</a>
    </div>
    
    <div class="solution">
        <h3>解决方案 4: 本地资源优先</h3>
        <div class="step">点击下方按钮启用本地资源优先模式</div>
        <div class="step">系统将先尝试使用本地资源，如果本地不存在再尝试CDN</div>
        <div class="step">这是一种平衡的方案，可以提高稳定性同时保持性能</div>
        <a href="javascript:useLocalResourcesFirst()" class="button">本地资源优先</a>
    </div>
    
    <h2>高级解决方案</h2>
    
    <div class="solution">
        <h3>解决方案 5: 使用不同的浏览器</h3>
        <div class="step">尝试使用 Chrome、Firefox、Edge 或 Safari 等不同的浏览器</div>
        <div class="step">不同浏览器对网络资源的处理方式不同，可能会有所帮助</div>
    </div>
    
    <div class="solution">
        <h3>解决方案 6: 检查网络连接</h3>
        <div class="step">确保您的网络连接稳定</div>
        <div class="step">如果使用Wi-Fi，请尝试靠近路由器或切换到有线连接</div>
        <div class="step">尝试重启您的路由器</div>
    </div>
    
    <div class="tip">
        <h3>提示</h3>
        <p>如果您频繁遇到资源加载问题，建议使用"本地资源优先"模式，这将大大提高应用的稳定性。</p>
    </div>
    
    <h2>联系支持</h2>
    
    <p>如果您尝试了上述所有解决方案但问题仍然存在，请联系我们的技术支持团队获取帮助。</p>
    
    <div>
        <a href="javascript:goBack()" class="button" style="background-color: #757575;">返回上一页</a>
        <a href="/" class="button" style="background-color: #757575;">返回首页</a>
    </div>
    
    <script>
        // 返回上一页
        function goBack() {
            window.history.back();
        }
        
        // 清除缓存并刷新
        function clearCacheAndReload() {
            localStorage.removeItem('useLocalLibs');
            localStorage.removeItem('localFirst');
            // 强制刷新，绕过缓存
            window.location.href = window.location.href.split('?')[0] + '?cache_bust=' + Date.now();
        }
        
        // 使用本地资源
        function useLocalResources() {
            localStorage.setItem('useLocalLibs', 'true');
            window.location.reload();
        }
        
        // 本地资源优先
        function useLocalResourcesFirst() {
            localStorage.setItem('localFirst', 'true');
            window.location.reload();
        }
    </script>
</body>
</html> 