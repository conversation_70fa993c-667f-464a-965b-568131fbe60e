"""
积分缓存模块 - 后端内存缓存，解决频繁数据库访问问题

更新说明：
- 将缓存TTL从5分钟延长到30分钟，减少不必要的数据库查询
- 在用户正常使用过程中，30分钟内的积分变化较少，缓存命中率更高
- 积分发生变动时（如扣减、充值）会主动更新缓存，保证数据一致性
"""
import time
import threading
from typing import Dict, Optional, Tuple

class CreditsCache:
    """
    简单的积分内存缓存
    - 缓存用户积分信息，减少数据库访问
    - 设置合理的过期时间
    - 线程安全
    """
    
    def __init__(self, cache_ttl: int = 3600):  # 默认60分钟过期
        self.cache_ttl = cache_ttl
        self._cache: Dict[str, Tuple[int, float]] = {}  # user_id -> (credits, timestamp)
        self._lock = threading.RLock()
    
    def get(self, user_id: str) -> Optional[int]:
        """
        获取缓存的积分
        
        Args:
            user_id: 用户ID
            
        Returns:
            积分数量，如果缓存不存在或已过期则返回None
        """
        with self._lock:
            if user_id not in self._cache:
                return None
            
            credits, timestamp = self._cache[user_id]
            
            # 检查是否过期
            if time.time() - timestamp > self.cache_ttl:
                del self._cache[user_id]
                return None
            
            return credits
    
    def set(self, user_id: str, credits: int) -> None:
        """
        设置用户积分缓存
        
        Args:
            user_id: 用户ID
            credits: 积分数量
        """
        with self._lock:
            self._cache[user_id] = (credits, time.time())
    
    def update_credits(self, user_id: str, new_credits: int) -> None:
        """
        更新用户积分（扣减后）
        
        Args:
            user_id: 用户ID
            new_credits: 新的积分数量
        """
        self.set(user_id, new_credits)
    
    def invalidate(self, user_id: str) -> None:
        """
        使特定用户的缓存失效
        
        Args:
            user_id: 用户ID
        """
        with self._lock:
            if user_id in self._cache:
                del self._cache[user_id]
    
    def clear_expired(self) -> int:
        """
        清理过期缓存
        
        Returns:
            清理的缓存条目数量
        """
        current_time = time.time()
        expired_keys = []
        
        with self._lock:
            for user_id, (_, timestamp) in self._cache.items():
                if current_time - timestamp > self.cache_ttl:
                    expired_keys.append(user_id)
            
            for key in expired_keys:
                del self._cache[key]
        
        return len(expired_keys)
    
    def get_cache_stats(self) -> Dict:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息
        """
        with self._lock:
            total_entries = len(self._cache)
            current_time = time.time()
            expired_count = 0
            
            for _, (_, timestamp) in self._cache.items():
                if current_time - timestamp > self.cache_ttl:
                    expired_count += 1
            
            return {
                'total_entries': total_entries,
                'active_entries': total_entries - expired_count,
                'expired_entries': expired_count,
                'cache_ttl': self.cache_ttl
            }

# 全局缓存实例
credits_cache = CreditsCache(cache_ttl=3600)  # 60分钟缓存

def get_cached_credits(user_id: str) -> Optional[int]:
    """
    获取缓存的用户积分
    
    Args:
        user_id: 用户ID
        
    Returns:
        积分数量，如果缓存不存在或已过期则返回None
    """
    return credits_cache.get(user_id)

def cache_user_credits(user_id: str, credits: int) -> None:
    """
    缓存用户积分
    
    Args:
        user_id: 用户ID
        credits: 积分数量
    """
    credits_cache.set(user_id, credits)

def update_cached_credits(user_id: str, new_credits: int) -> None:
    """
    更新缓存中的用户积分（通常在扣减后调用）
    
    Args:
        user_id: 用户ID
        new_credits: 新的积分数量
    """
    credits_cache.update_credits(user_id, new_credits)

def invalidate_user_cache(user_id: str) -> None:
    """
    使用户积分缓存失效
    
    Args:
        user_id: 用户ID
    """
    credits_cache.invalidate(user_id)

def get_cache_statistics() -> Dict:
    """
    获取缓存统计信息
    
    Returns:
        缓存统计信息
    """
    return credits_cache.get_cache_stats() 