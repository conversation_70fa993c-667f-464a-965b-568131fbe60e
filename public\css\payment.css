/* 充值模态框样式 */
.recharge-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.recharge-modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 20px;
    text-align: center; /* 文字居中 */
}

.recharge-modal-header {
    display: flex;
    justify-content: center; /* 居中对齐 */
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
    position: relative; /* 使关闭按钮绝对定位 */
}

.recharge-modal-header h2 {
    flex: 1; /* 占据剩余空间 */
    text-align: center; /* 标题居中 */
}

.close-modal {
    position: absolute; /* 绝对定位 */
    right: 10px; /* 右侧对齐 */
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.close-modal:hover {
    color: #000;
}

/* 微信二维码部分 */
.wechat-qrcode-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f8f8;
    border-radius: 8px;
}

.wechat-qrcode {
    width: 150px;
    height: 150px;
    object-fit: contain;
    margin-bottom: 10px;
}

.qrcode-prompt {
    font-size: 16px;
    color: #333;
    margin: 0;
    text-align: center;
}

/* 水平布局的套餐容器 */
.package-container.horizontal {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 20px;
    margin-top: 15px;
}

.package-card {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    position: relative;
    transition: transform 0.2s, box-shadow 0.2s;
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center; /* 文字居中 */
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.package-card.popular {
    border-color: #4a90e2;
    box-shadow: 0 4px 8px rgba(74, 144, 226, 0.2);
}

.popular-badge {
    position: absolute;
    top: -10px;
    right: 10px;
    background: #4a90e2;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.package-description, .package-credits, .package-price {
    text-align: center; /* 文字居中 */
}

.select-package-btn {
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 8px; /* 圆润按钮 */
    padding: 10px 20px;
    cursor: pointer;
    margin-top: 10px; /* 与上方文字保持间距 */
}

.select-package-btn:hover {
    background-color: #357ab8;
}

/* 底部文字与上方套餐保持适当间距 */
.recharge-modal-footer {
    margin-top: 20px;
}

.payment-note {
    margin-top: 20px;
    text-align: center; /* 文字居中 */
}

/* 响应式布局 */
@media (max-width: 768px) {
    /* 调整模态框位置，避免被顶部标题栏遮挡 */
    .recharge-modal {
        align-items: flex-start;
        padding-top: 80px;
        z-index: 10000;
    }
    
    .recharge-modal-content {
        width: 95%;
        max-width: none;
        margin: 0 auto;
        padding: 15px;
        max-height: calc(100vh - 100px);
    }
    
    /* 调整标题字号 */
    .recharge-modal-header h2 {
        font-size: 18px;
        margin: 0;
    }
    
    /* 调整套餐容器为垂直布局 */
    .package-container.horizontal {
        flex-direction: column;
        gap: 15px;
    }
    
    /* 调整套餐卡片样式 */
    .package-card {
        padding: 15px;
    }
    
    .package-card h3 {
        font-size: 16px;
        margin: 0 0 8px 0;
    }
    
    .package-description {
        font-size: 14px;
        margin: 5px 0;
    }
    
    .package-credits {
        font-size: 16px;
        font-weight: bold;
        margin: 8px 0;
    }
    
    .package-price {
        font-size: 18px;
        font-weight: bold;
        color: #4a90e2;
        margin: 8px 0;
    }
    
    /* 调整按钮样式 */
    .select-package-btn {
        padding: 10px 16px;
        font-size: 14px;
        margin-top: 10px;
    }
    
    /* 调整微信二维码区域 */
    .wechat-qrcode {
        width: 120px;
        height: 120px;
    }
    
    .qrcode-prompt {
        font-size: 14px;
    }
    
    /* 调整套餐选择描述文字 */
    .recharge-description {
        font-size: 14px;
        margin: 10px 0;
    }
    
    /* 调整底部说明文字 */
    .payment-note {
        font-size: 13px;
        margin-top: 15px;
        line-height: 1.4;
    }
    
    /* 调整热门标签 */
    .popular-badge {
        font-size: 11px;
        padding: 3px 6px;
        top: -8px;
        right: 8px;
    }
    
    /* 调整处理状态文字 */
    .processing-message, .error-message {
        font-size: 14px;
    }
    
    .try-again-btn {
        font-size: 14px;
        padding: 8px 16px;
        margin-top: 10px;
    }
}

/* 处理状态样式 */
.payment-processing-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    text-align: center;
    margin: 20px 0;
}

.processing-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #4a90e2;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.processing-message {
    font-size: 16px;
    color: #333;
    margin: 0;
}

.processing-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.error-message {
    color: #e74c3c;
    font-size: 16px;
    margin: 10px 0;
}

.try-again-btn {
    background-color: #4a90e2;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 14px;
}

.try-again-btn:hover {
    background-color: #357ab8;
}

/* 调整套餐描述的字体大小和行高 */
.package-description {
    font-size: 14px; /* 调整字体大小 */
    line-height: 1.4; /* 设置行高 */
    max-height: 2.8em; /* 限制最大高度为两行 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis; /* 添加省略号 */
    white-space: nowrap; /* 单行显示 */
}