/**
 * Firefox 浏览器兼容性修复脚本
 * 使用 MutationObserver 监控并修复布局问题
 */

(function() {
    // 检测是否为 Firefox 浏览器
    function isFirefox() {
        return navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
    }

    // 如果不是 Firefox，则直接返回
    if (!isFirefox()) {
        console.log('非 Firefox 浏览器，不应用特殊修复');
        return;
    }

    console.log('检测到 Firefox 浏览器，启用宽度修复');

    // 等待 DOM 完全加载
    document.addEventListener('DOMContentLoaded', function() {
        // 初始应用修复
        applyWidthFix();
        
        // 页面加载后延迟应用修复，避免其他脚本覆盖我们的修复
        setTimeout(applyWidthFix, 100);
        setTimeout(applyWidthFix, 500);
        setTimeout(applyWidthFix, 1000);
        
        // 创建 MutationObserver 监听 DOM 变化
        const observer = new MutationObserver(function(mutations) {
            let needsFix = false;
            
            // 检查变化是否与我们关注的元素相关
            mutations.forEach(function(mutation) {
                if (mutation.target.id === 'aiContent' || 
                    mutation.target.id === 'chapters' ||
                    mutation.target.id === 'content' ||
                    mutation.target.className === 'container' ||
                    mutation.target.className.includes('column') ||
                    // 检查样式变化
                    mutation.attributeName === 'style') {
                    needsFix = true;
                }
            });
            
            if (needsFix) {
                // 应用宽度修复
                applyWidthFix();
            }
        });
        
        // 配置观察选项
        const config = { 
            attributes: true, 
            childList: true, 
            subtree: true,
            attributeFilter: ['style', 'class', 'id']
        };
        
        // 开始观察容器和其子元素
        const container = document.querySelector('.container');
        if (container) {
            observer.observe(container, config);
            console.log('已启动 DOM 变化监视器');
        }
        
        // 监听窗口大小变化
        window.addEventListener('resize', applyWidthFix);
    });

    // 应用宽度修复函数
    function applyWidthFix() {
        console.log('应用 Firefox 特定宽度修复');
        
        // 获取主容器及三个列元素
        const container = document.querySelector('.container');
        const chaptersCol = document.getElementById('chapters');
        const contentCol = document.getElementById('content');
        const aiContentCol = document.getElementById('aiContent');
        
        if (!container || !chaptersCol || !contentCol || !aiContentCol) {
            console.log('无法找到必要的列元素，跳过修复');
            return;
        }
        
        // 强制设置容器的宽度和显示模式
        container.style.setProperty('display', 'flex', 'important');
        container.style.setProperty('width', 'calc(100% - 20px)', 'important');
        container.style.setProperty('margin', '10px 10px', 'important');
        
        // 强制设置三列的宽度比例
        chaptersCol.style.setProperty('width', '20%', 'important');
        chaptersCol.style.setProperty('min-width', '20%', 'important');
        chaptersCol.style.setProperty('flex', '0 0 20%', 'important');
        chaptersCol.style.setProperty('max-width', '20%', 'important');
        
        contentCol.style.setProperty('width', '45%', 'important');
        contentCol.style.setProperty('min-width', '45%', 'important');
        contentCol.style.setProperty('flex', '0 0 45%', 'important');
        contentCol.style.setProperty('max-width', '45%', 'important');
        
        // 修改这里，将宽度从35%改为33%，并添加右边距
        aiContentCol.style.setProperty('width', '35%', 'important');
        aiContentCol.style.setProperty('min-width', '35%', 'important');
        aiContentCol.style.setProperty('flex', '0 0 35%', 'important');
        aiContentCol.style.setProperty('max-width', '35%', 'important');
        aiContentCol.style.setProperty('margin-right', '20px', 'important');
        aiContentCol.style.setProperty('padding-right', '20px', 'important');
        aiContentCol.style.setProperty('box-sizing', 'border-box', 'important');
        aiContentCol.style.setProperty('flex-basis', '35%', 'important');
        aiContentCol.style.setProperty('flex-grow', '0', 'important');
        aiContentCol.style.setProperty('flex-shrink', '0', 'important');
        
        // 禁用某些可能导致问题的CSS属性
        document.querySelectorAll('.column').forEach(function(col) {
            col.style.setProperty('contain', 'none', 'important');
            col.style.setProperty('will-change', 'auto', 'important');
        });
        
        // 修复生成按钮的位置和样式
        const summaryBtn = document.getElementById('generateSummaryBtn');
        const mindmapBtn = document.getElementById('generateMindmapBtn');
        
        if (summaryBtn) {
            summaryBtn.style.setProperty('width', '90%', 'important');
            summaryBtn.style.setProperty('margin', '10px auto', 'important');
            summaryBtn.style.setProperty('display', 'block', 'important');
        }
        
        if (mindmapBtn) {
            mindmapBtn.style.setProperty('width', '90%', 'important');
            mindmapBtn.style.setProperty('margin', '10px auto', 'important');
            mindmapBtn.style.setProperty('display', 'block', 'important');
        }
        
        // 修复标签页容器宽度
        const aiTabs = document.querySelector('.ai-tabs');
        if (aiTabs) {
            aiTabs.style.setProperty('width', '100%', 'important');
        }
        
        console.log('Firefox 特定宽度修复已应用');
    }
})();