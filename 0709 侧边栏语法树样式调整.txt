public/css/grammar-styles.css

/* 侧边栏展示样式 */
.grammar-sidebar {
    position: fixed;
    top: 0;
    right: -530px;
    width: 530px;
    height: 100%;
    background: white;
    z-index: 9999;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    transition: right 0.3s ease;
}

// ... existing code ...

/* 新增多种展示方式的样式 */

/* 侧边栏展示样式 */
.grammar-sidebar {
    position: fixed;
    top: 0;
    right: -530px;
    width: 530px;
    height: 100%;
    background: white;
    z-index: 9999;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    transition: right 0.3s ease;
}

public/js/grammar-analyzer.js

    getNodeWidth(text, type) {
        // 对于长文本，使用固定的最大宽度以支持换行
        if (text.length > 20) {
            return Math.min(320, Math.max(text.length * 8 + 40, 180));
        }
        
        const baseWidth = text.length * 12 + 20;
        const minWidth = type === 'component' ? 120 : 100;
        return Math.max(baseWidth, minWidth);
    }

->

    getNodeWidth(text, type) {
        // 对于长文本，使用固定的最大宽度以支持换行，增加宽度以适应更宽的侧边栏
        if (text.length > 20) {
            return Math.min(430, Math.max(text.length * 10 + 50, 240));
        }
        
        const baseWidth = text.length * 15 + 30;
        const minWidth = type === 'component' ? 160 : 140;
        return Math.max(baseWidth, minWidth);
    }


            .attr('x2', d => {
                // 根据深度设置初始长度，后续会根据文本调整
                const initialLength = d.depth === 0 ? 200 : 150;
                const maxLineLength = width - d.y - 30;
                return Math.min(initialLength, maxLineLength);
            })

->

            .attr('x2', d => {
                // 根据深度设置初始长度，后续会根据文本调整，增加长度以适应更宽的侧边栏
                const initialLength = d.depth === 0 ? 280 : 220;
                const maxLineLength = width - d.y - 20;
                return Math.min(initialLength, maxLineLength);
            })

public/js/subtitle-manager.js

        // 调整主内容区域
        const mainContent = document.querySelector('.main-content') || document.body;
        if (mainContent !== document.body) {
            mainContent.style.marginRight = '550px';
            mainContent.style.transition = 'margin-right 0.3s ease';
        } else {
            // 如果没有找到 .main-content，则创建一个包装器
            const wrapper = document.createElement('div');
            wrapper.className = 'main-content-wrapper';
            wrapper.style.marginRight = '450px';
            wrapper.style.transition = 'margin-right 0.3s ease';

->

        // 调整主内容区域
        const mainContent = document.querySelector('.main-content') || document.body;
        if (mainContent !== document.body) {
            mainContent.style.marginRight = '550px';
            mainContent.style.transition = 'margin-right 0.3s ease';
        } else {
            // 如果没有找到 .main-content，则创建一个包装器
            const wrapper = document.createElement('div');
            wrapper.className = 'main-content-wrapper';
            wrapper.style.marginRight = '550px';
            wrapper.style.transition = 'margin-right 0.3s ease';

## 修改完成总结

我已经根据您的需求完成了所有修改：

### 1. 右侧边栏宽度增加1/3
- **修改前**：400px
- **修改后**：530px (增加130px，约为1/3)
- **修改文件**：`public/css/grammar-styles.css`

### 2. 节点解释文字长度相应增加
- **修改文件**：`public/js/grammar-analyzer.js`
- **具体优化**：
  - 长文本最大宽度：320px → 430px
  - 文字宽度计算系数提升：8倍→10倍，12倍→15倍
  - 基础宽度边距增加：+20 → +30  
  - 最小宽度提升：component类型 120px→160px，其他 100px→140px
  - 初始线条长度增加：200px/150px → 280px/220px
  - 优化文本边距和截断处理逻辑

### 3. 主内容区域适配
- **修改文件**：`public/js/subtitle-manager.js`
- **调整内容**：主内容包装器右边距从450px增加到550px

### 修改效果：
1. ✅ 右侧边栏宽度增加1/3，从400px扩展到530px
2. ✅ 节点解释文字长度显著增加，能显示更多详细内容
3. ✅ 主内容区域自动适配新的侧边栏宽度
4. ✅ 保持关闭侧边栏时的正确布局恢复

现在语法树右侧边栏拥有更宽的显示空间，节点的解释文字也能显示更长的内容，充分利用了增加的显示区域。您可以测试语法树功能来验证改进效果。