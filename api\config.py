import os
import logging
import secrets
from dotenv import load_dotenv

# 加载.env文件中的环境变量
load_dotenv()

# 设置日志记录
logger = logging.getLogger(__name__)

# 使用环境变量作为进程级别的缓存标记，避免模块重载时重复输出日志
_SUPABASE_CONFIG_LOGGED_ENV_KEY = 'SUPABASE_CONFIG_LOGGED'

def get_or_generate_flask_secret_key(key_file='.flask_secret_key'):
    """
    获取或生成Flask应用的Secret Key，优先使用环境变量中的密钥，
    如果不存在则尝试从持久化文件中读取，
    如果文件也不存在则生成新的密钥并保存
    
    Args:
        key_file (str): 保存密钥的文件路径
    
    Returns:
        str: 用于Flask应用的Secret Key
    """
    try:
        # 优先使用环境变量中的密钥
        secret_key = os.getenv('FLASK_SECRET_KEY')
        
        if secret_key:
            logger.info("使用环境变量中的FLASK_SECRET_KEY")
            return secret_key
        
        # 环境变量中没有，尝试从文件读取
        secret_file = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), key_file)
        
        if os.path.exists(secret_file):
            try:
                with open(secret_file, 'r') as f:
                    secret_key = f.read().strip()
                    if secret_key:
                        logger.info(f"从文件 {key_file} 读取SECRET_KEY成功")
                        return secret_key
            except Exception as e:
                logger.warning(f"读取密钥文件失败: {str(e)}")
        
        # 没有找到有效密钥，生成一个新的
        logger.warning("未找到有效的FLASK_SECRET_KEY，正在生成新密钥")
        secret_key = secrets.token_hex(24)  # 生成一个48字符的随机十六进制密钥
        
        # 尝试保存到文件以便于持久化
        try:
            with open(secret_file, 'w') as f:
                f.write(secret_key)
            os.chmod(secret_file, 0o600)  # 设置权限，只有拥有者可读写
            logger.info(f"新的SECRET_KEY已生成并保存至 {key_file}")
        except Exception as e:
            logger.warning(f"无法保存密钥到文件: {str(e)}, 请注意重启后密钥会丢失")
        
        return secret_key
    except Exception as e:
        logger.error(f"获取或生成SECRET_KEY时出错: {str(e)}")
        # 出错时返回一个临时密钥，确保应用能够启动
        return secrets.token_hex(24)

# 缓存Supabase配置，避免重复加载
_supabase_config_cache = None

# 使用进程级别的标记来确保只在第一次初始化时输出日志
import threading
_config_lock = threading.Lock()

def get_supabase_config():
    """
    从环境变量获取Supabase配置（带缓存）
    
    Returns:
        dict: 包含Supabase URL和API密钥的字典
    """
    global _supabase_config_cache
    
    # 如果已有缓存，直接返回
    if _supabase_config_cache is not None:
        return _supabase_config_cache
    
    # 使用线程锁确保配置只被初始化一次
    with _config_lock:
        # 双重检查，防止竞态条件
        if _supabase_config_cache is not None:
            return _supabase_config_cache
            
        try:
            # 尝试从环境变量读取配置
            supabase_url = os.getenv('SUPABASE_URL')
            
            # 尝试获取两种可能的API密钥
            supabase_anon_key = os.getenv('SUPABASE_ANON_KEY')
            supabase_service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY_AUDIO')
            
            # 优先使用服务角色密钥（权限更高），如果没有则使用匿名密钥
            supabase_key = supabase_service_key or supabase_anon_key
            
            # 检查是否已经输出过日志（使用环境变量标记）
            config_logged = os.getenv(_SUPABASE_CONFIG_LOGGED_ENV_KEY) == 'true'
            
            # 如果环境变量未设置，记录警告
            if not supabase_url or not supabase_key:
                if not config_logged:
                    logger.warning("Supabase配置不完整或缺失，部分功能可能不可用")
                    if not supabase_url:
                        logger.warning("缺少 SUPABASE_URL 环境变量")
                    if not supabase_key:
                        logger.warning("缺少 SUPABASE_ANON_KEY 和 SUPABASE_SERVICE_ROLE_KEY 环境变量")
                    # 设置环境变量标记，避免重复日志
                    os.environ[_SUPABASE_CONFIG_LOGGED_ENV_KEY] = 'true'
            else:
                # 只在第一次加载时输出日志
                if not config_logged:
                    logger.info(f"成功加载Supabase配置: URL={supabase_url[:20]}..., KEY类型={supabase_service_key and '服务密钥' or '匿名密钥'}")
                    # 设置环境变量标记，避免重复日志
                    os.environ[_SUPABASE_CONFIG_LOGGED_ENV_KEY] = 'true'
            
            # 缓存配置
            _supabase_config_cache = {
                'url': supabase_url,
                'key': supabase_key
            }
            
            return _supabase_config_cache
            
        except Exception as e:
            config_logged = os.getenv(_SUPABASE_CONFIG_LOGGED_ENV_KEY) == 'true'
            if not config_logged:
                logger.error(f"获取Supabase配置时出错: {str(e)}")
                os.environ[_SUPABASE_CONFIG_LOGGED_ENV_KEY] = 'true'
            # 缓存失败的配置，避免重复尝试
            _supabase_config_cache = {'url': None, 'key': None}
            return _supabase_config_cache

def get_jwt_secret():
    """
    获取JWT密钥配置
    
    Returns:
        str: JWT密钥
    """
    try:
        jwt_secret = os.getenv('JWT_SECRET')
        if not jwt_secret:
            logger.warning("JWT密钥未设置，令牌验证功能可能受影响")
        return jwt_secret
    except Exception as e:
        logger.error(f"获取JWT密钥时出错: {str(e)}")
        return None

# 在此处添加积分检查开关，支持环境变量配置
# 默认开启，可通过设置环境变量为 "false" 来关闭对应功能的积分检查
CHECK_CREDITS_PODCAST_SEARCH = os.getenv("CHECK_CREDITS_PODCAST_SEARCH", "true").lower() == "true"
CHECK_CREDITS_UPLOAD_SUBTITLE = os.getenv("CHECK_CREDITS_UPLOAD_SUBTITLE", "true").lower() == "true"
CHECK_CREDITS_BOOKMARK_SAVE = os.getenv("CHECK_CREDITS_BOOKMARK_SAVE", "true").lower() == "true"
CHECK_CREDITS_BOOKMARK_LOAD = os.getenv("CHECK_CREDITS_BOOKMARK_LOAD", "true").lower() == "true"

# 新增：动态缓存时间管理机制配置
AUXILIARY_FEATURES_SKIP_CHECK_MINUTES = int(os.getenv("AUXILIARY_FEATURES_SKIP_CHECK_MINUTES", "60"))  # 默认60分钟

# 新增：强制登录时查询数据库积分配置
FORCE_CREDITS_REFRESH_ON_LOGIN = os.getenv("FORCE_CREDITS_REFRESH_ON_LOGIN", "true").lower() == "true"