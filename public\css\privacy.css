:root {
    --text-color: #333;
    --primary-color: #007bff; /* 替换为适合的颜色 */
}
/* 隐私政策页面特定样式 */
.privacy-container {
    max-width: 1000px;
    margin: 5px auto 10px; /* Further reduced top margin */
    padding: 0 20px;
}

.privacy-content {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 40px;
}

.privacy-content h1 {
    font-size: 1.5rem; /* Reduced font size for main title */
    text-align: center;
    margin-bottom: 20px;
    color: var(--text-color);
}

.last-updated {
    text-align: center;
    color: #666;
    margin-bottom: 40px;
    font-style: italic;
}

.privacy-content section {
    margin-bottom: 30px;
}

.privacy-content h2 {
    font-size: 1.1rem; /* Reduced font size for section headings */
    color: var(--primary-color);
    margin-bottom: 15px;
    text-align: left;
}

.privacy-content p, .privacy-content ul {
    margin-bottom: 15px;
    line-height: 1.7;
    color: #333;
}

.privacy-content ul {
    padding-left: 25px;
}

.privacy-content li {
    margin-bottom: 8px;
}

.privacy-content strong {
    font-weight: 600;
}

.contact-info {
    padding: 10px 15px;
    background-color: #f5f7fa;
    border-left: 4px solid var(--primary-color);
    font-weight: 500;
}

/* 为logo添加链接样式 */
.logo h1 a {
    color: inherit;
    text-decoration: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .privacy-container {
        margin-top: 100px;
    }
    
    .privacy-content {
        padding: 25px;
    }
    
    .privacy-content h1 {
        font-size: 2rem;
    }
    
    .privacy-content h2 {
        font-size: 1.2rem;
    }
}