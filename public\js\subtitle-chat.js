// 字幕问答功能管理器
class SubtitleChatManager {
    constructor() {
        this.modal = null;
        this.currentContext = null;
        this.currentSubtitle = null;
        this.previousSubtitle = null;
        this.nextSubtitle = null;
        this.subtitleIndex = -1;
        this.isLoading = false;
        this.isPlaying = false;
        this.playInterval = null;
        
        this.init();
        
        // 监听语言切换事件（如果有）
        document.addEventListener('languageChanged', () => {
            this.updateLocalizedTexts();
        });
    }
    
    init() {
        this.createModal();
        this.bindEvents();
    }
    
    createModal() {
        if (document.getElementById('subtitleChatModal')) {
            this.modal = document.getElementById('subtitleChatModal');
            return;
        }
        
        const titleText = this.getLocalizedText('subtitle.chat.title') || '字幕分析';
        const placeholderText = this.getLocalizedText('subtitle.chat.placeholder') || '可以继续提问...';
        const sendText = this.getLocalizedText('subtitle.chat.send') || '发送';
        const closeText = this.getLocalizedText('subtitle.chat.close') || '关闭';
        const playText = this.getLocalizedText('subtitle.chat.play') || '播放';
        
        const modalHTML = `
            <div id="subtitleChatModal" class="subtitle-chat-modal">
                <div class="subtitle-chat-content">
                    <div class="subtitle-chat-header">
                        <h3 class="subtitle-chat-title">${titleText}</h3>
                        <div class="subtitle-chat-controls">
                            <button id="subtitlePlayPauseBtn" class="subtitle-play-btn" title="${playText}">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="subtitle-chat-close" title="${closeText}">&times;</button>
                        </div>
                    </div>
                    <div class="subtitle-chat-body">
                        <div class="subtitle-context-display" id="subtitleContextDisplay">
                            <!-- 上下文字幕将显示在这里 -->
                        </div>
                        <div class="subtitle-chat-messages" id="subtitleChatMessages"></div>
                        <div class="subtitle-chat-input">
                            <div class="chat-input-container">
                                <textarea id="subtitleChatInput" class="chat-input" placeholder="${placeholderText}" rows="1"></textarea>
                                <button id="subtitleChatSendBtn" class="chat-send-btn">${sendText}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = document.getElementById('subtitleChatModal');
        
        // 确保初始语言文本正确
        this.updateLocalizedTexts();
    }
    
    bindEvents() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('subtitle-chat-close')) {
                this.closeModal();
            }
            if (e.target.id === 'subtitleChatModal') {
                this.closeModal();
            }
            if (e.target.id === 'subtitleChatSendBtn') {
                this.handleSendMessage();
            }
            if (e.target.id === 'subtitlePlayPauseBtn' || e.target.closest('#subtitlePlayPauseBtn')) {
                this.togglePlayPause();
            }
        });
        
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal && this.modal.style.display === 'flex') {
                this.closeModal();
            }
            if (e.target.id === 'subtitleChatInput' && e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.handleSendMessage();
            }
        });
    }
    
    async openModal(subtitleIndex, subtitles) {
        if (subtitleIndex < 0 || !subtitles || subtitles.length === 0) {
            console.error('SubtitleChatManager: 无效的字幕索引或字幕数据');
            return;
        }
        
        this.subtitleIndex = subtitleIndex;
        this.currentSubtitle = subtitles[subtitleIndex];
        this.previousSubtitle = subtitleIndex > 0 ? subtitles[subtitleIndex - 1] : null;
        this.nextSubtitle = subtitleIndex < subtitles.length - 1 ? subtitles[subtitleIndex + 1] : null;
        
        // 构建上下文
        this.buildContext();
        
        // 重复播放当前字幕行
        this.repeatCurrentSubtitle();
        
        // 清空之前的消息
        const messagesContainer = document.getElementById('subtitleChatMessages');
        messagesContainer.innerHTML = '';
        
        const chatInput = document.getElementById('subtitleChatInput');
        chatInput.value = '';
        
        // 显示上下文
        this.displayContext();
        
        // 再次刷新文本，防止语言文件稍后才加载
        this.updateLocalizedTexts();
        
        this.modal.style.display = 'flex';
        setTimeout(() => this.modal.classList.add('show'), 10);
        
        setTimeout(() => chatInput.focus(), 300);
        
        // 自动发送默认分析请求
        const defaultQuestion = this.getLocalizedText('subtitle.chat.default_question') || '请分析这个句子的含义、语法结构和习语';
        await this.sendMessage(defaultQuestion, true);
    }
    
    closeModal() {
        this.modal.classList.remove('show');
        setTimeout(() => this.modal.style.display = 'none', 300);
        this.isLoading = false;
        this.stopPlayback();
        this.updateSendButton();
    }
    
    buildContext() {
        this.currentContext = {
            previous: this.previousSubtitle ? this.previousSubtitle.text : '',
            current: this.currentSubtitle ? this.currentSubtitle.text : '',
            next: this.nextSubtitle ? this.nextSubtitle.text : ''
        };
    }
    
    displayContext() {
        const contextDisplay = document.getElementById('subtitleContextDisplay');
        if (!contextDisplay) return;
        
        let contextHTML = '';
        
        // 构建连续的文本段落，当前句加粗显示
        let textContent = '';
        if (this.previousSubtitle) {
            textContent += this.previousSubtitle.text + ' ';
        }
        if (this.currentSubtitle) {
            textContent += `<strong>${this.currentSubtitle.text}</strong> `;
        }
        if (this.nextSubtitle) {
            textContent += this.nextSubtitle.text;
        }
        
        contextHTML = `<div class="context-paragraph">${textContent}</div>`;
        
        contextDisplay.innerHTML = contextHTML;
    }
    
    repeatCurrentSubtitle() {
        if (!this.currentSubtitle || !window.SubtitleManager) return;
        
        // 播放当前字幕的时间段
        const startTime = this.currentSubtitle.startTime;
        const endTime = this.currentSubtitle.endTime;
        
        // 跳转到开始时间并播放
        if (window.SubtitleManager.seekToTime) {
            window.SubtitleManager.seekToTime(startTime);
        }
        
        // 设置自动在结束时间暂停（如果有音频控制功能）
        this.schedulePlaybackEnd(endTime - startTime);
        this.isPlaying = true;
        this.updatePlayPauseButton();
    }
    
    schedulePlaybackEnd(duration) {
        if (this.playInterval) {
            clearTimeout(this.playInterval);
        }
        
        this.playInterval = setTimeout(() => {
            this.pausePlayback();
        }, duration * 1000);
    }
    
    togglePlayPause() {
        if (this.isPlaying) {
            this.pausePlayback();
        } else {
            this.repeatCurrentSubtitle();
        }
    }
    
    pausePlayback() {
        this.isPlaying = false;
        if (this.playInterval) {
            clearTimeout(this.playInterval);
            this.playInterval = null;
        }
        
        // 使用 SubtitleManager 获取当前活跃播放器进行暂停
        if (window.SubtitleManager && typeof window.SubtitleManager.getActiveAudioPlayer === 'function') {
            const activePlayer = window.SubtitleManager.getActiveAudioPlayer();
            if (activePlayer && typeof activePlayer.pause === 'function') {
                activePlayer.pause();
            }
        }
        
        this.updatePlayPauseButton();
    }
    
    stopPlayback() {
        this.pausePlayback();
        this.isPlaying = false;
    }
    
    updatePlayPauseButton() {
        const btn = document.getElementById('subtitlePlayPauseBtn');
        if (!btn) return;
        
        const icon = btn.querySelector('i');
        if (this.isPlaying) {
            icon.className = 'fas fa-pause';
            btn.title = this.getLocalizedText('subtitle.chat.pause') || '暂停';
        } else {
            icon.className = 'fas fa-play';
            btn.title = this.getLocalizedText('subtitle.chat.play') || '播放';
        }
    }
    
    async handleSendMessage() {
        const chatInput = document.getElementById('subtitleChatInput');
        const message = chatInput.value.trim();
        
        if (!message || this.isLoading) return;
        
        this.isLoading = true;
        this.updateSendButton();
        
        try {
            chatInput.value = '';
            await this.sendMessage(message, false);
        } catch (error) {
            this.isLoading = false;
            this.updateSendButton();
        }
    }
    
    async sendMessage(message, isAutomatic = false) {
        // 立即显示用户消息和加载提示，提供即时的视觉反馈
        if (!isAutomatic) {
            this.addMessage(message, 'user');
        }
        
        // 立即显示"正在思考中"提示
        const loadingId = this.addLoadingMessage();
        
        // 设置加载状态
        this.isLoading = true;
        this.updateSendButton();
        
        try {
            // 前端积分初查：快速缓存检查
            if (window.CreditsManager) {
                const creditsManager = window.CreditsManager;
                const required = 5; // 字幕问答预估积分
                let cached = creditsManager.getCachedCredits(); if (cached === null) cached = 0;
                if (cached < required) {
                    // 缓存积分不足，移除加载提示并弹出模态框
                    this.removeMessage(loadingId);
                    this.isLoading = false; this.updateSendButton();
                    creditsManager.showInsufficientCreditsAlert(cached);
                    return;
                }
            }
            // 后端统一检查
            const hasCredits = await this.checkCredits();
            if (!hasCredits) {
                // 全局检查会弹框，移除加载提示
                this.removeMessage(loadingId);
                this.isLoading = false; this.updateSendButton();
                return;
            }
            
            await this.callAPI(message, loadingId, isAutomatic);
            
        } catch (error) {
            console.error('发送消息失败:', error);
            this.removeMessage(loadingId);
            this.showError(this.getLocalizedText('subtitle.chat.error_api_failed') || 'AI分析失败，请重试');
        } finally {
            this.isLoading = false;
            this.updateSendButton();
        }
    }
    
    async callAPI(userQuestion, loadingId, isAutomatic) {
        const response = await fetch('/api/subtitle-chat-stream', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify({
                current_subtitle: this.currentContext.current,
                previous_subtitle: this.currentContext.previous,
                next_subtitle: this.currentContext.next,
                user_question: isAutomatic ? '' : userQuestion,
                model: this.getCurrentModel(),
                temperature: 0.3,
                skip_credits_check: await this.shouldSkipCreditsCheck()
            })
        });
        
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status}`);
        }
        
        this.removeMessage(loadingId);
        const aiMessageId = this.addMessage('', 'ai');
        await this.handleStreamResponse(response, aiMessageId);
    }
    
    async shouldSkipCreditsCheck() {
        // 使用新的动态免检查机制
        if (!window.CreditsManager) return false;
        
        try {
            // 检查辅助功能免检查状态
            if (typeof window.CreditsManager.checkAuxiliarySkipStatus === 'function') {
                const skipStatus = window.CreditsManager.checkAuxiliarySkipStatus();
                
                if (skipStatus.canSkip) {
                    console.log(`🚀 [字幕问答] 辅助功能免检查生效，剩余${skipStatus.remainingMinutes}分钟`);
                    return true;
                } else {
                    console.log(`🔍 [字幕问答] 辅助功能免检查不生效(${skipStatus.reason})`);
                    return false;
                }
            }
            
            // 降级到原有逻辑
            const cachedCredits = window.CreditsManager.getCachedCredits();
            const requiredCredits = 5; // 字幕问答预估积分
            return cachedCredits !== null && cachedCredits >= (requiredCredits + 10);
        } catch (error) {
            console.error('检查是否跳过积分检查失败:', error);
            return false;
        }
    }
    
    async handleStreamResponse(response, messageId) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let accumulatedText = '';
        let creditsUpdated = false;
        
        try {
            while (true) {
                const { value, done } = await reader.read();
                if (done) break;
                
                const chunk = decoder.decode(value, { stream: true });
                accumulatedText += chunk;
                
                // 处理积分更新标签
                if (chunk.includes('<!-- CREDITS_UPDATE:') && !creditsUpdated) {
                    if (window.CreditsManager && typeof window.CreditsManager.processStreamCreditsUpdate === 'function') {
                        creditsUpdated = window.CreditsManager.processStreamCreditsUpdate(chunk);
                        if (creditsUpdated) {
                            console.log('从字幕问答流式响应中更新了积分，跳过后续API调用');
                        }
                    }
                }
                
                const displayText = this.cleanResponseText(accumulatedText);
                this.updateMessageContent(messageId, displayText);
                this.scrollToBottom();
            }
            
            // 只有在没有从流式响应中更新积分时才调用API
            if (!creditsUpdated && window.CreditsManager && typeof window.CreditsManager.fetchCreditsFromServer === 'function') {
                console.log('未收到流式积分更新，使用API获取最新积分');
                setTimeout(() => window.CreditsManager.fetchCreditsFromServer(true), 1000);
            }
        } catch (error) {
            if (error.name !== 'AbortError') {
                console.error('处理流式响应失败:', error);
                this.updateMessageContent(messageId, this.getLocalizedText('subtitle.chat.error_api_failed') || 'AI分析失败，请重试');
            }
        }
    }
    
    cleanResponseText(rawText) {
        // 移除积分更新标签和其他控制标签
        return rawText
            .replace(/<!--[\s\S]*?-->/g, '')
            .replace(/\[TOKEN_USAGE:[\s\S]*?\]/g, '')
            .trim();
    }
    
    addMessage(content, type) {
        const messagesContainer = document.getElementById('subtitleChatMessages');
        const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${type}`;
        messageDiv.id = messageId;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        contentDiv.textContent = content;
        
        messageDiv.appendChild(contentDiv);
        messagesContainer.appendChild(messageDiv);
        
        this.scrollToBottom();
        return messageId;
    }
    
    addLoadingMessage() {
        const messagesContainer = document.getElementById('subtitleChatMessages');
        const messageId = 'loading_' + Date.now();
        
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message ai loading';
        messageDiv.id = messageId;
        
        const loadingText = this.getLocalizedText('subtitle.chat.thinking') || 'AI正在思考中';
        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="thinking-indicator">
                    <span class="thinking-text">${loadingText}</span>
                    <span class="thinking-dots">
                        <span></span><span></span><span></span>
                    </span>
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
        return messageId;
    }
    
    removeMessage(messageId) {
        const message = document.getElementById(messageId);
        if (message) {
            message.remove();
        }
    }
    
    updateMessageContent(messageId, content) {
        const message = document.getElementById(messageId);
        if (message) {
            const contentDiv = message.querySelector('.message-content');
            if (contentDiv) {
                contentDiv.textContent = content;
            }
        }
    }
    
    scrollToBottom() {
        const messagesContainer = document.getElementById('subtitleChatMessages');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }
    
    updateSendButton() {
        const sendBtn = document.getElementById('subtitleChatSendBtn');
        if (sendBtn) {
            sendBtn.disabled = this.isLoading;
            sendBtn.style.opacity = this.isLoading ? '0.6' : '1';
        }
    }
    
    showError(message) {
        this.addMessage(message, 'error');
    }
    
    async checkCredits() {
        if (!window.checkCredits) {
            console.warn('积分检查功能不可用');
            return true;
        }
        
        try {
            return await window.checkCredits('subtitle_chat', this.getCurrentModel());
        } catch (error) {
            console.error('检查积分失败:', error);
            return true; // 出错时默认允许继续
        }
    }
    
    getCurrentModel() {
        if (window.currentModel) {
            return window.convertModelNameToId ? 
                window.convertModelNameToId(window.currentModel) : 
                window.currentModel;
        }
        return 'glm-4-flash'; // 默认模型
    }
    
    getLocalizedText(key) {
        if (window.UILanguage && typeof window.UILanguage.getText === 'function') {
            return window.UILanguage.getText(key) || key;
        }
        return key;
    }
    
    // 新增：根据当前语言更新所有文本
    updateLocalizedTexts() {
        if (!this.modal) return;
        const titleEl = this.modal.querySelector('.subtitle-chat-title');
        const inputEl = this.modal.querySelector('#subtitleChatInput');
        const sendBtn = this.modal.querySelector('#subtitleChatSendBtn');
        const playBtn = this.modal.querySelector('#subtitlePlayPauseBtn');
        
        if (titleEl) titleEl.textContent = this.getLocalizedText('subtitle.chat.title') || '字幕问答';
        if (inputEl) inputEl.placeholder = this.getLocalizedText('subtitle.chat.placeholder') || '可以继续提问...';
        if (sendBtn) sendBtn.textContent = this.getLocalizedText('subtitle.chat.send') || '发送';
        if (playBtn) playBtn.title = this.getLocalizedText(this.isPlaying ? 'subtitle.chat.pause' : 'subtitle.chat.play');
    }
}

// 创建全局实例
window.subtitleChatManager = new SubtitleChatManager(); 