/* 积分系统样式 */

/* 用户积分显示 */
.user-credits-container {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    background-color: rgba(33, 150, 243, 0.1);
    border: 2px solid #2196F3;
    border-radius: 10px;
    font-size: 16px;
    color: #333;
    height: 44px; /* 保持与其他元素一致的高度 */
    transition: all 0.3s ease;
    cursor: pointer; /* 添加此行 */
}

.user-credits-container:hover {
    background-color: rgba(33, 150, 243, 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 积分显示区域样式 */
.credits-display {
    display: flex;
    align-items: center;
    margin-right: 10px; /* 与充值按钮保持一定距离 */
}

.credits-icon {
    margin-right: 5px;
    font-size: 16px;
    color: #3498db; /* 蓝色 */
}

.credits-amount {
    font-weight: bold;
    color: #3498db; /* 蓝色 */
    display: inline-block;
    min-width: 60px; /* 为6位数预留足够空间 */
    text-align: right; /* 数字右对齐 */
    margin-right: 15px; /* 与充值按钮保持固定距离 */
}

/* 积分变动动画 */
@keyframes creditChange {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
        color: #2980b9; /* 深蓝色 */
    }
    100% {
        transform: scale(1);
    }
}

.credits-changed {
    animation: creditChange 0.5s ease;
}

/* 积分不足提示 */
.credits-alert {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.credits-alert-content {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    width: 350px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.credits-alert h3 {
    margin-top: 0;
    color: #333;
    font-size: 18px;
}

.credits-alert p {
    margin: 10px 0;
    color: #666;
    font-size: 16px; /* 修改为你需要的字号 */
}

.credits-alert-buttons {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.credits-alert-buttons button {
    padding: 8px 16px;
    margin: 0 5px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.credits-alert-buttons button:first-child {
    background-color: #f0f0f0;
    color: #333;
}

.credits-alert-buttons button:first-child:hover {
    background-color: #e0e0e0;
}

.credits-alert-buttons button:last-child {
    background-color: #3498db; /* 蓝色 */
    color: white;
}

.credits-alert-buttons button:last-child:hover {
    background-color: #2980b9; /* 深蓝色 */
}

/* 充值弹窗 */
.recharge-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    z-index: 1200; /* 增加z-index值，确保高于模型选择器 */
}

.recharge-modal-content {
    background-color: #fff;
    border-radius: 8px;
    padding: 25px;
    width: 300px;
    max-width: 60%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: relative;
}

.close-modal {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    color: #aaa;
    cursor: pointer;
    transition: color 0.3s;
}

.close-modal:hover {
    color: #333;
}

.recharge-modal h2 {
    margin-top: 0;
    color: #333;
    text-align: center;
    margin-bottom: 20px;
}

.recharge-options {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.recharge-option {
    width: 30%;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    margin-bottom: 15px;
    border: 2px solid transparent;
    transition: all 0.3s;
}

.recharge-option:hover {
    border-color: #3498db; /* 蓝色 */
    transform: translateY(-5px);
}

.recharge-option h3 {
    margin-top: 0;
    color: #333;
    font-size: 18px;
}

.recharge-option p {
    font-size: 20px;
    font-weight: bold;
    color: #3498db; /* 蓝色 */
    margin: 10px 0;
}

/* 充值按钮样式 */
.recharge-button {
    background-color: #3498db; /* 蓝色 */
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
    white-space: nowrap; /* 防止文本换行 */
    flex-shrink: 0; /* 防止按钮被压缩 */
    position: relative; /* 确保按钮位置固定 */
}

.recharge-button:hover {
    background-color: #2980b9; /* 深蓝色 */
}

.payment-qrcode {
    text-align: center;
}

.payment-qrcode h3 {
    margin-top: 0;
    color: #333;
}

.qrcode-container {
    margin: 20px 0;
    display: flex;
    justify-content: center;
}

.qrcode-container img {
    max-width: 200px;
    border: 1px solid #eee;
    padding: 10px;
    background-color: #fff;
}

#paymentCompleted {
    background-color: #3498db; /* 蓝色 */
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
    margin-top: 10px;
}

#paymentCompleted:hover {
    background-color: #2980b9; /* 深蓝色 */
}

/* 现代化积分加载动画 - 脉冲圆点 */
.credits-loading {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    vertical-align: middle;
    margin: 0 4px;
}

.credits-loading::before,
.credits-loading::after {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #4a5568;
    animation: credits-pulse 1.4s ease-in-out infinite;
}

.credits-loading::after {
    animation-delay: 0.2s;
}

@keyframes credits-pulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.4;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

/* 备选样式1：三点跳动 */
.credits-loading-bounce {
    display: inline-flex;
    align-items: center;
    gap: 1px;
    vertical-align: middle;
    margin: 0 4px;
}

.credits-loading-bounce::before,
.credits-loading-bounce::after {
    content: '•';
    color: #4a5568;
    font-size: 12px;
    animation: credits-bounce 1.2s ease-in-out infinite;
}

.credits-loading-bounce::after {
    animation-delay: 0.3s;
}

@keyframes credits-bounce {
    0%, 80%, 100% {
        transform: translateY(0);
        opacity: 0.6;
    }
    40% {
        transform: translateY(-2px);
        opacity: 1;
    }
}

/* 备选样式2：旋转渐变圆环 */
.credits-loading-ring {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: conic-gradient(from 0deg, transparent 0deg, #4a5568 360deg);
    animation: credits-spin 1s linear infinite;
    vertical-align: middle;
    margin: 0 4px;
    position: relative;
}

.credits-loading-ring::before {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    background: white;
    border-radius: 50%;
}

@keyframes credits-spin {
    to {
        transform: rotate(360deg);
    }
}

/* 备选样式3：简洁文字动画 */
.credits-loading-text::after {
    content: '';
    animation: credits-dots 1.5s steps(4, end) infinite;
}

@keyframes credits-dots {
    0% { content: ''; }
    25% { content: '.'; }
    50% { content: '..'; }
    75% { content: '...'; }
    100% { content: ''; }
}

/* 响应式设计 */
@media (max-width: 600px) {
    .recharge-options {
        flex-direction: column;
    }
    
    .recharge-option {
        width: 100%;
        margin-bottom: 10px;
    }
    
    /* 积分显示区域样式 */
    .user-credits-container {
        min-width: 150px;
        padding: 5px 8px;
    }
}
