/* 积分历史记录样式 */
.credit-history-modal {
    display: none !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 9999 !important;
    justify-content: center !important;
    align-items: center !important;
}

/* 当模态框激活时的样式 */
.credit-history-modal.active,
.credit-history-modal[style*="flex"] {
    display: flex !important;
}

.credit-history-container {
    background-color: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    width: 90% !important;
    max-width: 1200px !important; /* 增加最大宽度，从1000px增加到1200px */
    max-height: 85vh !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
    z-index: 10000 !important;
}

.credit-history-header {
    display: flex;
    justify-content: center; /* 居中对齐 */
    align-items: center;
    padding: 16px 20px; /* 调整高度 */
    border-bottom: 1px solid #e8e8e8;
    background-color: #1e88e5; /* 使用应用主色调 */
    color: white;
    font-size: 22px; /* 标题栏文字大小 */
    position: relative; /* 添加相对定位 */
}

.credit-history-header h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
}

.credit-history-close {
    position: absolute; /* 绝对定位 */
    right: 20px; /* 距离右边20px */
    background: none;
    border: none;
    color: white;
    font-size: 22px;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.credit-history-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.credit-history-content {
    padding: 0 15px; /* 添加左右内边距，使内容与容器边缘保持适当距离 */
    overflow-y: auto;
    flex: 1;
    height: 400px; /* 固定高度，确保在加载数据之前和之后保持一致 */
    position: relative; /* 添加相对定位 */
}

.credit-history-table {
    width: 100%;
    border-collapse: collapse;
    margin: auto; /* 水平居中 */
    table-layout: fixed; /* 使用固定布局，控制列宽 */
}

.credit-history-table th {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e8e8e8;
    font-size: 16px !important; /* 增加表格内容文字大小 */
    font-weight: 500;
}

.credit-history-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e8e8e8;
    font-size: 15px;
}

/* 设置各列的宽度 - 调整为5列布局 */
.credit-history-table th:nth-child(1),
.credit-history-table td:nth-child(1) {
    width: 18%; /* 操作时间列 */
    white-space: nowrap; /* 不换行 */
    overflow: hidden;
    text-overflow: ellipsis;
}

.credit-history-table th:nth-child(2),
.credit-history-table td:nth-child(2) {
    width: 16%; /* 操作类型列 */
    white-space: nowrap; /* 不换行 */
    overflow: hidden;
    text-overflow: ellipsis;
}

.credit-history-table th:nth-child(3),
.credit-history-table td:nth-child(3) {
    width: 40%; /* 音频列，给予最大的空间 */
    max-width: 400px;
    white-space: normal; /* 允许文本换行 */
    word-wrap: break-word; /* 长单词换行 */
    word-break: break-word; /* 长单词换行 */
    overflow: hidden;
    text-overflow: ellipsis;
}

.credit-history-table th:nth-child(4),
.credit-history-table td:nth-child(4) {
    width: 13%; /* 积分变动列 */
    white-space: nowrap; /* 不换行 */
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

.credit-history-table th:nth-child(5),
.credit-history-table td:nth-child(5) {
    width: 13%; /* 当前积分列 */
    white-space: nowrap; /* 不换行 */
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

.credit-history-table th {
    background-color: #f5f5f5;
    font-weight: 500;
    color: #333;
    position: sticky;
    top: 0;
    font-size: 16px; /* 增加表头文字大小 */
}

.credit-history-table tr:hover {
    background-color: #f9f9f9;
}

.credit-history-table .credits-negative {
    color: #e53935;
}

.credit-history-table .credits-positive {
    color: #43a047;
}

/* 音频信息显示样式 */
.credit-history-table .audio-title {
    font-weight: bold;
    color: #1e88e5;
    display: block;
    margin-bottom: 4px;
    font-size: 14px;
}

.credit-history-empty {
    padding: 40px;
    text-align: center;
    color: #757575;
    font-size: 18px; 
    font-weight: 500;
}

.credit-history-footer {
    padding: 12px 20px; /* 调整高度 */
    border-top: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f5f5f5;
    font-size: 15px;
}

.credit-history-footer .current-credits {
    font-weight: 500;
    color: #1e88e5;
    font-size: 14px; /* 调整当前积分的字号 */
}

.credit-history-pagination {
    display: flex;
    align-items: center;
    font-size: 14px; /* 调整翻页的字号 */
}

.pagination-button {
    background: none;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 12px;
    margin: 0 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.pagination-button:hover:not(:disabled) {
    background-color: #e3f2fd;
    border-color: #1e88e5;
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-info {
    margin: 0 10px;
}

/* 加载状态 */
.credit-history-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute; /* 添加绝对定位 */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8); /* 添加半透明背景 */
    z-index: 10; /* 确保加载状态在最上层 */
}

.credit-history-loading .spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1e88e5;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin-right: 10px;
    font-size: 14px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 书籍和章节显示样式 - 修改为水平布局 */
.book-chapter-container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: baseline;
    gap: 6px;
    width: 100%;
}

.book-title {
    font-weight: bold;
    color: #333;
    display: inline;
}

.chapter-title {
    font-size: 0.9em;
    color: #666;
    display: inline;
    border-left: none;
    padding-left: 0;
}

.chapter-divider {
    color: #999;
    display: inline;
}
