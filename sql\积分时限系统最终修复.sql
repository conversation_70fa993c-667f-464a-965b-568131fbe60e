-- ========================================
-- 积分时限管理系统完整修复SQL脚本
-- 修复日期: 2025-06-20
-- 修复内容: 确保credit_batches表正确记录每个积分批次的详细信息
-- ========================================

-- 1. 修复添加带有效期的积分函数 - 确保每次充值创建独立批次
CREATE OR REPLACE FUNCTION public.add_credits_with_expiry(
    p_user_id UUID,
    p_credits INTEGER,
    p_source_type VARCHAR(50) DEFAULT 'purchase',
    p_validity_days INTEGER DEFAULT 30,
    p_order_id VARCHAR(255) DEFAULT NULL,
    p_description TEXT DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_batch_id UUID;
    v_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
    -- 计算到期时间
    v_expires_at := NOW() + INTERVAL '1 day' * p_validity_days;
    
    -- 为每次充值创建独立的积分批次记录
    INSERT INTO public.credit_batches (
        user_id, credits_amount, remaining_credits, source_type, 
        expires_at, order_id, description
    ) VALUES (
        p_user_id, p_credits, p_credits, p_source_type,
        v_expires_at, p_order_id, p_description
    ) RETURNING id INTO v_batch_id;
    
    -- 更新用户总积分
    INSERT INTO public.user_credits (user_id, credits, created_at, updated_at)
    VALUES (p_user_id, p_credits, NOW(), NOW())
    ON CONFLICT (user_id) 
    DO UPDATE SET 
        credits = user_credits.credits + p_credits,
        updated_at = NOW();
    
    -- 记录积分操作
    INSERT INTO public.credit_operations (
        user_id, operation_type, credits_amount, current_credits,
        batch_id, operation_time
    ) VALUES (
        p_user_id, p_source_type, p_credits, 
        (SELECT credits FROM public.user_credits WHERE user_id = p_user_id),
        v_batch_id, NOW()
    );
    
    RAISE NOTICE '已为用户 % 添加 % 积分，批次ID: %, 到期时间: %', p_user_id, p_credits, v_batch_id, v_expires_at;
    
    RETURN v_batch_id;
END;
$$;

-- 2. 创建获取用户积分批次详情的函数
CREATE OR REPLACE FUNCTION public.get_user_credit_batches(p_user_id UUID)
RETURNS TABLE(
    batch_id UUID,
    credits_amount INTEGER,
    remaining_credits INTEGER,
    source_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_expired BOOLEAN,
    days_until_expiry INTEGER,
    order_id VARCHAR(255),
    description TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cb.id,
        cb.credits_amount,
        cb.remaining_credits,
        cb.source_type,
        cb.created_at,
        cb.expires_at,
        cb.is_expired,
        CASE 
            WHEN cb.expires_at <= NOW() THEN 0
            ELSE EXTRACT(DAY FROM cb.expires_at - NOW())::INTEGER
        END as days_until_expiry,
        cb.order_id,
        cb.description
    FROM public.credit_batches cb
    WHERE cb.user_id = p_user_id 
      AND cb.remaining_credits > 0
      AND NOT cb.is_expired
    ORDER BY cb.created_at ASC; -- 按创建时间排序，体现FIFO原则
END;
$$;

-- 3. 创建积分使用记录视图，包含批次信息
CREATE OR REPLACE VIEW public.user_credits_usage_detail AS
SELECT 
    co.id,
    co.user_id,
    co.operation_type,
    co.model,
    co.credits_amount,
    co.current_credits,
    co.operation_time,
    co.book_name,
    co.chapter_title,
    co.batch_id,
    cb.source_type as batch_source_type,
    cb.expires_at as batch_expires_at,
    CASE 
        WHEN co.credits_amount < 0 THEN '积分扣减'
        ELSE '积分增加'
    END as operation_category
FROM public.credit_operations co
LEFT JOIN public.credit_batches cb ON co.batch_id = cb.id
ORDER BY co.operation_time DESC;

-- 4. 创建用户积分总览函数，包含当前积分和即将到期的积分
CREATE OR REPLACE FUNCTION public.get_user_credits_summary(p_user_id UUID)
RETURNS TABLE(
    total_credits INTEGER,
    active_batches INTEGER,
    credits_expiring_soon INTEGER,
    earliest_expiry_date TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_total_credits INTEGER := 0;
    v_active_batches INTEGER := 0;
    v_expiring_soon INTEGER := 0;
    v_earliest_expiry TIMESTAMP WITH TIME ZONE;
BEGIN
    -- 获取总积分
    SELECT COALESCE(credits, 0) INTO v_total_credits
    FROM public.user_credits 
    WHERE user_id = p_user_id;
    
    -- 获取活跃批次数量
    SELECT COUNT(*) INTO v_active_batches
    FROM public.credit_batches 
    WHERE user_id = p_user_id 
      AND remaining_credits > 0 
      AND NOT is_expired
      AND expires_at > NOW();
    
    -- 获取7天内即将过期的积分总数
    SELECT COALESCE(SUM(remaining_credits), 0) INTO v_expiring_soon
    FROM public.credit_batches 
    WHERE user_id = p_user_id 
      AND remaining_credits > 0 
      AND NOT is_expired
      AND expires_at > NOW()
      AND expires_at <= (NOW() + INTERVAL '7 days');
    
    -- 获取最早的到期日期
    SELECT MIN(expires_at) INTO v_earliest_expiry
    FROM public.credit_batches 
    WHERE user_id = p_user_id 
      AND remaining_credits > 0 
      AND NOT is_expired
      AND expires_at > NOW();
    
    RETURN QUERY 
    SELECT v_total_credits, v_active_batches, v_expiring_soon, v_earliest_expiry;
END;
$$;

-- 5. 修复现有数据 - 为充值记录创建对应的积分批次
DO $$
DECLARE
    recharge_record RECORD;
    v_batch_id UUID;
BEGIN
    RAISE NOTICE '开始修复现有充值记录的积分批次数据...';
    
    -- 为每个充值操作创建对应的积分批次
    FOR recharge_record IN 
        SELECT user_id, credits_amount, operation_time, id as operation_id
        FROM public.credit_operations 
        WHERE operation_type = 'recharge' OR operation_type = '积分充值'
        ORDER BY operation_time ASC
    LOOP
        -- 检查是否已有对应的积分批次
        IF NOT EXISTS (
            SELECT 1 FROM public.credit_batches 
            WHERE user_id = recharge_record.user_id 
              AND credits_amount = recharge_record.credits_amount
              AND ABS(EXTRACT(EPOCH FROM (created_at - recharge_record.operation_time))) < 60
        ) THEN
            -- 创建积分批次记录
            INSERT INTO public.credit_batches (
                user_id, credits_amount, remaining_credits, source_type,
                created_at, expires_at, order_id
            ) VALUES (
                recharge_record.user_id, 
                recharge_record.credits_amount, 
                recharge_record.credits_amount, 
                'purchase',
                recharge_record.operation_time,
                recharge_record.operation_time + INTERVAL '30 days',
                'migrated_' || recharge_record.operation_id
            ) RETURNING id INTO v_batch_id;
            
            -- 更新对应的操作记录
            UPDATE public.credit_operations 
            SET batch_id = v_batch_id 
            WHERE id = recharge_record.operation_id;
            
            RAISE NOTICE '为充值记录创建积分批次: 用户=%, 积分=%, 批次ID=%', 
                         recharge_record.user_id, recharge_record.credits_amount, v_batch_id;
        END IF;
    END LOOP;
    
    RAISE NOTICE '积分批次数据修复完成';
END;
$$;

-- 6. 为新用户注册赠送积分创建批次
CREATE OR REPLACE FUNCTION public.handle_verified_user_with_batch()
RETURNS TRIGGER AS $$
DECLARE
    v_batch_id UUID;
BEGIN
    -- 只为已验证邮箱的用户创建积分记录
    IF NEW.email_confirmed_at IS NOT NULL THEN
        -- 检查是否已经有积分记录
        IF NOT EXISTS (SELECT 1 FROM public.user_credits WHERE user_id = NEW.id) THEN
            -- 创建积分批次
            SELECT public.add_credits_with_expiry(
                NEW.id, 
                30, 
                'signup_bonus', 
                30, 
                NULL, 
                '新用户注册赠送积分'
            ) INTO v_batch_id;
            
            RAISE NOTICE '为新用户 % 创建注册赠送积分批次: %', NEW.id, v_batch_id;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. 权限设置
GRANT EXECUTE ON FUNCTION public.add_credits_with_expiry TO service_role, authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_credit_batches TO service_role, authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_credits_summary TO service_role, authenticated;
GRANT SELECT ON public.user_credits_usage_detail TO service_role, authenticated;

-- 8. 创建积分到期提醒视图
CREATE OR REPLACE VIEW public.credits_expiry_alerts AS
SELECT 
    user_id,
    COUNT(*) as expiring_batches,
    SUM(remaining_credits) as expiring_credits,
    MIN(expires_at) as earliest_expiry,
    EXTRACT(DAY FROM MIN(expires_at) - NOW()) as days_until_expiry
FROM public.credit_batches
WHERE remaining_credits > 0 
  AND NOT is_expired
  AND expires_at > NOW()
  AND expires_at <= (NOW() + INTERVAL '7 days')
GROUP BY user_id
ORDER BY earliest_expiry ASC;

-- 8. 完成提示
DO $$
BEGIN
    RAISE NOTICE '积分时限系统修复完成！';
    RAISE NOTICE '- 修复了credit_batches表的批次记录问题';
    RAISE NOTICE '- 添加了get_user_credit_batches函数获取用户积分批次详情';
    RAISE NOTICE '- 添加了get_user_credits_summary函数获取积分总览';
    RAISE NOTICE '- 创建了user_credits_usage_detail视图包含批次信息';
    RAISE NOTICE '- 为现有充值记录创建了对应的积分批次';
    RAISE NOTICE '- 添加了credits_expiry_alerts视图用于到期提醒';
END;
$$; 