/**
 * 资源加载器 - 处理CDN资源加载失败的情况
 * 提供多CDN源尝试、本地备份和降级功能
 */

class ResourceLoader {
  constructor() {
    // 创建自定义事件，用于通知所有库都已加载
    this.librariesLoadedEvent = new CustomEvent('libraries-loaded');
    
    // 添加页面类型检测
    this.currentPage = this.detectCurrentPage();
    
    // 需要加载的库及其备选源
    this.libraries = [
      {
        name: 'marked',
        global: 'marked',
        cdnUrls: [
          'https://cdn.jsdelivr.net/npm/marked/marked.min.js',
          'https://unpkg.com/marked@4.3.0/marked.min.js',
          'https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js',
          'https://fastly.jsdelivr.net/npm/marked/marked.min.js',
          'https://gcore.jsdelivr.net/npm/marked/marked.min.js'
        ],
        localUrl: '/js/lib/marked.min.js',
        loaded: false,
        required: this.currentPage === 'main',
        initFunction: this.initMarked
      },
      {
        name: 'mermaid',
        global: 'mermaid',
        cdnUrls: [
          'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js',
          'https://unpkg.com/mermaid@10/dist/mermaid.min.js',
          'https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.0.0/mermaid.min.js',
          'https://fastly.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js',
          'https://gcore.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js'
        ],
        localUrl: '/js/lib/mermaid.min.js',
        loaded: false,
        required: this.currentPage === 'main',
        initFunction: this.initMermaid
      },
      {
        name: 'd3',
        global: 'd3',
        cdnUrls: [
          'https://d3js.org/d3.v7.min.js',
          'https://cdn.jsdelivr.net/npm/d3@7/dist/d3.min.js',
          'https://cdnjs.cloudflare.com/ajax/libs/d3/7.0.0/d3.min.js',
          'https://fastly.jsdelivr.net/npm/d3@7/dist/d3.min.js',
          'https://gcore.jsdelivr.net/npm/d3@7/dist/d3.min.js'
        ],
        localUrl: '/js/lib/d3.v7.min.js',
        loaded: false,
        required: this.currentPage === 'main'
      },
      {
        name: 'supabase',
        global: 'supabase',
        cdnUrls: [
          'https://cdn.jsdelivr.net/npm/@supabase/supabase-js/dist/umd/supabase.min.js',
          'https://unpkg.com/@supabase/supabase-js/dist/umd/supabase.min.js',
          'https://cdnjs.cloudflare.com/ajax/libs/supabase-js/2.21.0/umd/supabase.min.js',
          'https://fastly.jsdelivr.net/npm/@supabase/supabase-js/dist/umd/supabase.min.js',
          'https://gcore.jsdelivr.net/npm/@supabase/supabase-js/dist/umd/supabase.min.js'
        ],
        localUrl: '/js/lib/supabase.min.js',
        loaded: false,
        required: true
      }
    ];
    
    // 初始化状态
    this.allLoaded = false;
    this.loadStarted = false;
    this.loadTimeout = null;
    this.useLocalOnly = localStorage.getItem('useLocalLibs') === 'true';
    this.localFirst = localStorage.getItem('localFirst') === 'true';
    this.maxRetries = 2;
    this.currentRetry = 0;
    
    // 绑定方法
    this.loadLibraries = this.loadLibraries.bind(this);
    this.checkAllLoaded = this.checkAllLoaded.bind(this);
    this.createFallbacks = this.createFallbacks.bind(this);
    this.showRecoveryDialog = this.showRecoveryDialog.bind(this);
    this.detectLoadingIssues = this.detectLoadingIssues.bind(this);
    this.downloadIfMissing = this.downloadIfMissing.bind(this);
    this.detectCurrentPage = this.detectCurrentPage.bind(this);
    this.shouldShowNotifications = this.shouldShowNotifications.bind(this);
  }
  
  /**
   * 检测当前页面类型
   * @returns {string} 页面类型: 'index', 'log' 或 'main'
   */
  detectCurrentPage() {
    const path = window.location.pathname.toLowerCase();
    if (path.endsWith('/main.html')) return 'main';
    if (path.endsWith('/log.html')) return 'log';
    return 'index';
  }
  
  /**
   * 判断是否应该显示通知
   * @returns {boolean} 是否显示通知
   */
  shouldShowNotifications() {
    return this.currentPage === 'main';
  }
  
  /**
   * 显示资源恢复对话框
   */
  showRecoveryDialog() {
    // 如果不是main页面，不显示恢复对话框
    if (!this.shouldShowNotifications()) return;
    
    // 检查是否有必要显示恢复对话框
    const missingLibs = this.libraries.filter(lib => 
      lib.required && (window[lib.global] === undefined || typeof window[lib.global] !== 'object')
    );
    
    if (missingLibs.length === 0) return;
    
    // 创建对话框元素
    const dialogId = 'resource-recovery-dialog';
    let dialog = document.getElementById(dialogId);
    
    // 如果对话框已存在，不重复创建
    if (dialog) return;
    
    dialog = document.createElement('div');
    dialog.id = dialogId;
    dialog.className = 'resource-recovery-dialog';
    dialog.innerHTML = `
      <div class="recovery-content">
        <h3 data-i18n="recovery.title">资源加载问题</h3>
        <p data-i18n="recovery.message">部分资源加载失败，这可能导致应用功能异常。请尝试以下解决方法：</p>
        <div class="recovery-actions">
          <button id="reloadPage" class="action-button" data-i18n="recovery.reload">刷新页面</button>
          <button id="clearCache" class="action-button" data-i18n="recovery.clear_cache">清除缓存并刷新</button>
          <button id="switchToLocal" class="action-button" data-i18n="recovery.use_local">使用本地资源</button>
          <button id="localFirst" class="action-button" data-i18n="recovery.local_first">本地资源优先</button>
          <button id="dismissDialog" class="action-button" data-i18n="recovery.dismiss">忽略并继续</button>
        </div>
        <p style="margin-top: 15px; text-align: center;">
          <a href="/resource-recovery-guide" target="_blank" style="color: #4285f4; text-decoration: underline;" data-i18n="recovery.guide_link">查看详细修复指南</a>
        </p>
      </div>
    `;
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
      .resource-recovery-dialog {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 10000;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .recovery-content {
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }
      .recovery-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 20px;
      }
      .action-button {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        background-color: #4285f4;
        color: white;
        cursor: pointer;
        font-weight: bold;
        flex-grow: 1;
        min-width: 120px;
      }
      .action-button:hover {
        background-color: #3367d6;
      }
      #dismissDialog {
        background-color: #757575;
      }
      #dismissDialog:hover {
        background-color: #616161;
      }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(dialog);
    
    // 添加按钮事件
    document.getElementById('reloadPage').addEventListener('click', () => {
      window.location.reload();
    });
    
    document.getElementById('clearCache').addEventListener('click', () => {
      localStorage.removeItem('useLocalLibs');
      // 强制刷新，绕过缓存
      window.location.href = window.location.href + (window.location.href.includes('?') ? '&' : '?') + 'cache_bust=' + Date.now();
    });
    
    document.getElementById('switchToLocal').addEventListener('click', () => {
      localStorage.setItem('useLocalLibs', 'true');
      window.location.reload();
    });
    
    document.getElementById('localFirst').addEventListener('click', () => {
      localStorage.setItem('localFirst', 'true');
      window.location.reload();
    });
    
    document.getElementById('dismissDialog').addEventListener('click', () => {
      dialog.remove();
    });
    
    // 翻译对话框内容（如果UI语言系统已加载）
    if (window.UILanguage && typeof window.UILanguage.translateElement === 'function') {
      window.UILanguage.translateElement(dialog);
    }
  }
  
  /**
   * 检测资源加载问题
   */
  detectLoadingIssues() {
    // 如果不是main页面，不检测加载问题
    if (!this.shouldShowNotifications()) return;
    
    // 等待一段时间后检查
    setTimeout(() => {
      const requiredGlobals = this.libraries
        .filter(lib => lib.required)
        .map(lib => lib.global);
      
      const missingGlobals = requiredGlobals.filter(name => 
        window[name] === undefined || typeof window[name] !== 'object'
      );
      
      if (missingGlobals.length > 0) {
        console.warn(`检测到资源加载问题: ${missingGlobals.join(', ')}`);
        this.showRecoveryDialog();
      }
    }, 8000); // 给予8秒加载时间
  }
  
  /**
   * 从CDN下载缺失的文件到本地目录
   * @param {string} cdnUrl - CDN上文件的URL
   * @param {string} localPath - 本地保存路径
   * @returns {Promise<boolean>} - 是否下载成功
   */
  async downloadIfMissing(cdnUrl, localPath) {
    // 这个函数只能在服务器端使用，浏览器端无法直接写入文件系统
    // 可以通过向服务器发送请求实现
    try {
      // 构建一个下载请求的URL
      const downloadRequestUrl = `/api/download-resource?url=${encodeURIComponent(cdnUrl)}&path=${encodeURIComponent(localPath)}`;
      
      // 发送下载请求
      const response = await fetch(downloadRequestUrl);
      
      // 检查响应状态
      if (response.ok) {
        console.log(`成功下载文件: ${cdnUrl} -> ${localPath}`);
        return true;
      } else {
        console.error(`下载文件失败: ${cdnUrl}，状态码: ${response.status}`);
        return false;
      }
    } catch (error) {
      console.error(`下载文件出错: ${cdnUrl}`, error);
      return false;
    }
  }
  
  /**
   * 检查所有库是否已加载完成
   */
  checkAllLoaded() {
    const allLoaded = this.libraries.every(lib => lib.loaded || !lib.required);
    
    if (allLoaded && !this.allLoaded) {
      this.allLoaded = true;
      console.log('所有必要库加载完成');
      
      // 初始化库（如果需要）
      this.libraries.forEach(lib => {
        if (lib.loaded && lib.initFunction && typeof lib.initFunction === 'function') {
          try {
            lib.initFunction();
          } catch (error) {
            console.error(`初始化库 ${lib.name} 失败:`, error);
          }
        }
      });
      
      // 保存加载结果到localStorage
      localStorage.setItem('librariesLoadStatus', JSON.stringify({
        timestamp: Date.now(),
        status: 'success'
      }));
      
      // 触发加载完成事件
      window.dispatchEvent(this.librariesLoadedEvent);
    }
    
    return allLoaded;
  }
  
  /**
   * 为必要的库创建降级方案
   */
  createFallbacks() {
    this.libraries.forEach(lib => {
      if (lib.required && !lib.loaded && window[lib.global] === undefined) {
        console.warn(`创建 ${lib.name} 库的降级方案`);
        
        // 根据库名称创建不同的降级处理
        switch(lib.global) {
          case 'marked':
            // 简单的Markdown转HTML降级方案
            window.marked = {
              parse: function(text) {
                return text
                  .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                  .replace(/\*(.*?)\*/g, '<em>$1</em>')
                  .replace(/\n\n/g, '<br><br>')
                  .replace(/\n/g, '<br>');
              }
            };
            break;
            
          case 'mermaid':
            // 简单的占位符
            window.mermaid = {
              initialize: function() {},
              render: function(id, text, callback) {
                const el = document.getElementById(id);
                if (el) {
                  el.innerHTML = `<div class="mermaid-fallback">Mermaid图表生成失败</div>`;
                }
                if (typeof callback === 'function') {
                  callback('');
                }
              }
            };
            break;
            
          case 'd3':
            // 简单的D3降级，仅提供一些基本功能
            window.d3 = {
              select: function() { return { attr: function() { return this; }, style: function() { return this; } }; },
              selectAll: function() { return { attr: function() { return this; }, style: function() { return this; } }; }
            };
            break;
            
          default:
            // 为其他库创建空对象
            window[lib.global] = {};
        }
        
        lib.loaded = true; // 标记为已加载，避免继续尝试加载
        lib.isFallback = true; // 标记为降级方案
      }
    });
  }

  /**
   * 加载单个库
   * @param {Object} lib - 库配置对象
   * @returns {Promise<boolean>} - 加载是否成功
   */
  async loadLibrary(lib) {
    // 如果已加载，直接返回成功
    if (lib.loaded) return true;
    
    // 如果全局对象已存在，标记为已加载
    if (window[lib.global] !== undefined) {
      console.log(`库 ${lib.name} 已通过其他方式加载`);
      lib.loaded = true;
      return true;
    }
    
    return new Promise(async (resolve) => {
      // 创建加载状态指示器（只在main页面显示）
      let statusEl = null;
      if (this.shouldShowNotifications()) {
        const statusId = `lib-status-${lib.name}`;
        statusEl = document.getElementById(statusId);
        
        if (!statusEl && document.body) {
          statusEl = document.createElement('div');
          statusEl.id = statusId;
          statusEl.className = 'resource-loading-indicator';
          statusEl.textContent = `正在加载 ${lib.name}...`;
          document.body.appendChild(statusEl);
        }
      }
      
      // 决定加载顺序
      let urlsToTry = [];
      
      if (this.useLocalOnly) {
        // 仅使用本地资源
        urlsToTry = [lib.localUrl];
      } else if (this.localFirst) {
        // 本地优先，然后CDN
        urlsToTry = [lib.localUrl, ...lib.cdnUrls];
      } else {
        // 默认顺序：CDN优先，然后本地
        urlsToTry = [...lib.cdnUrls, lib.localUrl];
      }
      
      // 尝试加载，从第一个URL开始
      let loaded = false;
      
      for (let i = 0; i < urlsToTry.length && !loaded; i++) {
        const url = urlsToTry[i];
        try {
          // 更新加载状态
          if (statusEl) {
            statusEl.textContent = `正在加载 ${lib.name} (${i+1}/${urlsToTry.length})...`;
          }
          
          // 尝试加载脚本
          await new Promise((scriptResolve, scriptReject) => {
            const script = document.createElement('script');
            script.src = url;
            script.async = true;
            
            script.onload = () => {
              loaded = true;
              scriptResolve();
            };
            
            script.onerror = scriptReject;
            
            document.head.appendChild(script);
            
            // 设置10秒超时
            setTimeout(() => scriptReject(new Error('加载超时')), 10000);
          });
          
          if (loaded) {
            console.log(`成功加载库 ${lib.name} (来源: ${url})`);
            lib.loaded = true;
            
            // 如果是加载成功的CDN URL，且没有本地文件，尝试下载到本地
            // 仅在main页面执行下载
            if (url !== lib.localUrl && !this.useLocalOnly && this.currentPage === 'main') {
              try {
                // 提取本地路径
                const localPath = lib.localUrl.startsWith('/') ? lib.localUrl.substring(1) : lib.localUrl;
                this.downloadIfMissing(url, localPath);
              } catch (downloadError) {
                console.warn(`为 ${lib.name} 下载备份失败:`, downloadError);
              }
            }
            
            // 更新状态为成功
            if (statusEl) {
              statusEl.textContent = `${lib.name} 加载成功`;
              statusEl.classList.add('success');
              // 2秒后移除状态指示器
              setTimeout(() => {
                statusEl.remove();
              }, 2000);
            }
          }
        } catch (err) {
          console.warn(`从 ${url} 加载 ${lib.name} 失败:`, err);
          // 继续尝试下一个URL
        }
      }
      
      // 所有URL都尝试过了，但仍未成功加载
      if (!loaded) {
        console.error(`无法加载库 ${lib.name}`);
        
        // 更新状态为失败
        if (statusEl) {
          statusEl.textContent = `${lib.name} 加载失败`;
          statusEl.classList.add('error');
        }
        
        // 如果是必需库，创建降级版本
        if (lib.required) {
          this.createFallbacks();
        }
      }
      
      resolve(loaded);
    });
  }
  
  /**
   * 加载所有配置的库
   */
  async loadLibraries() {
    // 避免重复加载
    if (this.loadStarted) return;
    this.loadStarted = true;
    
    // 清除任何现有的超时
    if (this.loadTimeout) {
      clearTimeout(this.loadTimeout);
    }
    
    // 显示加载状态条幅（只在main页面）
    const showLoadingBanner = () => {
      // 如果不是main页面或者已经是本地优先/仅本地模式，不显示
      if (!this.shouldShowNotifications() || this.useLocalOnly || this.localFirst) {
        return;
      }
      
      const banner = document.createElement('div');
      banner.className = 'fallback-mode-banner';
      banner.innerHTML = `
        <span>资源加载中，如果长时间未完成，请尝试使用本地模式</span>
        <button id="useLocalResourcesBtn">使用本地资源</button>
      `;
      document.body.appendChild(banner);
      
      document.getElementById('useLocalResourcesBtn').addEventListener('click', () => {
        localStorage.setItem('useLocalLibs', 'true');
        window.location.reload();
      });
      
      // 10秒后自动移除横幅
      setTimeout(() => {
        banner.remove();
      }, 10000);
    };
    
    // 3秒后显示加载状态条幅（只对main页面）
    if (this.shouldShowNotifications()) {
      setTimeout(showLoadingBanner, 3000);
    }
    
    // 创建加载超时（只对main页面）
    if (this.shouldShowNotifications()) {
      this.loadTimeout = setTimeout(() => {
        this.detectLoadingIssues();
      }, 15000);
    }
    
    console.log(`开始加载库，模式: ${this.useLocalOnly ? '仅本地' : (this.localFirst ? '本地优先' : 'CDN优先')}`);
    
    // 根据当前页面类型，过滤出需要加载的库
    const pageDependencies = {
      'index': ['supabase'], 
      'log': ['supabase'],
      'main': ['marked', 'mermaid', 'd3', 'supabase']
    };
    
    const requiredLibraries = this.libraries.filter(lib => 
      pageDependencies[this.currentPage].includes(lib.name)
    );
    
    // 并行加载筛选后的库
    const loadPromises = requiredLibraries.map(lib => this.loadLibrary(lib));
    
    try {
      await Promise.allSettled(loadPromises);
      
      // 检查是否所有必需的库都已加载
      const allLoaded = this.checkAllLoaded();
      
      // 仅在main页面显示加载失败提示
      if (!allLoaded && this.shouldShowNotifications()) {
        console.warn('部分必需库加载失败');
        // 创建降级解决方案
        this.createFallbacks();
        
        // 显示恢复对话框
        this.showRecoveryDialog();
      } else {
        console.log('所有必要库加载成功');
      }
    } catch (error) {
      console.error('加载库时出错:', error);
      
      // 仅在main页面显示恢复对话框
      if (this.shouldShowNotifications()) {
        this.showRecoveryDialog();
      }
    } finally {
      // 清除加载超时
      if (this.loadTimeout) {
        clearTimeout(this.loadTimeout);
        this.loadTimeout = null;
      }
    }
  }
  
  /**
   * 初始化marked库
   */
  initMarked() {
    if (window.marked) {
      try {
        // 配置marked选项
        window.marked.setOptions({
          breaks: true,           // 将换行符转换为br标签
          gfm: true,              // 使用GitHub风格的Markdown
          headerIds: true,        // 自动添加标题ID
          sanitize: false,        // 不清理用户输入
          smartLists: true,       // 使用更智能的列表行为
          smartypants: true       // 使用更智能的标点符号
        });
        console.log('marked库已初始化');
      } catch (error) {
        console.error('初始化marked库失败:', error);
      }
    }
  }
  
  /**
   * 初始化mermaid库
   */
  initMermaid() {
    if (window.mermaid) {
      try {
        // 配置mermaid选项
        window.mermaid.initialize({
          startOnLoad: true,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'monospace',
          flowchart: {
            useMaxWidth: true
          }
        });
        console.log('mermaid库已初始化');
      } catch (error) {
        console.error('初始化mermaid库失败:', error);
      }
    }
  }
}

// 创建并导出资源加载器实例
window.resourceLoader = new ResourceLoader();

// 在DOMContentLoaded事件中开始加载
document.addEventListener('DOMContentLoaded', () => {
  // 开始加载库
  window.resourceLoader.loadLibraries();
  
  // 设置检测（只对main页面生效）
  if (window.resourceLoader.shouldShowNotifications()) {
    window.resourceLoader.detectLoadingIssues();
  }
});