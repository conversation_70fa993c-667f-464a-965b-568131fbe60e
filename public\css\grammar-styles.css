/* 语法分析可视化样式 */

.grammar-analysis-container {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin: 16px 0;
    border: 1px solid #e0e0e0;
}

.grammar-analysis-title {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.grammar-analysis-title i {
    color: #4CAF50;
}

.analyzed-sentence {
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #4CAF50;
    font-size: 14px;
    color: #555;
}

.analyzed-sentence strong {
    color: #2c3e50;
}

/* 标签页样式 */
.grammar-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.grammar-tab-btn {
    padding: 10px 16px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    transition: all 0.3s ease;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
}

.grammar-tab-btn:hover {
    background-color: #f8f9fa;
    color: #333;
}

.grammar-tab-btn.active {
    background-color: #4CAF50;
    color: white;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.grammar-tab-btn i {
    font-size: 12px;
}

/* 内容区域样式 */
.grammar-content {
    min-height: 300px;
    background: #fafafa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

/* 语法分析内容样式 */
.grammar-analysis-content {
    line-height: 1.8;
    color: #333;
}

.grammar-analysis-content h1,
.grammar-analysis-content h2,
.grammar-analysis-content h3,
.grammar-analysis-content h4 {
    color: #2c3e50;
    margin-top: 20px;
    margin-bottom: 12px;
}

.grammar-analysis-content h1 { font-size: 20px; }
.grammar-analysis-content h2 { font-size: 18px; }
.grammar-analysis-content h3 { font-size: 16px; }
.grammar-analysis-content h4 { font-size: 14px; }

.grammar-analysis-content p {
    margin-bottom: 12px;
}

.grammar-analysis-content ul,
.grammar-analysis-content ol {
    margin: 12px 0;
    padding-left: 20px;
}

.grammar-analysis-content li {
    margin-bottom: 6px;
}

.grammar-analysis-content strong {
    color: #2c3e50;
    font-weight: 600;
}

.grammar-analysis-content em {
    color: #666;
    font-style: italic;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.loading::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 词性标注可视化样式 */
.pos-visualization {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #ddd;
}

.pos-visualization h4 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
}

.pos-words-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: flex-start;
}

.pos-word-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    min-width: 60px;
}

.pos-word-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.word-text {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    text-align: center;
}

.pos-tag {
    font-size: 11px;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: 12px;
    color: #555;
    border: 1px solid #ddd;
    text-align: center;
    min-width: 40px;
    white-space: nowrap;
}

/* 基本可视化样式 */
.basic-grammar-viz {
    text-align: center;
    padding: 40px 20px;
}

.viz-info {
    margin-bottom: 24px;
}

.viz-info i {
    font-size: 48px;
    color: #4CAF50;
    margin-bottom: 12px;
    display: block;
}

.viz-info p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.content-summary {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    text-align: left;
}

.content-summary h4 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-size: 16px;
}

.content-summary p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* 学习要点样式 */
.grammar-learning-points h4 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.grammar-learning-points h4 i {
    color: #FF9800;
    font-size: 18px;
}

.learning-points-list {
    background: white;
    border-radius: 8px;
    padding: 16px 20px;
    border: 1px solid #e9ecef;
    margin: 0;
    list-style: none;
}

.learning-points-list li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    color: #555;
    line-height: 1.6;
    position: relative;
    padding-left: 20px;
}

.learning-points-list li:last-child {
    border-bottom: none;
}

.learning-points-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #4CAF50;
    font-weight: bold;
}

.default-learning-points {
    background: white;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e9ecef;
}

.default-learning-points p {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-weight: 600;
}

.default-learning-points ul {
    margin: 0;
    padding-left: 20px;
}

.default-learning-points li {
    color: #666;
    line-height: 1.6;
    margin-bottom: 6px;
}

/* 占位符样式 */
.viz-placeholder,
.learning-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.viz-placeholder i,
.learning-placeholder i {
    display: block;
    margin-bottom: 16px;
    color: #bbb;
}

.viz-placeholder p,
.learning-placeholder p {
    margin: 0;
    font-size: 14px;
}

/* 错误状态样式 */
.grammar-error {
    text-align: center;
    padding: 40px 20px;
    color: #dc3545;
    background: #fff5f5;
    border: 1px solid #fecaca;
    border-radius: 8px;
}

.grammar-error i {
    font-size: 32px;
    display: block;
    margin-bottom: 12px;
}

.grammar-error p {
    margin: 0;
    font-size: 14px;
}

/* 无句子检测样式 */
.grammar-no-sentences {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.grammar-no-sentences i {
    font-size: 32px;
    display: block;
    margin-bottom: 12px;
    color: #adb5bd;
}

.grammar-no-sentences p {
    margin: 0 0 8px 0;
    font-size: 14px;
}

.grammar-no-sentences .hint {
    font-size: 12px;
    color: #888;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .grammar-analysis-container {
        margin: 12px 0;
        padding: 16px;
        border-radius: 8px;
    }
    
    .grammar-tabs {
        flex-wrap: wrap;
        gap: 6px;
    }
    
    .grammar-tab-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    .grammar-content {
        padding: 16px;
        min-height: 250px;
    }
    
    .pos-words-container {
        gap: 8px;
    }
    
    .pos-word-item {
        padding: 6px;
        min-width: 50px;
    }
    
    .word-text {
        font-size: 13px;
    }
    
    .pos-tag {
        font-size: 10px;
        padding: 1px 6px;
    }
    
    .analyzed-sentence {
        padding: 10px 12px;
        font-size: 13px;
    }
    
    .grammar-analysis-title {
        font-size: 16px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.grammar-content > * {
    animation: fadeInUp 0.3s ease;
}

.pos-word-item {
    animation: fadeInUp 0.3s ease;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .grammar-analysis-container {
        background: #1e1e1e;
        border-color: #333;
        color: #e0e0e0;
    }
    
    .analyzed-sentence {
        background: #2a2a2a;
        color: #ccc;
        border-left-color: #4CAF50;
    }
    
    .analyzed-sentence strong {
        color: #e0e0e0;
    }
    
    .grammar-content {
        background: #262626;
        border-color: #333;
        color: #e0e0e0;
    }
    
    .pos-visualization,
    .content-summary,
    .learning-points-list,
    .default-learning-points {
        background: #1e1e1e;
        border-color: #333;
        color: #e0e0e0;
    }
    
    .pos-word-item {
        background: #2a2a2a;
        color: #e0e0e0;
    }
    
    .word-text {
        color: #e0e0e0;
    }
    
    .pos-tag {
        color: #ccc;
        border-color: #555;
    }
}

/* 语法可视化按钮 - 放在字幕控制区域 */
.grammar-viz-btn {
    padding: 8px 16px;
    background: #2196F3;
    transform: translateY(-2px);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

/* 悬停主色调 */
.grammar-viz-btn:hover:not(:disabled) {
    filter: brightness(1.1);
    transform: translateY(-1px);
    background: #1976D2;
}

.grammar-viz-btn:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.grammar-viz-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #6c757d;
    box-shadow: none;
}

.grammar-viz-btn i {
    font-size: 16px;
}

.grammar-viz-btn span {
    font-weight: 500;
}

/* 语法可视化模态框 */
#grammarVisualizationModal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

#grammarVisualizationModal .subtitle-chat-content {
    width: 95vw !important;
    max-width: 1400px !important;
    height: 90vh !important;
    max-height: 90vh !important;
    overflow: hidden !important;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    display: flex;
    flex-direction: column;
}

#grammarVisualizationModal .subtitle-chat-body {
    flex: 1;
    overflow: hidden !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* 语法树容器重新设计 - 占满整个弹框主体 */
#grammarVisualizationModal .grammar-tree-section {
    height: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
}

#grammarVisualizationModal .grammar-tree-container {
    height: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 20px !important;
    overflow: auto !important;
    background: #fafafa !important;
    border: none !important;
    border-radius: 0 !important;
}

#grammarVisualizationModal .grammar-tree-placeholder {
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 18px !important;
    color: #666 !important;
}

.grammar-modal-content .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 24px;
    margin: -20px -24px 20px -24px;
    border-radius: 12px 12px 0 0;
}

.grammar-modal-content .modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: white;
}

.grammar-modal-content .modal-close {
    color: white;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.grammar-modal-content .modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.grammar-text-display {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.grammar-text-display h4 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.grammar-text-display p {
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    font-weight: 500;
}

.grammar-analysis-container {
    min-height: 300px;
}

/* 加载状态 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6c757d;
}

.loading-container .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-english, .error {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
}

.error {
    color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .grammar-viz-btn span {
        display: none; /* 在小屏幕上只显示图标 */
    }
    
    .grammar-modal-content {
        width: 95vw;
        max-height: 85vh;
    }
    
    .grammar-modal-content .modal-header {
        margin: -16px -16px 16px -16px;
        padding: 12px 16px;
    }
}

/* 语法树可视化样式 */
.grammar-tree-title {
    margin: 20px 0 15px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 8px;
}

.grammar-tree-title i {
    color: #4CAF50;
}

.grammar-tree-container {
    background: #fafafa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin: 0;
    height: calc(90vh - 120px);
    overflow: auto;
    width: 100%;
}

.grammar-tree-svg {
    width: 100%;
    height: auto;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    overflow: visible;
}

/* 主垂直连接线 */
.grammar-tree-svg .main-vertical-line {
    stroke: #4CAF50;
    stroke-width: 1.5;
    opacity: 0.8;
}

/* 连接线样式 */
.grammar-tree-svg .link {
    stroke: #4CAF50;
    stroke-width: 1.5;
    fill: none;
    opacity: 0.8;
    transition: all 0.2s ease;
}

.grammar-tree-svg .link:hover {
    stroke: #2E7D32;
    stroke-width: 2;
    opacity: 1;
}

/* 节点组样式 */
.grammar-tree-svg .node-group {
    cursor: pointer;
    transition: all 0.2s ease;
}

.grammar-tree-svg .node-group:hover {
    filter: brightness(1.1);
}

/* 节点水平线样式 */
.grammar-tree-svg .node-line {
    stroke: #4CAF50;
    stroke-width: 1.5;
    transition: all 0.2s ease;
}

.grammar-tree-svg .node-group:hover .node-line {
    stroke: #2E7D32;
    stroke-width: 2.5;
}

/* 节点文本样式 */
.grammar-tree-svg .node-text {
    font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
    font-size: 14px;
    fill: #333;
    user-select: none;
    pointer-events: none;
    transition: fill 0.2s ease;
}

/* 不同深度的节点文本样式 */
.grammar-tree-svg .node-group.depth-0 .node-text {
    font-size: 16px;
    font-weight: bold;
    fill: #2c3e50;
}

.grammar-tree-svg .node-group.depth-1 .node-text {
    font-size: 15px;
    font-weight: 600;
    fill: #34495e;
}

.grammar-tree-svg .node-group.depth-2 .node-text {
    font-size: 14px;
    font-weight: normal;
    fill: #555;
}

/* 不同类型节点的文本颜色 */
.grammar-tree-svg .node-group.component .node-text {
    fill: #2E7D32;
    font-weight: bold;
}

.grammar-tree-svg .node-group.component_label .node-text {
    fill: #7B1FA2;
    font-weight: 600;
}

.grammar-tree-svg .node-group.role .node-text {
    fill: #F57C00;
}

.grammar-tree-svg .node-group.structure .node-text {
    fill: #C2185B;
}

.grammar-tree-svg .node-group.tense .node-text {
    fill: #1976D2;
}

.grammar-tree-svg .node-group.voice .node-text {
    fill: #388E3C;
}

.grammar-tree-svg .node-group.info .node-text {
    fill: #616161;
}

.grammar-tree-svg .node-group.list .node-text {
    fill: #795548;
}

.grammar-tree-svg .node-group.loading .node-text {
    fill: #9E9E9E;
    font-style: italic;
}

/* 悬停效果 */
.grammar-tree-svg .node-group:hover .node-text {
    fill: #2E7D32 !important;
    font-weight: 600;
}

/* 滚动条样式 */
.grammar-tree-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.grammar-tree-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.grammar-tree-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.grammar-tree-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 弹框中的语法树容器 */
#grammarVisualizationModal .grammar-tree-container {
    height: calc(90vh - 140px) !important;
    overflow: auto !important;
}

/* 侧边栏中的语法树容器 */
#grammarSidebar #grammarVizContainer .grammar-tree-container {
    flex: 1;
    overflow: auto;
    background: #ffffff !important;
    height: auto;
    max-height: none;
}

/* 错误和警告样式 */
.warning {
    margin: 10px 0;
    padding: 10px;
    background-color: #fff3cd;
    color: #856404;
    border-radius: 4px;
    border: 1px solid #ffeeba;
    font-size: 14px;
}

.error-message {
    padding: 20px;
    text-align: center;
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    margin: 10px 0;
}

/* 下载工具栏样式 */
.download-toolbar {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.download-button {
    padding: 8px 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s ease;
}

.download-button:hover {
    background: #0056b3;
}

.download-button:active {
    background: #004085;
}

.download-button i {
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .grammar-tree-container {
        height: calc(80vh - 100px);
        max-height: calc(80vh - 100px);
        padding: 8px;
    }
    
    .grammar-tree-svg .node-text {
        font-size: 12px;
    }
    
    .grammar-tree-svg .node-group.depth-0 .node-text {
        font-size: 14px;
    }
    
    .grammar-tree-svg .node-group.depth-1 .node-text {
        font-size: 13px;
    }
    
    .download-toolbar {
        padding: 8px;
        gap: 8px;
    }
    
    .download-button {
        padding: 6px 10px;
        font-size: 12px;
    }
}

/* 新增多种展示方式的样式 */

/* 侧边栏展示样式 */
.grammar-sidebar {
    position: fixed;
    top: 0;
    right: -530px;
    width: 530px;
    height: 100%;
    background: white;
    z-index: 9999;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    transition: right 0.3s ease;
}

.grammar-sidebar.show {
    right: 0;
}

.grammar-sidebar-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.grammar-sidebar-header h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.grammar-sidebar-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.grammar-sidebar-close:hover {
    background: #e9ecef;
    color: #333;
}

.grammar-sidebar-body {
    padding: 15px 15px 0 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.grammar-sentence-info {
    background: #e3f2fd;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-size: 13px;
    line-height: 1.4;
    color: #1976d2;
}

.grammar-sidebar #grammarVizContainer {
    padding: 15px 15px 0 15px;
    flex: 1;
    background: #fafafa;
    border-radius: 6px;
    overflow: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .grammar-sidebar {
        width: 100%;
        right: -100%;
    }
    
    .grammar-sidebar.show {
        right: 0;
    }
    
    .grammar-fullscreen-header {
        padding: 10px 15px;
    }
    
    .grammar-fullscreen-header h2 {
        font-size: 18px;
    }
}

/* ========== 侧边栏语法树容器覆盖 ========== */
#grammarSidebar #grammarVizContainer {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 0;
    overflow: hidden;
}
#grammarSidebar #grammarVizContainer .grammar-tree-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin: 0;
    padding: 0;
}
#grammarSidebar #grammarVizContainer .grammar-tree-container {
    flex: 1;
    height: auto !important;
    padding: 10px !important;
    margin: 0;
    overflow: auto;
    max-height: none !important;
    background: #ffffff !important;
}

/* 弹框模式下的容器优化 */
#grammarVisualizationModal .grammar-tree-container {
    height: calc(90vh - 140px) !important;
    max-height: calc(90vh - 140px) !important;
    width: 100% !important;
    padding: 20px !important;
    overflow: auto !important;
    background: #fafafa !important;
    border: none !important;
    border-radius: 0 !important;
}

#grammarVisualizationModal .grammar-tree-svg {
    width: 100% !important;
    height: auto !important;
    min-height: calc(90vh - 180px) !important;
    background: #ffffff !important;
    border-radius: 0 !important;
}

/* 专用于语法可视化模态框的样式覆盖 */
#grammarVisualizationModal .subtitle-chat-header {
    flex-shrink: 0;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 8px 8px 0 0;
}

#grammarVisualizationModal .subtitle-chat-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

#grammarVisualizationModal .subtitle-chat-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

#grammarVisualizationModal .subtitle-chat-close:hover {
    background: #e9ecef;
    color: #333;
}

/* 语法树SVG在弹框中的样式 */
#grammarVisualizationModal .grammar-tree-svg {
    width: 100% !important;
    height: auto !important;
    min-height: calc(90vh - 140px) !important;
    background: #ffffff !important;
    border-radius: 0 !important;
}

/* 新增多种展示方式的样式 */

/* 侧边栏展示样式 */
.grammar-sidebar {
    position: fixed;
    top: 0;
    right: -530px;
    width: 530px;
    height: 100%;
    background: white;
    z-index: 9999;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    transition: right 0.3s ease;
}

.grammar-sidebar.show {
    right: 0;
}

.grammar-sidebar-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.grammar-sidebar-header h3 {
    margin: 0;
    color: #333;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.grammar-sidebar-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.grammar-sidebar-close:hover {
    background: #e9ecef;
    color: #333;
}

.grammar-sidebar-body {
    padding: 15px 15px 0 15px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.grammar-sentence-info {
    background: #e3f2fd;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-size: 13px;
    line-height: 1.4;
    color: #1976d2;
}

.grammar-sidebar #grammarVizContainer {
    padding: 15px 15px 0 15px;
    flex: 1;
    background: #fafafa;
    border-radius: 6px;
    overflow: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .grammar-sidebar {
        width: 100%;
        right: -100%;
    }
    
    .grammar-sidebar.show {
        right: 0;
    }
    
    .grammar-fullscreen-header {
        padding: 10px 15px;
    }
    
    .grammar-fullscreen-header h2 {
        font-size: 18px;
    }
}

/* ========== 侧边栏语法树容器覆盖 ========== */
#grammarSidebar #grammarVizContainer {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 0;
    overflow: hidden;
}
#grammarSidebar #grammarVizContainer .grammar-tree-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin: 0;
    padding: 0;
}
#grammarSidebar #grammarVizContainer .grammar-tree-container {
    flex: 1;
    height: auto !important;
    padding: 10px !important;
    margin: 0;
    overflow: auto;
    max-height: none !important;
    background: #ffffff !important;
}

/* 语法树容器优化 - 确保内容能够完全显示并支持滚动 */
.grammar-tree-container {
    background: #fafafa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin: 0;
    height: calc(90vh - 120px);
    max-height: calc(90vh - 120px);
    overflow: auto;
    width: 100%;
    position: relative;
}

/* 确保语法树SVG能够自动调整大小 */
.grammar-tree-svg {
    width: 100%;
    height: auto;
    min-height: 400px;
    background: #ffffff;
    border-radius: 6px;
    display: block;
}

/* 弹框模式下的容器优化 */
#grammarVisualizationModal .grammar-tree-container {
    height: calc(90vh - 140px) !important;
    max-height: calc(90vh - 140px) !important;
    width: 100% !important;
    padding: 20px !important;
    overflow: auto !important;
    background: #fafafa !important;
    border: none !important;
    border-radius: 0 !important;
}

#grammarVisualizationModal .grammar-tree-svg {
    width: 100% !important;
    height: auto !important;
    min-height: calc(90vh - 180px) !important;
    background: #ffffff !important;
    border-radius: 0 !important;
}

/* 降级内容样式 */
.grammar-fallback {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    margin: 10px 0;
}

.grammar-fallback h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.grammar-fallback h4 i {
    color: #ffc107;
}

.grammar-fallback .fallback-content p {
    margin: 0 0 15px 0;
    color: #6c757d;
    font-size: 14px;
}

.grammar-fallback .raw-content {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Microsoft YaHei', sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    max-height: 400px;
    overflow-y: auto;
}

/* 错误信息样式增强 */
.grammar-tree-container .error {
    padding: 20px;
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
} 