/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
    background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
    height: 100vh;
    color: #333;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

/* 主容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0px 4px 8px 4px !important; /* 增加底部间距 */
    height: calc(100vh - 80px); /* 调整高度计算，适应更高的header */
    display: flex;
    flex-direction: column;
    gap: 0px; /* 减少到无间距 */
    width: 100%;
    overflow: hidden;
    box-sizing: border-box;
}

/* 调整头部样式 - 增加高度 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #2196F3;
    padding: 12px 20px; /* 大幅增加内边距，让标题栏更高 */
}

/* 调整标题字体大小和间距 */
.header h1 {
    font-size: 1.6rem; /* 将1.8rem改为1.6rem，减少标题大小 */
    margin-bottom: 0; /* 将5px改为0，去除底部边距 */
    text-shadow: none;
}

.header .subtitle {
    font-size: 0.9rem; /* 将1rem改为0.9rem，减少副标题大小 */
    opacity: 0.9;
}

/* 用户菜单样式 */
.user-menu {
    position: relative;
}

.menu-btn {
    background: rgba(33, 150, 243, 0.1); /* Light blue background matching landing page */
    border: 2px solid rgba(33, 150, 243, 0.3);
    color: #2196F3; /* Match landing page blue */
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 1001;
    position: relative;
}

.menu-btn:hover {
    background: rgba(33, 150, 243, 0.2);
    border-color: rgba(33, 150, 243, 0.5);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    min-width: 200px;
    z-index: 1002;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}


.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 6px 16px;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.8rem; /* 将字体从 0.85rem 调整为更小的 0.8rem */
    font-weight: 500;
    position: relative;
}

.dropdown-menu a:hover {
    background-color: #f7fafc;
}

.dropdown-menu i {
    width: 16px;
    text-align: center;
}

.credits-amount {
    margin-left: auto;
    background: transparent !important; /* 去掉蓝色背景 */
    color: #4a5568 !important; /* 改为灰黑色字体 */
    padding: 2px 4px !important; /* 减少内边距 */
    border-radius: 0 !important; /* 去掉圆角 */
    font-size: 0.8rem !important;
    font-weight: 600 !important;
    display: inline-block !important;  /* 强制显示 */
    min-width: 30px !important;       /* 设置最小宽度确保即使积分为0也能看到 */
    text-align: center !important;    /* 文字居中 */
    opacity: 1 !important;           /* 强制不透明 */
    visibility: visible !important;   /* 强制可见 */
    position: relative !important;    /* 确保定位正确 */
    z-index: 10 !important;          /* 确保在最上层 */
}

.dropdown-divider {
    height: 1px;
    background: #e2e8f0;
    margin: 8px 16px;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto 1fr;
    gap: 2px; /* 减少网格间距，与整体紧凑设计保持一致 */
    overflow: hidden;
    margin: 0;
    width: 100%;
    min-height: 0;
    box-sizing: border-box;
}

/* 音频输入区域 */
.audio-upload-section {
    grid-column: 1 / 2;
    grid-row: 1 / 2;
}

/* 上传文件播放器 */
.audio-player-section {
    grid-column: 2 / 3;
    grid-row: 1 / 2;
}

/* 录音文件播放器 */
.recording-player-section {
    grid-column: 3 / 4;
    grid-row: 1 / 2;
}

/* 智能问答区域 */
.qa-section {
    grid-column: 1 / 4;
    grid-row: 2 / 3;
    display: flex;
    flex-direction: column;
    min-height: 200px;
    height: 90%; /* 恢复原始高度 */
    overflow: hidden;
}

/* 智能问答区域的卡片特殊处理 */
.qa-section .card {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: hidden;
    position: relative;
    padding: 8px 12px 1px 12px; /* 增加内边距：上8px 左右12px，保持底部1px */
}

/* 卡片样式 */
.card {
    background: white;
    border-radius: 8px; /* Match landing page radius */
    padding: 8px 12px 6px 12px; /* 增加内边距：上8px 左右12px 下6px */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Match landing page shadows */
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative; /* 添加相对定位 */
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15); /* Match landing page hover shadow */
}

.card h2 {
    color: #4a5568;
    margin-bottom: 10px; /* 减少标题底部间距 */
    margin-top: 0; /* 确保标题顶部无额外间距 */
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* AI分析区域标题行 */
.ai-section .card h2 {
    margin-bottom: 12px;
}

.ai-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.ai-header h2 {
    margin-bottom: 0;
    flex: 1;
}

/* 智能问答区域标题行 */
.qa-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 0px;    /* ↑ 元素顶部到按钮的距离 */
    margin-bottom: 6px;
    padding-bottom: 3px;
    border-bottom: 1px solid #e9ecef;
}

.qa-header h2 {
    margin-bottom: 0;
    flex: 1;
}

.qa-controls {
    display: flex;
    align-items: center;
    gap: 0;
}

/* 2. 全部用 margin 控制各段距离 */
.qa-controls .search-box {
    margin-right: 1px; /* 搜索框 → 随时问 */
  }
  .qa-controls #questionBtn {
    margin-right: 8px; /* 随时问 → 下载 */
    transform: translateY(-2px); /* 正值向下，负值向上，按需调整 */
  }

.qa-controls .search-box {
    height: 40px;
    display: flex;
    align-items: center;
    border-radius: 6px;
    overflow: hidden;
    flex-grow: 1;
    margin-right: 10px;
    border: 1px solid #ced4da;
    background-color: white;
}

.qa-controls .search-box input {
    height: 100%;
    border: none;
    outline: none;
    padding: 0 12px;
    flex-grow: 1;
    font-size: 14px;
    background-color: transparent;
}

.qa-controls .search-box .search-btn {
    height: 100%;
    border: none;
    background-color: #2196F3;
    color: white;
    padding: 0 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 统一按钮样式 - 修复大小不一致问题 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    min-width: 100px;
    justify-content: center;
    background: #2196F3;
    color: white;
    height: 40px; /* Ensure consistent height */
    box-sizing: border-box;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
    background: #1976D2; /* Darker blue on hover - match landing page */
    color: white; /* 悬停时文字变为白色，确保在蓝色背景下清晰可辨 */
}

/* 移除多余的按钮变体，统一使用主色调 */
.btn-primary {
    background: #2196F3; /* Match landing page blue */
    color: white;
}

.btn-secondary {
    background: rgba(33, 150, 243, 0.1); /* Light blue background */
    color: #2196F3;
    border: 2px solid #2196F3;
}

.btn-accent, .btn-info {
    background: #2196F3; /* Match landing page blue */
    color: white;
}

/* 上传控制区域 - 使按钮大小一致 */
.upload-controls {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
}



.upload-controls .btn {
    flex: 1; /* Make buttons take equal width */
    min-width: 140px; /* Set minimum width for buttons */
    text-align: center;
}

/* 录制状态 */
.recording-status {
    background: #fff5f5;
    border: 2px solid #fed7d7;
    border-radius: 8px;
    padding: 6px 12px;
    text-align: center;
    display: inline-flex;
    align-items: center;
    height: 40px;
    box-sizing: border-box;
}

.recording-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 1rem;
    width: 100%;
    height: 100%;
    padding: 0 2px;
}

.recording-dot {
    color: #e53e3e;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* 文件上传进度条样式 */
.upload-progress {
    margin-top: 12px;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 2px solid rgba(33, 150, 243, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
    display: none; /* 默认隐藏 */
}

.upload-progress:not(.hidden) {
    display: block; /* 显示时覆盖默认隐藏 */
}

.upload-progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    color: #2196F3;
}

.upload-progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(33, 150, 243, 0.1);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.upload-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2196F3 0%, #1976D2 100%);
    border-radius: 3px;
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
}

.upload-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: uploadShimmer 1.5s infinite;
}

@keyframes uploadShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 简化的上传状态提示样式 */
.upload-status {
    margin-top: 12px;
    padding: 10px 12px;
    background: #f0f9ff;
    border: 2px solid rgba(33, 150, 243, 0.3);
    border-radius: 8px;
    text-align: center;
    display: none; /* 默认隐藏 */
}

.upload-status:not(.hidden) {
    display: block;
}

.upload-status-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #2196F3;
    font-weight: 500;
    font-size: 0.9rem;
}

.upload-icon {
    font-size: 1.1rem;
    animation: uploadBounce 1.5s infinite;
}

@keyframes uploadBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-3px); }
    60% { transform: translateY(-2px); }
}

.upload-loading-dots {
    display: flex;
    gap: 3px;
}

.upload-loading-dots .dot {
    width: 4px;
    height: 4px;
    background: #2196F3;
    border-radius: 50%;
    animation: uploadDotPulse 1.4s infinite ease-in-out;
}

.upload-loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.upload-loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.upload-loading-dots .dot:nth-child(3) { animation-delay: 0; }

@keyframes uploadDotPulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 音频播放器样式 */
.player-container {
    display: flex;
    flex-direction: column;
    gap: 8px; /* 减少内部元素间距 */
    flex: 1;
    position: relative;
}

.audio-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 8px; /* 减少内边距 */
    background: #f7fafc;
    border-radius: 6px;
    font-weight: 600;
    font-size: 0.9rem;
}

/* 音频播放器样式 */
audio {
    width: 100%;
    max-width: 420px;
    height: 40px;
    border-radius: 6px;
    outline: none;
    background: #f8f9fa;
    display: block;
    margin: 0 auto;
}

#audioPlayer,
#uploadedPlayer,
#recordingPlayer {
    width: 100%;
    max-width: 420px;
    height: 40px;
    border-radius: 6px;
    outline: none;
    background: #f8f9fa;
    display: block;
    margin: 0 auto;
}

/* 针对 WebKit 浏览器优化音频控件 */
audio::-webkit-media-controls-panel {
    background-color: #f8f9fa;
    border-radius: 6px;
}

audio::-webkit-media-controls-play-button,
audio::-webkit-media-controls-pause-button {
    background-color: #2196F3; /* Match landing page blue */
    border-radius: 50%;
}

audio::-webkit-media-controls-volume-slider-container {
    padding-right: 8px;
}

audio::-webkit-media-controls-current-time-display,
audio::-webkit-media-controls-time-remaining-display {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #4a5568;
}

.player-controls {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 10px 0;
}

.control-btn {
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.3);
    color: #2196F3;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(33, 150, 243, 0.2);
    border-color: rgba(33, 150, 243, 0.5);
}

.play-btn {
    background: #2196F3;
    color: white;
}

.play-btn:hover {
    background: #1976D2;
}

/* 播放速度控制器 - 移除旧的speed-control样式 */
.speed-selector {
    position: relative;
    display: inline-block;
}

.speed-display {
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.3);
    color: #2196F3;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.7rem; /* 减小字号 */
    font-weight: 500;
    user-select: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.speed-display:hover {
    background: rgba(33, 150, 243, 0.2);
    border-color: rgba(33, 150, 243, 0.5);
}

.speed-options {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid rgba(33, 150, 243, 0.3);
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-5px);
    transition: all 0.3s ease;
    width: 100%; /* 使下拉菜单宽度与按钮保持一致 */
    scroll-behavior: smooth; /* 添加平滑滚动效果 */
}

.speed-options.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.speed-option {
    padding: 6px 12px;
    cursor: pointer;
    font-size: 0.7rem; /* 减小字号 */
    color: #333;
    transition: background-color 0.2s ease;
    text-align: center;
}

.speed-option:hover {
    background-color: rgba(33, 150, 243, 0.1);
}

.speed-option.active {
    background-color: #2196F3;
    color: white;
}

.speed-option.active:hover {
    background-color: #1976D2;
}

.progress-container {
    display: flex !important;
    align-items: center;
    gap: 8px;
    margin: 8px 0;
    position: relative;
    z-index: 1;
    overflow: visible !important;
}

.progress-container span {
    min-width: 35px;
    text-align: center;
    font-family: 'Courier New', monospace;
    font-weight: 500;
    font-size: 0.8rem;
}

.progress-bar {
    flex: 1;
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    position: relative;
    cursor: pointer;
    margin: 0 4px;
    min-width: 0;
    overflow: visible !important; /* 确保书签标记可见 */
}

.progress-fill {
    height: 100%;
    background: #2196F3; /* Match landing page blue */
    border-radius: 2px;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: -4px;
    width: 12px;
    height: 12px;
    background: #6b7280;
    border: 2px solid #fff;
    border-radius: 50%;
    cursor: pointer;
    transform: translateX(-50%);
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.progress-handle:hover {
    transform: translateX(-50%) scale(1.1);
    background: #4b5563;
}

.ai-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* AI分析内容区域样式 - 减少空白 */
.analysis-result {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.content-area {
    flex: 1;
    overflow-y: auto;
    padding: 10px 15px; /* 减少顶部padding */
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    line-height: 1.6;
    font-size: 0.9rem; /* 统一字号 */
    min-height: 0;
    max-height: 100%;
}

/* 自定义滚动条样式 */
.content-area::-webkit-scrollbar {
    width: 8px;
}

.content-area::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.content-area::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.content-area::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 移除流式内容的多余空白 */
.streaming-content {
    position: relative;
    margin: 0;
    padding: 0;
}

.streaming-text {
    line-height: 1.6;
    word-wrap: break-word;
    margin: 0;
    padding: 0;
}

/* 确保流式内容紧凑显示 */
.streaming-text p {
    margin: 0 0 8px 0;
}

.streaming-text p:last-child {
    margin-bottom: 0;
}

/* AI结果显示容器 - 独立滚动区域，固定高度 */
.chat-messages-container {
    height: 300px; /* 固定高度，不会随内容变化 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 10px; /* 与输入框保持间距 */
    flex-shrink: 0; /* 防止被压缩 */
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    min-height: 100px;
    max-height: 100%; /* 在固定的容器内滚动 */
}

/* 独立的输入框容器 - 固定在底部 */
.chat-input-container {
    display: flex;
    padding: 10px;
    background: #fff;
    gap: 10px;
    width: 100%;
    box-sizing: border-box;
    flex-shrink: 0; /* 防止被压缩 */
    border-radius: 0 0 8px 8px; /* 底部圆角 */
    margin-top: auto; /* 确保在剩余空间的底部 */
}

.chat-input {
    flex: 1;
    min-width: 0; /* 允许输入框缩小 */
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    outline: none;
    font-size: 14px;
    transition: border-color 0.3s;
    box-sizing: border-box;
    max-width: 100%;
}

.chat-input:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
    /* Maintain container centering */
    .container {
        margin: 0 auto;
        width: 95%;
    }
    
    /* Make buttons more consistent on mobile */
    .upload-controls .btn {
        flex: 1;
        min-width: 0; /* Allow buttons to shrink on mobile */
    }
    
    .chat-input-container {
        padding-bottom: 0;
    }
}

/* Ensure chat input button has consistent size */
.chat-input-container .btn {
    height: 45px;
    padding: 0 20px;
    min-width: 45px;
    width: auto;
}

/* 移除AI分析区域卡片的hover效果 */
.ai-section .card:hover {
    transform: none;
}

/* 移除AI分析区域按钮的hover效果 */
.ai-section .btn:not(:disabled):hover {
    transform: none;
    box-shadow: none;
}

/* 移除AI分析区域中的滚动条hover效果 */
.ai-section .content-area::-webkit-scrollbar-thumb:hover {
    background: #c1c1c1;
}

/* 移除AI分析区域内控制按钮的hover效果 */
.ai-section .control-btn:hover {
    background: #2196F3; /* Match landing page blue */
    transform: none;
}

/* 移除AI分析区域内的各种过渡动画 */
.ai-section .streaming-chunk {
    animation: none;
}

.ai-section .content-updated {
    animation: none;
}

/* 通用模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.modal.show, .modal:not(.hidden) {
    display: flex !important;
}

.modal-content {
    background-color: #fff;
    border-radius: 12px;
    padding: 12px 20px 20px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0;
    padding: 0 0 8px;
    min-height: 32px;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    margin: 0;
    color: #4a5568;
    font-size: 1.1rem;
    line-height: 1;
    display: flex;
    align-items: center;
    height: 24px;
    padding: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #a0aec0;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: #f7fafc;
    color: #4a5568;
}

.modal-body {
    padding: 0;
}

/* 积分信息样式 */
.credits-info {
    text-align: center;
}

.current-credits {
    font-size: 1.5rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 20px;
    padding: 20px;
    background: #2196F3; /* Match landing page blue - solid color */
    color: white; /* White text for better contrast */
    border: none;
    border-radius: 8px;
}

.current-credits span {
    color: white; /* White text for better contrast */
    font-size: 2rem;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 20px;
        max-height: 85vh;
    }
    
    .modal-header h3 {
        font-size: 1.1rem;
    }
    
    .current-credits {
        font-size: 1.3rem;
        padding: 15px;
    }
    
    .current-credits span {
        font-size: 1.8rem;
    }
}

/* === UI Optimization Overrides (2024-06-12) === */

/* 1. 显示自定义进度条，用于书签功能 */
.progress-container {
    display: flex !important;
    align-items: center;
    gap: 8px;
    margin: 8px 0;
    position: relative;
    z-index: 1;
    overflow: visible !important;
}

/* 2. 统一智能问答区域高度 */
.main-content {
    grid-template-rows: auto 1fr;
}

/* 删除重复的qa-section规则 */

/* 4. 隐藏左下角 Toast 提示 */
#statusToast,
.toast {
    display: none !important;
}

/* 删除冲突的样式规则 */

/* 删除冲突的消息区间距样式 */

.chat-input-container {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

/* === UI Optimization Overrides (2024-06-13) === */
.upload-controls .btn {
    padding: 12px 28px !important; /* 增大上下内边距 */
    white-space: nowrap; /* 防止按钮文本换行 */
    line-height: 1.4; /* 提高行高，文字居中更舒适 */
}

/* 删除冲突的消息区高度限制 */

/* 删除冲突的样式规则 */

/* === UI Optimization Overrides (2024-06-13b) === */
/* 1. 上传区域按钮与标题留白 */
.upload-controls {
    margin-top: 10px !important;
    margin-bottom: 12px !important; /* 保留下方适度间距 */
}

/* 删除冲突的样式规则 */

/* 删除冲突的定位样式 */

/* 删除冲突的消息区高度限制 */

/* AI分析动态加载指示器样式 */
.ai-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 20px;
    text-align: center;
}

.loading-dots {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
}

.loading-dots .dot {
    width: 8px;
    height: 8px;
    background: #2196F3; /* Match landing page blue */
    border-radius: 50%;
    animation: loadingPulse 1.4s infinite ease-in-out both;
}

.loading-dots .dot:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dots .dot:nth-child(2) {
    animation-delay: -0.16s;
}

.loading-dots .dot:nth-child(3) {
    animation-delay: 0;
}

@keyframes loadingPulse {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.loading-text {
    color: #4a5568;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 文件名显示优化 */
#uploadedFileName,
#recordingFileName {
    max-width: 360px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    font-size: 0.9rem;
}

/* 错误消息样式 */
.error-message {
    color: #e53e3e;
    background: #fed7d7;
    border: 1px solid #feb2b2;
    border-radius: 6px;
    padding: 12px;
    text-align: center;
    font-size: 0.9rem;
}

/* 聊天消息中的AI加载指示器样式 */
.chat-message .ai-loading {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    margin: 5px 0;
}

.chat-message .ai-loading .loading-dots {
    justify-content: flex-start;
    margin-bottom: 8px;
}

.chat-message .ai-loading .loading-text {
    font-size: 0.85rem;
}

/* 聊天消息中的错误气泡样式 */
.error-bubble {
    background: #fed7d7 !important;
    color: #e53e3e !important;
    border: 1px solid #feb2b2;
    border-radius: 8px !important;
    font-size: 16px !important;
}

/* 聊天消息布局样式 */
.chat-message {
    margin-bottom: 12px;
    display: flex;
    flex-direction: column;
    width: 100%;
}

/* 用户消息样式 - 右对齐 */
.chat-message.message-user {
    align-items: flex-end;
}

.chat-message.message-user .message-bubble {
    background: #2196F3 !important; /* 用户消息使用蓝色背景 */
    color: white !important;
    max-width: 70%;
    border-radius: 18px 18px 4px 18px;
    padding: 12px 16px;
    margin-left: auto;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.chat-message.message-user .message-time {
    text-align: right;
    font-size: 0.75rem;
    color: #999;
    margin-top: 4px;
    margin-right: 8px;
}

/* AI消息样式 - 左对齐 */
.chat-message.message-ai,
.chat-message.ai-message {
    align-items: flex-start;
}

.chat-message.message-ai .message-bubble,
.chat-message.ai-message .message-bubble,
.bubble-ai {
    background: #f1f3f4 !important; /* AI消息使用灰色背景 */
    color: #333 !important;
    max-width: 70%;
    border-radius: 10px !important;
    padding: 12px 16px;
    margin-right: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    font-size: 16px !important;
}

.chat-message.message-ai .message-time,
.chat-message.ai-message .message-time {
    text-align: left;
    font-size: 0.75rem;
    color: #999;
    margin-top: 4px;
    margin-left: 8px;
}

/* AI分析指示器在聊天中的样式 */
.chat-message.ai-message .ai-loading {
    background: #f1f3f4;
    border-radius: 18px !important;
    padding: 16px 20px;
    max-width: 70%;
    margin-right: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.chat-message.ai-message .ai-loading .loading-dots {
    justify-content: flex-start;
    margin-bottom: 8px;
}

.chat-message.ai-message .ai-loading .loading-text {
    font-size: 0.85rem;
    color: #666;
}

/* 继续对话提示样式 */
.continue-prompt {
    background: linear-gradient(135deg, #e3f2fd, #f0f8ff);
    border: 2px solid #2196F3;
    border-radius: 12px;
    padding: 16px 20px;
    margin: 10px 0 15px 0;
    text-align: center;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
    transition: all 0.3s ease;
}

.continue-prompt:not(.hidden) {
    animation: slideInPrompt 0.5s ease-out;
}

@keyframes slideInPrompt {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.prompt-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: #1976D2;
    font-weight: 600;
    font-size: 0.95rem;
}

.prompt-content i {
    font-size: 1.2rem;
    color: #2196F3;
}

/* 导出按钮特殊样式 */
#exportQABtn {
    min-width: 44px;
    padding: 10px 12px;
    border-radius: 8px;
}

#exportQABtn:disabled {
    opacity: 0.4;
}

#exportQABtn:not(:disabled):hover {
    background: rgba(33, 150, 243, 0.2);
    border-color: #1976D2;
    color: #1976D2;
}

/* 书签按钮样式 - 统一为主色调 */
.bookmark-btn {
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.3);
    color: #2196F3;
}

.bookmark-btn:hover {
    background: rgba(33, 150, 243, 0.2);
    border-color: rgba(33, 150, 243, 0.5);
}

.bookmark-btn.active {
    background: #2196F3;
    color: white;
}

.bookmark-play-btn {
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.3);
    color: #2196F3;
}

.bookmark-play-btn:hover:not(:disabled) {
    background: rgba(33, 150, 243, 0.2);
    border-color: rgba(33, 150, 243, 0.5);
}

.bookmark-play-btn.playing {
    background: #2196F3;
    color: white;
}

.bookmark-play-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.load-bookmark-btn {
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.3);
    color: #2196F3;
}

.load-bookmark-btn:hover {
    background: rgba(33, 150, 243, 0.2);
    border-color: rgba(33, 150, 243, 0.5);
    transform: scale(1.05);
}

.export-bookmark-btn {
    background: rgba(33, 150, 243, 0.1);
    border: 1px solid rgba(33, 150, 243, 0.3);
    color: #2196F3;
}

.export-bookmark-btn:hover {
    background: rgba(33, 150, 243, 0.2);
    border-color: rgba(33, 150, 243, 0.5);
    transform: scale(1.05);
}

/* 进度条书签标记样式 */
.progress-bar {
    flex: 1;
    height: 4px;
    background: #e2e8f0;
    border-radius: 2px;
    position: relative;
    cursor: pointer;
    margin: 0 4px;
    min-width: 0;
    overflow: visible !important; /* 确保书签标记可见 */
}

.bookmark-marker {
    position: absolute !important;
    top: -10px !important;
    width: 12px !important;
    height: 20px !important;
    background: #2196F3 !important;
    border: 2px solid #fff !important;
    border-radius: 3px !important;
    cursor: pointer !important;
    transform: translateX(-50%) !important;
    transition: all 0.2s ease !important;
    z-index: 999 !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.bookmark-marker:hover {
    transform: translateX(-50%) scale(1.3);
    background: #1976D2;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.bookmark-marker::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #2196F3;
    filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.2));
}

/* 确保书签标记不被父容器裁剪 */
.main-content,
.container,
.main-wrapper,
.app-container,
body {
    overflow: visible !important;
}

/* 但保持聊天区域的滚动 */
.chat-messages {
    overflow-y: auto !important;
}

/* 确保进度条区域始终可见书签标记 */
.audio-player-section,
.recording-player-section {
    overflow: visible !important;
}

.card {
    overflow: visible !important;
}

.player-container {
    overflow: visible !important;
}

/* 拖拽提示样式 */
.drop-hint {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    pointer-events: none;
}

.drop-hint.hidden {
    display: none;
}

.drop-hint-content {
    background: white;
    padding: 40px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 400px;
}

.drop-hint-content i {
    font-size: 48px;
    color: #4CAF50;
    margin-bottom: 16px;
}

.drop-hint-content p {
    font-size: 18px;
    font-weight: 600;
    margin: 8px 0;
    color: #333;
}

.drop-hint-content small {
    color: #666;
    font-size: 14px;
}

/* 播客相关样式 */
.podcast-episodes {
    display: flex; /* 使用Flexbox布局 */
    flex-direction: column; /* 设置为垂直方向 */
    height: 100%; /* 确保容器占满可用高度 */
    padding: 0; /* 移除父容器的统一内边距 */
}

.podcast-episodes h4 {
    flex-shrink: 0;
    margin: 16px 16px 8px 16px; /* 四边外边距，恢复左右间距 */
    font-size: 16px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.episodes-list-container {
    flex-grow: 1; /* 让列表占据所有可用空间 */
    overflow-y: auto; /* 仅在列表内容超出时显示滚动条 */
    padding: 0 16px; /* 恢复左右内边距 */
}

#podcastEpisodesList {
    padding-bottom: 8px; /* 为列表底部增加少量空间 */
}

.load-more-container {
    padding: 8px 16px 2px 16px; /* 减小底部内边距从16px到8px */
    flex-shrink: 0; /* 防止按钮容器被压缩 */
    display: flex;
    justify-content: center; /* 按钮居中 */
}

.podcast-episodes .load-more-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: calc(100% - 28px); /* 减去滚动条可能占用的宽度 */
    max-width: 100%; /* 确保不会超出容器 */
    padding: 6px 16px; /* 调小按钮高度 */
    height: 32px; /* 固定按钮高度，防止加载状态时高度变化 */
    background-color: #4A90E2; /* 主色调蓝色 */
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0; /* 移除外边距，由容器的内边距控制 */
    box-sizing: border-box; /* 确保padding包含在高度内 */
}

.podcast-episodes .load-more-btn:hover:not(:disabled) {
    background-color: #357ABD; /* 悬停时颜色变深 */
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
}

.podcast-episodes .load-more-btn:disabled {
    cursor: not-allowed;
    opacity: 0.7;
    background-color: #4A90E2; /* 保持原色调，通过透明度显示禁用状态 */
    transform: none; /* 禁用时不应用变换效果 */
    box-shadow: none; /* 禁用时不显示阴影 */
}

/* 加载动画 */
.loading-spin-animation {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 播客按钮样式优化 */
.episode-download-btn, .episode-play-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap; /* 防止文字换行 */
}

/* 播放按钮固定宽度 */
.episode-play-btn {
    min-width: 70px; /* 设置最小宽度，适应Play/Pause切换 */
    justify-content: center;
}

/* 优化按钮hover效果 */
.episode-download-btn:hover:not(:disabled) {
    background: #007bff;
    border-color: #007bff;
    color: white;
    transform: translateY(-1px);
}

.episode-play-btn:hover:not(:disabled) {
    background: #28a745;
    border-color: #28a745;
    color: white;
    transform: translateY(-1px);
}

.episode-download-btn:disabled,
.episode-play-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #e9ecef;
    color: #6c757d;
}

.episode-download-btn i.fa-spinner,
.episode-play-btn i.fa-spinner {
    color: #6c757d;
}

/* 播客进度条样式 */
.episode-progress {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 8px;
}

.episode-progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
    border-radius: 2px;
}

.episode-progress.hidden {
    display: none;
}

/* 播客节目卡片样式优化 */
.episode-item {
    margin-bottom: 16px;
    padding: 12px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    width: calc(100% - 8px); /* 调整宽度，减少右侧间距 */
    max-width: 100%;
}

/* === 字幕功能样式 === */

/* Tab导航样式 */
.qa-tabs {
    display: flex;
    border: none;
    margin: 0;
    align-items: center;
}

.qa-tab-btn {
    background: none;
    border: none;
    padding: 8px 16px;
    margin-right: 8px;
    cursor: pointer;
    font-size: 0.95rem;
    font-weight: 500;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
    border-radius: 6px 6px 0 0;
}

.qa-tab-btn:hover {
    color: #2196F3;
    background: rgba(33, 150, 243, 0.05);
}

.qa-tab-btn.active {
    color: #2196F3;
    border-bottom-color: #2196F3;
    background: rgba(33, 150, 243, 0.08);
}

/* Tab内容区域 */
.qa-tab-content {
    display: none;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    width: 100%;
}

.qa-tab-content.active {
    display: flex;
    flex-direction: column;
}

/* 只给字幕Tab内容添加底部间距 */
#subtitleTabContent.qa-tab-content {
    padding-bottom: 15px;
}

/* 当字幕Tab激活时，调整头部间距 */
#subtitleTabBtn.qa-tab-btn.active ~ #subtitleTabContent.active::before,
#subtitleTabContent.qa-tab-content.active {
    margin-top: -7px; /* 字幕tab时减少顶部间距 */
}

/* 字幕上传区域 */
.subtitle-upload-area {
    padding: 30px 20px;
    text-align: center;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.subtitle-upload-zone {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 50px 30px;
    background: #fafafa;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    max-width: 400px;
}

.subtitle-upload-zone:hover,
.subtitle-upload-zone.drag-over {
    border-color: #2196F3;
    background: rgba(33, 150, 243, 0.05);
}

.subtitle-upload-zone i {
    font-size: 2.5rem;
    color: #999;
    margin-bottom: 15px;
    display: block;
}

.subtitle-upload-zone:hover i,
.subtitle-upload-zone.drag-over i {
    color: #2196F3;
}

.subtitle-upload-zone p {
    font-size: 1.1rem;
    color: #666;
    margin: 10px 0 5px 0;
}

.subtitle-upload-zone small {
    color: #999;
    font-size: 0.9rem;
}

/* 字幕显示区域 */
.subtitle-display-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    position: relative;
}

.subtitle-display-area.hidden {
    display: none;
}

/* 清除按钮重新设计 */
.clear-subtitle-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.8rem;
    color: #666;
    cursor: pointer;
    z-index: 10;
    transition: all 0.2s ease;
    backdrop-filter: blur(5px);
}

.clear-subtitle-btn:hover {
    background: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
    color: #dc3545;
}

/* 字幕容器 */
.subtitle-container {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: white;
    min-height: 250px;
    height: 100%; /* 充分利用空间 */
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: #ccc #f5f5f5;
    padding-bottom: 42px; /* 添加底部内边距，防止最后一行被遮挡 */
}

.subtitle-container::-webkit-scrollbar {
    width: 6px;
}

.subtitle-container::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 3px;
}

.subtitle-container::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
    min-height: 120px; /* 增加滚动条最小高度，约3倍长度 */
}

.subtitle-container::-webkit-scrollbar-thumb:hover {
    background: #999;
}

/* === 字幕问答模态框样式 === */
.subtitle-chat-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.subtitle-chat-modal.show {
    opacity: 1;
}

.subtitle-chat-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 1000px; /* 增加宽度约1/4 */
    height: 600px; /* 固定高度 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.subtitle-chat-modal.show .subtitle-chat-content {
    transform: translateY(0);
}

.subtitle-chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px 12px; /* 减小padding */
    border-bottom: 1px solid #f0f0f0;
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

.subtitle-chat-title {
    margin: 0;
    font-size: 1.2rem; /* 减小字号 */
    font-weight: 600;
    color: #2c3e50;
}

.subtitle-chat-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.subtitle-play-btn {
    background: #3498db;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.subtitle-play-btn:hover {
    background: #2980b9;
    transform: scale(1.05);
}

.subtitle-chat-close {
    background: none;
    border: none;
    font-size: 1.8rem;
    color: #7f8c8d;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.subtitle-chat-close:hover {
    background: #e74c3c;
    color: white;
}

.subtitle-chat-body {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    padding: 0;
}

/* 上下文显示区域 */
.subtitle-context-display {
    padding: 12px 24px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    max-height: 135px; /* 增加约1/4高度 */
    overflow-y: auto;
}

.context-paragraph {
    font-size: 0.85rem;
    line-height: 1.6;
    color: #495057;
    margin: 0;
    padding: 10px 15px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
}

.context-paragraph strong {
    color: #2196F3;
    font-weight: 600;
    background: rgba(33, 150, 243, 0.1);
    padding: 2px 4px;
    border-radius: 3px;
}

/* 删除不再使用的样式 */
.context-subtitle,
.context-label,
.context-text,
.context-current,
.context-previous,
.context-next {
    /* 这些类不再需要 */
    display: none;
}

/* 消息区域 */
.subtitle-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 12px 24px; /* 减小padding */
    min-height: 200px;
    background: white;
}

.chat-message {
    margin-bottom: 16px;
    display: flex;
    align-items: flex-start;
}

.chat-message.user {
    justify-content: flex-end;
}

.chat-message.user .message-content {
    background: #3498db;
    color: white;
    border-radius: 18px 18px 4px 18px;
    padding: 8px 14px; /* 减小padding */
    max-width: 70%;
    word-wrap: break-word;
    font-size: 0.9rem; /* 减小字号 */
}

.chat-message.ai {
    justify-content: flex-start;
}

.chat-message.ai .message-content {
    background: #f1f3f4;
    color: #2c3e50;
    border-radius: 18px 18px 18px 4px;
    padding: 8px 14px; /* 减小padding */
    max-width: 85%;
    word-wrap: break-word;
    white-space: pre-wrap;
    line-height: 1.5;
    font-size: 0.9rem; /* 减小字号 */
}

.chat-message.error .message-content {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
    border-radius: 18px 18px 18px 4px;
    padding: 10px 16px;
    max-width: 85%;
}

/* 加载动画 */
.thinking-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
}

.thinking-text {
    color: #7f8c8d;
    font-style: italic;
    font-size: 0.9rem;
}

.thinking-dots {
    display: flex;
    gap: 3px;
}

.thinking-dots span {
    width: 6px;
    height: 6px;
    background: #3498db;
    border-radius: 50%;
    animation: thinking-bounce 1.4s infinite ease-in-out both;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking-bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* 输入区域 */
.subtitle-chat-input {
    padding: 8px 24px 12px; /* 大幅减小上下padding */
    background: white;
    border-top: 1px solid #e9ecef;
}

.subtitle-chat-input .chat-input-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

.subtitle-chat-input .chat-input-container textarea {
    flex: 1;
    resize: none;
    font-size: 0.9rem; /* 减小字号 */
    padding: 8px 12px; /* 减小padding */
}

.subtitle-chat-input .chat-send-btn {
    background: #3498db;
    border: none;
    padding: 6px 16px; /* 减小padding */
    color: #fff;
    border-radius: 6px;
    font-size: 0.85rem; /* 减小字号 */
    cursor: pointer;
    transition: all 0.2s ease;
    width: auto;
    height: 34px; /* 减小高度 */
}

.subtitle-chat-input .chat-send-btn:hover:not(:disabled) {
    background: #2980b9;
    transform: translateY(-1px);
}

.subtitle-chat-input .chat-send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .subtitle-chat-content {
        width: 95%;
        max-height: 90vh;
        margin: 0 10px;
    }
    
    .subtitle-chat-header {
        padding: 16px 20px 12px;
    }
    
    .subtitle-chat-title {
        font-size: 1.1rem;
    }
    
    .subtitle-chat-messages {
        padding: 12px 20px;
        min-height: 150px;
        max-height: 250px;
    }
    
    .subtitle-chat-input {
        padding: 12px 20px 16px;
    }
    
    .context-display {
        padding: 12px 20px;
    }
    
    .chat-message.user .message-content,
    .chat-message.ai .message-content {
        max-width: 90%;
    }
}

/* 字幕行样式 */
.subtitle-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.subtitle-item:last-child {
    border-bottom: none;
}

.subtitle-item:hover {
    background: rgba(33, 150, 243, 0.05);
}

.subtitle-item.active {
    background: rgba(33, 150, 243, 0.15);
    border-left: 4px solid #2196F3;
    border-top: 1px solid rgba(33, 150, 243, 0.2);
    border-bottom: 1px solid rgba(33, 150, 243, 0.2);
    border-right: 1px solid rgba(33, 150, 243, 0.2);
    padding-left: 11px;
    transform: translateX(2px);
}

.subtitle-time {
    font-size: 0.8rem;
    color: #666;
    font-weight: 500;
    margin-bottom: 4px;
}

.subtitle-text {
    font-size: 0.95rem;
    color: #333;
    line-height: 1.5;
    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
}

.subtitle-item.active .subtitle-time {
    color: #2196F3;
}

.subtitle-item.active .subtitle-text {
    color: #1976D2;
    font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .qa-tab-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
    
    .subtitle-upload-zone {
        padding: 30px 15px;
    }
    
    .subtitle-upload-zone i {
        font-size: 2rem;
    }
    
    .subtitle-controls {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
}

/* Loading bubble shrink */
.chat-message.ai.loading .message-content{
    background:transparent;
    padding:4px 0;
    box-shadow:none;
}

/* 在 QA 控制区中，为随时问按钮单独增加左外边距 */
.qa-controls #questionBtn {
    margin-left: 1px !important; /* 调整为所需间距 */
}

/* 移除字幕问答加载提示的转圈spinner */
#subtitleChatModal .chat-message.loading::before {
    display: none !important;
    content: none !important;
}

