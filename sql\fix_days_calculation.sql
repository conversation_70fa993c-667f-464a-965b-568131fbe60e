-- ========================================
-- 修复积分有效期天数计算逻辑
-- 解决购买后立即显示29天而不是30天的问题
-- ========================================

-- 1. 修复获取用户积分批次详情函数中的天数计算
CREATE OR REPLACE FUNCTION public.get_user_credit_batches(p_user_id UUID)
RETURNS TABLE(
    batch_id UUID,
    credits_amount INTEGER,
    remaining_credits INTEGER,
    source_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_expired BOOLEAN,
    days_until_expiry INTEGER,
    order_id VARCHAR(255),
    description TEXT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cb.id,
        cb.credits_amount,
        cb.remaining_credits,
        cb.source_type,
        cb.created_at,
        cb.expires_at,
        cb.is_expired,
        CASE 
            WHEN cb.expires_at <= NOW() THEN 0
            ELSE CEIL(EXTRACT(EPOCH FROM cb.expires_at - NOW()) / 86400)::INTEGER
        END as days_until_expiry,
        cb.order_id,
        cb.description
    FROM public.credit_batches cb
    WHERE cb.user_id = p_user_id 
      AND cb.remaining_credits > 0
      AND NOT cb.is_expired
    ORDER BY cb.created_at ASC; -- 按创建时间排序，体现FIFO原则
END;
$$;

-- 2. 测试修复结果
DO $$
DECLARE
    test_user_id UUID;
    test_record RECORD;
BEGIN
    -- 查找一个有积分的用户进行测试
    SELECT user_id INTO test_user_id
    FROM public.user_credits 
    WHERE credits > 0 
    LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        RAISE NOTICE '测试用户ID: %', test_user_id;
        
        -- 测试修复后的函数
        FOR test_record IN 
            SELECT * FROM public.get_user_credit_batches(test_user_id)
        LOOP
            RAISE NOTICE '批次ID: %, 积分: %, 剩余天数: %, 到期时间: %', 
                         test_record.batch_id, 
                         test_record.remaining_credits, 
                         test_record.days_until_expiry,
                         test_record.expires_at;
        END LOOP;
    ELSE
        RAISE NOTICE '没有找到测试用户';
    END IF;
END;
$$;

-- 3. 验证修复
SELECT 
    'Days calculation fixed successfully!' as status,
    'Now using CEIL(EXTRACT(EPOCH FROM expires_at - NOW()) / 86400) for accurate day calculation' as explanation; 